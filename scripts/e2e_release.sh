#!/bin/bash

echo "Loggin into ecr..."
aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 268764888866.dkr.ecr.eu-west-1.amazonaws.com

APP_NAME="ingestor-e2e"

OUTPUT=$(./gradlew assemble && ./gradlew jib)
if [[ $OUTPUT =~ Built.*/.*:([0-9].[0-9]*.[0-9]*-[a-z0-9]*) ]]; then
  APP_VERSION="${BASH_REMATCH[1]}"
  echo "Installing version ${APP_VERSION}"
  helm package .helm/ --app-version $APP_VERSION
  helm upgrade --install -n e2e $APP_NAME *.tgz -f .helm/values_e2e.yaml
fi
