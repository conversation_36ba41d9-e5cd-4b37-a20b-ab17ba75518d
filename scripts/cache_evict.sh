#!/bin/bash

# IMPORTANT:
# on macos run `brew install util-linux` in order to have setsid available
# without setsid the kubectl forward will be killed as soon as the script is killed, hence the quotes service can not be scaled back to 0

CACHE_NAME=$1
ENV=$2
NAMESPACE="default"

if [ $ENV == "staging" ];  then
  NAMESPACE="staging"
fi

INGESTOR_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=ingestor-$ENV -o jsonpath="{.items[0].metadata.name}")

kubectl port-forward -n $NAMESPACE "$INGESTOR_POD" 5005:8080 &

PORTFORWARD_PROCESS_PID="$!"

echo "K8S Portforward process $PORTFORWARD_PROCESS_PID"

sleep 10

function cleanup {
  echo "Killing K8S Portforward process"
  kill "$PORTFORWARD_PROCESS_PID"
}

trap cleanup EXIT

echo "Evicting ingestor cache $CACHE_NAME."

curl -v -X POST http://localhost:5005/v1/caches/$CACHE_NAME/invalidate
