server.port: 8080

spring:
  profiles:
    active: dev
  application:
    name: WSF Ingestor

service:
  data:
    mongodb:
      main:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
      competitions:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
      allleagues:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
        database: ${ENV_ALL_LEAGUES_MONGO_DB_DATABASE}
  kafka:
    use-iam-authentication: ${ENV_KAFKA_USE_IAM_AUTHENTICATION}
    servers: ${ENV_KAFKA_SERVERS}
    topic:
      player_events: ${ENV_KAFKA_TOPIC_PLAYER_EVENTS}
      player_odds: ${ENV_KAFKA_TOPIC_PLAYER_ODDS}
      team_events: ${ENV_KAFKA_TOPIC_TEAM_EVENTS}
      team_odds: ${ENV_KAFKA_TOPIC_TEAM_ODDS}
      fixture_entity_events: ${ENV_KAFKA_TOPIC_FIXTURE_ENTITY_EVENTS}
      player_transfer_events: ${ENV_KAFKA_TOPIC_PLAYER_TRANSFER_EVENTS}
      live_ratings: ${ENV_KAFKA_TOPIC_LIVE_RATINGS}
      final_fixture_summaries: ${ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES}
      fixture_summary_updates: ${ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES}
      contestant_unavailabilities: ${ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES}
      fixture_changes: ${ENV_KAFKA_TOPIC_FIXTURE_CHANGES}
  aws:
    access-key: ********************
    secret-key: pMez7cC5qsb63lsAqtYbU5HJ4C/+3wgXzT5fLJjJ
  feeds:
    store:
      enabled: ${ENV_STORE_FEEDS}
      type: ${ENV_STORE_TYPE} # "file" or "s3"
      path: ${ENV_STORE_FEEDS_PATH}
  entity-mapper:
    host: ${ENV_ENTITY_MAPPER_HOST}
    port: ${ENV_ENTITY_MAPPER_PORT}

crons:
  current-tournament-updater:
    enabled: ${ENV_TOURNAMENT_CRON_ENABLED}
    schedule: ${ENV_TOURNAMENT_CRON_SCHEDULE}
  squads-updater:
    enabled: ${ENV_SQUADS_CRON_ENABLED}
    schedule: ${ENV_SQUADS_CRON_SCHEDULE}
  matches-updater:
    enabled: ${ENV_MATCHES_CRON_ENABLED}
    schedule: ${ENV_MATCHES_CRON_SCHEDULE}
  matches-watcher-updater:
    enabled: ${ENV_MATCHES_WATCHER_CRON_ENABLED}
    schedule: ${ENV_MATCHES_WATCHER_CRON_SCHEDULE}
  matches-stats-updater:
    enabled: ${ENV_STATS_CRON_ENABLED}
    interval-secs: ${ENV_STATS_CRON_INTERVAL_SEC}
    max-repetitions: ${ENV_STATS_CRON_REPS}
  settlement-updater:
    enabled: ${ENV_SETTLEMENT_CRON_ENABLED}
    interval-secs: ${ENV_SETTLEMENT_CRON_INTERVAL_SEC}
    max-repetitions: ${ENV_SETTLEMENT_CRON_REPS}
  player-availability-updater:
    enabled: ${ENV_AVAILABILITY_CRON_ENABLED}
    schedule: ${ENV_AVAILABILITY_CRON_SCHEDULE}
    days-window: 5
    not-alert-fixtures-within-hours: 61
    alert-fixtures-within-hours: 72

email:
  protocol: smtp
  host: smtp.gmail.com
  port: 587
  username: <EMAIL>
  password: ${ENV_EMAIL_APP_PASSWORD}
  opta:
    recipient: <EMAIL>
    subscribers:
      - <EMAIL>

opta:
  sdapi:
    host: ${ENV_OPTA_SD_URL}
    port: ${ENV_OPTA_SD_PORT}
    outlet-auth-key: ${ENV_OPTA_OUTLET_KEY}
  sddp:
    url: ${ENV_OPTA_SP_URL}
    outlet-auth-key: ${ENV_OPTA_OUTLET_KEY}

sportmonks:
  api:
    v2:
      host: soccer.sportmonks.com
    v3:
      host: api.sportmonks.com
    token: ${ENV_SPORTMONKS_API_TOKEN}

management:
  info.git.mode: full
  endpoints.web:
    exposure:
      include: health, prometheus, info
    base-path: /
  endpoint:
    health.show-details: always

2_HOURS: 7200
3_HOURS: 10800
6_HOURS: 21600
25_HOURS: 90000

cache:
  redis:
    serverless: ${ENV_REDIS_SERVERLESS}
    primaryHost: ${ENV_REDIS_PRIMARY_HOST}
    readerHost: ${ENV_REDIS_READER_HOST}
    timeoutSecs: 60
    db: ${ENV_REDIS_DB}
  caches:
    ingestor_team_active_players:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 100
    ingestor_opta_players:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 2500
    ingestor_opta_fixtures:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 50
    ingestor_team_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 100
    ingestor_player_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 2500
    ingestor_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 50
    ingestor_opta_players_events:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 100000
    ingestor_match_locks:
      redis:
        ttlSecs: ${2_HOURS}
      local:
        ttlSecs: ${2_HOURS}
        maxSize: 50
    ingestor_fixture_summaries:
      redis:
        ttlSecs: ${3_HOURS}
      local:
        ttlSecs: ${3_HOURS}
        maxSize: 200

ENV_ALL_LEAGUES_MONGO_DB_DATABASE: all_leagues

ENV_KAFKA_USE_IAM_AUTHENTICATION: false
ENV_KAFKA_TOPIC_PLAYER_ODDS: "player_odds_triggers"
ENV_KAFKA_TOPIC_TEAM_ODDS: "team_odds_triggers"
ENV_KAFKA_TOPIC_PLAYER_EVENTS: "player_events"
ENV_KAFKA_TOPIC_TEAM_EVENTS: "team_events"
ENV_KAFKA_TOPIC_FIXTURE_ENTITY_EVENTS: "fixture_entity_events"
ENV_KAFKA_TOPIC_PLAYER_TRANSFER_EVENTS: "player_transfer_events"
ENV_KAFKA_TOPIC_LIVE_RATINGS: "live_ratings"
ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES: "final_fixture_summaries"
ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES: "fixture_summary_updates"
ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES: "contestant_unavailabilities"
ENV_KAFKA_TOPIC_FIXTURE_CHANGES: "fixture_changes"

ENV_STORE_FEEDS: false
ENV_STORE_TYPE: file
ENV_STORE_FEEDS_PATH: /tmp/opta_feeds/


ENV_TOURNAMENT_CRON_ENABLED: false
ENV_TOURNAMENT_CRON_SCHEDULE: ""

ENV_SQUADS_CRON_ENABLED: false
ENV_SQUADS_CRON_SCHEDULE: ""

ENV_MATCHES_CRON_ENABLED: false
ENV_MATCHES_CRON_SCHEDULE: ""

ENV_MATCHES_WATCHER_CRON_ENABLED: false
ENV_MATCHES_WATCHER_CRON_SCHEDULE: ""

ENV_STATS_CRON_ENABLED: false
ENV_STATS_CRON_INTERVAL_SEC: 1
ENV_STATS_CRON_REPS: 1

ENV_SETTLEMENT_CRON_ENABLED: false
ENV_SETTLEMENT_CRON_INTERVAL_SEC: 1
ENV_SETTLEMENT_CRON_REPS: 1

ENV_AVAILABILITY_CRON_ENABLED: false
ENV_AVAILABILITY_CRON_SCHEDULE: ""

ENV_REDIS_SERVERLESS: false
ENV_REDIS_PRIMARY_HOST:
ENV_REDIS_READER_HOST:
ENV_REDIS_DB: 0

---
spring:
  config:
    activate:
      on-profile: dev

ENV_COMMON_MONGO_DB_URI_FULL: mongodb://127.0.0.1:27017 #host.docker.internal # just works inside docker to access docker-compose containers, to run without docker(using the ide) change to localhost

ENV_KAFKA_SERVERS: http://localhost:9092

ENV_OPTA_SD_URL: localhost
ENV_OPTA_SD_PORT: 1080
ENV_OPTA_SP_URL: localhost:1080
ENV_OPTA_OUTLET_KEY: "test"

ENV_SPORTMONKS_API_TOKEN: "test"

ENV_ENTITY_MAPPER_HOST: localhost
ENV_ENTITY_MAPPER_PORT: 5000
---
spring:
  config:
    activate:
      on-profile: e2e

ENV_COMMON_MONGO_DB_OPTIONS: socketTimeoutMS=5000&connectTimeoutMS=5000&authSource=admin&maxIdleTimeMS=80000&minPoolSize=0&maxPoolSize=20

ENV_COMMON_MONGO_DB_URI_FULL: ${DB_URL}/?${ENV_COMMON_MONGO_DB_OPTIONS}

ENV_KAFKA_TOPIC_PLAYER_EVENTS: "e2e_player_events"
ENV_KAFKA_TOPIC_TEAM_EVENTS: "e2e_team_events"
ENV_KAFKA_TOPIC_PLAYER_ODDS: "e2e_player_odds_triggers"
ENV_KAFKA_TOPIC_TEAM_ODDS: "e2e_team_odds_triggers"
ENV_KAFKA_TOPIC_FIXTURE_ENTITY_EVENTS: "e2e_fixture_entity_events"
ENV_KAFKA_TOPIC_PLAYER_TRANSFER_EVENTS: "e2e_player_transfer_events"
ENV_KAFKA_TOPIC_LIVE_RATINGS: "e2e_live_ratings"
ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES: "e2e_final_fixture_summaries"
ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES: "e2e_fixture_summary_updates"
ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES: "e2e_contestant_unavailabilities"
ENV_KAFKA_TOPIC_FIXTURE_CHANGES: "e2e_fixture_changes"

ENV_KAFKA_USE_IAM_AUTHENTICATION: true
ENV_KAFKA_SERVERS: b-2.wsfe2etests.l2muj9.c2.kafka.eu-west-1.amazonaws.com:9098,b-1.wsfe2etests.l2muj9.c2.kafka.eu-west-1.amazonaws.com:9098

ENV_OPTA_SD_URL: api.performfeeds.com
ENV_OPTA_SD_PORT: 80
ENV_OPTA_SP_URL: wss://content.performgroup.io

ENV_STORE_FEEDS: false

ENV_REDIS_SERVERLESS: true
ENV_REDIS_PRIMARY_HOST: "wsf-e2e-lwyqkj.serverless.euw1.cache.amazonaws.com"
ENV_REDIS_READER_HOST: "wsf-e2e-lwyqkj.serverless.euw1.cache.amazonaws.com"
ENV_REDIS_DB: 0

ENV_ENTITY_MAPPER_HOST: entity-mapper-e2e
ENV_ENTITY_MAPPER_PORT: 80
---
spring:
  config:
    activate:
      on-profile: sandbox

ENV_COMMON_MONGO_DB_OPTIONS: socketTimeoutMS=5000&connectTimeoutMS=5000&authSource=admin&maxIdleTimeMS=80000&minPoolSize=0&maxPoolSize=20

ENV_COMMON_MONGO_DB_URI_FULL: ${DB_URL}/?${ENV_COMMON_MONGO_DB_OPTIONS}

ENV_KAFKA_TOPIC_PLAYER_EVENTS: "sandbox_player_events"
ENV_KAFKA_TOPIC_TEAM_EVENTS: "sandbox_team_events"
ENV_KAFKA_TOPIC_PLAYER_ODDS: "sandbox_player_odds_triggers"
ENV_KAFKA_TOPIC_TEAM_ODDS: "sandbox_team_odds_triggers"
ENV_KAFKA_TOPIC_FIXTURE_ENTITY_EVENTS: "sandbox_fixture_entity_events"
ENV_KAFKA_TOPIC_PLAYER_TRANSFER_EVENTS: "sandbox_player_transfer_events"
ENV_KAFKA_TOPIC_LIVE_RATINGS: "sandbox_live_ratings"
ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES: "sandbox_final_fixture_summaries"
ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES: "sandbox_fixture_summary_updates"
ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES: "sandbox_contestant_unavailabilities"
ENV_KAFKA_TOPIC_FIXTURE_CHANGES: "sandbox_fixture_changes"

ENV_KAFKA_SERVERS: b-1.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092,b-2.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092

ENV_TOURNAMENT_CRON_ENABLED: false
ENV_TOURNAMENT_CRON_SCHEDULE: 0 55 0 ? * * * # every day at 00:55am
ENV_SQUADS_CRON_ENABLED: false
ENV_SQUADS_CRON_SCHEDULE: 0 0 9 ? * * * # every day at 9am
ENV_MATCHES_CRON_ENABLED: false
ENV_MATCHES_CRON_SCHEDULE: 0 0 10 ? * * * # every day at 10am
ENV_MATCHES_WATCHER_CRON_ENABLED: false
ENV_MATCHES_WATCHER_CRON_SCHEDULE: 0 0 0/3 ? * * * # every 3 hours
ENV_STATS_CRON_ENABLED: false
ENV_STATS_CRON_INTERVAL_SEC: 30
ENV_STATS_CRON_REPS: 1000 # match shouldn't last more than 30 * 1000 = 9000sec = 500min
ENV_SETTLEMENT_CRON_ENABLED: false
ENV_SETTLEMENT_CRON_INTERVAL_SEC: 30
ENV_SETTLEMENT_CRON_REPS: 1
ENV_AVAILABILITY_CRON_ENABLED: false
ENV_AVAILABILITY_CRON_SCHEDULE: 0 17 0,12 ? * * * # At 17 minutes past the hour, every 12 hours

ENV_OPTA_SD_URL: api.performfeeds.com
ENV_OPTA_SD_PORT: 80
ENV_OPTA_SP_URL: wss://content.performgroup.io

ENV_STORE_FEEDS: false

ENV_REDIS_PRIMARY_HOST: "wsf-cluster.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_READER_HOST: "wsf-cluster-ro.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_DB: 1

ENV_ENTITY_MAPPER_HOST: entity-mapper-sandbox
ENV_ENTITY_MAPPER_PORT: 80
---
spring:
  config:
    activate:
      on-profile: staging

ENV_COMMON_MONGO_DB_OPTIONS: socketTimeoutMS=5000&connectTimeoutMS=5000&authSource=admin&maxIdleTimeMS=80000&minPoolSize=1&maxPoolSize=20

ENV_COMMON_MONGO_DB_URI_FULL: ${DB_URL}/?${ENV_COMMON_MONGO_DB_OPTIONS}

ENV_KAFKA_TOPIC_PLAYER_EVENTS: "staging_player_events"
ENV_KAFKA_TOPIC_TEAM_EVENTS: "staging_team_events"
ENV_KAFKA_TOPIC_PLAYER_ODDS: "staging_player_odds_triggers"
ENV_KAFKA_TOPIC_TEAM_ODDS: "staging_team_odds_triggers"
ENV_KAFKA_TOPIC_FIXTURE_ENTITY_EVENTS: "staging_fixture_entity_events"
ENV_KAFKA_TOPIC_PLAYER_TRANSFER_EVENTS: "staging_player_transfer_events"
ENV_KAFKA_TOPIC_LIVE_RATINGS: "staging_live_ratings"
ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES: "staging_contestant_unavailabilities"
ENV_KAFKA_TOPIC_FIXTURE_CHANGES: "staging_fixture_changes"

ENV_KAFKA_SERVERS: b-1.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092,b-2.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092

ENV_TOURNAMENT_CRON_ENABLED: false
ENV_TOURNAMENT_CRON_SCHEDULE: 0 55 0 ? * * * # every day at 00:55am
ENV_SQUADS_CRON_ENABLED: false
ENV_SQUADS_CRON_SCHEDULE: 0 0 9 ? * * * # every day at 9am
ENV_MATCHES_CRON_ENABLED: false
ENV_MATCHES_CRON_SCHEDULE: 0 0 10 ? * * * # every day at 10am
ENV_MATCHES_WATCHER_CRON_ENABLED: true
ENV_MATCHES_WATCHER_CRON_SCHEDULE: 0 0 0/3 ? * * * # every 3 hours
ENV_STATS_CRON_ENABLED: true
ENV_STATS_CRON_INTERVAL_SEC: 60
ENV_STATS_CRON_REPS: 1000 # match shouldn't last more than 30 * 1000 = 9000sec = 500min
ENV_SETTLEMENT_CRON_ENABLED: false
ENV_SETTLEMENT_CRON_INTERVAL_SEC: 30
ENV_SETTLEMENT_CRON_REPS: 1
ENV_AVAILABILITY_CRON_ENABLED: true
ENV_AVAILABILITY_CRON_SCHEDULE: 0 17 0,12 ? * * * # At 17 minutes past the hour, every 12 hours

ENV_OPTA_SD_URL: api.performfeeds.com
ENV_OPTA_SD_PORT: 80
ENV_OPTA_SP_URL: wss://content.performgroup.io

ENV_STORE_FEEDS: false

ENV_REDIS_PRIMARY_HOST: "wsf-cluster.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_READER_HOST: "wsf-cluster-ro.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_DB: 2

ENV_ENTITY_MAPPER_HOST: entity-mapper-staging
ENV_ENTITY_MAPPER_PORT: 80

---
spring:
  config:
    activate:
      on-profile: prod

ENV_COMMON_MONGO_DB_OPTIONS: socketTimeoutMS=5000&connectTimeoutMS=2500&authSource=admin&maxIdleTimeMS=80000&minPoolSize=5&maxPoolSize=20

ENV_COMMON_MONGO_DB_URI_FULL: ${DB_URL}/?${ENV_COMMON_MONGO_DB_OPTIONS}

ENV_KAFKA_SERVERS: b-1.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092,b-2.wsf-cluster.k0kpfg.c3.kafka.eu-west-1.amazonaws.com:9092

ENV_SQUADS_CRON_ENABLED: true
ENV_SQUADS_CRON_SCHEDULE: 0 0 9 ? * * * # every day at 9am utc
ENV_MATCHES_CRON_ENABLED: true
ENV_MATCHES_CRON_SCHEDULE: 0 0 2 ? * * * # every day at 2am utc
ENV_MATCHES_WATCHER_CRON_ENABLED: true
ENV_MATCHES_WATCHER_CRON_SCHEDULE: 0 0 0/2 ? * * * # every 2 hours
ENV_STATS_CRON_ENABLED: true
ENV_STATS_CRON_INTERVAL_SEC: 60
ENV_STATS_CRON_REPS: 600 # mins (3h before + 90mins match + 30m halftime and extra + 5h breaks)
ENV_SETTLEMENT_CRON_ENABLED: true
ENV_SETTLEMENT_CRON_INTERVAL_SEC: 300 # 5m
ENV_SETTLEMENT_CRON_REPS: 72 # 6h
ENV_TOURNAMENT_CRON_ENABLED: true
ENV_TOURNAMENT_CRON_SCHEDULE: 0 55 0 ? * * * # every day at 00:55am utc
ENV_AVAILABILITY_CRON_ENABLED: true
ENV_AVAILABILITY_CRON_SCHEDULE: 0 17 0,12 ? * * * # At 17 minutes past the hour, every 12 hours

ENV_OPTA_SD_URL: api.performfeeds.com
ENV_OPTA_SD_PORT: 80
ENV_OPTA_SP_URL: wss://content.performgroup.io

ENV_STORE_FEEDS: true
ENV_STORE_TYPE: s3

ENV_REDIS_PRIMARY_HOST: "wsf-cluster.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_READER_HOST: "wsf-cluster-ro.lwyqkj.ng.0001.euw1.cache.amazonaws.com"
ENV_REDIS_DB: 0

ENV_ENTITY_MAPPER_HOST: entity-mapper-prod
ENV_ENTITY_MAPPER_PORT: 80
