<html>
<head>
    <title>Insert Stats</title>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.6.6/angular.min.js" type="text/javascript"></script>
    <script src="/js/lib/stats.js" type="text/javascript"></script>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.3/css/bootstrap.min.css"
          integrity="sha384-Zug+QiDoJOrZ5t4lssLdxGhVrurbmBWopoEl+M6BdEfwnCJZtKxi1KgxUyJq13dy" crossorigin="anonymous"/>
</head>
<body ng-app="statsModule" ng-controller="home" data-th-attr="ng-init='init(\'' + ${tournamentId} + '\')'">
<div class="container">
    <h1>Stats</h1>
    <p>Tournament: <span th:text="${tournamentId}"/></p>
    <div ng-show="messageStatsSent" class="alert alert-success">
        {{messageStatsSent}}
    </div>
    <form ng-submit="postStats()">
        <div class="container" ng-repeat="player in players track by $index"
             ng-init="stats[$index].player.id=player.player.id">
            <div class="row">{{player.player.name}}</div>
            <div class="row">
                <div class="col-2 form-group">
                    <label for="avgRatingPlayer">Avg Rating</label>
                    <input type="text" size="2" class="form-control form-control-sm" id="avgRatingPlayer"
                           ng-model="stats[$index].avgTournamentRating"/>
                </div>
                <div class="col-2 form-group">
                    <label for="variancePlayer">Variance</label>
                    <input type="text" size="2" class="form-control form-control-sm" id="variancePlayer"
                           ng-model="stats[$index].ratingVariance"/>
                </div>
                <div class="col-2 form-group">
                    <label for="matchesPlayed">Matches Played</label>
                    <input type="text" size="2" class="form-control form-control-sm" id="matchesPlayed"
                           ng-model="stats[$index].matchesPlayed"/>
                </div>
            </div>
        </div>

        <input type="submit" value="Save Stats"/>
    </form>
</div>
</body>
</html>
