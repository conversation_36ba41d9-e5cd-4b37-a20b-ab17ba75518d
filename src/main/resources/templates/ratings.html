<html>
<head>
    <title>Import Ratings CSV</title>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.6.6/angular.min.js" type="text/javascript"></script>
    <script src="/js/lib/ratings.js" type="text/javascript"></script>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.3/css/bootstrap.min.css"
          integrity="sha384-Zug+QiDoJOrZ5t4lssLdxGhVrurbmBWopoEl+M6BdEfwnCJZtKxi1KgxUyJq13dy" crossorigin="anonymous"/>
</head>
<body ng-app="ratingsModule">
<div class="container">
    <h1>Ratings</h1>
    <div ng-controller="home">
        <input type="file" file-model="myFile"/>
        <button ng-click="uploadFile()">Upload</button>
        {{message}}
    </div>
</div>
</body>
</html>
