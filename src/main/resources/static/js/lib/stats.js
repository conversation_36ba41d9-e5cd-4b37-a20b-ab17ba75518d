angular.module('statsModule', [])
    .controller('home', ['$scope', '$http', function($scope, $http) {

        $scope.stats = [];

        $scope.refreshPlayers = function() {
          $http.get('/tournaments/' + $scope.tournamentId + '/players').then(
            function(data, status, headers, config) {
                $scope.players = data.data;
            }, function(data, status, headers, config) {
                alert( "failure message: " + JSON.stringify({data: data}));
            });
        }

        $scope.postStats = function() {
          $http.post('/tournaments/' + $scope.tournamentId + '/stats', $scope.stats).then(
              function(data, status, headers, config) {
                  $scope.messageStatsSent = "Stats saved";
              }, function(data, status, headers, config) {
                  alert( "failure message: " + JSON.stringify({data: data}));
              }
           );
        };

        $scope.init = function(tournamentId) {
          $scope.tournamentId = tournamentId;
          $scope.refreshPlayers();
        };

    }]);
