var myApp = angular.module('ratingsModule', []);

myApp.directive('fileModel', ['$parse', function ($parse) {
    return {
       restrict: 'A',
       link: function(scope, element, attrs) {
          var model = $parse(attrs.fileModel);
          var modelSetter = model.assign;

          element.bind('change', function() {
             scope.$apply(function() {
                modelSetter(scope, element[0].files[0]);
             });
          });
       }
    };
 }]);

myApp.service('fileUpload', ['$http', function ($http) {
    this.uploadFileToUrl = function(file, uploadUrl) {

    }
 }]);

myApp.controller('home', ['$scope', '$http', function($scope, $http, fileUpload) {
    $scope.uploadFile = function() {
       var file = $scope.myFile;
       console.log('file is ' );
       console.dir(file);
       var uploadUrl = "/ratings/csv";
       var fd = new FormData();
       fd.append('file', file);

       $http.post(uploadUrl, fd, {
          transformRequest: angular.identity,
          headers: {'Content-Type': undefined}
       }).then(function(success) {
          $scope.message = "Ratings saved";
       }).catch(function(error) {
          $scope.message = error.data.message;
       });
    };
 }]);
