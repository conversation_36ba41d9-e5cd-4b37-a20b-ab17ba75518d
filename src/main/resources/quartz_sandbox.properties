# thread-pool
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread=true
# nr of matches in parallel
org.quartz.threadPool.threadCount=2
# job-store
# Enable this property for RAMJobStore
#org.quartz.jobStore.class=org.quartz.simpl.RAMJobStore
# Use the MongoDB store
org.quartz.jobStore.class=com.novemberain.quartz.mongodb.MongoDBJobStore
# database name
org.quartz.jobStore.dbName=quartz
# Will be used to create collections like mycol_jobs, mycol_triggers, mycol_calendars, mycol_locks
org.quartz.jobStore.collectionPrefix=mycol
# thread count setting is ignored by the MongoDB store but Quartz requries it
