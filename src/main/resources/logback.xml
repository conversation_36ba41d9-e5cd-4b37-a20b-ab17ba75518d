<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.wsf.dataingestor" level="${app.log.level:-INFO}"/>
    <logger name="org.springframework.boot.web.embedded.tomcat" level="INFO"/>
    <logger name="com.wsf.dataingestor.opta.clients.handlers.SDDPHandler" level="INFO"/>
    <logger name="com.wsf.dataingestor.services.stats.PlayerStatsEnricher" level="INFO"/>
    <logger name="com.wsf.dataingestor.services.stats.GoalDerivedStatsEnricher" level="INFO"/>
    <logger name="com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingsProcessor" level="INFO"/>
    <logger name="com.wsf.dataingestor.services.stats.TeamDerivedStatsEnricher" level="INFO"/>
    <logger name="com.wsf.dataingestor.services.events.BetStopService" level="INFO"/>

    <root level="${root.log.level:-ERROR}">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
