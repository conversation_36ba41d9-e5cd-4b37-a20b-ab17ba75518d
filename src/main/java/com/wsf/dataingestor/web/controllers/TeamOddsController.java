package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.factories.TeamNewOddsFactory;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.domain.common.Sport;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;
import com.wsf.kafka.domain.TeamNewOdds;
import com.wsf.kafka.domain.metadata.KafkaMetadata;


import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.modules.feed.FeedConstants.MANUAL_FEED_ID;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static java.util.UUID.randomUUID;

@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
public class TeamOddsController {

  private final TeamService teamService;
  private final TournamentService tournamentService;
  private final KafkaService kafkaService;

  @RequestMapping(value = "/competitions/{competitionId}/tournaments/{tournamentId}/teams/{teamId}/markets/{marketId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForTeamAndMarket(@PathVariable String competitionId, @PathVariable String tournamentId,
                                           @PathVariable String teamId, @PathVariable String marketId) {
    Team team = teamService.getById(competitionId, teamId);
    sendNewOddsRequestToKafka(team, tournamentId, marketId);
  }

  private void sendNewOddsRequestToKafka(Team team, String tournamentId, String marketId) {
    String eventId = randomUUID().toString();
    log.info("sending odds request for teamId={} tournamentId={} marketId={} eventId={}", team.getId().toString(),
      tournamentId, marketId, eventId);

    Tournament tournament = tournamentService.findByTournamentId(tournamentId);
    Sport.SportType sportType = tournament.getCompetition().getSport().getType();
    MetadataRetriever metadataRetriever = getMetadataRetriever(sportType, SOCCER_TEAM);
    MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
    KafkaMetadata metadata = metadataRetriever.getMetadata(team.getIdAsString(), List.of(), retrievalFlags);

    TeamNewOdds teamNewOdds = TeamNewOddsFactory.teamNewOdds(team, tournamentId, marketId, metadata, eventId,
      MANUAL_FEED_ID);

    kafkaService.sendGenerateOdds(teamNewOdds);
  }

}
