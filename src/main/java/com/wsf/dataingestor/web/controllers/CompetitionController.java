package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.crons.CurrentTournamentUpdater;
import com.wsf.dataingestor.crons.FixturesUpdater;
import com.wsf.dataingestor.crons.SquadsUpdater;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.domain.common.Competition;

@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/v1/competitions")
public class CompetitionController {

  private final CompetitionService competitionService;
  private final CurrentTournamentUpdater currentTournamentUpdater;
  private final SquadsUpdater squadsUpdater;
  private final FixturesUpdater fixturesUpdater;


  @RequestMapping(value = "/{competitionId}/tournaments/ingest", method = RequestMethod.POST)
  public void createTournamentForCompetition(@PathVariable String competitionId) {
    log.info("Ingesting current tournament for competitionId={}", competitionId);

    Competition competition = competitionService.findByCompetitionId(competitionId);
    currentTournamentUpdater.ingestCurrentTournamentForCompetition(competition);

    log.info("Current tournament ingestion for competitionId={} processed", competitionId);
  }

  @RequestMapping(value = "/{competitionId}/squads/ingest", method = RequestMethod.POST)
  public void createSquadsForCompetition(@PathVariable String competitionId,
                                         @RequestParam(required = false, defaultValue = "false") Boolean forceDeletePlayers) {
    log.info("Ingesting squads for competitionId={} forceDeletePlayers={}", competitionId, forceDeletePlayers);

    squadsUpdater.ingestPlayersForCompetition(competitionId, forceDeletePlayers);

    log.info("Squads ingestion for competitionId={} processed", competitionId);
  }

  @RequestMapping(value = "/{competitionId}/fixtures/ingest", method = RequestMethod.POST)
  public void createFixturesForCompetition(@PathVariable String competitionId) {
    log.info("Ingesting fixtures for competitionId={}", competitionId);

    Competition competition = competitionService.findByCompetitionId(competitionId);
    fixturesUpdater.ingestFixturesForCompetition(competition);

    log.info("Fixtures ingestion for competitionId={} processed", competitionId);
  }
}
