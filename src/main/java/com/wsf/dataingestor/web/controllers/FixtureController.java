package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.NoSuchElementException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.factories.PlayerNewOddsFactory;
import com.wsf.dataingestor.factories.TeamNewOddsFactory;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.opta.parsers.MA1FeedParser;
import com.wsf.dataingestor.opta.services.OptaFixturesRetriever;
import com.wsf.dataingestor.services.FixtureChangeProcessor;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.fixtures.FixturesIngestionService;
import com.wsf.dataingestor.services.ratings.PreFixtureDataProcessor;
import com.wsf.dataingestor.sportmonks.parsers.SeasonFeedParser;
import com.wsf.dataingestor.sportmonks.services.SportmonksFixtureRetriever;
import com.wsf.dataingestor.web.model.FixtureChangeDto;
import com.wsf.dataingestor.web.model.FixtureStatusDTO;
import com.wsf.dataingestor.web.model.MatchStateDto;
import com.wsf.dataingestor.web.services.MatchStateService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.PlayerNewOdds;
import com.wsf.kafka.domain.TeamNewOdds;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.modules.feed.FeedConstants.MANUAL_FEED_ID;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static java.util.Objects.nonNull;
import static java.util.UUID.randomUUID;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1")
public class FixtureController {

  private final OptaFixturesRetriever optaFixturesRetriever;
  private final SportmonksFixtureRetriever sportmonksFixtureRetriever;
  private final MA1FeedParser ma1FeedParser;
  private final SeasonFeedParser seasonFeedParser;
  private final FixtureService fixtureService;
  private final FixtureChangeProcessor fixtureChangeProcessor;
  private final PlayerService playerService;
  private final KafkaService kafkaService;

  private final PreFixtureDataProcessor preFixtureDataProcessor;
  private final FixturesIngestionService fixturesIngestionService;
  private final MatchStateService matchStateService;

  @RequestMapping(value = "/fixtures/sportmonks/season", method = RequestMethod.POST)
  public void createSportmonksMatches(@RequestBody String seasonFeed) throws IOException {
    FixturesFeed feed = seasonFeedParser.parseFeed(seasonFeed);

    log.info("Received season feedId={}", feed.getFeedId());

    fixturesIngestionService.processFixturesFeed(sportmonksFixtureRetriever, feed);

    log.info("feed season feedId={} processed", feed.getFeedId());
  }

  @RequestMapping(value = "/fixtures/opta/ma1", method = RequestMethod.POST)
  public void createOptaMatches(@RequestBody String ma1feed) throws IOException {
    FixturesFeed ma1ParsedFeed = ma1FeedParser.parseFeed(ma1feed);

    log.info("Received ma1 feedId={}", ma1ParsedFeed.getFeedId());

    fixturesIngestionService.processFixturesFeed(optaFixturesRetriever, ma1ParsedFeed);

    log.info("feed ma1 feedId={} processed", ma1ParsedFeed.getFeedId());
  }

  @RequestMapping(value = "/fixtures", method = RequestMethod.GET)
  public List<Fixture> getFixtures(@RequestParam String tournamentId) {
    return fixtureService.getAllFixturesOrderedBySequentialId(tournamentId);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/pre-match-check", method = RequestMethod.POST)
  public void preMatchCheck(@PathVariable String fixtureId) {
    Fixture fixture = fixtureService.getFixture(fixtureId);
    preFixtureDataProcessor.sendPreFixture(fixture);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/match-state", method = RequestMethod.GET)
  public ResponseEntity<Object> getFixtureMatchStatus(@PathVariable String fixtureId) {
    try {
      var fixture = fixtureService.getFixture(fixtureId);
      MatchStateDto matchStateDto = matchStateService.createMatchStatusData(fixture);
      return ResponseEntity.ok(matchStateDto);
    } catch (NoSuchElementException noSuchElementException) {
      return ResponseEntity.status(HttpStatus.GONE).body("fixture not found");
    }
  }

  @RequestMapping(value = "/fixtures/{fixtureId}", method = RequestMethod.PATCH)
  public Fixture patchMatch(@PathVariable String fixtureId, @RequestBody FixtureStatusDTO patchedFixture) {
    log.info("Patching fixtureId={}: {}", fixtureId, patchedFixture);

    Fixture fixture = fixtureService.getFixture(fixtureId);
    if (nonNull(patchedFixture.getStatus())) {
      fixture.setStatus(patchedFixture.getStatus());
      fixture.setProcessStatus(patchedFixture.getProcessStatus());
    }

    fixtureService.saveFixture(fixture);
    return fixture;
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForFixture(@PathVariable String fixtureId) {
    Fixture fixture = fixtureService.getFixture(fixtureId);
    List<Player> allPlayersForFixture = playerService.getActivePlayersForFixture(fixture);
    String eventId = randomUUID().toString();

    log.info("generating odds for fixtureId={} eventId={}", fixtureId, eventId);

    MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
    MetadataRetriever playerMetadataRetriever = getMetadataRetriever(fixture, SOCCER_PLAYER);
    MetadataRetriever teamMetadataRetriever = getMetadataRetriever(fixture, SOCCER_TEAM);

    allPlayersForFixture.forEach(player -> {
      KafkaMetadata metadata = playerMetadataRetriever.getMetadata(player.getIdAsString(), List.of(), retrievalFlags);
      PlayerNewOdds playerNewOdds = PlayerNewOddsFactory.withoutMarketId(player, metadata, eventId, MANUAL_FEED_ID);
      kafkaService.sendGenerateOdds(playerNewOdds);
    });

    String tournamentId = fixture.getTournament().getIdAsString();

    Team homeTeam = fixture.getHomeTeam();
    Team awayTeam = fixture.getAwayTeam();

    KafkaMetadata homeMetadata = teamMetadataRetriever.getMetadata(homeTeam.getIdAsString(), List.of(), retrievalFlags);
    KafkaMetadata awayMetadata = teamMetadataRetriever.getMetadata(awayTeam.getIdAsString(), List.of(), retrievalFlags);

    TeamNewOdds homeNewOdds = TeamNewOddsFactory.withoutMarketId(homeTeam, tournamentId, homeMetadata, eventId,
      MANUAL_FEED_ID);
    TeamNewOdds awayNewOdds = TeamNewOddsFactory.withoutMarketId(awayTeam, tournamentId, awayMetadata, eventId,
      MANUAL_FEED_ID);

    kafkaService.sendGenerateOdds(homeNewOdds);
    kafkaService.sendGenerateOdds(awayNewOdds);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/changed", method = RequestMethod.POST)
  public void processFixtureChangeEvent(@PathVariable String fixtureId,
                                        @RequestBody FixtureChangeDto fixtureChangeDto) {
    log.info("Received fixture change event for fixtureId={}: {}", fixtureId, fixtureChangeDto);
    fixtureChangeProcessor.process(fixtureId, fixtureChangeDto);
  }
}
