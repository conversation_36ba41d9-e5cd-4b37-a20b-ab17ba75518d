package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.factories.PlayerNewOddsFactory;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.PlayerNewOdds;
import com.wsf.kafka.domain.metadata.KafkaMetadata;


import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.modules.feed.FeedConstants.MANUAL_FEED_ID;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.domain.common.Sport.SportType;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.groupingBy;

@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
public class PlayerOddsController {

  private final PlayerService playerService;
  private final KafkaService kafkaService;
  private final CompetitionService competitionService;

  @RequestMapping(value = "/competitions/{competitionId}/players/{playerId}/markets/{marketId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForPlayerAndMarket(@PathVariable String competitionId, @PathVariable String playerId,
                                             @PathVariable String marketId) {
    Player player = playerService.getOrThrowById(competitionId, playerId);
    sendNewOddsRequestToKafka(player, marketId);
  }

  @RequestMapping(value = "/competitions/{competitionId}/tournaments/{tournamentId}/teams/{teamId}/players/markets/{marketId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForTeamPlayersAndMarket(@PathVariable String competitionId, @PathVariable String tournamentId,
                                                  @PathVariable String teamId, @PathVariable String marketId) {
    List<Player> teamPlayers = playerService.getActivePlayersByTeamIdAndTournamentId(competitionId, teamId,
      tournamentId);

    teamPlayers.forEach(player -> sendNewOddsRequestToKafka(player, marketId));
  }

  @RequestMapping(value = "/competitions/{competitionId}/players/{playerId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForPlayer(@PathVariable String competitionId, @PathVariable String playerId) {
    Player player = playerService.getOrThrowById(competitionId, playerId);
    sendNewOddsRequestToKafka(player, null);
  }

  @RequestMapping(value = "/competitions/{competitionId}/tournaments/{tournamentId}/teams/{teamId}/players/generate-odds", method = RequestMethod.POST)
  public void generateOddsForTeamPlayers(@PathVariable String competitionId, @PathVariable String tournamentId,
                                         @PathVariable String teamId) {
    List<Player> teamPlayers = playerService.getActivePlayersByTeamIdAndTournamentId(competitionId, teamId,
      tournamentId);
    teamPlayers.forEach(player -> sendNewOddsRequestToKafka(player, null));
  }

  @RequestMapping(value = "/competitions/{competitionId}/tournaments/{tournamentId}/generate-odds", method = RequestMethod.POST)
  public void generateOddsForTournament(@PathVariable String competitionId, @PathVariable String tournamentId) {

    Map<String, List<Player>> teamIdToPlayers = playerService.getAllPlayersByTournamentId(competitionId, tournamentId)
      .stream()
      .collect(groupingBy(p -> p.getTeam().getId().toString()));

    teamIdToPlayers.forEach((teamId, players) -> {
      players.forEach(player -> sendNewOddsRequestToKafka(player, null));
    });
  }

  private void sendNewOddsRequestToKafka(Player player, String marketId) {
    String eventId = randomUUID().toString();
    log.info("sending odds request for playerId={} tournamentId={} marketId={} eventId={}", player.getId().toString(),
      player.getTournament().getId().toString(), marketId, eventId);

    Competition competition = competitionService.findByCompetitionId(
      player.getTournament().getCompetition().getIdAsString());

    SportType sportType = competition.getSport().getType();
    MetadataRetriever metadataRetriever = getMetadataRetriever(sportType, SOCCER_PLAYER);
    MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
    KafkaMetadata metadata = metadataRetriever.getMetadata(player.getIdAsString(), List.of(), retrievalFlags);

    PlayerNewOdds playerNewOdds = PlayerNewOddsFactory.playerNewOdds(player, marketId, metadata, eventId,
      MANUAL_FEED_ID);
    kafkaService.sendGenerateOdds(playerNewOdds);
  }

}
