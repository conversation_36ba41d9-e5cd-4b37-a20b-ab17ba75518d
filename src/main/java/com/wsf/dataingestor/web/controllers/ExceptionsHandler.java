package com.wsf.dataingestor.web.controllers;

import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import com.wsf.dataingestor.exceptions.ResourceNotFoundException;
import com.wsf.dataingestor.metrics.MetricsManager;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class ExceptionsHandler {

  private final MetricsManager metricsRegistry;

  @ExceptionHandler(ConstraintViolationException.class)
  public ResponseEntity<String> handleBadRequest(WebRequest req, ConstraintViolationException exception) {
    log.error("Error processing request: {}", req.getDescription(true), exception);
    logMetrics(exception);

    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
  }

  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<String> handleNotFound(WebRequest req, ResourceNotFoundException exception) {
    log.error("Resource not found: {}", req.getDescription(true), exception);
    logMetrics(exception);

    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(exception.getMessage());
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<String> handle(WebRequest req, Exception exception) {
    log.error("Error processing request: {}", req.getDescription(true), exception);
    logMetrics(exception);

    return ResponseEntity
      .status(HttpStatus.SERVICE_UNAVAILABLE)
      .body("an error occurred with your request. Please contact us if the issue persists");
  }

  private void logMetrics(Exception exception) {
    if (StringUtils.containsIgnoreCase(ExceptionUtils.getRootCauseMessage(exception), "Broken pipe")) {
      // needed to test this issue and not alert in grafana every time it happens
      metricsRegistry.BROKEN_PIPE_ERROR.increment();
    } else {
      metricsRegistry.API_ERROR.increment();
    }
  }

}
