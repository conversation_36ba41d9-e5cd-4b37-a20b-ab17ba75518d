package com.wsf.dataingestor.web.controllers;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.crons.SettlementUpdater;
import com.wsf.dataingestor.crons.SettlementUpdater.SettlementResultAndMatchStatus;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPFeedParser;
import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser;
import com.wsf.dataingestor.opta.parsers.ma3.MA3FeedParser;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ratings.FinalFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.LineUpService;
import com.wsf.dataingestor.services.ratings.MatchDataFeedRequestMapper;
import com.wsf.dataingestor.services.ratings.MatchDataFeedRequestMapper.MatchStatsDto;
import com.wsf.dataingestor.services.ratings.MatchSettlementProcessor;
import com.wsf.dataingestor.services.ratings.player.PlayerFinalRatingsProcessor;
import com.wsf.dataingestor.sportmonks.parsers.StatsFeedParser;
import com.wsf.dataingestor.web.services.RatingsProcessorService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.MANUAL_SETTLEMENT_FEED_ID;
import static com.wsf.domain.common.Fixture.FixtureStatus.PLAYED;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.UNINITIALIZED;
import static java.time.Instant.now;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1")
public class RatingController {

  private final ObjectMapper jsonObjectMapper;
  private final FixtureService fixtureService;
  private final LineUpService lineUpService;
  private final PlayerService playerService;
  private final MA2FeedParser ma2FeedParserFinal;
  private final MA2FeedParser ma2FeedParserLive;
  private final MA3FeedParser ma3FeedParser;
  private final MA18DPFeedParser ma18FeedParser;
  private final StatsFeedParser statsFeedParser;
  private final SettlementUpdater settlementUpdater;
  private final RatingsProcessorService ratingsProcessorService;
  private final MatchSettlementProcessor matchSettlementProcessor;
  private final MatchDataFeedRequestMapper matchDataFeedRequestMapper;
  private final PlayerFinalRatingsProcessor playerFinalRatingsProcessor;
  private final FinalFixtureSummaryNotifier finalFixtureSummaryNotifier;

  public RatingController(ObjectMapper om, FixtureService fixtureService, LineUpService lineUpService,
                          PlayerService playerService,
                          @Qualifier("ma2FeedParserFinal") MA2FeedParser ma2FeedParserFinal,
                          @Qualifier("ma2FeedParserLive") MA2FeedParser ma2FeedParserLive, MA3FeedParser ma3FeedParser,
                          MA18DPFeedParser ma18FeedParser, StatsFeedParser statsFeedParser,
                          SettlementUpdater settlementUpdater, RatingsProcessorService ratingsProcessorService,
                          MatchSettlementProcessor matchSettlementProcessor,
                          MatchDataFeedRequestMapper matchDataFeedRequestMapper,
                          PlayerFinalRatingsProcessor playerFinalRatingsProcessor,
                          FinalFixtureSummaryNotifier finalFixtureSummaryNotifier) {
    jsonObjectMapper = om;
    this.fixtureService = fixtureService;
    this.lineUpService = lineUpService;
    this.playerService = playerService;
    this.ma2FeedParserFinal = ma2FeedParserFinal;
    this.ma2FeedParserLive = ma2FeedParserLive;
    this.ma3FeedParser = ma3FeedParser;
    this.ma18FeedParser = ma18FeedParser;
    this.statsFeedParser = statsFeedParser;
    this.settlementUpdater = settlementUpdater;
    this.ratingsProcessorService = ratingsProcessorService;
    this.matchSettlementProcessor = matchSettlementProcessor;
    this.matchDataFeedRequestMapper = matchDataFeedRequestMapper;
    this.playerFinalRatingsProcessor = playerFinalRatingsProcessor;
    this.finalFixtureSummaryNotifier = finalFixtureSummaryNotifier;
  }

  @RequestMapping(value = "/simulator-settle", method = RequestMethod.POST)
  public void postSettlementOpta(@RequestHeader(value = "filenameMA2", required = false) String ma2FileName,
                                 @RequestHeader(value = "filenameMA3", required = false) String ma3FileName,
                                 @RequestBody String ma2AndMa3Feed) throws IOException {
    var feeds = jsonObjectMapper.readValue(ma2AndMa3Feed, HashMap.class);

    var ma2RawFeed = jsonObjectMapper.writeValueAsString(feeds.get("ma2"));
    var ma3RawFeed = jsonObjectMapper.writeValueAsString(feeds.get("ma3"));

    if (ma2RawFeed == null || ma3RawFeed == null) {
      throw new IllegalArgumentException("Invalid parameters: provide { \"ma2\":{feed},\"ma3\":{feed}");
    }
    MatchDataFeed ma2Feed = ma2FeedParserFinal.parseFeed(ma2RawFeed);
    MatchEventsFeed ma3Feed = ma3FeedParser.parseFeed(ma3RawFeed);
    Fixture fixture = ma2Feed.getFixture();

    log.info("Received ma2 filename={} for fixtureId={}", ma2FileName, fixture.getId().toString());
    log.info("Received ma3 filename={} for fixtureId={}", ma3FileName, fixture.getId().toString());

    ma2Feed.mergeEvents(ma3Feed);
    ratingsProcessorService.processSettlement(ma2Feed);

    log.info("feed ma2 filename={} processed", ma2FileName);
    log.info("feed ma3 filename={} processed", ma3FileName);
  }

  @RequestMapping(value = "/simulator-settle/sportmonks", method = RequestMethod.POST)
  public void postSettlementSportmonks(@RequestBody String fixtureStatsFeed) throws IOException {
    if (fixtureStatsFeed == null) {
      throw new IllegalArgumentException("Invalid fixtureStatsFeed");
    }

    MatchDataFeed statsFeed = statsFeedParser.parseFeed(fixtureStatsFeed);
    Fixture fixture = statsFeed.getFixture();
    log.info("Received fixtureStats file={} for fixtureId={}", fixtureStatsFeed, fixture.getIdAsString());

    ratingsProcessorService.processSettlement(statsFeed);
    log.info("fixtureStats feed processed");
  }

  /**
   * @param fixtureId
   * @param forceSettlement settle fixture even if status is not PLAYED or CANCELLED
   */
  @RequestMapping(value = "/fixtures/{fixtureId}/settle", method = RequestMethod.POST)
  public void postSettlement(@PathVariable String fixtureId, @RequestParam(required = false) boolean forceSettlement) {
    Fixture fixture = fixtureService.getFixture(fixtureId);
    SettlementResultAndMatchStatus result = settlementUpdater.tryMatchSettlement(fixture, forceSettlement);
    if (result.isSettlementDone()) {
      fixtureService.updateFixture(fixture, Map.of("status", result.getFixtureStatus()));
    }
  }

  @RequestMapping(value = "/stats/ma2", method = RequestMethod.POST)
  public void postMA2Feed(@RequestHeader(value = "filename", required = false) String filename,
                          @RequestParam(required = false, defaultValue = "false") Boolean updatePlayerPositions,
                          @RequestBody String ma2feed) throws IOException {

    MatchDataFeed matchDataFeed = ma2FeedParserLive.parseFeed(ma2feed);
    Fixture fixture = matchDataFeed.getFixture();

    log.info("Received ma2 filename={} for fixtureId={}", filename, fixture.getId().toString());

    if (updatePlayerPositions) {
      lineUpService.updatePlayersPosition(matchDataFeed);
    }

    if (matchDataFeed.getFixtureStatus().isFinished()) {
      ratingsProcessorService.processFinishedMatch(matchDataFeed);
    } else if (LIVE == matchDataFeed.getFixtureStatus()) {
      ratingsProcessorService.processSnapshotRatings(matchDataFeed);
    }

    log.info("feed ma2 filename={} processed", filename);
  }

  @RequestMapping(value = "/stats/ma18dp", method = RequestMethod.POST)
  public void postMA18DPFeed(@RequestHeader(value = "filename", required = false) String filename,
                             @RequestBody String ma18dpFeed) throws IOException {

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(ma18dpFeed);
    Fixture fixture = matchDataFeed.getFixture();

    log.info("Received ma18dp filename={} for fixtureId={}", filename, fixture.getId().toString());

    ratingsProcessorService.processLiveRatings(matchDataFeed);

    log.info("feed ma18dp filename={} processed", filename);
  }

  @RequestMapping(value = "/lineups/ma2", method = RequestMethod.POST)
  public void postMA2FeedLineups(@RequestHeader(value = "filename", required = false) String filename,
                                 @RequestBody String ma2feed) throws IOException {
    MatchDataFeed matchDataFeed = ma2FeedParserLive.parseFeed(ma2feed);
    Fixture fixture = matchDataFeed.getFixture();

    log.info("Lineups: received ma2 filename={} for fixtureId={}", filename, fixture.getId().toString());

    ratingsProcessorService.processLineUps(matchDataFeed, fixture);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/startmatch", method = RequestMethod.POST)
  public void postStartMatch(@PathVariable String fixtureId) {
    log.info("Start Fixture: received start fixture for fixtureId={}", fixtureId);
    ratingsProcessorService.startFixture(fixtureId);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/manual-settle", method = RequestMethod.POST)
  public void postManualSettlement(@PathVariable String fixtureId, @RequestBody @Valid MatchStatsDto matchStatsDto) {
    MatchDataFeed matchDataFeed = matchDataFeedRequestMapper.createMatchDataFeedForManualSettlement(fixtureId,
      matchStatsDto);
    matchSettlementProcessor.processFeed(matchDataFeed, true);
    finalFixtureSummaryNotifier.sendSettlementSummary(matchDataFeed);

    Fixture fixture = fixtureService.getFixture(fixtureId);
    fixtureService.storeFixtureFinished(fixture);
  }

  @RequestMapping(value = "/fixtures/{fixtureId}/players/{playerId}/settle/void", method = RequestMethod.POST)
  public void postManualSettlementForPlayer(@PathVariable String fixtureId, @PathVariable String playerId) {
    log.info("Manual settlement: voiding playerId={} for fixtureId={}", playerId, fixtureId);
    Fixture fixture = fixtureService.getFixture(fixtureId);
    Player player = playerService.getOrThrowById(fixture.getTournament().getCompetitionId(), playerId);

    playerFinalRatingsProcessor.settlePlayer(fixture, PLAYED, player, MANUAL_SETTLEMENT_FEED_ID, now(), UNINITIALIZED);
  }
}
