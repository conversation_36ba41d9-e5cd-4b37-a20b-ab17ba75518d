package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.tournaments.TournamentsIngestionService;
import com.wsf.dataingestor.sportmonks.parsers.LeagueFeedParser;

@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/v1")
public class TournamentController {

  private final LeagueFeedParser leagueFeedParser;
  private final TournamentsIngestionService tournamentsIngestionService;

  @RequestMapping(value = "/tournaments/current/sportmonks", method = RequestMethod.POST)
  public void createCurrentTournamentFromSportmonks(@RequestBody String tournamentFeed) throws IOException {

    CurrentTournamentFeed currentTournamentFeed = leagueFeedParser.parseFeed(tournamentFeed);

    log.info("Received sm current tournament feed feedId={} for competitionId={}", currentTournamentFeed.getFeedId(),
      currentTournamentFeed.getCompetition().getId().toString());

    tournamentsIngestionService.processTournamentFeed(List.of(currentTournamentFeed));

    log.info("feed sm current tournament feedId={} processed", currentTournamentFeed.getFeedId());
  }
}
