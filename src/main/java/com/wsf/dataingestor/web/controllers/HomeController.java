package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@RequiredArgsConstructor
@Controller
public class HomeController {

  @RequestMapping(value = "/ui/ratings", produces = MediaType.TEXT_HTML_VALUE)
  public String ratings() {
    return "ratings";
  }

  @RequestMapping(value = "/ui/tournaments/{tournamentId}/stats", produces = MediaType.TEXT_HTML_VALUE)
  public String stats(@PathVariable String tournamentId, Model model) {
    model.addAttribute("tournamentId", tournamentId);
    return "statistics";
  }
}
