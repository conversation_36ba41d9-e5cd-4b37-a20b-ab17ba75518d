package com.wsf.dataingestor.web.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.config.cache.CacheClearer;

import static java.util.Optional.ofNullable;

@Slf4j
@RestController
@RequestMapping("/v1/caches")
@RequiredArgsConstructor
public class CacheManagementController {

  private final CacheManager cacheManager;
  private final CacheClearer cacheClearer;

  @RequestMapping(value = "/{cacheName}/invalidate", method = RequestMethod.POST)
  public ResponseEntity<Object> evictCache(@PathVariable String cacheName) {
    return ofNullable(cacheManager.getCache(cacheName))
      .map(cache -> {
        cacheClearer.clearCache(cacheName);
        return ResponseEntity.ok().build();
      })
      .orElse(ResponseEntity.notFound().build());
  }
}
