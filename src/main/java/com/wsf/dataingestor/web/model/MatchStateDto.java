package com.wsf.dataingestor.web.model;

import lombok.Builder;

import java.io.Serializable;
import com.wsf.domain.common.Fixture.FixtureStatus;

@Builder
public record MatchStateDto(FixtureStatus status, MatchDataDto data) implements Serializable {
  @Builder
  public record MatchDataDto(Integer matchTime,
                             Integer matchPeriod,
                             Integer homeScore,
                             Integer awayScore,
                             Integer homeSentOff,
                             Integer awaySentOff) {}
}
