package com.wsf.dataingestor.web.model;

import lombok.Builder;

import java.time.Instant;
import com.wsf.domain.common.Fixture.FixtureProcessStatus;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.kafka.domain.EventInfo;

@Builder
public record FixtureChangeDto(OperationType operationType,
                               Instant originalFixtureDate,
                               FixtureStatus originalFixtureStatus,
                               FixtureProcessStatus originalProcessStatus,
                               EventInfo eventInfo) {
  public enum OperationType {
    NEW,
    UPDATE
  }
}