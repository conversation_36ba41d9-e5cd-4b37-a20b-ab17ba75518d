package com.wsf.dataingestor.web.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.services.OptaFixtureDataProcessor;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ratings.FinalFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.FixtureStartDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchFinalDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchSettlementProcessor;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.cache.OngoingMatchDataCacheService.buildInitialOngoingMatchData;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;

@Slf4j
@Service
@RequiredArgsConstructor
public class RatingsProcessorService {

  private final OngoingMatchDataCacheService ongoingMatchDataCache;
  private final FixtureStartDataProcessor fixtureStartDataProcessor;
  private final OptaFixtureDataProcessor matchDataRetriever;
  private final OptaFixturePushDataRetriever matchPushDataRetriever;
  private final MatchFinalDataProcessor matchFinalDataProcessor;
  private final MatchSettlementProcessor matchSettlementProcessor;
  private final FinalFixtureSummaryNotifier finalFixtureSummaryNotifier;
  private final FixtureService fixtureService;

  public void startFixture(String fixtureId) {
    Fixture fixture = fixtureService.getFixture(fixtureId);
    fixtureService.storeFixtureProcessStatusConnected(fixture, ExternalProvider.OPTA);
  }

  public void processLineUps(MatchDataFeed feed, Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    ongoingMatchDataCache.remove(fixtureId);
    OngoingMatchData cachedData = ongoingMatchDataCache.getOrCompute(fixtureId,
      fId -> buildInitialOngoingMatchData(fId, feed.getLatestUpdateTs()));
    fixtureStartDataProcessor.processFeed(fixture, feed, cachedData);
    ongoingMatchDataCache.set(fixtureId, cachedData);
  }

  public void processSnapshotRatings(MatchDataFeed matchDataFeed) {
    Fixture fixture = matchDataFeed.getFixture();
    log.info("feed with feedId={} fixtureId={} received", matchDataFeed.getFeedId(), fixture.getId().toString());
    MatchDataFeed.FeedFixtureStatus fixtureStatus = matchDataRetriever.processMA2(matchDataFeed).getFixtureStatus();
    if (PLAYED == fixtureStatus) {
      fixtureService.storeFixtureFinished(fixture);
    }
  }

  public void processLiveRatings(MatchDataFeed matchDataFeed) {
    matchPushDataRetriever.processMA18DPFeed(matchDataFeed);
  }

  public void processFinishedMatch(MatchDataFeed matchDataFeed) {
    matchFinalDataProcessor.processFeed(matchDataFeed);
    Fixture fixture = matchDataFeed.getFixture();
    fixtureService.storeFixtureFinished(fixture);
  }

  public void processSettlement(MatchDataFeed feed) {
    Fixture fixture = feed.getFixture();
    matchSettlementProcessor.processFeed(feed, false);
    finalFixtureSummaryNotifier.sendSettlementSummary(feed);
    fixtureService.storeFixtureFinished(fixture);
    fixtureService.storeFixtureProcessStatusSettled(fixture);
  }
}
