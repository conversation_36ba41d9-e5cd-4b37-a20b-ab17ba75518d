package com.wsf.dataingestor.web.services;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.web.model.MatchStateDto;
import com.wsf.dataingestor.web.model.MatchStateDto.MatchDataDto;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static java.util.stream.Collectors.groupingBy;

@Service
@RequiredArgsConstructor
public class MatchStateService {
  private final OngoingMatchDataCacheService ongoingMatchDataCacheService;

  public MatchStateDto createMatchStatusData(Fixture fixture) {
    if (fixture.getStatus() != Fixture.FixtureStatus.LIVE) {
      var data = new MatchDataDto(0, MatchPeriod.FIRST_HALF.getPeriodId(), 0, 0, 0, 0);
      return new MatchStateDto(fixture.getStatus(), data);
    }

    OngoingMatchData ongoingMatchData = ongoingMatchDataCacheService.get(fixture.getIdAsString());
    String homeTeamId = fixture.getHomeTeam().getIdAsString();
    String awayTeamId = fixture.getAwayTeam().getIdAsString();
    Map<String, List<PlayerMatchEventDTO>> teamIdToRedCards = ongoingMatchData.getEventIdToPlayerMatchEvents().values()
      .stream()
      .flatMap(Set::stream)
      .filter(playerMatchEventDTO -> RED_CARD == playerMatchEventDTO.getEvent())
      .collect(groupingBy(PlayerMatchEventDTO::getTeamId));

    int homeSentOff = teamIdToRedCards.getOrDefault(homeTeamId, List.of())
      .size();
    int awaySentOff = teamIdToRedCards.getOrDefault(awayTeamId, List.of())
      .size();

    var data = new MatchDataDto(ongoingMatchData.getMatchTime(), ongoingMatchData.getMatchPeriod().getPeriodId(),
      ongoingMatchData.getHomeScore(), ongoingMatchData.getAwayScore(), homeSentOff, awaySentOff);
    return new MatchStateDto(fixture.getStatus(), data);
  }
}
