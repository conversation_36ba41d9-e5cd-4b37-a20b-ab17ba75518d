package com.wsf.dataingestor.utils.http;

import lombok.RequiredArgsConstructor;

import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RetryableHttpClient {

  private static final HttpResponse.BodyHandler<byte[]> AS_BYTE_ARRAY = HttpResponse.BodyHandlers.ofByteArray();

  private final HttpClient httpClient;

  @Retryable(value = IOException.class, maxAttempts = 3)
  public HttpResponse<byte[]> send(HttpRequest request) throws IOException, InterruptedException {
    return httpClient.send(request, AS_BYTE_ARRAY);
  }


}
