package com.wsf.dataingestor.utils;

import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

import static com.google.common.primitives.Bytes.concat;
import static org.apache.commons.lang3.StringUtils.rightPad;

@UtilityClass
public class NumberUtils {

  private static final String ONE_AS_STR = "1";
  private static final char ZERO_AS_CHAR = '0';

  public float addCommaToInt(int num, int nDecimals) {
    String divider = rightPad(ONE_AS_STR, nDecimals + 1, ZERO_AS_CHAR);
    return num / Float.parseFloat(divider);
  }

  public int removeCommaAndReturnInt(float number, int nDecimals) {
    BigDecimal rounded = roundDecimals(number, nDecimals);
    String floatStr = String.valueOf(rounded).replace(".", "").replace(",", "");
    return Integer.parseInt(floatStr);
  }

  public static BigDecimal roundDecimals(float d, int decimalPlace) {
    BigDecimal bd = new BigDecimal(Float.toString(d));
    bd = bd.setScale(decimalPlace, RoundingMode.HALF_UP);
    return bd;
  }

  private static byte[] bigDecimalToByte(BigDecimal num) {
    BigInteger sig = new BigInteger(num.unscaledValue().toString());
    int scale = num.scale();
    byte[] bscale = new byte[] {(byte) (scale >>> 24), (byte) (scale >>> 16), (byte) (scale >>> 8), (byte) (scale)};
    return concat(bscale, sig.toByteArray());
  }

}
