package com.wsf.dataingestor.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;

import static java.time.temporal.ChronoUnit.DAYS;

public class TimeUtils {

  public final static ZoneId UTC_ZONE_ID = ZoneId.of("UTC");

  public static Date tomorrowAt(LocalDateTime today, LocalTime time) {
    Instant dateTime = today
      .withHour(time.getHour())
      .withMinute(time.getMinute())
      .plus(1, DAYS)
      .toInstant(ZoneOffset.UTC);

    return Date.from(dateTime);
  }
}
