package com.wsf.dataingestor.metrics;

import lombok.Value;

import java.util.List;
import com.google.common.collect.ImmutableList;

import static com.wsf.dataingestor.metrics.TagHelper.Keys.CRON_NAME;
import static com.wsf.dataingestor.metrics.TagHelper.Keys.FEED_ID;
import static com.wsf.dataingestor.metrics.TagHelper.Keys.FIXTURE_ID;
import static com.wsf.dataingestor.metrics.TagHelper.Keys.TOURNAMENT;
import static java.util.stream.Collectors.toList;


public class TagHelper {

  public static List<io.micrometer.core.instrument.Tag> toTag(List<TagHelper.Tag> tags) {
    return tags
      .stream()
      .map(tag -> io.micrometer.core.instrument.Tag.of(tag.getKey(), tag.getValue()))
      .collect(toList());
  }

  public static List<Tag> ratingsErrorTags(String tournament) {
    return ImmutableList.of(Tag.of(TOURNAMENT.value(), tournament));
  }

  public static List<Tag> playersErrorTags(String tournament) {
    return ImmutableList.of(Tag.of(TOURNAMENT.value(), tournament));
  }

  public static List<Tag> cronScheduleErrorTags(String cronName) {
    return ImmutableList.of(Tag.of(CRON_NAME.value(), cronName));
  }

  public static List<Tag> feedWebSocketErrorTags(String feedId, String fixtureId) {
    return ImmutableList.of(Tag.of(FEED_ID.value(), feedId), Tag.of(FIXTURE_ID.value(), fixtureId));
  }

  enum Keys {
    COMPETITION_ID,
    TOURNAMENT,
    MATCH,
    FEED_ID,
    FIXTURE_ID,
    PLAYER_ID,
    PLAYER_EXTERNAL_ID,
    CRON_NAME;

    public String value() {
      return this
        .name().toLowerCase();
    }
  }

  @Value(staticConstructor = "of")
  public static class Tag {
    String key;
    String value;
  }
}
