package com.wsf.dataingestor.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.function.Supplier;
import org.springframework.stereotype.Service;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.THREAD_START_LATENCY;
import static com.wsf.dataingestor.metrics.TagHelper.toTag;
import static com.wsf.dataingestor.utils.StreamUtils.buildStream;
import static java.time.Duration.between;
import static java.time.Instant.now;
import static java.util.Collections.emptyList;

@Service
public class MetricsManager {
  private static final String POOL_NAME_TAG = "pool-name";

  private final MeterRegistry meterRegistry;

  public final Counter RATINGS_FEED_PLAYER_NOT_FOUND_BY_ID_BUT_FOUND_BY_NAME;
  public final Counter RATINGS_FEED_PROCESSING_ERROR;
  public final Counter CHANGED_FIXTURE_SCHEDULE;
  public final Counter RATINGS_FEED_CONNECTION_CLOSED_ERROR;
  public final Counter RATINGS_FEED_RATING_PROCESSED;
  public final Counter SQUADS_FEED_PLAYER_PROCESSED;
  public final Counter SQUADS_FEED_PLAYER_ERROR;
  public final Counter SQUADS_FEED_PLAYER_DELETED;
  public final Counter SQUADS_FEED_MANY_TEAM_PLAYERS_DELETED_ERROR;
  public final Counter SQUADS_FEED_PARSING_ERROR;
  public final Counter SQUADS_FEED_UNMAPPED_PLAYER;
  public final Counter MATCHES_FEED_UNMAPPED_TEAM;
  public final Counter MATCHES_FEED_UNMAPPED_STAGE;
  public final Counter MATCHES_FEED_UNMAPPED_FIXTURES;
  public final Counter MATCHES_FEED_PARSING_ERROR;
  public final Counter TOURNAMENTS_FEED_PARSING_ERROR;
  public final Counter SETTLEMENT_UPDATE_ERROR;
  public final Counter NO_SETTLEMENT_ERROR;
  public final Counter NO_EVENT_INFO_ERROR;
  public final Counter MATCH_START_UPDATE_ERROR;
  public final Counter CRON_SCHEDULE_ERROR;
  public final Counter MA2DP_WEBSOCKET_MAX_RETRIES_REACHED;
  public final Counter API_ERROR;
  public final Counter PLAYER_AVAILABILITY_FEED_ERROR;
  public final Counter SUBSTITUTIONS_RECEIVED;
  public final Counter RELATED_SUBSTITUTIONS_RECEIVED;

  public final Counter BROKEN_PIPE_ERROR;
  public final Timer PLAYER_MATCH_CACHE_WRITE_LATENCY;
  public final Timer RATINGS_FEED_RATING_PROCESSING_LATENCY;
  public final Timer RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY;

  public MetricsManager(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    RATINGS_FEED_PLAYER_NOT_FOUND_BY_ID_BUT_FOUND_BY_NAME = meterRegistry.counter(
      Metrics.RATINGS_FEED_PLAYER_NOT_FOUND_BY_ID_BUT_FOUND_BY_NAME.value());
    RATINGS_FEED_PROCESSING_ERROR = meterRegistry.counter(Metrics.RATINGS_FEED_PROCESSING_ERROR.value());
    RATINGS_FEED_CONNECTION_CLOSED_ERROR = meterRegistry.counter(Metrics.RATINGS_FEED_CONNECTION_CLOSED_ERROR.value());
    RATINGS_FEED_RATING_PROCESSED = meterRegistry.counter(Metrics.RATINGS_FEED_RATING_PROCESSED.value());
    RATINGS_FEED_RATING_PROCESSING_LATENCY = buildTimer(Metrics.RATINGS_FEED_RATING_PROCESSING_LATENCY.value(),
      emptyList());
    SQUADS_FEED_PLAYER_PROCESSED = meterRegistry.counter(Metrics.SQUADS_FEED_PLAYER_PROCESSED.value());
    SQUADS_FEED_PLAYER_ERROR = meterRegistry.counter(Metrics.SQUADS_FEED_PLAYER_ERROR.value());
    SQUADS_FEED_PLAYER_DELETED = meterRegistry.counter(Metrics.SQUADS_FEED_PLAYER_DELETED.value());
    SQUADS_FEED_PARSING_ERROR = meterRegistry.counter(Metrics.SQUADS_FEED_PARSING_ERROR.value());
    SQUADS_FEED_MANY_TEAM_PLAYERS_DELETED_ERROR = meterRegistry.counter(
      Metrics.SQUADS_FEED_MANY_TEAM_PLAYERS_DELETED_ERROR.value());
    SQUADS_FEED_UNMAPPED_PLAYER = meterRegistry.counter(Metrics.SQUADS_FEED_UNMAPPED_PLAYER.value());
    MATCHES_FEED_UNMAPPED_TEAM = meterRegistry.counter(Metrics.MATCHES_FEED_UNMAPPED_TEAM.value());
    MATCHES_FEED_UNMAPPED_STAGE = meterRegistry.counter(Metrics.MATCHES_FEED_UNMAPPED_STAGE.value());
    MATCHES_FEED_UNMAPPED_FIXTURES = meterRegistry.counter(Metrics.MATCHES_FEED_UNMAPPED_FIXTURES.value());
    MATCHES_FEED_PARSING_ERROR = meterRegistry.counter(Metrics.MATCHES_FEED_PARSING_ERROR.value());
    TOURNAMENTS_FEED_PARSING_ERROR = meterRegistry.counter(Metrics.TOURNAMENTS_FEED_PARSING_ERROR.value());
    SETTLEMENT_UPDATE_ERROR = meterRegistry.counter(Metrics.SETTLEMENT_UPDATE_ERROR.value());
    NO_SETTLEMENT_ERROR = meterRegistry.counter(Metrics.NO_SETTLEMENT_ERROR.value());
    NO_EVENT_INFO_ERROR = meterRegistry.counter(Metrics.NO_EVENT_INFO_ERROR.value());
    MATCH_START_UPDATE_ERROR = meterRegistry.counter(Metrics.MATCH_START_UPDATE_ERROR.value());
    CRON_SCHEDULE_ERROR = meterRegistry.counter(Metrics.CRON_SCHEDULE_ERROR.value());
    MA2DP_WEBSOCKET_MAX_RETRIES_REACHED = meterRegistry.counter(Metrics.MA2DP_WEBSOCKET_MAX_RETRIES_REACHED.value());
    API_ERROR = meterRegistry.counter(Metrics.API_ERROR.value());
    BROKEN_PIPE_ERROR = meterRegistry.counter(Metrics.BROKEN_PIPE_ERROR.value());
    PLAYER_MATCH_CACHE_WRITE_LATENCY = buildTimer(Metrics.PLAYER_MATCH_CACHE_WRITE_LATENCY.value(), emptyList());
    PLAYER_AVAILABILITY_FEED_ERROR = meterRegistry.counter(Metrics.PLAYER_AVAILABILITY_FEED_ERROR.value());
    SUBSTITUTIONS_RECEIVED = meterRegistry.counter(Metrics.SUBSTITUTIONS_RECEIVED.value());
    RELATED_SUBSTITUTIONS_RECEIVED = meterRegistry.counter(Metrics.RELATED_SUBSTITUTIONS_RECEIVED.value());
    RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY = buildTimer(Metrics.RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY.value(),
      emptyList());
    CHANGED_FIXTURE_SCHEDULE = meterRegistry.counter(Metrics.CHANGED_FIXTURE_SCHEDULE.value());
  }

  public void counter(String metric) {
    meterRegistry.counter(metric, emptyList()).increment();
  }

  public void gauge(MetricsManager.Metrics metric, Supplier<Number> supplier, List<TagHelper.Tag> tags) {
    Gauge
      .builder(metric.value(), supplier).tags(toTag(tags)).register(meterRegistry);
  }

  public void recordTimer(String metric, Duration duration, List<TagHelper.Tag> tags) {
    buildTimer(metric, tags).record(duration);
  }

  public void measureThreadStartLatency(String poolName, Instant start, List<TagHelper.Tag> tags) {
    TagHelper.Tag poolNameTag = TagHelper.Tag.of(POOL_NAME_TAG, poolName);
    List<TagHelper.Tag> finalTags = buildStream(poolNameTag, tags).toList();

    buildTimer(THREAD_START_LATENCY.value(), finalTags).record(between(start, now()));
  }

  private Timer buildTimer(String metric, List<TagHelper.Tag> tags) {
    return Timer
      .builder(metric).publishPercentiles(0.5, 0.90, 0.95).tags(toTag(tags)).register(meterRegistry);
  }

  public enum Metrics {
    RATINGS_FEED_PLAYER_NOT_FOUND_BY_ID_BUT_FOUND_BY_NAME,
    RATINGS_FEED_PROCESSING_ERROR,
    RATINGS_FEED_CONNECTION_CLOSED_ERROR,
    RATINGS_FEED_RATING_PROCESSED,
    RATINGS_FEED_BUILD_AND_PUBLISH_LATENCY,
    RATINGS_FEED_RATING_PROCESSING_LATENCY,
    RATINGS_FEED_RATING_FROM_RECEIVED_LATENCY,
    RATINGS_FEED_MA18DP_FROM_RECEIVED_LATENCY,
    SQUADS_FEED_PLAYER_PROCESSED,
    SQUADS_FEED_PLAYER_ERROR,
    SQUADS_FEED_PLAYER_DELETED,
    SQUADS_FEED_PARSING_ERROR,
    SQUADS_FEED_MANY_TEAM_PLAYERS_DELETED_ERROR,
    SQUADS_FEED_UNMAPPED_PLAYER,
    MATCHES_FEED_UNMAPPED_TEAM,
    MATCHES_FEED_UNMAPPED_STAGE,
    MATCHES_FEED_UNMAPPED_FIXTURES,
    SUBSTITUTIONS_RECEIVED,
    RELATED_SUBSTITUTIONS_RECEIVED,

    MATCHES_FEED_PARSING_ERROR,
    TOURNAMENTS_FEED_PARSING_ERROR,
    SETTLEMENT_UPDATE_ERROR,
    NO_SETTLEMENT_ERROR,
    NO_EVENT_INFO_ERROR,
    MATCH_START_UPDATE_ERROR,

    CRON_SCHEDULE_ERROR,

    PLAYER_MATCH_CACHE_WRITE_LATENCY,
    RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY,

    MA2DP_WEBSOCKET_MAX_RETRIES_REACHED,

    INDEX_QUOTES_SERVICE_POOL_SIZE,
    INDEX_QUOTES_SERVICE_POOL_WAITERS_SIZE,

    API_ERROR,
    BROKEN_PIPE_ERROR,
    NR_THREADS_IN_POOL,
    NR_TASKS_IN_POOL_QUEUE,
    PLAYER_AVAILABILITY_FEED_ERROR,
    THREAD_START_LATENCY,
    CHANGED_FIXTURE_SCHEDULE;

    public String value() {
      return this
        .name().toLowerCase();
    }
  }
}