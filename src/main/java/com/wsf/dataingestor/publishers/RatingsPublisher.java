package com.wsf.dataingestor.publishers;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.services.LivePlayerRatingService;
import com.wsf.dataingestor.services.LiveTeamRatingService;
import com.wsf.dataingestor.services.PlayerRatingService;
import com.wsf.dataingestor.services.TeamRatingService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.TeamRating;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

@Service
@RequiredArgsConstructor
public class RatingsPublisher {
  private final PlayerRatingService playerRatingService;
  private final TeamRatingService teamRatingService;
  private final LivePlayerRatingService livePlayerRatingService;
  private final LiveTeamRatingService liveTeamRatingService;
  private final KafkaService kafkaService;

  public void publishAndStore(PlayerRating rating, boolean isFinal, KafkaMetadata metadata) {
    if (isFinal) {
      kafkaService.sendFinalPlayerRating(rating, Fixture.FixtureStatus.LIVE, false, metadata);
    } else {
      kafkaService.sendLivePlayerRating(rating, metadata);
    }
    playerRatingService.storeRating(rating);
    livePlayerRatingService.storeRating(rating, isFinal);
  }

  public void publishAndStore(TeamRating teamRating, boolean isFinal, KafkaMetadata metadata) {
    kafkaService.sendLiveTeamRating(teamRating, metadata);
    teamRatingService.storeRating(teamRating);
    liveTeamRatingService.storeRating(teamRating, isFinal);
  }
}
