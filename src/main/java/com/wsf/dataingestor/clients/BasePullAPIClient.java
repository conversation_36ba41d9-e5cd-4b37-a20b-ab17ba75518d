package com.wsf.dataingestor.clients;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.wsf.dataingestor.clients.http.HttpFeedClient;
import com.wsf.dataingestor.exceptions.FeedException;
import com.wsf.dataingestor.exceptions.FeedNotFoundException;
import com.wsf.dataingestor.exceptions.ResourceNotFoundException;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

import static java.lang.System.currentTimeMillis;
import static java.util.Objects.nonNull;

@RequiredArgsConstructor
@Slf4j
public abstract class BasePullAPIClient<T> implements PullAPIClient<T> {

  private final HttpFeedClient httpFeedClient;
  private final FeedParser<T> feedParser;
  private final FeedStoreService feedStoreService;

  @Override
  public T retrieveParsedFeed(String entityId) throws FeedException {
    byte[] feed = null;
    try {
      feed = httpFeedClient.getFeed(entityId);
      T parsedFeed = feedParser.parseFeed(feed);
      String feedId = getFeedStorePath(parsedFeed);
      feedStoreService.storeFeed(feed, feedId, feedId);
      return parsedFeed;
    } catch (Exception e) {
      if (nonNull(feed)) {
        String path = String.format("feeds/errors/error_%s_%s.json", entityId, currentTimeMillis());
        feedStoreService.storeFeed(feed, path, path);
        log.error("Error feed stored to path: {}", path);
      }

      String message = String.format("Error processing feed for entityId: %s", entityId);
      if (e instanceof ResourceNotFoundException) {
        throw new FeedNotFoundException(message, e);
      }
      throw new FeedException(message, e);
    }
  }

  protected abstract String getFeedStorePath(T parsedFeed);

}
