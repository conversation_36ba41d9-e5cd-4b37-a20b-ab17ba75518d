package com.wsf.dataingestor.clients.http;

import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wsf.dataingestor.config.EntityMapperConfig;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.UnmappedPlayer;
import com.wsf.domain.common.UnmappedStage;
import com.wsf.domain.common.UnmappedTeam;

import static java.lang.String.format;
import static java.util.Collections.emptyMap;
import static java.util.Objects.nonNull;
import static org.springframework.http.HttpMethod.POST;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnmappedEntityClient {

  private static final String PLAYERS_ENDPOINT = "/v1/unmapped-players/find-or-create";
  private static final String TEAMS_ENDPOINT = "/v1/unmapped-teams/find-or-create";
  private static final String STAGES_ENDPOINT = "/v1/unmapped-stages/find-or-create";

  private final EntityMapperConfig entityMapperConfig;
  private final RestTemplate restTemplate;

  public boolean createUnmappedPlayer(String externalFirstName, String externalLastName, String externalMatchName,
                                      LocalDate birthDate, String externalPlayerId, ExternalProvider provider) {
    UnmappedPlayerRequest request = new UnmappedPlayerRequest(externalFirstName, externalLastName, externalMatchName,
      birthDate, externalPlayerId, provider);
    return nonNull(callAndReturnIfCreated(request, PLAYERS_ENDPOINT, UnmappedPlayer.class));
  }

  public UnmappedTeam createUnmappedTeam(String externalTeamName, String externalTeamAbbreviation,
                                         String externalTeamId, ExternalProvider provider) {
    UnmappedTeamRequest request = new UnmappedTeamRequest(externalTeamName, externalTeamAbbreviation, externalTeamId,
      provider);
    return callAndReturnIfCreated(request, TEAMS_ENDPOINT, UnmappedTeam.class);
  }

  public void createUnmappedStage(String externalStageName, String externalStageId, String competitionId,
                                  ExternalProvider provider) {
    UnmappedStageRequest request = new UnmappedStageRequest(externalStageId, externalStageName, competitionId,
      provider);
    callAndReturnIfCreated(request, STAGES_ENDPOINT, UnmappedStage.class);
  }

  private <T> T callAndReturnIfCreated(Object request, String endpoint, Class<T> clazz) {
    try {
      ResponseEntity<T> resp = callEntityMapper(request, endpoint, clazz);
      if (resp.getStatusCode() == HttpStatus.CREATED) {
        return resp.getBody();
      }
      throw new IllegalArgumentException(format("Response code %s is not handled", resp.getStatusCode()));
    } catch (HttpClientErrorException e) {
      if (e.getStatusCode().value() == 404) {
        return null;
      } else {
        throw new IllegalStateException("Error while calling the entity-mapper", e);
      }
    }
  }

  private <T, Z> ResponseEntity<T> callEntityMapper(Z request, String endpoint, Class<T> clazz) {
    String uri = UriComponentsBuilder
      .newInstance()
      .scheme("http")
      .host(entityMapperConfig.getHost())
      .port(entityMapperConfig.getPort())
      .path(endpoint)
      .build()
      .toUriString();
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
    HttpEntity<Z> body = new HttpEntity<>(request, httpHeaders);
    return restTemplate.exchange(uri, POST, body, clazz, emptyMap());
  }

  @Value
  static class UnmappedPlayerRequest {
    @JsonProperty("first_name")
    String firstName;
    @JsonProperty("last_name")
    String lastName;
    @JsonProperty("match_name")
    String matchName;
    @JsonProperty("birth_date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    LocalDate birthDate;
    @JsonProperty("provider_player_id")
    String externalPlayerId;
    ExternalProvider provider;
  }

  @Value
  static class UnmappedTeamRequest {
    @JsonProperty("name")
    String externalTeamName;
    @JsonProperty("abbreviation")
    String externalTeamAbbreviation;
    @JsonProperty("provider_team_id")
    String externalTeamId;
    ExternalProvider provider;
  }

  @Value
  static class UnmappedStageRequest {
    @JsonProperty("provider_stage_id")
    String externalStageId;
    @JsonProperty("provider_stage_name")
    String externalStageName;
    @JsonProperty("competition_id")
    String competitionId;
    ExternalProvider provider;
  }
}
