package com.wsf.dataingestor.clients.stores;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.wsf.dataingestor.clients.FeedStore;

@Slf4j
public class S3Store implements FeedStore {

  private final boolean storeEnabled;
  private final AmazonS3 s3Client;

  public S3Store(boolean storeEnabled, AmazonS3 s3Client) {
    this.storeEnabled = storeEnabled;
    this.s3Client = s3Client;
  }

  @Override
  public void store(byte[] content, String filePath) throws IOException {
    if (storeEnabled) {
      try (ByteArrayInputStream inputStream = new ByteArrayInputStream(content)) {
        s3Client.putObject("wsf-feeds", filePath, inputStream, new ObjectMetadata());
      }
    }
  }
}
