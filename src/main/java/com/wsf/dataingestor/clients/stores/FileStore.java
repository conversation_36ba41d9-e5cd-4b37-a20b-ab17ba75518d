package com.wsf.dataingestor.clients.stores;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import com.wsf.dataingestor.clients.FeedStore;

@Slf4j
public class FileStore implements FeedStore {

  private final boolean storeEnabled;
  private final String feedsFolder;

  public FileStore(boolean storeEnabled, String feedsFolder) {
    this.storeEnabled = storeEnabled;
    this.feedsFolder = feedsFolder;
  }

  @Override
  public void store(byte[] content, String name) throws IOException {
    if (storeEnabled) {
      Files.createDirectories(Path.of(feedsFolder));
      Path path = Paths.get(feedsFolder, name);

      if (!path.toFile().exists()) {
        try (FileWriter fw = new FileWriter(path.toString(), false);
             BufferedWriter bw = new BufferedWriter(fw)) {

          bw.write(new String(content));
        }
      } else {
        log.debug("File {} already exists", path.toString());
      }
    }
  }
}
