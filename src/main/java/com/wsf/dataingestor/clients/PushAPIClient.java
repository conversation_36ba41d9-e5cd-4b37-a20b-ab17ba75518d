package com.wsf.dataingestor.clients;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.models.MatchDataFeed;

public interface PushAPIClient {
  void startConnectionAndProcessFeed(String optaFixtureId, int seqId, Consumer<MatchDataFeed> feedProcessor,
                                     BiConsumer<String, Exception> errorHandler,
                                     Consumer<WebSocketManager.WSData> onSubscribeHandler,
                                     Runnable onUnsubscribeHandler);
}
