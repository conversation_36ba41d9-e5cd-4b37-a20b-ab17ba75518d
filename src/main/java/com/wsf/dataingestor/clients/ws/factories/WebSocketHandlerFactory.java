package com.wsf.dataingestor.clients.ws.factories;

import lombok.RequiredArgsConstructor;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.handlers.ReconnectHandler;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.handlers.SDDPHandler;

@Service
@RequiredArgsConstructor
public class WebSocketHandlerFactory {

  private final ObjectMapper jsonObjectMapper;
  private final MetricsManager metricsManager;

  public SDDPHandler buildHandler(String optaFixtureId, int seqId, String outlet, String feedName,
                                  Consumer<byte[]> feedHandler, Consumer<WebSocketManager.WSData> onSubscribeHandler,
                                  Runnable onUnsubscribeHandler, BiConsumer<String, Exception> onErrorHandler,
                                  ReconnectHandler reconnectHandler) {
    return SDDPHandler
      .builder()
      .optaFixtureId(optaFixtureId)
      .feedName(feedName)
      .seqId(seqId)
      .outlet(outlet)
      .jsonObjectMapper(jsonObjectMapper)
      .reconnectHandler(reconnectHandler)
      .metricsManager(metricsManager)
      .feedHandler(feedHandler)
      .onSubscribeHandler(onSubscribeHandler)
      .onUnsubscribeHandler(onUnsubscribeHandler)
      .onErrorHandler(onErrorHandler)
      .build();
  }
}
