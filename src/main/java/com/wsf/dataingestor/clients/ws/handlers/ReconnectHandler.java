package com.wsf.dataingestor.clients.ws.handlers;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import com.neovisionaries.ws.client.WebSocket;

@Slf4j
public class ReconnectHandler {

  private static final int MAX_RETRIES = 100;

  private final AtomicInteger retries = new AtomicInteger(0);

  private final String fixtureId;
  private final ExecutorService executorService;

  public ReconnectHandler(String fixtureId, ExecutorService executorService) {
    this.fixtureId = fixtureId;
    this.executorService = executorService;
  }

  public void reconnect(WebSocket webSocket) throws IOException, MaxNrRetriesExceeded {
    if (retries.incrementAndGet() < MAX_RETRIES) {
      log.info("Trying reconnecting to the websocket for fixtureId={}: {}", fixtureId, webSocket);
      webSocket.recreate().connect(executorService);
    } else {
      throw new MaxNrRetriesExceeded();
    }
  }

  @NoArgsConstructor
  public static class MaxNrRetriesExceeded extends Exception {
    public MaxNrRetriesExceeded(String message) {
      super(message);
    }
  }
}
