package com.wsf.dataingestor.clients.ws;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import org.springframework.stereotype.Service;
import com.neovisionaries.ws.client.WebSocket;
import com.wsf.dataingestor.clients.ws.handlers.WSHandler;

import static com.google.common.collect.Maps.newConcurrentMap;
import static java.util.Objects.nonNull;

@Slf4j
@Service
public class WebSocketManager {

  // use weak/soft references?
  private final Map<String, WSData> matchIdToWebsocket = newConcurrentMap();

  public WSData getMatchWebSocket(String fixtureId) {
    return matchIdToWebsocket.get(fixtureId);
  }

  public void setMatchWebSocket(String fixtureId, WSData webSocket) {
    matchIdToWebsocket.put(fixtureId, webSocket);
  }

  public void removeMatchWebSocket(String fixtureId) {
    matchIdToWebsocket.remove(fixtureId);
  }

  public void disconnectAllWebSockets() {
    matchIdToWebsocket.forEach((key, value) -> disconnectWebSocket(key));
  }

  public void disconnectWebSocket(String fixtureId) {
    log.info("Disconnecting websocket for fixtureId={}", fixtureId);
    WebSocketManager.WSData matchWebSocket = getMatchWebSocket(fixtureId);
    if (nonNull(matchWebSocket)) {
      closeWebSocket(matchWebSocket);
    } else {
      log.warn("could not find websocket data for fixtureId={}", fixtureId);
    }
  }

  private void closeWebSocket(WebSocketManager.WSData wsData) {
    wsData.getHandler().unsubscribeFromFixture(wsData.getWebSocket());
  }

  @Getter
  @RequiredArgsConstructor
  public static class WSData {
    private final WebSocket webSocket;
    private final WSHandler handler;
  }

}
