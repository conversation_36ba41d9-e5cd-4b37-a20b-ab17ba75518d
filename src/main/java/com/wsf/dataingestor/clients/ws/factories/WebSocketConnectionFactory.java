package com.wsf.dataingestor.clients.ws.factories;

import java.io.IOException;
import org.springframework.stereotype.Service;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketAdapter;
import com.neovisionaries.ws.client.WebSocketFactory;

@Service
public class WebSocketConnectionFactory {
  public WebSocket createWebSocketConnection(String url, WebSocketAdapter webSocketAdapter) throws IOException {
    return new WebSocketFactory().createSocket(url, 5000).addListener(webSocketAdapter);
  }
}
