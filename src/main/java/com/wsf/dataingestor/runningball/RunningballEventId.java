package com.wsf.dataingestor.runningball;

public class RunningballEventId {
  public static final String SCOUT_OFFLINE = "515";
  public static final String CONNECTION_PROBLEMS = "516";
  public static final String POSSIBLE_VAR = "540";
  public static final String VAR_STARTED = "533";
  public static final String NO_VAR = "534";
  public static final String RUNNINGBALL_SYSTEM_CODE_ID = "513";

  public static final String UNAVAILABLE_CONNECTION_MESSAGE_ID = "404";
  public static final String TECHNICAL_PROBLEMS_MESSAGE_ID = "405";
  public static final String TV_BROADCASTING_ERROR_MESSAGE_ID = "406";

  public static final String RED_CARD_HOME = "1032";
  public static final String YELLOW_CARD_HOME = "1034";
  public static final String RED_OR_YELLOW_CARD_HOME = "1045";
  public static final String POSSIBLE_CARD = "226";
  public static final String RED_CARD_AWAY = "2056";
  public static final String YELLOW_CARD_AWAY = "2058";
  public static final String RED_OR_YELLOW_CARD_AWAY = "2069";
}
