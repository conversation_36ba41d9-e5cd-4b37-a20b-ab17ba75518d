package com.wsf.dataingestor.runningball.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.models.runningball.EventList;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.parsers.ParsersUtils;
import com.wsf.dataingestor.runningball.RunningballEventId;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.runningball.RunningballEventId.POSSIBLE_CARD;
import static com.wsf.dataingestor.runningball.RunningballEventId.RED_CARD_AWAY;
import static com.wsf.dataingestor.runningball.RunningballEventId.RED_CARD_HOME;
import static com.wsf.dataingestor.runningball.RunningballEventId.RED_OR_YELLOW_CARD_AWAY;
import static com.wsf.dataingestor.runningball.RunningballEventId.RED_OR_YELLOW_CARD_HOME;
import static com.wsf.dataingestor.runningball.RunningballEventId.RUNNINGBALL_SYSTEM_CODE_ID;
import static com.wsf.dataingestor.runningball.RunningballEventId.TECHNICAL_PROBLEMS_MESSAGE_ID;
import static com.wsf.dataingestor.runningball.RunningballEventId.TV_BROADCASTING_ERROR_MESSAGE_ID;
import static com.wsf.dataingestor.runningball.RunningballEventId.UNAVAILABLE_CONNECTION_MESSAGE_ID;
import static com.wsf.dataingestor.runningball.RunningballEventId.YELLOW_CARD_AWAY;
import static com.wsf.dataingestor.runningball.RunningballEventId.YELLOW_CARD_HOME;
import static java.lang.Integer.parseInt;
import static java.lang.Math.abs;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
@RequiredArgsConstructor
public class RunningballParser implements FeedParser<List<MatchDataFeed>> {
  private static final String FEED_CODE = "rb";
  private static final Map<String, SoccerMatchEvent> SUPPORTED_EVENT_CODE_IDS = new HashMap<>();

  private static final List<String> SYSTEM_MESSAGES_TO_PARSE = List.of(UNAVAILABLE_CONNECTION_MESSAGE_ID,
    TECHNICAL_PROBLEMS_MESSAGE_ID, TV_BROADCASTING_ERROR_MESSAGE_ID);

  private final XmlMapper xmlMapper;
  private final OptaFeedParserUtils optaFeedParserUtils;

  static {

    /* BETSTOP events */
    SUPPORTED_EVENT_CODE_IDS.put("220", SoccerMatchEvent.BET_STOP); // close market
    SUPPORTED_EVENT_CODE_IDS.put(RUNNINGBALL_SYSTEM_CODE_ID, SoccerMatchEvent.LIVE_COVERAGE_CANCELLED); // close market
    SUPPORTED_EVENT_CODE_IDS.put(RunningballEventId.SCOUT_OFFLINE, SoccerMatchEvent.BET_STOP);
    SUPPORTED_EVENT_CODE_IDS.put(RunningballEventId.CONNECTION_PROBLEMS, SoccerMatchEvent.BET_STOP);
    SUPPORTED_EVENT_CODE_IDS.put("1029", SoccerMatchEvent.GOAL); // home
    SUPPORTED_EVENT_CODE_IDS.put("2053", SoccerMatchEvent.GOAL); // away
    SUPPORTED_EVENT_CODE_IDS.put(POSSIBLE_CARD, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(RED_CARD_HOME, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(YELLOW_CARD_HOME, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(RED_OR_YELLOW_CARD_HOME, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(RED_CARD_AWAY, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(YELLOW_CARD_AWAY, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put(RED_OR_YELLOW_CARD_AWAY, SoccerMatchEvent.CARD);
    SUPPORTED_EVENT_CODE_IDS.put("144", SoccerMatchEvent.PENALTY); //possible
    SUPPORTED_EVENT_CODE_IDS.put("146", SoccerMatchEvent.NO_PENALTY);
    SUPPORTED_EVENT_CODE_IDS.put("1064", SoccerMatchEvent.BREAKAWAY); //home
    SUPPORTED_EVENT_CODE_IDS.put("2088", SoccerMatchEvent.BREAKAWAY); //away
    SUPPORTED_EVENT_CODE_IDS.put("1027", SoccerMatchEvent.DANGEROUS_FREEKICK); //dangerous free kick home
    SUPPORTED_EVENT_CODE_IDS.put("2051", SoccerMatchEvent.DANGEROUS_FREEKICK); //dangerous free kick away
    //    SUPPORTED_EVENT_CODE_IDS.put("1028", SoccerMatchEvent.DANGEROUS_FREEKICK); // free kick home
    //    SUPPORTED_EVENT_CODE_IDS.put("2052", SoccerMatchEvent.DANGEROUS_FREEKICK); // free kick away
    //    SUPPORTED_EVENT_CODE_IDS.put("207", SoccerMatchEvent.DANGEROUS_FREEKICK); // temp - all possible free kicks for now, since the dangerous free kick events are not sent immediately
    SUPPORTED_EVENT_CODE_IDS.put("1031", SoccerMatchEvent.PENALTY); //home
    SUPPORTED_EVENT_CODE_IDS.put("2055", SoccerMatchEvent.PENALTY); //away
    SUPPORTED_EVENT_CODE_IDS.put("1039", SoccerMatchEvent.SHOT); //shot on target home
    SUPPORTED_EVENT_CODE_IDS.put("1040", SoccerMatchEvent.SHOT); //shot off target home
    SUPPORTED_EVENT_CODE_IDS.put("1041", SoccerMatchEvent.SHOT); //shot woodwork home
    SUPPORTED_EVENT_CODE_IDS.put("1058", SoccerMatchEvent.SHOT); //shot blocked home
    SUPPORTED_EVENT_CODE_IDS.put("2063", SoccerMatchEvent.SHOT); //shot on target away
    SUPPORTED_EVENT_CODE_IDS.put("2064", SoccerMatchEvent.SHOT); //shot off target away
    SUPPORTED_EVENT_CODE_IDS.put("2065", SoccerMatchEvent.SHOT); //shot woodwork away
    SUPPORTED_EVENT_CODE_IDS.put("2082", SoccerMatchEvent.SHOT); //shot blocked away

    SUPPORTED_EVENT_CODE_IDS.put("1066", SoccerMatchEvent.POSSIBLE_CORNER); //possible corner home
    SUPPORTED_EVENT_CODE_IDS.put("2090", SoccerMatchEvent.POSSIBLE_CORNER); //possible corner away

    SUPPORTED_EVENT_CODE_IDS.put("1053", SoccerMatchEvent.GOALKICK); //goal kick home
    SUPPORTED_EVENT_CODE_IDS.put("2077", SoccerMatchEvent.GOALKICK); //goal kick away

    SUPPORTED_EVENT_CODE_IDS.put("1071", SoccerMatchEvent.FOUL); //possible free kick home
    SUPPORTED_EVENT_CODE_IDS.put("2095", SoccerMatchEvent.FOUL); //possible free kick away
    SUPPORTED_EVENT_CODE_IDS.put("1042", SoccerMatchEvent.FOUL); //foul home
    SUPPORTED_EVENT_CODE_IDS.put("2066", SoccerMatchEvent.FOUL); //foul away

    SUPPORTED_EVENT_CODE_IDS.put(RunningballEventId.POSSIBLE_VAR, SoccerMatchEvent.POSSIBLE_VAR);
    SUPPORTED_EVENT_CODE_IDS.put(RunningballEventId.VAR_STARTED, SoccerMatchEvent.VAR_CHECK_START);
    SUPPORTED_EVENT_CODE_IDS.put(RunningballEventId.NO_VAR, SoccerMatchEvent.NO_VAR_CHECK);

    SUPPORTED_EVENT_CODE_IDS.put("132", SoccerMatchEvent.MATCH_SUSPENDED); //Injury break
    SUPPORTED_EVENT_CODE_IDS.put("149", SoccerMatchEvent.MATCH_SUSPENDED); //Game suspended

    SUPPORTED_EVENT_CODE_IDS.put("1043", SoccerMatchEvent.OFFSIDE); //Offside home
    SUPPORTED_EVENT_CODE_IDS.put("2067", SoccerMatchEvent.OFFSIDE); //Offside away

    SUPPORTED_EVENT_CODE_IDS.put("20", SoccerMatchEvent.MATCH_FINISHED); //Game finished


    /* BETSTART events */

    SUPPORTED_EVENT_CODE_IDS.put("1", SoccerMatchEvent.BET_START); // stop 1st half
    SUPPORTED_EVENT_CODE_IDS.put("148", SoccerMatchEvent.RESTART); // restart match
    SUPPORTED_EVENT_CODE_IDS.put("1044", SoccerMatchEvent.KICKOFF); // ko home
    SUPPORTED_EVENT_CODE_IDS.put("2068", SoccerMatchEvent.KICKOFF); // ko away
    SUPPORTED_EVENT_CODE_IDS.put("1051", SoccerMatchEvent.SAFE); // safe home
    SUPPORTED_EVENT_CODE_IDS.put("2075", SoccerMatchEvent.SAFE); // safe away
  }

  public List<MatchDataFeed> parseFeed(String feed) throws JsonProcessingException {
    Instant receivedTs = Instant.now();

    JsonNode tree = xmlMapper.readTree(feed);

    if (!tree.has("event_list")) {
      return List.of();
    }

    log.debug("feed: {}", feed);
    EventList eventList = xmlMapper.readValue(feed, EventList.class);
    log.debug("{}", eventList);

    Map<String, List<EventList.Event>> optaMatchIdToEvents = eventList.getEvents()
      .stream()
      .collect(groupingBy(EventList.Event::getOptaMatchId));

    if (optaMatchIdToEvents.isEmpty()) {
      return List.of();
    }

    return optaMatchIdToEvents.entrySet()
      .stream()
      .map(entry -> parseMatchAndEvents(receivedTs, eventList, entry))
      .filter(Objects::nonNull)
      .toList();
  }

  private MatchDataFeed parseMatchAndEvents(Instant receivedTs, EventList eventList,
                                            Map.Entry<String, List<EventList.Event>> matchAndEvents) {
    String optaMatchId = matchAndEvents.getKey();
    List<EventList.Event> matchEvents = matchAndEvents.getValue();

    Predicate<EventList.Event> supportedMessageIds = event ->
      !Objects.equals(event.getEventCodeId(), RUNNINGBALL_SYSTEM_CODE_ID) ||
        SYSTEM_MESSAGES_TO_PARSE.contains(event.getMessageId());

    List<MatchDataFeed.MatchEventDTO> events = matchEvents
      .stream()
      .filter(event -> SUPPORTED_EVENT_CODE_IDS.containsKey(event.getEventCodeId()))
      .filter(supportedMessageIds)
      .map(event -> new MatchDataFeed.MatchEventDTO(event.getEventNumber(),
        SUPPORTED_EVENT_CODE_IDS.get(event.getEventCodeId()), event.getTimestamp()))
      .collect(Collectors.toList());

    String matchTime = matchEvents.get(0).getMinute();

    Fixture fixture = optaFeedParserUtils.getFixtureByOptaFixtureId(optaMatchId);

    if (isNull(fixture)) {
      log.warn("Fixture with optaId={} could not be found in our DB", optaMatchId);
      return null;
    }

    String feedId = ParsersUtils.buildFeedId(FEED_CODE, optaMatchId, abs(eventList.hashCode()),
      receivedTs.toEpochMilli());

    return MatchDataFeed
      .builder()
      .receivedTs(receivedTs)
      .latestUpdateTs(eventList.getDateGenerated())
      .feedId(feedId)
      .matchTimeMin(parseInt(matchTime))
      .fixture(fixture)
      .fixtureStatus(LIVE)
      .playersData(emptyList())
      .matchEvents(events)
      .isSnapshot(false)
      .provider(MatchDataFeed.FeedProvider.RUNNINGBALL)
      .build();
  }
}
