package com.wsf.dataingestor.runningball.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.runningball.services.RunningballService;

import static java.util.Objects.nonNull;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1")
public class RunningballController {

  private final RunningballService runningballService;

  @RequestMapping(value = "/runningball", method = RequestMethod.POST, consumes = {
    MediaType.APPLICATION_FORM_URLENCODED_VALUE})
  public void runningball(@RequestHeader(value = "filename", required = false) String filename,
                          @RequestParam MultiValueMap<String, String> paramMap) {
    if (nonNull(filename)) {
      log.info("Runningball feed with filename={} received", filename);
    }
    String feed = paramMap.get("rb_data").get(0);
    runningballService.processFeed(feed);
  }

}
