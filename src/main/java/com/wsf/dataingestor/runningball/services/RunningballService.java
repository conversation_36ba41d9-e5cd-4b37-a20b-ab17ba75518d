package com.wsf.dataingestor.runningball.services;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.runningball.parsers.RunningballParser;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.events.LiveCoverageService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.domain.common.Fixture;

import static com.wsf.domain.common.Fixture.DB_FIELD_IS_RUNNING_BALL_CONNECTED;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.LIVE_COVERAGE_CANCELLED;

@Slf4j
@Service
@RequiredArgsConstructor
public class RunningballService {

  private static final String STORAGE_PATH = "opta/runningball/";

  private final RunningballParser runningballParser;
  private final MatchEventsProcessor matchEventsProcessor;
  private final OngoingMatchDataCacheService ongoingMatchDataCacheService;
  private final FeedStoreService feedStoreService;
  private final MetricsManager metricsRegistry;
  private final LiveCoverageService liveCoverageService;
  private final FixtureService fixtureService;

  public void processFeed(String feed) {
    try {
      List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(feed);

      if (!matchDataFeeds.isEmpty()) {
        var firstMatchDataFeed = matchDataFeeds.get(0);
        String feedId = firstMatchDataFeed.getFeedId();
        log.info("Runningball feed feedId={} received", feedId);

        byte[] rawContent = feed.getBytes(StandardCharsets.UTF_8);
        feedStoreService.storeFeed(rawContent, feedId, getFeedName(feedId));

        matchDataFeeds.forEach(this::processMatchFeed);
      }
    } catch (Exception e) {
      Counter metric = metricsRegistry.RATINGS_FEED_PROCESSING_ERROR;
      log.error("Error parsing feed, error={}: {}", metric.getId().getName(), feed, e);
      metric.increment();
    }
  }

  private void processMatchFeed(MatchDataFeed matchDataFeed) {
    Fixture fixture = matchDataFeed.getFixture();
    boolean fixtureToBeHandled = fixture.getActive() && fixture.getIsLiveEnabled();
    if (fixtureToBeHandled) {
      if (fixture.getIsRunningBallConnected() == null || !fixture.getIsRunningBallConnected()) {
        fixtureService.updateFixture(fixture, Map.of(DB_FIELD_IS_RUNNING_BALL_CONNECTED, true));
      }
      if (fixture.getStatus() == LIVE) {
        OngoingMatchData cachedMatchData = ongoingMatchDataCacheService.get(fixture.getIdAsString());
        matchEventsProcessor.processMatchEvents(matchDataFeed, cachedMatchData);
      } else {
        matchDataFeed.getMatchEvents()
          .stream()
          .filter(matchEventDTO -> matchEventDTO.getEvent().equals(LIVE_COVERAGE_CANCELLED))
          .findFirst()
          .ifPresent(matchEventDTO -> liveCoverageService.suspendLiveCoverageForFixture(fixture, matchEventDTO,
            matchDataFeed.getFeedId()));
      }
    }
  }

  private String getFeedName(String feedId) {
    return STORAGE_PATH + feedId;
  }
}
