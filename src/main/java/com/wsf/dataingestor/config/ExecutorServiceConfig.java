package com.wsf.dataingestor.config;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jano7.executor.KeySequentialExecutor;
import com.jano7.executor.TaskExceptionHandler;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.metrics.MetricsManager.Metrics;
import com.wsf.dataingestor.metrics.TagHelper.Tag;

import static com.google.common.util.concurrent.MoreExecutors.getExitingExecutorService;

@Configuration
@Slf4j
public class ExecutorServiceConfig {
  public static final int NR_THREADS_PLAYERS = 35;
  public static final int NR_THREADS_FIXTURES = 20;
  public static final int NR_THREADS_FEEDS = 3;

  public static final String FIXTURES_POOL_NAME = "fixtures-pool";
  public static final String PLAYERS_POOL_NAME = "players-pool";
  public static final String FEEDS_POOL_NAME = "feeds-storage-pool";

  @Bean("fixturesPool")
  public Executor fixturesPool() {
    var executor = getExitingExecutorService(getThreadPoolExecutor(NR_THREADS_FIXTURES, FIXTURES_POOL_NAME));
    return new KeySequentialExecutor(executor);
  }

  @Bean("websocketReconnectPool")
  public ExecutorService websocketReconnectPool() {
    return getExitingExecutorService(getThreadPoolExecutor(2, "websocket-reconnect-pool"));
  }

  @Bean("playersPool")
  public Executor playersPool(MetricsManager metricsManager) {
    ThreadPoolExecutor threadPoolExecutor = meteredThreadPoolExecutor(NR_THREADS_PLAYERS, metricsManager,
      PLAYERS_POOL_NAME);

    return new KeySequentialExecutor(threadPoolExecutor, buildExceptionHandler());
  }

  @Bean("betStartScheduler")
  public ScheduledExecutorService betStartScheduler() {
    ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("bet-start-scheduler-%d").build();
    return MoreExecutors.getExitingScheduledExecutorService(new ScheduledThreadPoolExecutor(4, threadFactory));
  }

  @Bean("feedsStoragePool")
  public Executor feedsStoragePool(MetricsManager metricsManager) {
    ThreadPoolExecutor threadPoolExecutor = meteredThreadPoolExecutor(NR_THREADS_FEEDS, metricsManager,
      FEEDS_POOL_NAME);
    return MoreExecutors.getExitingExecutorService(threadPoolExecutor);
  }

  private static ThreadPoolExecutor getThreadPoolExecutor(int nrThreads, String name) {
    ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(name + "-%d").build();
    return new ThreadPoolExecutor(nrThreads, nrThreads, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
      threadFactory);
  }

  private static ThreadPoolExecutor meteredThreadPoolExecutor(int nrThreads, MetricsManager metricsManager,
                                                              String poolName) {
    ThreadPoolExecutor threadPoolExecutor = getThreadPoolExecutor(nrThreads, poolName);
    BlockingQueue<Runnable> queue = threadPoolExecutor.getQueue();

    metricsManager.gauge(Metrics.NR_THREADS_IN_POOL, threadPoolExecutor::getPoolSize,
      List.of(Tag.of("pool_name", poolName)));
    metricsManager.gauge(Metrics.NR_TASKS_IN_POOL_QUEUE, queue::size, List.of(Tag.of("pool_name", poolName)));

    return threadPoolExecutor;
  }

  private static TaskExceptionHandler<Runnable> buildExceptionHandler() {
    return new TaskExceptionHandler<>() {
      @Override
      public void onException(Runnable runnable, Throwable cause) {
        log.error("Error while running task in sequential executor", cause);
      }
    };
  }
}
