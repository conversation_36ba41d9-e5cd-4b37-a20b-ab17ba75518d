package com.wsf.dataingestor.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import com.wsf.dataingestor.opta.clients.MA1Client;
import com.wsf.dataingestor.opta.clients.http.MA1HttpClient;
import com.wsf.dataingestor.opta.parsers.MA1FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Configuration
public class Ma1ClientConfig {

  @Primary
  @Bean("ma1HttpClientOnlyBettingAllowed")
  public MA1HttpClient ma1HttpClientOnlyBettingAllowed(RetryableHttpClient httpClient,
                                                       @Value("${opta.sdapi.host}") String host,
                                                       @Value("${opta.sdapi.port}") int port,
                                                       @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    boolean shouldFetchOnlyBettingAllowedFixtures = true;
    return new MA1HttpClient(httpClient, host, port, outletAuthKey, shouldFetchOnlyBettingAllowedFixtures);
  }

  @Bean("ma1HttpClientNoFilters")
  public MA1HttpClient ma1HttpClientNoFilters(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                                              @Value("${opta.sdapi.port}") int port,
                                              @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    boolean shouldFetchOnlyBettingAllowedFixtures = false;
    return new MA1HttpClient(httpClient, host, port, outletAuthKey, shouldFetchOnlyBettingAllowedFixtures);
  }

  @Primary
  @Bean("ma1ClientOnlyBettingAllowed")
  public MA1Client ma1ClientOnlyBettingAllowed(@Qualifier("ma1HttpClientOnlyBettingAllowed") MA1HttpClient httpClient,
                                               MA1FeedParser ma1FeedParser, FeedStoreService feedStoreService) {
    return new MA1Client(httpClient, ma1FeedParser, feedStoreService);
  }

  @Bean("ma1ClientNoFilters")
  public MA1Client ma1ClientNoFilters(@Qualifier("ma1HttpClientNoFilters") MA1HttpClient httpClient,
                                      MA1FeedParser ma1FeedParser, FeedStoreService feedStoreService) {
    return new MA1Client(httpClient, ma1FeedParser, feedStoreService);
  }

}
