package com.wsf.dataingestor.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("service.kafka")
public class KafkaProperties {
  private boolean useIAMAuthentication = false;
  private String servers;
  private Topic topic;

  @Data
  public static class Topic {
    private String playerEvents;
    private String teamEvents;
    private String playerOdds;
    private String teamOdds;
    private String fixtureEntityEvents;
    private String liveRatings;
    private String playerTransferEvents;
    private String finalFixtureSummaries;
    private String fixtureSummaryUpdates;
    private String contestantUnavailabilities;
    private String fixtureChanges;
  }
}
