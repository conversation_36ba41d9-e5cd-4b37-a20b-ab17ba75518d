package com.wsf.dataingestor.config.db;

import io.micrometer.core.instrument.MeterRegistry;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import com.wsf.config.DatabaseConnectionHandler;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.repository.ContestantUnavailabilityRepository;
import com.wsf.repository.main.MarketRepository;

@Configuration
public class MainDBConfig {

  @Bean
  public DatabaseConnectionHandler connectionHandler(MeterRegistry meterRegistry) {
    return new DatabaseConnectionHandler(meterRegistry);
  }

  @Bean
  public MarketRepository marketRepository(@Qualifier("mainMongoTemplate") MongoTemplate template) {
    return new MarketRepository(template);
  }

  @Bean
  public ContestantUnavailabilityRepository contestantUnavailabilityRepository(
    @Qualifier("mainMongoTemplate") MongoTemplate template) {
    return new ContestantUnavailabilityRepository(template);
  }

  @Bean(name = "mainMongoTemplate")
  public MongoTemplate mainMongoTemplate(@Value("${service.data.mongodb.main.uri}") String dbUri,
                                         DatabaseConnectionHandler databaseConnectionHandler) {
    String databaseName = "main";
    return databaseConnectionHandler.buildMongoTemplate(dbUri, databaseName);
  }

}
