package com.wsf.dataingestor.config.db;

import lombok.RequiredArgsConstructor;

import java.util.function.Supplier;
import org.springframework.stereotype.Service;
import com.wsf.domain.common.Competition;
import com.wsf.repository.common.CompetitionRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static java.lang.String.format;

@Service
@RequiredArgsConstructor
public class DBUtils {

  private final CompetitionRepository competitionRepository;

  public <T> T retrieveRepo(RepositoryFactory<T> repositoryFactory, String competitionId) {
    Supplier<String> dbNameSupplier = () -> competitionRepository.findById(competitionId)
      .filter(Competition::getActive)
      .map(Competition::getDbName)
      .orElseThrow(() -> new IllegalArgumentException(
        format("Competition with id %s does not exist or it's inactive", competitionId)));
    return repositoryFactory.getOrComputeRepo(competitionId, dbNameSupplier);
  }

}
