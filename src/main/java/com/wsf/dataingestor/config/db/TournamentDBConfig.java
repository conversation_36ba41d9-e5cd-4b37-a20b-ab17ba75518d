package com.wsf.dataingestor.config.db;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.core.MongoTemplate;
import com.wsf.config.DatabaseConnectionHandler;
import com.wsf.repository.common.CompetitionRepository;
import com.wsf.repository.common.DBManagerStandard;
import com.wsf.repository.common.FixtureRepository;
import com.wsf.repository.common.MasterPlayerRepository;
import com.wsf.repository.common.MasterTeamRepository;
import com.wsf.repository.common.StageRepository;
import com.wsf.repository.common.TournamentConfigRepository;
import com.wsf.repository.common.TournamentRepository;
import com.wsf.repository.common.factories.RepositoryFactory;
import com.wsf.repository.common.factories.RepositoryManager;
import com.wsf.repository.customer.CompetitionConfigRepository;
import com.wsf.repository.customer.CompetitionConfigRepositoryMongoDB;

@Configuration
public class TournamentDBConfig {

  @Bean
  public DBManagerStandard dbManager(@Value("${service.data.mongodb.competitions.uri}") String dbUri,
                                     DatabaseConnectionHandler databaseConnectionHandler) {
    return new DBManagerStandard(dbName -> databaseConnectionHandler.buildMongoTemplate(dbUri, dbName));
  }

  @Bean
  public CompetitionRepository competitionRepository(@Qualifier("allLeaguesMongoTemplate") MongoTemplate template) {
    return new CompetitionRepository(template);
  }

  @Bean
  public CompetitionConfigRepository competitionConfigRepository(
    @Qualifier("allLeaguesMongoTemplate") MongoTemplate template) {
    return new CompetitionConfigRepositoryMongoDB(template);
  }

  @Bean
  public MasterPlayerRepository masterPlayerRepository(@Qualifier("allLeaguesMongoTemplate") MongoTemplate template) {
    return new MasterPlayerRepository(template);
  }

  @Bean
  public MasterTeamRepository masterTeamRepository(@Qualifier("allLeaguesMongoTemplate") MongoTemplate template) {
    return new MasterTeamRepository(template);
  }

  @Bean
  public FixtureRepository buildFixtureRepository(
    @Qualifier("allLeaguesMongoTemplate") MongoTemplate allLeaguesMongoTemplate) {
    return new FixtureRepository(allLeaguesMongoTemplate);
  }

  @Bean
  public TournamentRepository buildTournamentRepository(
    @Qualifier("allLeaguesMongoTemplate") MongoTemplate allLeaguesMongoTemplate) {
    return new TournamentRepository(allLeaguesMongoTemplate);
  }

  @Bean
  public TournamentConfigRepository buildTournamentConfigRepository(
    @Qualifier("allLeaguesMongoTemplate") MongoTemplate allLeaguesMongoTemplate) {
    return new TournamentConfigRepository(allLeaguesMongoTemplate);
  }

  @Bean
  public StageRepository buildStageRepository(
    @Qualifier("allLeaguesMongoTemplate") MongoTemplate allLeaguesMongoTemplate) {
    return new StageRepository(allLeaguesMongoTemplate);
  }

  @Bean
  public RepositoryManager repositoryManager(DBManagerStandard dbManager) {
    return new RepositoryManager(dbManager);
  }

  @Bean
  public RepositoryFactory.PlayerRepositoryFactory playerRepositoryFactory(RepositoryManager repositoryManager) {
    return repositoryManager.buildPlayerRepositoryFactory();
  }

  @Bean
  public RepositoryFactory.PlayerRatingRepositoryFactory playerRatingRepositoryFactory(
    RepositoryManager repositoryManager) {
    return repositoryManager.buildPlayerRatingRepositoryFactory();
  }

  @Bean
  public RepositoryFactory.TeamRatingRepositoryFactory teamRatingRepositoryFactory(
    RepositoryManager repositoryManager) {
    return repositoryManager.buildTeamRatingRepositoryFactory();
  }

  @Bean
  public RepositoryFactory.LivePlayerRatingRepositoryFactory livePlayerRatingRepositoryFactory(
    RepositoryManager repositoryManager) {
    return repositoryManager.buildLivePlayerRatingRepositoryFactory();
  }

  @Bean
  public RepositoryFactory.LiveTeamRatingRepositoryFactory liveTeamRatingRepositoryFactory(
    RepositoryManager repositoryManager) {
    return repositoryManager.buildLiveTeamRatingRepositoryFactory();
  }

  @Bean
  public RepositoryFactory.TeamRepositoryFactory teamRepositoryFactory(RepositoryManager repositoryManager) {
    return repositoryManager.buildTeamRepositoryFactory();
  }

  @Primary
  @Bean(name = "allLeaguesMongoTemplate")
  public MongoTemplate allLeaguesMongoTemplate(@Value("${service.data.mongodb.allleagues.uri}") String dbUri,
                                               @Value("${service.data.mongodb.allleagues.database}") String dbName,
                                               DatabaseConnectionHandler databaseConnectionHandler) {
    return databaseConnectionHandler.buildMongoTemplate(dbUri, dbName);
  }
}
