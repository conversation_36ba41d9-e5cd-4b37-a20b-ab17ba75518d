package com.wsf.dataingestor.config;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("crons.player-availability-updater")
public class PlayerAvailabilityUpdaterConfig {

  private boolean enabled;
  private String scheduled;
  private int daysWindow;
  private int alertFixturesWithinHours;
  private int notAlertFixturesWithinHours;
}