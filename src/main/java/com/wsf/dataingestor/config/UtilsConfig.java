package com.wsf.dataingestor.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.sports.soccer.index.SoccerIndexCalculator;

@Configuration
public class UtilsConfig {

  @Bean
  public Utils ratingsUtils(MetricsManager metrics) {
    return new Utils(new SoccerIndexCalculator(), metrics);
  }
}
