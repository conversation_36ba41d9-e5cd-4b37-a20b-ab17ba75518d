package com.wsf.dataingestor.config;

import java.util.Set;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.wsf.dataingestor.services.stats.AllStatistic0Validator;
import com.wsf.dataingestor.services.stats.StatsValidator;

import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;

@Configuration
public class StatsValidatorConfig {

  @Bean("playerStatsValidator")
  public StatsValidator playerStatsValidator() {
    return new AllStatistic0Validator(Set.of(SHOT.getStatisticName(), FOUL.getStatisticName()));
  }

  @Bean("teamStatsValidator")
  public StatsValidator teamStatsValidator() {
    return new AllStatistic0Validator(Set.of(TEAM_SHOTS, TEAM_FOULS, TEAM_CORNERS));
  }
}
