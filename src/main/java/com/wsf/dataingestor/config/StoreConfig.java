package com.wsf.dataingestor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.amazonaws.services.s3.AmazonS3;
import com.wsf.dataingestor.clients.FeedStore;
import com.wsf.dataingestor.clients.stores.FileStore;
import com.wsf.dataingestor.clients.stores.S3Store;

@Configuration
public class StoreConfig {

  @Bean
  public FeedStore feedStore(@Value("${service.feeds.store.enabled}") Boolean storeFeedEnabled,
                             @Value("${service.feeds.store.type}") StoreType storeType,
                             @Value("${service.feeds.store.path}") String feedsFolder, AmazonS3 s3Client) {
    switch (storeType) {
      case S3:
        return new S3Store(storeFeedEnabled, s3Client);
      case FILE:
        return new FileStore(storeFeedEnabled, feedsFolder);
      default:
        throw new IllegalArgumentException("Store type not supported");
    }
  }

  enum StoreType {
    S3,
    FILE
  }

}
