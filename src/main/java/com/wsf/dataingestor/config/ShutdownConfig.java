package com.wsf.dataingestor.config;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.clients.ws.WebSocketManager;

@Slf4j
@Component
public class ShutdownConfig {

  private final WebSocketManager webSocketManager;

  @Autowired
  public ShutdownConfig(WebSocketManager webSocketManager) {
    this.webSocketManager = webSocketManager;
  }

  @PreDestroy
  public void onExit() {
    log.info("### Stopping ingestor ###");
    webSocketManager.disconnectAllWebSockets();
  }
}
