package com.wsf.dataingestor.config;

import io.micrometer.core.instrument.MeterRegistry;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.wsf.kafka.config.KafkaCommunicationConfig;
import com.wsf.kafka.enums.ClientType;
import com.wsf.kafka.json.GsonMapper;
import com.wsf.kafka.producers.KafkaClientFactory;
import com.wsf.kafka.producers.client.KafkaClient;

import static org.apache.kafka.clients.producer.ProducerConfig.ACKS_CONFIG;

@Configuration
public class KafkaConfig {

  @Bean
  public KafkaClient kafkaClient(KafkaProperties kafkaProperties, MeterRegistry meterRegistry) {
    KafkaCommunicationConfig configMap = new KafkaCommunicationConfig(kafkaProperties.isUseIAMAuthentication()).addConfig(
      ACKS_CONFIG, "1");

    return KafkaClientFactory
      .builder()
      .type(ClientType.KAFKA_SERVER)
      .url(kafkaProperties.getServers())
      .config(configMap.toMap())
      .jsonMapper(new GsonMapper())
      .meterRegistry(meterRegistry)
      .playerEventsTopic(kafkaProperties.getTopic().getPlayerEvents())
      .teamEventsTopic(kafkaProperties.getTopic().getTeamEvents())
      .playerOddsTriggersTopic(kafkaProperties.getTopic().getPlayerOdds())
      .teamOddsTriggersTopic(kafkaProperties.getTopic().getTeamOdds())
      .fixtureEntityEventsTopic(kafkaProperties.getTopic().getFixtureEntityEvents())
      .playerTransferEventsTopic(kafkaProperties.getTopic().getPlayerTransferEvents())
      .liveRatingsTopic(kafkaProperties.getTopic().getLiveRatings())
      .finalFixtureSummariesTopic(kafkaProperties.getTopic().getFinalFixtureSummaries())
      .fixtureSummaryUpdatesTopic(kafkaProperties.getTopic().getFixtureSummaryUpdates())
      .contestantUnavailabilitiesTopic(kafkaProperties.getTopic().getContestantUnavailabilities())
      .build()
      .createClient();
  }
}
