package com.wsf.dataingestor.config.cache;

import io.lettuce.core.ReadFrom;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

@Profile({"dev", "sandbox", "staging", "prod", "e2e"})
@Slf4j
@Configuration
public class RemoteCacheConfig implements CachingConfigurer {

  @Profile({"dev", "e2e", "sandbox"})
  @Bean
  public Object clearCache(RedisCacheManager redisCacheManager, CacheClearer cacheClearer) {
    redisCacheManager.getCacheConfigurations().keySet()
      .forEach(cacheClearer::clearCache);
    return null;
  }

  @Bean
  public LettuceConnectionFactory redisConnectionFactory(CacheConfigurationProperties cacheConfig) {
    CacheConfigurationProperties.RedisConfiguration redisProps = cacheConfig.getRedis();

    log.info("Redis (/Lettuce) configuration enabled. With cache timeout " + redisProps.getTimeoutSecs() + " seconds.");

    if (redisProps.isServerless()) {
      return createStandaloneRedisConnection(redisProps);
    } else {
      return createMasterReplicaRedisConnection(redisProps);
    }
  }

  private LettuceConnectionFactory createStandaloneRedisConnection(
    CacheConfigurationProperties.RedisConfiguration redisProps) {
    LettuceClientConfiguration clientConfig = LettuceClientConfiguration
      .builder().useSsl().build();

    RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
    redisConfig.setHostName(redisProps.getPrimaryHost());
    redisConfig.setPort(redisProps.getRedisPort());
    redisConfig.setDatabase(redisProps.getDb());

    return new LettuceConnectionFactory(redisConfig, clientConfig);
  }

  private LettuceConnectionFactory createMasterReplicaRedisConnection(
    CacheConfigurationProperties.RedisConfiguration redisProps) {
    LettuceClientConfiguration clientConfig = LettuceClientConfiguration
      .builder().readFrom(ReadFrom.REPLICA_PREFERRED).build();

    RedisStaticMasterReplicaConfiguration redisStaticMasterReplicaConfiguration = new RedisStaticMasterReplicaConfiguration(
      redisProps.getPrimaryHost(), redisProps.getRedisPort());

    redisStaticMasterReplicaConfiguration.addNode(redisProps.getReaderHost(), redisProps.getRedisPort());
    redisStaticMasterReplicaConfiguration.setDatabase(redisProps.getDb());

    return new LettuceConnectionFactory(redisStaticMasterReplicaConfiguration, clientConfig);
  }

  @Bean
  public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory cf) {
    RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
    redisTemplate.setConnectionFactory(cf);
    return redisTemplate;
  }

  @Bean
  public RedisCacheConfiguration cacheConfiguration(CacheConfigurationProperties properties,
                                                    ObjectMapper objectMapper) {
    return createCacheConfiguration(properties.getRedis().getTimeoutSecs(), objectMapper);
  }

  @Bean("redisCacheManager")
  public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory,
                                   CacheConfigurationProperties properties, ObjectMapper objectMapper) {
    Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

    properties.getCaches()
      .forEach((key, value) -> cacheConfigurations.put(key,
        createCacheConfiguration(value.getRedis().getTtlSecs(), objectMapper)));

    return RedisCacheManager
      .builder(redisConnectionFactory)
      .cacheDefaults(cacheConfiguration(properties, objectMapper))
      .withInitialCacheConfigurations(cacheConfigurations)
      .enableStatistics()
      .build();
  }

  private static RedisCacheConfiguration createCacheConfiguration(long timeoutInSeconds, ObjectMapper objectMapper) {
    objectMapper = objectMapper.copy();
    objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL,
      JsonTypeInfo.As.PROPERTY);
    GenericJackson2JsonRedisSerializer valueSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);
    return RedisCacheConfiguration
      .defaultCacheConfig()
      .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(valueSerializer))
      .entryTtl(Duration.ofSeconds(timeoutInSeconds));
  }
}
