package com.wsf.dataingestor.config.cache;

import lombok.Data;

import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "cache")
@Data
public class CacheConfigurationProperties {

  private RedisConfiguration redis;
  private Map<String, CacheConfiguration> caches;

  @Data
  static class RedisConfiguration {
    private boolean serverless = false;
    private long timeoutSecs = 60;
    private int redisPort = 6379;
    private String primaryHost = "localhost";
    private String readerHost = "localhost";
    private int db = 1;
  }

  @Data
  static class CacheConfiguration {
    private CacheConfig redis;
    private CacheConfig local;
  }

  @Data
  static class CacheConfig {
    private Integer ttlSecs;
    private Integer maxSize;
  }
}
