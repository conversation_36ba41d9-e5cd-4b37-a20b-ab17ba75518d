package com.wsf.dataingestor.config.cache;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@EnableConfigurationProperties(CacheConfigurationProperties.class)
public class CacheConfig {

  @Bean
  @Primary
  public CacheManager layeringCacheManager(@Qualifier("redisCacheManager") CacheManager redisCacheManager,
                                           @Qualifier("caffeineCacheManager") CacheManager caffeineCacheManager) {
    return new LayeringCacheManager(caffeineCacheManager, redisCacheManager);
  }

}
