package com.wsf.dataingestor.config.cache;


import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import org.springframework.cache.Cache;
import org.springframework.cache.support.AbstractValueAdaptingCache;

import static java.util.Objects.nonNull;

@Slf4j
public class LayeringCache extends AbstractValueAdaptingCache {

  /**
   * The name of the cache
   */
  private final String name;
  /**
   * redis cache
   */
  private final Cache redisCache;
  /**
   * Caffeine cache
   */
  private final Cache caffeineCache;
  /**
   * Whether to use the first level cache
   */
  private boolean useFirstCache;

  public LayeringCache(String name, Cache redisCache, Cache caffeineCache) {

    super(true);
    this.name = name;
    this.useFirstCache = true;
    this.redisCache = redisCache;
    this.caffeineCache = caffeineCache;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public Object getNativeCache() {
    return this;
  }

  public Cache getSecondaryCache() {
    return this.redisCache;
  }

  @Override
  public ValueWrapper get(Object key) {
    ValueWrapper wrapper = null;
    if (useFirstCache) {
      // Query the first level cache
      wrapper = caffeineCache.get(key);
      log.debug("Query the first level cache key: {}, the return value is: {}", key, wrapper);
    }

    if (wrapper == null) {
      // Query the secondary cache
      wrapper = redisCache.get(key);
      log.debug("Query the secondary cache key: {}, the return value is: {}", key, wrapper);
      if (useFirstCache && nonNull(wrapper)) {
        caffeineCache.put(key, wrapper.get());
      }
    }
    return wrapper;
  }

  @Override
  public <T> T get(Object key, Class<T> type) {
    T value = null;
    if (useFirstCache) {
      // Query the first level cache
      value = caffeineCache.get(key, type);
      log.debug("Query the first level cache key: {}, the return value is: {}", key, value);
    }

    if (value == null) {
      // Query the secondary cache
      value = redisCache.get(key, type);
      if (useFirstCache && nonNull(value)) {
        caffeineCache.put(key, value);
      }
      log.debug("Query the secondary cache key: {}, the return value is: {}", key, value);
    }
    return value;
  }

  @Override
  public <T> T get(Object key, Callable<T> valueLoader) {
    T value = null;
    if (useFirstCache) {
      // Query the primary cache, if the primary cache has no value, call getForSecondaryCache(k, valueLoader) to query the secondary cache
      value = (T) caffeineCache.get(key, () -> getForSecondaryCache(key, valueLoader));
    } else {
      // Directly query the secondary cache
      value = (T) getForSecondaryCache(key, valueLoader);
    }
    return value;
  }

  @Override
  public void put(Object key, Object value) {
    if (useFirstCache) {
      caffeineCache.put(key, value);
    }
    redisCache.put(key, value);
  }

  @Override
  public ValueWrapper putIfAbsent(Object key, Object value) {
    if (useFirstCache) {
      caffeineCache.putIfAbsent(key, value);
    }
    return redisCache.putIfAbsent(key, value);
  }

  @Override
  public void evict(Object key) {
    // When deleting, first delete the second-level cache and then delete the first-level cache, otherwise there will be concurrency problems
    redisCache.evict(key);
    if (useFirstCache) {
      caffeineCache.evict(key);
    }
  }

  @Override
  public void clear() {
    log.info("Clearing cache {}", name);
    redisCache.clear();
    if (useFirstCache) {
      caffeineCache.clear();
    }
  }

  @Override
  protected Object lookup(Object key) {
    Object value = null;
    if (useFirstCache) {
      value = caffeineCache.get(key);
      log.debug("Query the first level cache key: {}, the return value is: {}", key, value);
    }
    if (value == null) {
      value = redisCache.get(key);
      log.debug("Query the secondary cache key: {}, the return value is: {}", key, value);
    }
    return value;
  }

  /**
   * Query the secondary cache
   *
   * @param key
   * @param valueLoader
   * @return
   */
  private <T> Object getForSecondaryCache(Object key, Callable<T> valueLoader) {
    T value = redisCache.get(key, valueLoader);
    log.debug("Query the secondary cache key: {}, the return value is: {}", key, value);
    return value;
  }
}
