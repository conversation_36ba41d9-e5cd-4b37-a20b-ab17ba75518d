package com.wsf.dataingestor.config.cache;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableCaching
public class CacheConstants {
  public static final String TEAM_DATA_CACHE_NAME = "ingestor_team_fixture_data";
  public static final String PLAYER_DATA_CACHE_NAME = "ingestor_player_fixture_data";
  public static final String FIXTURE_DATA_CACHE_NAME = "ingestor_fixture_data";
  public static final String OPTA_FIXTURES_CACHE_NAME = "ingestor_opta_fixtures";
  public static final String TEAM_ACTIVE_PLAYERS_CACHE_NAME = "ingestor_team_active_players";
  public static final String OPTA_PLAYERS_CACHE_NAME = "ingestor_opta_players";
  public static final String OPTA_PLAYERS_EVENTS_CACHE_NAME = "ingestor_opta_players_events";
  public static final String MATCH_LOCKS_CACHE_NAME = "ingestor_match_locks";
  public static final String FIXTURE_SUMMARIES_CACHE_NAME = "ingestor_fixture_summaries";
}
