package com.wsf.dataingestor.config.cache;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.github.benmanes.caffeine.cache.Caffeine;

@Slf4j
@Configuration
public class LocalCacheConfig {

  @Bean("caffeineCacheManager")
  public CacheManager cacheManager(CacheConfigurationProperties properties) {
    SimpleCacheManager cacheManager = new SimpleCacheManager();

    List<CaffeineCache> caches = properties.getCaches().entrySet()
      .stream()
      .map(entry -> {
        CacheConfigurationProperties.CacheConfig localConfig = entry.getValue().getLocal();
        return caffeineConfig(entry.getKey(), localConfig.getTtlSecs(), localConfig.getMaxSize());
      })
      .collect(Collectors.toList());

    cacheManager.setCaches(caches);

    return cacheManager;
  }

  private static CaffeineCache caffeineConfig(String cacheName, int ttlSec, int maxSize) {
    Caffeine<Object, Object> cacheConfig = Caffeine.newBuilder().maximumSize(maxSize).recordStats();
    if (ttlSec > 0) {
      cacheConfig.expireAfterWrite(ttlSec, TimeUnit.SECONDS);
    }
    return new CaffeineCache(cacheName, cacheConfig.build());
  }

}
