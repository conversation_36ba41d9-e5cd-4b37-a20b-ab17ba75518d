package com.wsf.dataingestor.config.cache;

import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

@RequiredArgsConstructor
public class LayeringCacheManager implements CacheManager {

  private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>(16);

  private final CacheManager firstLevelCacheManager;
  private final CacheManager secondLevelCacheManage;

  @Override
  public Cache getCache(String name) {
    Cache cache = this.cacheMap.get(name);
    if (cache == null) {
      synchronized (this.cacheMap) {
        cache = this.cacheMap.get(name);
        if (cache == null) {
          cache = createCache(name);
          this.cacheMap.put(name, cache);
        }
      }
    }
    return cache;
  }

  @Override
  public Collection<String> getCacheNames() {
    return Collections.unmodifiableSet(this.cacheMap.keySet());
  }

  protected Cache createCache(String name) {
    Cache firstLevelCacheManager = this.firstLevelCacheManager.getCache(name);
    Cache secondLevelCacheManager = this.secondLevelCacheManage.getCache(name);
    return new LayeringCache(name, secondLevelCacheManager, firstLevelCacheManager);
  }

}
