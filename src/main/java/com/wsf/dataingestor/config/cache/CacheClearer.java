package com.wsf.dataingestor.config.cache;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CacheClearer {

  private final CacheManager cacheManager;
  private final CacheConfigurationProperties cacheConfig;
  private final RedisTemplate<String, String> redisTemplate;

  public CacheClearer(CacheManager cacheManager, CacheConfigurationProperties cacheConfig,
                      RedisTemplate<String, String> redisTemplate) {
    this.cacheManager = cacheManager;
    this.cacheConfig = cacheConfig;
    this.redisTemplate = redisTemplate;
  }

  public void clearCache(String cacheName) {
    log.info("Clearing cache {}", cacheName);
    if (cacheConfig.getRedis().isServerless()) {
      clearCacheForServerless(cacheName);
    } else {
      clearCacheForStandard(cacheName);
    }
  }

  private void clearCacheForStandard(String cacheName) {
    log.info("Using standard Redis - clearing cache {} using Cache.clear()", cacheName);
    cacheManager.getCache(cacheName).clear();
  }

  private void clearCacheForServerless(String cacheName) {
    log.info("Using serverless Redis - clearing cache {} using SCAN", cacheName);
    String pattern = cacheName + "::*";
    deleteKeysByPattern(pattern, cacheName);
  }

  private void deleteKeysByPattern(String pattern, String cacheName) {
    try {
      redisTemplate.execute((RedisCallback<Object>) connection -> {
        ScanOptions options = ScanOptions.scanOptions().match(pattern)
          .count(1000).build();
        Cursor<byte[]> cursor = connection.scan(options);

        while (cursor.hasNext()) {
          byte[] key = cursor.next();
          connection.del(key);
        }
        cursor.close();
        return null;
      });
    } catch (Exception e) {
      log.error("Error clearing cache {}: {}", cacheName, e.getMessage());
    }
  }
}
