package com.wsf.dataingestor.config;

import java.io.IOException;
import org.bson.types.ObjectId;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.JsonNodeDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.dataformat.xml.deser.FromXmlParser;
import com.fasterxml.jackson.datatype.guava.GuavaModule;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;
import static com.fasterxml.jackson.databind.MapperFeature.REQUIRE_SETTERS_FOR_GETTERS;
import static com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS;

@Configuration
public class JsonConfig {

  @Bean
  public MappingJackson2HttpMessageConverter jsonConf() {
    MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
    converter.setObjectMapper(jsonObjectMapper());

    return converter;
  }

  @Primary
  @Bean
  public ObjectMapper jsonObjectMapper() {
    return Jackson2ObjectMapperBuilder
      .json()
      .serializerByType(ObjectId.class, new ToStringSerializer())
      .failOnUnknownProperties(false)
      .createXmlMapper(false)
      .serializationInclusion(NON_NULL)
      .featuresToEnable(REQUIRE_SETTERS_FOR_GETTERS)
      .build()
      .configure(WRITE_DATES_AS_TIMESTAMPS, false)
      .registerModule(new GuavaModule());
  }

  @Bean
  public XmlMapper xmlObjectMapper() {
    return (XmlMapper) Jackson2ObjectMapperBuilder
      .xml()
      .serializerByType(ObjectId.class, new ToStringSerializer())
      .failOnUnknownProperties(false)
      .createXmlMapper(true)
      .serializationInclusion(NON_NULL)
      .build()
      .configure(WRITE_DATES_AS_TIMESTAMPS, false)
      .registerModule(new SimpleModule().addDeserializer(JsonNode.class, new JsonNodeDeserializer() {
        @Override
        public JsonNode deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
          String rootName = ((FromXmlParser) p).getStaxReader().getLocalName();
          return ctxt.getNodeFactory().objectNode().set(rootName, super.deserialize(p, ctxt));
        }
      }));
  }

}
