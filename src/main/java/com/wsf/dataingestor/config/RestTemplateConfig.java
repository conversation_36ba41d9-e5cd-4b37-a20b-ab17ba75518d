package com.wsf.dataingestor.config;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

  @Bean
  public RestTemplate restTemplate() {
    return new RestTemplate(buildClientHttpRequestFactory());
  }

  private static ClientHttpRequestFactory buildClientHttpRequestFactory() {
    PoolingHttpClientConnectionManager connectionManager = PoolingHttpClientConnectionManagerBuilder
      .create()
      .setDefaultConnectionConfig(ConnectionConfig
        .custom()
        .setSocketTimeout(Timeout.ofSeconds(10))
        .setConnectTimeout(Timeout.ofSeconds(5))
        .build())
      .build();
    HttpClient client = HttpClientBuilder
      .create().setConnectionManager(connectionManager).build();
    return new HttpComponentsClientHttpRequestFactory(client);
  }
}
