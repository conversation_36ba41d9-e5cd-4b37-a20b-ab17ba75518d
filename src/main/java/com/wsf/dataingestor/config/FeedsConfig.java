package com.wsf.dataingestor.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.clients.MA3Client;
import com.wsf.dataingestor.opta.clients.http.MA2HttpClient;
import com.wsf.dataingestor.opta.clients.http.MA3HttpClient;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPEventsMapper;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18ValidEvents;
import com.wsf.dataingestor.opta.parsers.ma18dp.Ma18DPSubstitutionsHandler;
import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser;
import com.wsf.dataingestor.opta.parsers.ma2.StatsMapper;
import com.wsf.dataingestor.opta.parsers.ma3.MA3EventsMapper;
import com.wsf.dataingestor.opta.parsers.ma3.MA3FeedParser;
import com.wsf.dataingestor.opta.parsers.ma3.MA3ValidEvents;
import com.wsf.dataingestor.services.FeedStoreService;

import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_PLAYER_STATS_FINAL;
import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_PLAYER_STATS_LIVE;
import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_TEAM_STATS_FINAL;
import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_TEAM_STATS_LIVE;

@Configuration
public class FeedsConfig {

  @Primary
  @Bean("ma2ClientLive")
  public MA2Client ma2ClientLive(MA2HttpClient httpClient, @Qualifier("ma2FeedParserLive") MA2FeedParser ma2FeedParser,
                                 FeedStoreService feedStoreService) {
    return new MA2Client(httpClient, ma2FeedParser, feedStoreService);
  }

  @Bean("ma2ClientFinal")
  public MA2Client ma2ClientFinal(MA2HttpClient httpClient,
                                  @Qualifier("ma2FeedParserFinal") MA2FeedParser ma2FeedParser,
                                  FeedStoreService feedStoreService) {
    return new MA2Client(httpClient, ma2FeedParser, feedStoreService);
  }

  @Bean
  public MA3Client ma3ClientFinal(MA3HttpClient httpClient, MA3FeedParser ma3FeedParser,
                                  FeedStoreService feedStoreService) {
    return new MA3Client(httpClient, ma3FeedParser, feedStoreService);
  }

  @Primary
  @Bean("ma2FeedParserLive")
  public MA2FeedParser ma2FeedParserLive(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils) {
    StatsMapper statsMapper = new StatsMapper(OPTA_TO_WSF_PLAYER_STATS_LIVE, OPTA_TO_WSF_TEAM_STATS_LIVE);
    return new MA2FeedParser(jsonObjectMapper, statsMapper, optaFeedParserUtils);
  }

  @Bean("ma2FeedParserFinal")
  public MA2FeedParser ma2FeedParserFinal(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils) {
    StatsMapper statsMapper = new StatsMapper(OPTA_TO_WSF_PLAYER_STATS_FINAL, OPTA_TO_WSF_TEAM_STATS_FINAL);
    return new MA2FeedParser(jsonObjectMapper, statsMapper, optaFeedParserUtils);
  }

  @Bean
  public MA3FeedParser ma3FeedParserFinal(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils,
                                          MA3EventsMapper eventsMapper) {
    return new MA3FeedParser(jsonObjectMapper, optaFeedParserUtils, eventsMapper);
  }

  @Bean
  public MA18DPEventsMapper ma18EventMapper(OptaCachedEventService optaCachedEventService,
                                            Ma18DPSubstitutionsHandler ma18DPSubstitutionsHandler,
                                            MetricsManager metricsManager) {
    return MA18DPEventsMapper
      .builder()
      .optaCachedEventService(optaCachedEventService)
      .playerEventTypeIdToQualifiersId(MA18ValidEvents.playerEventTypeIdToFilters)
      .matchEventTypeIdToQualifiersId(MA18ValidEvents.matchEventTypeIdToFilters)
      .ma18DPSubstitutionsHandler(ma18DPSubstitutionsHandler)
      .metricsManager(metricsManager)
      .build();
  }

  @Bean
  public MA3EventsMapper ma3EventMapper(MetricsManager metricsManager) {
    return MA3EventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(MA3ValidEvents.eventTypeIdToFilters)
      .metricsManager(metricsManager)
      .build();
  }
}
