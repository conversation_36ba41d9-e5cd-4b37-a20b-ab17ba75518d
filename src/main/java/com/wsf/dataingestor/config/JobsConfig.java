package com.wsf.dataingestor.config;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Properties;
import org.quartz.CronScheduleBuilder;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SpringBeanJobFactory;
import com.wsf.dataingestor.crons.CurrentTournamentUpdater;
import com.wsf.dataingestor.crons.FixturesUpdater;
import com.wsf.dataingestor.crons.FixturesWatcherUpdater;
import com.wsf.dataingestor.crons.PlayerAvailabilityUpdater;
import com.wsf.dataingestor.crons.SquadsUpdater;

import static com.wsf.dataingestor.crons.CurrentTournamentUpdater.CURRENT_TOURNAMENT_JOB_POLLING_GROUP;
import static com.wsf.dataingestor.crons.CurrentTournamentUpdater.CURRENT_TOURNAMENT_JOB_POLLING_JOB_KEY;
import static com.wsf.dataingestor.crons.PlayerAvailabilityUpdater.DAYS_WINDOW_PARAM;
import static com.wsf.dataingestor.crons.PlayerAvailabilityUpdater.PLAYER_AVAILABILITY_JOB_POLLING_GROUP;
import static com.wsf.dataingestor.crons.PlayerAvailabilityUpdater.PLAYER_AVAILABILITY_JOB_POLLING_JOB_KEY;
import static java.lang.String.format;

@Slf4j
@Configuration
@Profile({"sandbox", "staging", "prod"})
public class JobsConfig {

  private static final String QUARTZ_JOB_STORE_MONGO_URI_PROP_NAME = "org.quartz.jobStore.mongoUri";

  @Autowired
  private ApplicationContext applicationContext;

  @Bean
  public SpringBeanJobFactory springBeanJobFactory() {
    AutoWiringSpringBeanJobFactory jobFactory = new AutoWiringSpringBeanJobFactory();
    log.debug("Configuring Job factory");

    jobFactory.setApplicationContext(applicationContext);
    return jobFactory;
  }

  @Bean
  public Scheduler scheduler(Trigger matchesUpdaterTrigger, Trigger squadsUpdaterTrigger,
                             Trigger matchesWatcherUpdaterTrigger, Trigger currentTournamentUpdaterTrigger,
                             Trigger availabilityTrigger, JobDetail matchesUpdaterJobDetail,
                             JobDetail squadsUpdaterJobDetail, JobDetail matchesWatcherUpdaterJobDetail,
                             JobDetail currentTournamentUpdaterJobDetail, JobDetail availabilityJobDetail,
                             @Value("${crons.matches-updater.enabled}") Boolean matchesUpdaterEnabled,
                             @Value("${crons.squads-updater.enabled}") Boolean squadsUpdaterEnabled,
                             @Value("${crons.matches-watcher-updater.enabled}") Boolean matchesWatcherUpdaterEnabled,
                             @Value("${crons.current-tournament-updater.enabled}") Boolean currentTournamentUpdaterEnabled,
                             @Value("${crons.player-availability-updater.enabled}") Boolean availabilityUpdaterEnabled,
                             SchedulerFactoryBean factory) throws SchedulerException {
    log.debug("Getting a handle to the Scheduler");
    Scheduler scheduler = factory.getScheduler();

    scheduleOrUpdateJob(matchesUpdaterEnabled, matchesUpdaterTrigger, matchesUpdaterJobDetail, scheduler);
    scheduleOrUpdateJob(squadsUpdaterEnabled, squadsUpdaterTrigger, squadsUpdaterJobDetail, scheduler);
    scheduleOrUpdateJob(matchesWatcherUpdaterEnabled, matchesWatcherUpdaterTrigger, matchesWatcherUpdaterJobDetail,
      scheduler);
    scheduleOrUpdateJob(currentTournamentUpdaterEnabled, currentTournamentUpdaterTrigger,
      currentTournamentUpdaterJobDetail, scheduler);
    scheduleOrUpdateJob(availabilityUpdaterEnabled, availabilityTrigger, availabilityJobDetail, scheduler);

    log.debug("Starting Scheduler threads");
    scheduler.start();
    return scheduler;
  }

  private static void scheduleOrUpdateJob(Boolean isEnabled, Trigger trigger, JobDetail jobDetail,
                                          Scheduler scheduler) throws SchedulerException {
    if (!isEnabled) {
      scheduler.deleteJob(jobDetail.getKey());
    } else if (!scheduler.checkExists(jobDetail.getKey())) {
      scheduler.scheduleJob(jobDetail, trigger);
    } else {
      scheduler.rescheduleJob(trigger.getKey(), trigger);
    }
  }

  @Bean
  public SchedulerFactoryBean schedulerFactoryBean(Environment env,
                                                   @Value("${service.data.mongodb.main.uri}") String dbUrl) throws IOException {
    SchedulerFactoryBean factoryBean = new SchedulerFactoryBean();
    AutoWiringSpringBeanJobFactory jobFactory = new AutoWiringSpringBeanJobFactory();
    jobFactory.setApplicationContext(applicationContext);

    factoryBean.setJobFactory(jobFactory);
    factoryBean.setQuartzProperties(quartzProperties(env.getActiveProfiles()[0], dbUrl));
    factoryBean.setApplicationContextSchedulerContextKey("applicationContext");
    return factoryBean;
  }

  public static Properties quartzProperties(String springActiveProfile, String dbUrl) throws IOException {
    PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
    Properties localProps = new Properties();
    localProps.put(QUARTZ_JOB_STORE_MONGO_URI_PROP_NAME, dbUrl);
    propertiesFactoryBean.setProperties(localProps);
    propertiesFactoryBean.setLocation(new ClassPathResource(format("/quartz_%s.properties", springActiveProfile)));
    propertiesFactoryBean.afterPropertiesSet();
    return propertiesFactoryBean.getObject();
  }

  @Bean
  public JobDetail squadsUpdaterJobDetail() {
    return JobBuilder
      .newJob()
      .ofType(SquadsUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .withIdentity(SquadsUpdater.SQUADS_JOB_POLLING_JOB_KEY, SquadsUpdater.SQUADS_JOB_POLLING_GROUP)
      .withDescription("Invoking Opta TM3 feed in order to update squads")
      .build();
  }

  @Bean
  public JobDetail matchesUpdaterJobDetail() {
    return JobBuilder
      .newJob()
      .ofType(FixturesUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .withIdentity(FixturesUpdater.FIXTURES_JOB_POLLING_JOB_KEY, FixturesUpdater.FIXTURES_JOB_POLLING_GROUP)
      .withDescription("Invoking Opta MA1 feed in order to update matches")
      .build();
  }

  @Bean
  public JobDetail matchesWatcherUpdaterJobDetail() {
    return JobBuilder
      .newJob()
      .ofType(FixturesWatcherUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .withIdentity(FixturesWatcherUpdater.FIXTURE_WATCHER_JOB_POLLING_JOB_KEY,
        FixturesWatcherUpdater.FIXTURE_WATCHER_JOB_POLLING_GROUP)
      .withDescription("Checks for which matches we should start a scheduled job to check ratings")
      .build();
  }

  @Bean
  public Trigger squadsUpdaterTrigger(@Value("${crons.squads-updater.schedule}") String cronSquads,
                                      JobDetail squadsUpdaterJobDetail) {
    return TriggerBuilder
      .newTrigger()
      .forJob(squadsUpdaterJobDetail)
      .withIdentity(SquadsUpdater.SQUADS_JOB_POLLING_JOB_KEY, SquadsUpdater.SQUADS_JOB_POLLING_GROUP)
      .withDescription("Trigger for Squads Updater")
      .withSchedule(CronScheduleBuilder.cronSchedule(cronSquads))
      .build();
  }

  @Bean
  public Trigger matchesUpdaterTrigger(@Value("${crons.matches-updater.schedule}") String cronMatches,
                                       JobDetail matchesUpdaterJobDetail) {
    return TriggerBuilder
      .newTrigger()
      .forJob(matchesUpdaterJobDetail)
      .withIdentity(FixturesUpdater.FIXTURES_JOB_POLLING_JOB_KEY, FixturesUpdater.FIXTURES_JOB_POLLING_GROUP)
      .withDescription("Trigger for Matches Updater")
      .withSchedule(CronScheduleBuilder.cronSchedule(cronMatches))
      .build();
  }

  @Bean
  public Trigger matchesWatcherUpdaterTrigger(
    @Value("${crons.matches-watcher-updater.schedule}") String cronMatchesWatcher,
    JobDetail matchesWatcherUpdaterJobDetail) {
    return TriggerBuilder
      .newTrigger()
      .forJob(matchesWatcherUpdaterJobDetail)
      .withIdentity(FixturesWatcherUpdater.FIXTURE_WATCHER_JOB_POLLING_JOB_KEY,
        FixturesWatcherUpdater.FIXTURE_WATCHER_JOB_POLLING_GROUP)
      .withDescription("Trigger for Matches Watcher Updater")
      .withSchedule(CronScheduleBuilder.cronSchedule(cronMatchesWatcher))
      .build();
  }

  @Bean
  public static JobDetail currentTournamentUpdaterJobDetail() {
    return JobBuilder
      .newJob()
      .ofType(CurrentTournamentUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .withIdentity(CURRENT_TOURNAMENT_JOB_POLLING_JOB_KEY, CURRENT_TOURNAMENT_JOB_POLLING_GROUP)
      .withDescription("Checks if a new current tournament should be created for the active competitions")
      .build();
  }

  @Bean
  public Trigger currentTournamentUpdaterTrigger(
    @Value("${crons.current-tournament-updater.schedule}") String cronTournamentUpdate,
    JobDetail currentTournamentUpdaterJobDetail) {
    return TriggerBuilder
      .newTrigger()
      .forJob(currentTournamentUpdaterJobDetail)
      .withIdentity(CURRENT_TOURNAMENT_JOB_POLLING_JOB_KEY, CURRENT_TOURNAMENT_JOB_POLLING_GROUP)
      .withDescription("Trigger for Current Tournament Updater")
      .withSchedule(CronScheduleBuilder.cronSchedule(cronTournamentUpdate))
      .build();
  }

  @Bean
  public Trigger availabilityTrigger(@Value("${crons.player-availability-updater.schedule}") String schedule,
                                     @Value("${crons.player-availability-updater.days-window}") Integer daysWindow,
                                     JobDetail availabilityJobDetail) {
    return TriggerBuilder
      .newTrigger()
      .usingJobData(DAYS_WINDOW_PARAM, daysWindow)
      .forJob(availabilityJobDetail)
      .withIdentity(PLAYER_AVAILABILITY_JOB_POLLING_JOB_KEY, PLAYER_AVAILABILITY_JOB_POLLING_GROUP)
      .withDescription("Trigger for Availability Updater")
      .withSchedule(CronScheduleBuilder.cronSchedule(schedule))
      .build();
  }

  @Bean
  public static JobDetail availabilityJobDetail() {
    return JobBuilder
      .newJob()
      .ofType(PlayerAvailabilityUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .withIdentity(PLAYER_AVAILABILITY_JOB_POLLING_JOB_KEY, PLAYER_AVAILABILITY_JOB_POLLING_GROUP)
      .withDescription("Checks if there are new Availability players for Opta competitions")
      .build();
  }
}
