package com.wsf.dataingestor.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.MA18DPClient;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.services.OptaFixtureDataProcessor;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.HalfTimeNotifier;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.MatchFinalDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.dataingestor.services.squads.SquadsIngestionService;

@Configuration
public class ServiceConfig {

  @Bean
  public OptaFixturePushDataRetriever matchPushDataRetriever(OngoingMatchDataCacheService ongoingMatchDataCache,
                                                             MatchEventsProcessor matchEventsProcessor,
                                                             MA18DPClient matchDataPushClient,
                                                             MatchLiveDataProcessor matchLiveDataProcessor,
                                                             LiveFixtureSummaryNotifier liveFixtureSummaryNotifier,
                                                             FixtureService fixtureService, MetricsManager metrics,
                                                             ThreadPoolService threadPoolService,
                                                             WebSocketManager webSocketManager) {
    return new OptaFixturePushDataRetriever(ongoingMatchDataCache, matchDataPushClient, matchEventsProcessor,
      matchLiveDataProcessor, liveFixtureSummaryNotifier, fixtureService, threadPoolService, webSocketManager, metrics);
  }

  @Bean
  public OptaFixtureDataProcessor optaFixtureDataProcessor(OngoingMatchDataCacheService ongoingMatchDataCache,
                                                           @Qualifier("ma2ClientLive") MA2Client matchDataPullClient,
                                                           OptaFixturePushDataRetriever matchPushDataRetriever,
                                                           MatchLiveDataProcessor matchLiveDataProcessor,
                                                           MatchFinalDataProcessor matchFinalDataProcessor,
                                                           WebSocketManager webSocketManager,
                                                           HalfTimeNotifier halfTimeNotifier) {
    return new OptaFixtureDataProcessor(ongoingMatchDataCache, matchDataPullClient, matchPushDataRetriever,
      webSocketManager, matchLiveDataProcessor, matchFinalDataProcessor, halfTimeNotifier);
  }

  @Bean
  public SquadsIngestionService squadsIngestionService(PlayerService playerService, KafkaService kafkaService,
                                                       MetricsManager metrics, ObjectMapper objectMapper) {
    return new SquadsIngestionService(playerService, kafkaService, metrics, objectMapper);
  }
}
