package com.wsf.dataingestor.config;

import java.net.http.HttpClient;
import java.time.Duration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static java.time.temporal.ChronoUnit.SECONDS;

@Configuration
public class HttpClientConfig {

  @Bean
  public HttpClient httpClient() {
    return HttpClient
      .newBuilder()
      .version(HttpClient.Version.HTTP_2) // default
      .connectTimeout(Duration.of(10, SECONDS))
      .followRedirects(HttpClient.Redirect.NORMAL) // Always redirect, except from HTTPS URLs to HTTP URLs.
      .build();
  }
}
