package com.wsf.dataingestor.sports.soccer.index;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.sports.soccer.Constants.MINIMUM_MINUTES_PLAYED;

public class SoccerIndexCalculator implements IndexCalculator {
  private static final Map<Player.Position, PlayersBetaConstants> positionToConstants = Map.of(
    Player.Position.GOALKEEPER, new GoalkeeperBetaConstants(), Player.Position.DEFENDER, new DefenderBetaConstants(),
    Player.Position.MIDFIELDER, new MidfielderBetaConstants(), Player.Position.FORWARD, new ForwardBetaConstants());

  @Override
  public IndexPerformance calculate(Player.Position playerPosition, Map<String, Number> stats) {
    boolean isValid = checkIndexValidity(stats);
    double indexValue = calculateIndex(playerPosition, stats);

    return new IndexPerformance(isValid, indexValue);
  }

  private double calculateIndex(Player.Position playerPosition, Map<String, Number> stats) {
    PlayersBetaConstants playersBeta = positionToConstants.get(playerPosition);
    PlayersIndexDTO playersIndexDTO = calculateIndexParams(stats);
    return calculateFinalValue(playersBeta, playersIndexDTO);
  }

  private static double calculateFinalValue(PlayersBetaConstants playersBeta, PlayersIndexDTO playersIndexDTO) {
    double beta_totalRedCards = playersIndexDTO.getYellowCards() == 0 ?
                                playersBeta.totalRedCards :
                                playersBeta.totalRedCards - playersBeta.totalYellowCards;

    double val = 0.0;
    val += playersBeta.intercept;
    val += playersBeta.goalsMade * playersIndexDTO.getGoalsMade();
    val += playersBeta.ownGoals * playersIndexDTO.getOwnGoals();
    val += playersBeta.totalAssists * playersIndexDTO.getAssistsGoal();
    val += beta_totalRedCards * playersIndexDTO.getRedCards();
    val += playersBeta.earlyRedCards * playersIndexDTO.getEarlyRedCards();
    val += playersBeta.totalYellowCards * playersIndexDTO.getYellowCards();
    val += playersBeta.noCards * playersIndexDTO.getNoCards();
    val += playersBeta.penaltyKickMissed * playersIndexDTO.getPenaltiesMissed();
    val += playersBeta.getShotsOnGoal() * playersIndexDTO.getShotsOnGoal();
    val += playersBeta.getTotalShots() * playersIndexDTO.getTotalShots();
    val += playersBeta.getNoShots() * playersIndexDTO.getNoShots();
    val += playersBeta.getNoSOG() * playersIndexDTO.getNoShotsGoal();
    val += playersBeta.getHighSOG() * playersIndexDTO.getHighShotsGoal();
    val += playersBeta.getTotalCrosses() * playersIndexDTO.getTotalCrosses();
    val += playersBeta.getTotalFouls() * playersIndexDTO.getTotalFouls();
    val += playersBeta.getNoFouls() * playersIndexDTO.getNoFouls();
    val += playersBeta.getHighFouls() * playersIndexDTO.getHighFouls();
    val += playersBeta.getTotalKeyPasses() * playersIndexDTO.getTotalKeyPasses();
    val += playersBeta.getTotalSuccessfulPasses() * playersIndexDTO.getTotalSuccessfulPasses();
    val += playersBeta.getTotalPasses() * playersIndexDTO.getTotalPasses();
    val += playersBeta.getHighPassesPerc() * playersIndexDTO.getHighPassesPercentage();
    val += playersBeta.getLowPassPerc() * playersIndexDTO.getLowPassesPercentage();
    val += playersBeta.getOpponentTeamScore() * playersIndexDTO.getOpponentTeamScore();
    val += playersBeta.getCleanSheet() * playersIndexDTO.getCleanSheet();
    val += playersBeta.getTeamScore() * playersIndexDTO.getTeamScore();
    val += playersBeta.getHeavyLoss() * playersIndexDTO.getHeavy_loss();
    val += playersBeta.getNoBonus() * playersIndexDTO.getNoBonus();
    val += playersBeta.comboBonus * playersIndexDTO.getComboBonus();
    val += playersBeta.getTotalGkCleanSheet() * playersIndexDTO.getGKCleanSheet();
    val += playersBeta.getTotalGkSaves() * playersIndexDTO.getTotalGKSaves();
    val += playersBeta.gkPenaltiesSaved * playersIndexDTO.getGkPenaltiesSaved();
    val += playersBeta.getGkGoalsConceded() * playersIndexDTO.getTotalGKGoalsConceded();
    return (double) Math.round(val * 100) / 100;
  }

  private PlayersIndexDTO calculateIndexParams(Map<String, Number> stats) {
    return PlayersIndexDTO
      .builder()
      .teamScore(getStatsOrDefault(stats, "teamScore"))
      .opponentTeamScore(getStatsOrDefault(stats, "opponentTeamScore"))
      .minsPlayed(getStatsOrDefault(stats, "minsPlayed"))
      .redCards(getStatsOrDefault(stats, "redCards"))
      .yellowCards(getStatsOrDefault(stats, "yellowCards"))
      .totalShots(getStatsOrDefault(stats, "shots"))
      .shotsOnGoal(getStatsOrDefault(stats, "shotsOnGoal"))
      .totalFouls(getStatsOrDefault(stats, "fouls"))
      .totalSuccessfulPasses(getStatsOrDefault(stats, "completedPasses"))
      .totalPasses(getStatsOrDefault(stats, "passes"))
      .goalsMade(getStatsOrDefault(stats, "goalsMade"))
      .assistsGoal(getStatsOrDefault(stats, "assistsGoal"))
      .totalGKGoalsConceded(getStatsOrDefault(stats, "gkGoalsConceded"))
      .totalCrosses(getStatsOrDefault(stats, "crosses"))
      .totalKeyPasses(getStatsOrDefault(stats, "keyPasses"))
      .totalGKSaves(getStatsOrDefault(stats, "gkSaves"))
      .ownGoals(getStatsOrDefault(stats, "ownGoals"))
      .penaltiesMissed(getStatsOrDefault(stats, "penaltiesMissed"))
      .gkPenaltiesSaved(getStatsOrDefault(stats, "gkPenaltiesSaved"))
      .build();
  }

  private static double getStatsOrDefault(Map<String, Number> stats, String statName) {
    return stats.getOrDefault(statName, 0.0).doubleValue();
  }

  public boolean checkIndexValidity(Map<String, Number> stats) {

    if (stats.get("minsPlayed") == null) {
      return false;
    }
    if (stats.get("minsPlayed").intValue() < MINIMUM_MINUTES_PLAYED) {
      return false;
    }

    List<Number> mainProps = Arrays.asList(stats.getOrDefault("goalsMade", 0), stats.getOrDefault("assistsGoal", 0),
      stats.getOrDefault("redCards", 0), stats.getOrDefault("yellowCards", 0), stats.getOrDefault("penaltiesMissed", 0),
      stats.getOrDefault("gkPenaltiesSaved", 0), stats.getOrDefault("ownGoals", 0),
      stats.getOrDefault("gkGoalsConceded", 0));

    return mainProps
      .stream()
      .anyMatch(n -> n.intValue() > 0);
  }

}
