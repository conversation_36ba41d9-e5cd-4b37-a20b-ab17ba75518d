package com.wsf.dataingestor.sports.soccer;

import com.wsf.domain.customer.MatchInterval;

import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;

public class Constants {

  public static final String GOALS_MADE_REGULAR_TIMES =
    GOAL.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String ASSISTS_GOAL_REGULAR_TIMES =
    ASSIST_GOAL.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String SHOTS_REGULAR_TIMES = SHOT.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String SHOTS_ON_GOAL_REGULAR_TIMES =
    SHOT_ON_GOAL.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String SHOTS_OFF_GOAL = "shotsOffGoal";
  public static final String SHOTS_BLOCKED = "shotsBlocked";
  public static final String CROSSES = "crosses";
  public static final String FOULS_COMMITTED_REGULAR_TIMES =
    FOUL.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TACKLES_WON_REGULAR_TIMES =
    TACKLE_WON.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String MINS_PLAYED = "minsPlayed";
  public static final String INTERCEPTIONS = "interceptions";
  public static final String GK_PENALTIES_SAVED = "gkPenaltiesSaved";
  public static final String RED_CARDS_REGULAR_TIMES = RED_CARD.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String YELLOW_CARDS_REGULAR_TIMES =
    YELLOW_CARD.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String GOAL_KICKS_REGULAR_TIMES =
    GOALKICK.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String HOME_SCORE = "homeScore";
  public static final String AWAY_SCORE = "awayScore";
  public static final String TEAM_SCORE = "teamScore";
  public static final String OPPONENT_TEAM_SCORE = "opponentTeamScore";
  public static final String CORNERS_REGULAR_TIMES = CORNER.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String OFFSIDES_REGULAR_TIMES = OFFSIDE.getStatisticName() + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();

  // TEAM STATS
  public static final String TEAM_CORNERS = "teamCorners";
  public static final String TEAM_CORNERS_REGULAR_TIMES =
    TEAM_CORNERS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_GOALS = "teamGoals";
  public static final String TEAM_GOALS_REGULAR_TIMES =
    TEAM_GOALS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_SHOTS = "teamShots";
  public static final String TEAM_SHOTS_REGULAR_TIMES =
    TEAM_SHOTS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_SHOTS_ON_GOAL = "teamShotsOnGoal";
  public static final String TEAM_SHOTS_ON_GOAL_REGULAR_TIMES =
    TEAM_SHOTS_ON_GOAL + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_GOAL_KICKS = "teamGoalKicks";
  public static final String TEAM_GOAL_KICKS_REGULAR_TIMES =
    TEAM_GOAL_KICKS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_FOULS = "teamFouls";
  public static final String TEAM_FOULS_REGULAR_TIMES =
    TEAM_FOULS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_OFFSIDES = "teamOffsides";
  public static final String TEAM_OFFSIDES_REGULAR_TIMES =
    TEAM_OFFSIDES + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_YELLOW_CARDS = "teamYellowCards";
  public static final String TEAM_YELLOW_CARDS_REGULAR_TIMES =
    TEAM_YELLOW_CARDS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_OFFSIDES_KICK_REGULAR_TIMES =
    TEAM_OFFSIDES + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_RED_CARDS = "teamRedCards";
  public static final String TEAM_RED_CARDS_REGULAR_TIMES =
    TEAM_RED_CARDS + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();
  public static final String TEAM_TACKLES_WON = "teamTacklesWon";
  public static final String TEAM_TACKLES_WON_REGULAR_TIMES =
    TEAM_TACKLES_WON + "_" + MatchInterval.REGULAR_TIMES.toIntervalString();

  // WSF_INDEX
  public static Integer MINIMUM_MINUTES_PLAYED = 15;

}
