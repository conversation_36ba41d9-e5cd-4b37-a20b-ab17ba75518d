package com.wsf.dataingestor.sports.soccer.index;

import lombok.Getter;

public interface PlayersBetaConstants {
  double goalsMade = 6;
  double ownGoals = -10;
  double totalAssists = 3.5;
  double totalRedCards = -7.5;
  double earlyRedCards = -2.5;
  double totalYellowCards = -2;
  double noCards = 1.5;
  double penaltyKickMissed = -6;
  double comboBonus = 5;
  double gkPenaltiesSaved = 10;
  double intercept = 60;

  double getShotsOnGoal();

  double getTotalShots();

  double getNoShots();

  double getNoSOG();

  double getHighSOG();

  double getTotalCrosses();

  double getTotalFouls();

  double getNoFouls();

  double getHighFouls();

  double getTotalKeyPasses();

  double getTotalSuccessfulPasses();

  double getTotalPasses();

  double getHighPassesPerc();

  double getLowPassPerc();

  double getOpponentTeamScore();

  double getCleanSheet();

  double getTeamScore();

  double getHeavyLoss();

  double getNoBonus();

  double getTotalGkSaves();

  double getGkGoalsConceded();

  double getTotalGkCleanSheet();
}

@Getter
class DefenderBetaConstants implements PlayersBetaConstants {
  double shotsOnGoal = 1.5;
  double totalShots = -0.5;
  double noShots = 0;
  double noSOG = 0;
  double highSOG = 1.5;
  double totalCrosses = 0.15;
  double totalFouls = -0.5;
  double noFouls = 0.5;
  double highFouls = -0.3;
  double totalKeyPasses = 0.85;
  double totalSuccessfulPasses = 0.35;
  double totalPasses = -0.30;
  double highPassesPerc = 3;
  double lowPassPerc = -2;
  double opponentTeamScore = -1;
  double cleanSheet = 0.25;
  double teamScore = 0.5;
  double heavyLoss = -2.5;
  double noBonus = 0;
  double totalGkSaves = 0;
  double gkGoalsConceded = 0;
  double totalGkCleanSheet = 0;
}

@Getter
class GoalkeeperBetaConstants implements PlayersBetaConstants {
  double shotsOnGoal = 1.5;
  double totalShots = 0;
  double noShots = 0;
  double noSOG = 0;
  double highSOG = 1.5;
  double totalCrosses = 0.15;
  double totalFouls = -0.5;
  double noFouls = 0.5;
  double highFouls = -0.3;
  double totalKeyPasses = 0.85;
  double totalSuccessfulPasses = 0.35;
  double totalPasses = -0.30;
  double highPassesPerc = 3;
  double lowPassPerc = -2;
  double opponentTeamScore = -1;
  double cleanSheet = 0;
  double teamScore = 0.5;
  double heavyLoss = -2.5;
  double noBonus = 0;
  double totalGkSaves = 0.5;
  double gkGoalsConceded = -2;
  double totalGkCleanSheet = 5;
}

@Getter
class MidfielderBetaConstants implements PlayersBetaConstants {
  double shotsOnGoal = 1.5;
  double totalShots = -0.5;
  double noShots = 0;
  double noSOG = 0;
  double highSOG = 1.5;
  double totalCrosses = 0.15;
  double totalFouls = -0.5;
  double noFouls = 0.5;
  double highFouls = -0.3;
  double totalKeyPasses = 0.85;
  double totalSuccessfulPasses = 0.35;
  double totalPasses = -0.30;
  double highPassesPerc = 3;
  double lowPassPerc = -2;
  double opponentTeamScore = -1;
  double cleanSheet = 0.25;
  double teamScore = 0.5;
  double heavyLoss = -2.5;
  double noBonus = 0;
  double totalGkSaves = 0;
  double gkGoalsConceded = 0;
  double totalGkCleanSheet = 0;
}

@Getter
class ForwardBetaConstants implements PlayersBetaConstants {
  double shotsOnGoal = 1.5;
  double totalShots = -0.5;
  double noShots = -1.5;
  double noSOG = -1.5;
  double highSOG = 1.5;
  double totalCrosses = 0.15;
  double totalFouls = -0.5;
  double noFouls = 0.5;
  double highFouls = -0.3;
  double totalKeyPasses = 0.85;
  double totalSuccessfulPasses = 0.35;
  double totalPasses = -0.30;
  double highPassesPerc = 3;
  double lowPassPerc = -2;
  double opponentTeamScore = -1;
  double cleanSheet = 0.25;
  double teamScore = 0.5;
  double heavyLoss = -2.5;
  double noBonus = -3;
  double totalGkSaves = 0;
  double gkGoalsConceded = 0;
  double totalGkCleanSheet = 0;
}
