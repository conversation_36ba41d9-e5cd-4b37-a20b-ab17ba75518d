package com.wsf.dataingestor.sports.soccer.index;

import lombok.Builder;
import lombok.Data;

@Data
@Builder(builderClassName = "PlayersIndexDTOBuilder")
public class PlayersIndexDTO {
  private final double teamScore;
  private final double opponentTeamScore;
  private final double minsPlayed;
  private final double redCards;
  private final double yellowCards;
  private final double totalShots;
  private final double shotsOnGoal;
  private final double totalFouls;
  private final double totalSuccessfulPasses;
  private final double totalPasses;
  private final double goalsMade;
  private final double assistsGoal;
  private final double totalGKGoalsConceded;
  private final double totalCrosses;
  private final double totalKeyPasses;
  private final double totalGKSaves;
  private final double ownGoals;
  private final double penaltiesMissed;
  private final double gkPenaltiesSaved;
  private final double heavy_loss;
  private final double earlyRedCards;
  private final double noCards;
  private final double noShots;
  private final double noShotsGoal;
  private final double highShotsGoal;
  private final double noFouls;
  private final double highFouls;
  private final double highPassesPercentage;
  private final double lowPassesPercentage;
  private final double cleanSheet;
  private final double noBonus;
  private final double comboBonus;
  private final double GKCleanSheet;

  static class PlayersIndexDTOBuilder {
    public PlayersIndexDTO build() {

      double heavy_loss = (this.opponentTeamScore - this.teamScore > 1 && this.minsPlayed >= 30) ? 1.0 : 0.0;
      double earlyRedCards = (this.redCards > 0 && this.minsPlayed <= 45) ? 1.0 : 0.0;
      double noCards = (this.redCards <= 0 && this.yellowCards <= 0 && this.minsPlayed >= 60) ? 1.0 : 0.0;
      double noShots = (this.totalShots <= 0 && this.minsPlayed >= 15) ? 1.0 : 0.0;
      double noShotsGoal = (this.shotsOnGoal <= 0 && this.minsPlayed >= 30) ? 1.0 : 0.0;
      double highShotsGoal = (this.shotsOnGoal >= 3) ? 1.0 : 0.0;
      double noFouls = (this.totalFouls <= 0 && this.minsPlayed >= 60) ? 1.0 : 0.0;
      double highFouls = (this.totalFouls >= 3) ? 1.0 : 0.0;
      double highPassesPercentage = (this.totalSuccessfulPasses / this.totalPasses >= 0.95 && this.totalPasses > 10 &&
        this.minsPlayed >= 30) ? 1.0 : 0.0;
      double lowPassesPercentage = ((((this.totalSuccessfulPasses / this.totalPasses) <= 0.65) ||
        (this.totalPasses <= 5)) && (this.minsPlayed >= 30)) ? 1.0 : 0.0;
      double cleanSheet = (this.opponentTeamScore <= 0 && this.minsPlayed >= 45) ? 1.0 : 0.0;
      double noBonus = (this.goalsMade == 0 && this.assistsGoal == 0 && this.minsPlayed >= 40) ? 1.0 : 0.0;
      double comboBonus = (this.goalsMade > 0 && this.assistsGoal > 0) ? 1.0 : 0.0;
      double GKCleanSheet = (this.totalGKGoalsConceded <= 0 && this.minsPlayed >= 45) ? 1.0 : 0.0;

      return new PlayersIndexDTO(this.teamScore, this.opponentTeamScore, this.minsPlayed, this.redCards,
        this.yellowCards, this.totalShots, this.shotsOnGoal, this.totalFouls, this.totalSuccessfulPasses,
        this.totalPasses, this.goalsMade, this.assistsGoal, this.totalGKGoalsConceded, this.totalCrosses,
        this.totalKeyPasses, this.totalGKSaves, this.ownGoals, this.penaltiesMissed, this.gkPenaltiesSaved, heavy_loss,
        earlyRedCards, noCards, noShots, noShotsGoal, highShotsGoal, noFouls, highFouls, highPassesPercentage,
        lowPassesPercentage, cleanSheet, noBonus, comboBonus, GKCleanSheet);
    }
  }
}
