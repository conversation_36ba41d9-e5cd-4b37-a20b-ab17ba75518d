package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.function.Function;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.MasterTeam;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.UnmappedTeam;
import com.wsf.repository.common.MasterTeamRepository;
import com.wsf.repository.common.TeamRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@RequiredArgsConstructor
@Service
public class TeamService {

  private final RepositoryFactory.TeamRepositoryFactory teamRepositoryFactory;
  private final MasterTeamRepository masterTeamRepository;
  private final UnmappedEntityClient unmappedEntityClient;
  private final DBUtils dbUtils;
  private final MetricsManager metricsManager;

  public Team getById(String competitionId, String id) {
    TeamRepository repo = dbUtils.retrieveRepo(teamRepositoryFactory, competitionId);
    return repo.findById(id)
      .orElseThrow(() -> new NoSuchElementException(format("Can't find team with id %s", id)));
  }

  public Team findByExternalIdOrCreateUnmapped(String competitionId, String externalId, String teamName,
                                               String teamAbbreviation, ExternalProvider provider,
                                               Function<String, Optional<MasterTeam>> masterTeamFinder) {
    return ofNullable(masterTeamFinder.apply(externalId)
      .orElseGet(() -> {
        log.info("Creating unmapped team={} for competitionId={}", teamName, competitionId);
        UnmappedTeam unmappedTeam = unmappedEntityClient.createUnmappedTeam(teamName, teamAbbreviation, externalId,
          provider);

        if (nonNull(unmappedTeam)) {
          if (ofNullable(unmappedTeam.getIgnore()).orElse(false)) {
            log.warn("Unmapped team {} already exists but set as ignored.", unmappedTeam.getExternalTeamName());
          } else {
            log.warn("Unmapped team {} already exists and not set to be ignored.", unmappedTeam.getExternalTeamName());
            metricsManager.MATCHES_FEED_UNMAPPED_TEAM.increment();
          }
        }
        return null;
      }))
      .map(masterTeam -> findOrCreateTournamentTeam(competitionId, masterTeam))
      .orElse(null);
  }

  public Optional<MasterTeam> findMasterTeamByOptaId(String optaId) {
    return masterTeamRepository.findByOptaId(optaId);
  }

  public Optional<MasterTeam> findMasterTeamBySportmonksId(String sportmonksId) {
    return masterTeamRepository.findBySportmonksId(sportmonksId);
  }

  public Team findTeamByOptaId(String competitionId, String optaId) {
    TeamRepository teamRepository = dbUtils.retrieveRepo(teamRepositoryFactory, competitionId);
    return teamRepository.findByOptaId(optaId)
      .orElseThrow(() -> new IllegalArgumentException(
        format("could not find team with optaId %s for competitionId %s", optaId, competitionId)));
  }

  public Team findTeamBySportmonksId(String competitionId, String smId) {
    TeamRepository teamRepository = dbUtils.retrieveRepo(teamRepositoryFactory, competitionId);
    return teamRepository.findBySportmonksId(smId)
      .orElseThrow(() -> new IllegalArgumentException(
        format("could not find team with sportmonksId %s for competitionId %s", smId, competitionId)));
  }

  private Team findOrCreateTournamentTeam(String competitionId, MasterTeam team) {
    TeamRepository teamRepository = dbUtils.retrieveRepo(teamRepositoryFactory, competitionId);
    return teamRepository.findById(team.getId().toString())
      .map(tournamentTeam -> {
        tournamentTeam.setOptaId(team.getOptaId());
        tournamentTeam.setSportmonksId(team.getSportmonksId());
        tournamentTeam.setWyscoutId(team.getWyscoutId());
        tournamentTeam.setBetradarId(team.getBetradarId());
        return teamRepository.save(tournamentTeam);
      })
      .orElseGet(() -> {
        log.info("Creating teamName={} with id={}", team.getName(), team.getId().toString());
        return teamRepository.save(buildTournamentTeam(team));
      });
  }

  private static Team buildTournamentTeam(MasterTeam team) {
    return Team
      .builder()
      .id(team.getId())
      .name(team.getName())
      .optaId(team.getOptaId())
      .wyscoutId(team.getWyscoutId())
      .sportmonksId(team.getSportmonksId())
      .betradarId(team.getBetradarId())
      .toddId(team.getToddId())
      .build();
  }
}
