package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Provider;
import com.wsf.repository.common.FixtureRepository;

import static com.wsf.dataingestor.config.cache.CacheConstants.OPTA_FIXTURES_CACHE_NAME;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED_BY;
import static java.lang.String.format;

@Slf4j
@RequiredArgsConstructor
@Service
public class FixtureService {

  private final FixtureRepository fixtureRepository;

  @Retryable(value = DataAccessException.class, maxAttempts = 100, backoff = @Backoff(delay = 5000))
  public Fixture getFixture(String fixtureId) {
    return fixtureRepository.findById(fixtureId)
      .orElseThrow(() -> new NoSuchElementException(format("couldn't find fixture with id %s", fixtureId)));
  }

  public List<Fixture> findAllFixturesWithinNextMinutes(String tournamentId, long minutes) {
    Instant now = Instant.now();
    Instant minutesFromNow = Instant.now().plus(minutes, ChronoUnit.MINUTES);

    return fixtureRepository.findByTournamentIdAndDateBetween(tournamentId, now, minutesFromNow);
  }

  public List<Fixture> getAllFixturesOrderedBySequentialId(String tournamentId) {
    return fixtureRepository.findByTournamentIdOrderBySequentialIdDesc(tournamentId);
  }

  @Cacheable(value = OPTA_FIXTURES_CACHE_NAME, unless = "#result == null")
  public Fixture findByOptaFixtureId(String optaFixtureId) {
    return fixtureRepository.findByOptaFixtureId(optaFixtureId)
      .orElse(null);
  }

  public Fixture findByOptaFixtureIdOrTournamentAndTeamsAndDate(String optaFixtureId, String tournamentId,
                                                                String homeTeamId, String awayTeamId, Instant date) {
    return fixtureRepository
      .findByOptaFixtureIdOrTournamentAndTeamsAndDate(optaFixtureId, tournamentId, homeTeamId, awayTeamId, date, 1)
      .orElse(null);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public void saveFixture(Fixture fixture) {
    fixtureRepository.save(fixture);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public Fixture updateFixture(Fixture fixture, Map<String, Object> fieldsToUpdate) {
    return fixtureRepository.update(fixture.getId().toString(), fieldsToUpdate);
  }

  public void storeFixtureProcessStatusConnected(Fixture fixture, ExternalProvider externalProvider) {
    updateFixtureProcessStatus(fixture.getId().toString(), Fixture.FixtureProcessStatus.CONNECTED, externalProvider);
  }

  @CachePut(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public Fixture storeFixtureAsLiveCancelled(Fixture fixture, Provider provider) {
    Map<Provider, Boolean> isLiveEnabledBy = new HashMap<>(fixture.getIsLiveEnabledBy());
    isLiveEnabledBy.put(provider, false);
    return disableLiveForFixture(fixture.getId().toString(), isLiveEnabledBy);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public void storeFixtureStarted(Fixture fixture) {
    updateFixtureStatus(fixture.getId().toString(), Fixture.FixtureStatus.LIVE);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public Fixture storeFixtureFinished(Fixture fixture) {
    return updateFixtureStatus(fixture.getId().toString(), Fixture.FixtureStatus.PLAYED);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public void storeFixtureProcessStatusSettled(Fixture fixture) {
    updateFixtureProcessStatus(fixture.getId().toString(), Fixture.FixtureProcessStatus.SETTLED, null);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME, key = "#fixture.optaFixtureId", condition = "#fixture.optaFixtureId != null")
  public void removeFixtureProcessStatus(Fixture fixture) {
    updateFixtureProcessStatus(fixture.getId().toString(), null, null);
  }

  @CacheEvict(cacheNames = OPTA_FIXTURES_CACHE_NAME)
  public void evictByFixtureId(String optaFixtureId) {

  }

  private Fixture updateFixtureStatus(String fixtureId, Fixture.FixtureStatus fixtureStatus) {
    return fixtureRepository.update(fixtureId, fixtureStatus);
  }

  private Fixture disableLiveForFixture(String fixtureId, Map<Provider, Boolean> isLiveEnabledBy) {
    return fixtureRepository.update(fixtureId,
      Map.of(DB_FIELD_IS_LIVE_ENABLED, false, DB_FIELD_IS_LIVE_ENABLED_BY, isLiveEnabledBy));
  }

  private void updateFixtureProcessStatus(String fixtureId, Fixture.FixtureProcessStatus fixtureProcessStatus,
                                          ExternalProvider externalProvider) {
    fixtureRepository.update(fixtureId, fixtureProcessStatus, externalProvider);
  }

  public Optional<Fixture> getFixtureBySportmonksIdOrTournamentAndTeamsAndDate(String smFixtureId, String tournamentId,
                                                                               String homeTeamId, String awayTeamId,
                                                                               Instant time) {
    return fixtureRepository.findBySportmonksFixtureIdOrTournamentAndTeamsAndDate(smFixtureId, tournamentId, homeTeamId,
      awayTeamId, time, 1);
  }

  public Optional<Fixture> findBySportmonksFixtureId(String smFixtureId) {
    return fixtureRepository.findBySportmonksFixtureId(smFixtureId);
  }

  public List<Fixture> findByDayWindow(Instant startDate, int numberOfDays) {
    return fixtureRepository.findByDayWindow(startDate, numberOfDays);
  }
}