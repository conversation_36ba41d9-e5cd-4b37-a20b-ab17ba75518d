package com.wsf.dataingestor.services.stats;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import com.google.common.collect.Sets;
import com.wsf.domain.common.CompetitionConfig;

import static java.lang.String.format;
import static java.util.stream.Collectors.toSet;

@RequiredArgsConstructor
public final class AllStatistic0Validator implements StatsValidator {

  private final Set<String> statsToCheck;

  public void validateStats(List<Map<String, Number>> contestants, CompetitionConfig competitionConfig) {
    if (contestants.isEmpty()) {
      throw new IllegalArgumentException("Contestant stats cannot be empty");
    }

    var supportedStats = getSupportedStats(competitionConfig);
    Set<String> supportedStatsToCheck = Sets.intersection(statsToCheck, supportedStats);

    if (supportedStatsToCheck.isEmpty()) {
      return;
    }

    boolean allZero = contestants
      .stream()
      .allMatch(contestantStats -> supportedStatsToCheck
        .stream()
        .allMatch(statName -> isEmpty(contestantStats, statName)));

    if (allZero) {
      throw new IllegalArgumentException(
        format("All statistics values cannot be 0 for stats=%s and competition %s", supportedStatsToCheck,
          competitionConfig.getCompetition().getDesc()));
    }
  }

  private static Set<String> getSupportedStats(CompetitionConfig competitionConfig) {
    return competitionConfig.getMarkets()
      .stream()
      .map(CompetitionConfig.CompetitionMarketConfig::getPropName)
      .collect(toSet());
  }

  private static boolean isEmpty(Map<String, Number> stats, String statName) {
    var statsVal = stats.get(statName);
    return statsVal == null || statsVal.intValue() == 0;
  }
} 
