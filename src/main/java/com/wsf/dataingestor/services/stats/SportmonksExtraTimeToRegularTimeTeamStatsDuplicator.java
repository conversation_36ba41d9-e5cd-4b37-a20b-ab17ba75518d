package com.wsf.dataingestor.services.stats;

import java.util.Map;
import java.util.Set;

import static com.wsf.dataingestor.services.stats.StatsUtils.addRegularTimeStat;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;

public class SportmonksExtraTimeToRegularTimeTeamStatsDuplicator implements StatsDuplicator {

  private static final Set<String> SPORTMONKS_REGULAR_TIME_STATS_DUPLICATION = Set.of(TEAM_SHOTS, TEAM_SHOTS_ON_GOAL,
    TEAM_CORNERS, TEAM_FOULS, TEAM_OFFSIDES);

  @Override
  public void duplicateStats(Map<String, Number> originalStats) {
    SPORTMONKS_REGULAR_TIME_STATS_DUPLICATION.forEach(stats -> {
      if (originalStats.containsKey(stats)) {
        var statsValue = (int) originalStats.get(stats);
        addRegularTimeStat(originalStats, stats, statsValue);
      }
    });
  }
}
