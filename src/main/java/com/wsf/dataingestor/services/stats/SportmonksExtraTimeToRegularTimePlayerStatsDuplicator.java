package com.wsf.dataingestor.services.stats;

import java.util.Map;
import java.util.Set;

import static com.wsf.dataingestor.services.stats.StatsUtils.addRegularTimeStat;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;

public class SportmonksExtraTimeToRegularTimePlayerStatsDuplicator implements StatsDuplicator {

  private static final Set<String> SPORTMONKS_REGULAR_TIME_STATS_DUPLICATION = Set.of(SHOT.getStatisticName(), SHOT_ON_GOAL.getStatisticName(),
    FOUL.getStatisticName());

  @Override
  public void duplicateStats(Map<String, Number> originalStats) {
    SPORTMONKS_REGULAR_TIME_STATS_DUPLICATION.forEach(stats -> {
      if (originalStats.containsKey(stats)) {
        var statsValue = originalStats.get(stats).intValue();
        addRegularTimeStat(originalStats, stats, statsValue);
      }
    });
  }
}
