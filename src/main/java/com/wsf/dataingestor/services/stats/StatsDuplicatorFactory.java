package com.wsf.dataingestor.services.stats;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.EntityType;
import com.wsf.dataingestor.models.MatchDataFeed.FeedProvider;

import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatsDuplicatorFactory {

  public StatsDuplicator getStatsDuplicator(EntityType entityType, FeedProvider feedProvider) {
    return switch (entityType) {
      case PLAYER -> getPlayerStatsDuplicator(feedProvider);
      case TEAM -> getTeamStatsDuplicator(feedProvider);
      case MATCH -> null;
    };
  }

  private StatsDuplicator getPlayerStatsDuplicator(FeedProvider feedProvider) {
    return ofNullable(feedProvider)
      .map(provider -> switch (feedProvider) {
        case SPORTMONKS -> new SportmonksExtraTimeToRegularTimePlayerStatsDuplicator();
        case RUNNINGBALL, OPTA, WSF -> null;
      })
      .orElse(null);
  }

  private StatsDuplicator getTeamStatsDuplicator(FeedProvider feedProvider) {
    return ofNullable(feedProvider)
      .map(provider -> switch (feedProvider) {
        case SPORTMONKS -> new SportmonksExtraTimeToRegularTimeTeamStatsDuplicator();
        case RUNNINGBALL, OPTA, WSF -> null;
      })
      .orElse(null);
  }
}
