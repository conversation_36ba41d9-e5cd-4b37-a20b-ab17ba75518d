package com.wsf.dataingestor.services.stats;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.services.stats.StatsUtils.enrichMatchScoreFromCache;
import static com.wsf.dataingestor.services.stats.StatsUtils.mergeStats;
import static com.wsf.dataingestor.services.stats.StatsUtils.updateSentOff;
import static com.wsf.dataingestor.services.stats.StatsUtils.updateStatsFromEvents;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOAL_KICKS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Objects.nonNull;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamStatsEnricher {

  private final StatsDuplicatorFactory statsDuplicatorFactory;

  //@formatter:off
  private static final Map<SoccerMatchEvent, String> PLAYER_STAT_TO_TEAM_STATS_AGGREGATE_FROM_EVENTS_LIVE = Map.of(
    SHOT, TEAM_SHOTS,
    SHOT_ON_GOAL, TEAM_SHOTS_ON_GOAL,
    CORNER, TEAM_CORNERS,
    GOALKICK, TEAM_GOAL_KICKS,
    OFFSIDE, TEAM_OFFSIDES,
    YELLOW_CARD, TEAM_YELLOW_CARDS,
    GOAL, TEAM_GOALS,
    FOUL, TEAM_FOULS,
    RED_CARD, TEAM_RED_CARDS,
    TACKLE_WON, TEAM_TACKLES_WON
  );
  //@formatter:on

  //@formatter:off
  private static final Map<SoccerMatchEvent, String> PLAYER_STAT_TO_TEAM_STATS_AGGREGATE_FROM_EVENTS_POST_MATCH = Map.of(
    YELLOW_CARD, TEAM_YELLOW_CARDS,
    FOUL, TEAM_FOULS,
    SHOT, TEAM_SHOTS,
    SHOT_ON_GOAL, TEAM_SHOTS_ON_GOAL,
    OFFSIDE, TEAM_OFFSIDES,
    RED_CARD, TEAM_RED_CARDS,
    TACKLE_WON, TEAM_TACKLES_WON);
  //@formatter:on

  public Map<String, Number> enrichLiveStats(Fixture fixture, TeamDataDTO teamData, TeamMatchData teamCachedData,
                                             OngoingMatchData ongoingMatchData) {
    String teamId = teamData.getTeamId();

    Map<String, Number> mergedStats = mergeStats(teamCachedData, teamData.getStats());

    Map<String, Number> computedStats = enrichLiveTeamStats(fixture, teamData, ongoingMatchData, mergedStats);

    log.info("enriched stats for teamId={} and fixtureId={} timestamp: {} stats: {}", teamId, fixture.getIdAsString(),
      teamData.getTimestamp(), computedStats);
    return computedStats;
  }

  public Map<String, Number> enrichFinalStats(MatchDataFeed matchDataFeed, TeamDataDTO teamData) {
    Map<String, Number> updatableStats = teamData.getStats();
    List<EntityEventDTO> playerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    // FIXME: [PROD-6548] This check must be removed once we use the supportedEventTypes in createStatsFromPlayerMatchEvents for both TeamStatsEnricher and PlayerStatsEnricher
    if (matchDataFeed.getProvider() == MatchDataFeed.FeedProvider.WSF) {
      return updatableStats;
    }

    String teamId = teamData.getTeamId();
    String fixtureId = matchDataFeed.getFixture().getIdAsString();

    Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents = playerMatchEvents
      .stream()
      .collect(groupingBy(EntityEventDTO::getEventType, mapping(PlayerMatchEventDTO::fromMatchFeedEvent, toSet())));

    createStatsFromPlayerMatchEvents(teamId, eventTypeToEvents, updatableStats);
    enrichRegularTimeMarketStats(matchDataFeed.getProvider(), updatableStats);

    log.info("enriched final teamData for teamId={} in fixtureId={} stats: {}", teamId, fixtureId, updatableStats);

    return updatableStats;
  }

  private void enrichRegularTimeMarketStats(MatchDataFeed.FeedProvider feedProvider, Map<String, Number> mergedStats) {
    var teamStatsDuplicator = statsDuplicatorFactory.getStatsDuplicator(EntityType.TEAM, feedProvider);
    if (nonNull(teamStatsDuplicator)) {
      teamStatsDuplicator.duplicateStats(mergedStats);
    }
  }

  private static Map<String, Number> enrichLiveTeamStats(Fixture fixture, TeamDataDTO teamData,
                                                         OngoingMatchData ongoingMatchData,
                                                         Map<String, Number> mergedStats) {
    String teamId = teamData.getTeamId();
    if (nonNull(ongoingMatchData)) {
      // used for shots, sog, corners, goalkicks, sentoff and yellow cards
      var eventIdToEvents = ongoingMatchData.getEventIdToPlayerMatchEvents();
      Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents = eventIdToEvents.values()
        .stream()
        .flatMap(Set::stream)
        .collect(groupingBy(PlayerMatchEventDTO::getEvent, toSet()));
      createCumulativeLiveStatsFromPlayerMatchEvents(teamId, eventTypeToEvents, mergedStats);
      updateSentOff(fixture, teamId, mergedStats, eventTypeToEvents);
    }
    enrichMatchScoreFromCache(fixture, teamId, ongoingMatchData, mergedStats);
    return mergedStats;
  }

  private static void createStatsFromPlayerMatchEvents(String teamId,
                                                       Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents,
                                                       Map<String, Number> updatableStats) {
    var eventToTeamEvents = filterAggregatedEvents(teamId, eventTypeToEvents);
    PLAYER_STAT_TO_TEAM_STATS_AGGREGATE_FROM_EVENTS_POST_MATCH.entrySet()
      .stream()
      .filter(shouldUpdateStatsFromEvents(updatableStats))
      .forEach(entry -> {
        SoccerMatchEvent playerStats = entry.getKey();
        String teamStats = entry.getValue();
        updateStatsFromEvents(playerStats, teamStats, updatableStats, eventToTeamEvents, false);
      });
  }

  private static void createCumulativeLiveStatsFromPlayerMatchEvents(String teamId,
                                                                     Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents,
                                                                     Map<String, Number> updatableStats) {
    var eventToTeamEvents = filterAggregatedEvents(teamId, eventTypeToEvents);
    PLAYER_STAT_TO_TEAM_STATS_AGGREGATE_FROM_EVENTS_LIVE.forEach(
      (playerStats, teamStats) -> updateStatsFromEvents(playerStats, teamStats, updatableStats, eventToTeamEvents,
        false));
  }

  private static Predicate<Map.Entry<SoccerMatchEvent, String>> shouldUpdateStatsFromEvents(
    Map<String, Number> updatableStats) {
    return not(playerToTeamStatsEntry -> updatableStats.containsKey(playerToTeamStatsEntry.getValue()));
  }

  private static Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> filterAggregatedEvents(String teamId,
                                                                                        Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents) {
    return eventTypeToEvents.values()
      .stream()
      .flatMap(Set::stream)
      .filter(not(PlayerMatchEventDTO::isIgnore))
      .filter(not(PlayerMatchEventDTO::isOnBench))
      .filter(event -> event.getTeamId().equals(teamId))
      .collect(groupingBy(PlayerMatchEventDTO::getEvent, toSet()));
  }
}
