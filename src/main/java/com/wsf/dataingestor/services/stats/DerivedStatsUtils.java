package com.wsf.dataingestor.services.stats;

import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.LinkedList;
import java.util.Map;
import java.util.Set;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.domain.customer.MatchInterval;

import static java.lang.String.format;
import static java.util.Comparator.comparingInt;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toCollection;

@UtilityClass
public class DerivedStatsUtils {

  static void updateDerivedStat(Map<String, Number> stats, String statName, Integer statValue,
                                Integer statValueRegularTimes) {
    if (nonNull(statValue)) {
      stats.put(statName, statValue);
    }

    if (nonNull(statValueRegularTimes)) {
      String statNameRegularTimes = format("%s_%s", statName, MatchInterval.REGULAR_TIMES.toIntervalString());
      stats.put(statNameRegularTimes, statValueRegularTimes);
    }
  }

  static void removeDerivedStat(Map<String, Number> stats, String statName) {
    String statNameRegularTimes = format("%s_%s", statName, MatchInterval.REGULAR_TIMES.toIntervalString());
    stats.remove(statName);
    stats.remove(statNameRegularTimes);
  }

  static LinkedList<PlayerMatchEventDTO> filterAndSortByTime(Set<PlayerMatchEventDTO> playerEvents) {
    if (isNull(playerEvents)) {
      return new LinkedList<>();
    }
    return playerEvents
      .stream()
      .filter(not(PlayerMatchEventDTO::isIgnore))
      .filter(not(PlayerMatchEventDTO::isOnBench))
      .sorted(comparingInt(PlayerMatchEventDTO::getTimeMin))
      .collect(toCollection(LinkedList::new));
  }

  static boolean checkIfAnyEventWithNoPeriodId(Collection<PlayerMatchEventDTO> playerMatchEvents) {
    return playerMatchEvents
      .stream()
      .anyMatch(event -> isNull(event.getPeriodId()));
  }
}
