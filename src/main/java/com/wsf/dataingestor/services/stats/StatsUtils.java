package com.wsf.dataingestor.services.stats;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.google.common.collect.Maps;
import com.wsf.dataingestor.cache.CacheUtils;
import com.wsf.dataingestor.cache.models.EntityMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.customer.MatchInterval;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.sports.soccer.Constants.AWAY_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.HOME_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.OPPONENT_TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SCORE;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.SECOND_HALF;
import static com.wsf.domain.customer.soccer.Constants.AWAY_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.customer.soccer.Constants.HOME_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toList;

@Slf4j
public class StatsUtils {

  public static Map<String, Number> createCumulativeStatsFromPlayerEvents(String entityId, EntityMatchData cachedData,
                                                                          Map<String, Number> newStats,
                                                                          Map<String, Number> mergedStats,
                                                                          List<EntityEventDTO> newEvents,
                                                                          Set<SoccerMatchEvent> eventsToIgnore) {

    Map<String, Number> outputStats = new HashMap<>(mergedStats);
    newEvents
      .stream()
      .filter(
        event -> !eventsToIgnore.contains(event.getEventType())) // these events are handled with playerMatchEvents
      .filter(event -> !event.isUnknown())
      .filter(event -> isNull(cachedData) || !cachedData.getProcessedEvents()
        .contains(event.getEventId()))
      .forEach(eventInfo -> {
        String statName = eventInfo.getEventType().getStatisticName();
        boolean statChangeAlreadyExistsInCumulativeStats = newStats.containsKey(statName);
        if (statChangeAlreadyExistsInCumulativeStats) {
          return;
        }

        String fixtureId = ofNullable(cachedData)
          .map(EntityMatchData::getFixtureId)
          .orElse("");
        log.info("new event={} for entityId={} in fixtureId={}", statName, entityId, fixtureId);
        Object val = ofNullable(cachedData)
          .map(EntityMatchData::getStats)
          .map(stats -> stats.getOrDefault(statName, 0))
          .orElse(0);

        if (val instanceof Integer) {
          outputStats.put(statName, ((Integer) val) + 1);
        } else if (val instanceof Double) {
          outputStats.put(statName, ((Double) val) + 1);
        }
      });

    return outputStats;
  }

  public static List<PlayerMatchEventDTO> updateSentOff(Fixture fixture, String teamId,
                                                        Map<String, Number> updatableStats,
                                                        Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents) {
    if (eventTypeToEvents.containsKey(RED_CARD)) {
      List<PlayerMatchEventDTO> redCardsOnThePitch = eventTypeToEvents.get(RED_CARD)
        .stream()
        .filter(not(PlayerMatchEventDTO::isOnBench))
        .toList();
      List<PlayerMatchEventDTO> teamSentOffEvents = redCardsOnThePitch
        .stream()
        .filter(event -> teamId.equals(event.getTeamId()))
        .collect(toList());

      boolean isHomeTeam = fixture.getHomeTeam().getIdAsString().equals(teamId);

      int teamSentOffPlayers = teamSentOffEvents.size();
      int opponentTeamSentOffPlayers = redCardsOnThePitch.size() - teamSentOffPlayers;
      updatableStats.put(HOME_TEAM_SENT_OFF_STAT, isHomeTeam ? teamSentOffPlayers : opponentTeamSentOffPlayers);
      updatableStats.put(AWAY_TEAM_SENT_OFF_STAT, isHomeTeam ? opponentTeamSentOffPlayers : teamSentOffPlayers);
      return teamSentOffEvents;
    } else {
      updatableStats.put(HOME_TEAM_SENT_OFF_STAT, 0);
      updatableStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);
      return emptyList();
    }
  }

  public static void enrichMatchScoreFromCache(Fixture fixture, String teamId, OngoingMatchData ongoingMatchData,
                                               Map<String, Number> mergedStats) {
    if (nonNull(ongoingMatchData)) {
      boolean isPlayerPlayingHome = fixture.getHomeTeam().getId().toString().equals(teamId);
      enrichMatchScoreFromCache(ongoingMatchData, mergedStats, isPlayerPlayingHome);
    }
  }

  public static void enrichMatchScoreFromCache(OngoingMatchData ongoingMatchData, Map<String, Number> updatableStats,
                                               boolean isTeamPlayingHome) {
    int homeScore = ongoingMatchData.getHomeScore();
    int awayScore = ongoingMatchData.getAwayScore();

    enrichMatchScore(updatableStats, isTeamPlayingHome, homeScore, awayScore);
  }

  public static void enrichMatchScore(Map<String, Number> updatableStats, boolean isTeamPlayingHome, int homeScore,
                                      int awayScore) {
    updatableStats.put(HOME_SCORE, homeScore);
    updatableStats.put(AWAY_SCORE, awayScore);

    // TODO evaluate if removing these two in case we can recalculate them off the home and away score in the customizer
    updatableStats.put(TEAM_SCORE, isTeamPlayingHome ? homeScore : awayScore);
    updatableStats.put(OPPONENT_TEAM_SCORE, isTeamPlayingHome ? awayScore : homeScore);
  }

  public static Map<String, Number> mergeStats(EntityMatchData cachedData, Map<String, Number> newStats) {
    Map<String, Number> existingData = ofNullable(cachedData)
      .map(EntityMatchData::getStats)
      .orElseGet(Maps::newHashMap);
    return CacheUtils.mergeStats(existingData, newStats);
  }

  public static boolean isBetStop(EntityMatchData cachedData) {
    return nonNull(cachedData) && cachedData.isBetStop();
  }

  public static void updateStatsFromEvents(SoccerMatchEvent event, String statName, Map<String, Number> updatableStats,
                                           Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents,
                                           boolean wasContestantSubOnDuringExtraTimes) {
    Set<PlayerMatchEventDTO> eventsByType = eventTypeToEvents.get(event);

    if (isNull(eventsByType)) {
      updateStatsFromEvents(updatableStats, statName, emptySet(), wasContestantSubOnDuringExtraTimes);
    } else {
      updateStatsFromEvents(updatableStats, statName, eventsByType, wasContestantSubOnDuringExtraTimes);
    }
  }

  public static void addRegularTimeStat(Map<String, Number> updatableStats, String statName,
                                        int countOverRegularTimes) {
    String statNameRegularTimes = format("%s_%s", statName, MatchInterval.REGULAR_TIMES.toIntervalString());
    updatableStats.put(statNameRegularTimes, countOverRegularTimes);
  }

  private static void updateStatsFromEvents(Map<String, Number> updatableStats, String statName,
                                            Set<PlayerMatchEventDTO> playerMatchEvents,
                                            boolean wasContestantSubOnDuringExtraTimes) {
    int countOverFullMatch = playerMatchEvents.size();
    int countOverRegularTimes = (int) playerMatchEvents
      .stream()
      .filter(StatsUtils::wasDoneDuringRegularTimes)
      .count();

    updatableStats.put(statName, countOverFullMatch);

    if (!wasContestantSubOnDuringExtraTimes) {
      addRegularTimeStat(updatableStats, statName, countOverRegularTimes);
    }
  }

  private static boolean wasDoneDuringRegularTimes(PlayerMatchEventDTO event) {
    return isNull(event.getPeriodId()) || event.getPeriodId() == FIRST_HALF.getPeriodId() ||
      event.getPeriodId() == SECOND_HALF.getPeriodId();
  }
}
