package com.wsf.dataingestor.services.stats;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.ratings.ScoresComputer.Score;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedProvider;
import static com.wsf.dataingestor.services.ratings.ScoresComputer.computeScoreFromFeed;
import static com.wsf.dataingestor.services.stats.StatsUtils.createCumulativeStatsFromPlayerEvents;
import static com.wsf.dataingestor.services.stats.StatsUtils.enrichMatchScore;
import static com.wsf.dataingestor.services.stats.StatsUtils.enrichMatchScoreFromCache;
import static com.wsf.dataingestor.services.stats.StatsUtils.mergeStats;
import static com.wsf.dataingestor.services.stats.StatsUtils.updateSentOff;
import static com.wsf.dataingestor.services.stats.StatsUtils.updateStatsFromEvents;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.domain.common.MatchPeriod.getMatchPeriod;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlayerStatsEnricher {

  private final StatsDuplicatorFactory statsDuplicatorFactory;

  private static final Set<SoccerMatchEvent> STATS_AGGREGATED_FROM_EVENTS = Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, SHOT,
    SHOT_ON_GOAL, CORNER, GOALKICK, FOUL, OFFSIDE, RED_CARD, TACKLE_WON);
  private static final Set<SoccerMatchEvent> SUPPORTED_FINAL_EVENT_TYPES = Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, FOUL,
    SHOT, SHOT_ON_GOAL, OFFSIDE, TACKLE_WON);


  public Map<String, Number> enrichFinalPlayerStats(MatchDataFeed matchDataFeed, PlayerDataDTO playerData) {
    String playerId = playerData.getEntityId();
    String teamId = playerData.getParentEntityId();
    String fixtureId = matchDataFeed.getFixture().getIdAsString();
    Map<String, Number> updatableStats = playerData.getStats();
    List<EntityEventDTO> matchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();

    createStatsFromPlayerMatchEvents(matchDataFeed.getProvider(), playerId, matchEvents, updatableStats);
    enrichMatchScoreFromFeed(matchDataFeed, teamId, updatableStats);
    duplicateStatsForRegularTimeMarkets(matchDataFeed.getProvider(), updatableStats);

    log.info("enriched final playerData for playerId={} in fixtureId={} stats: {}", playerId, fixtureId,
      updatableStats);

    return updatableStats;
  }

  public Map<String, Number> enrichLiveStats(Fixture fixture, PlayerDataDTO playerData,
                                             PlayerMatchData playerCachedData, List<EntityEventDTO> playerEvents,
                                             OngoingMatchData ongoingMatchData) {
    Map<String, Number> mergedStats = mergeStats(playerCachedData, playerData.getStats());
    return enrichLivePlayerStats(fixture, playerData, playerCachedData, playerEvents, ongoingMatchData, mergedStats);
  }

  private void duplicateStatsForRegularTimeMarkets(FeedProvider feedProvider, Map<String, Number> updatableStats) {
    var playerStatsDuplicator = statsDuplicatorFactory.getStatsDuplicator(EntityType.PLAYER, feedProvider);
    if (nonNull(playerStatsDuplicator)) {
      playerStatsDuplicator.duplicateStats(updatableStats);
    }
  }

  private Map<String, Number> enrichLivePlayerStats(Fixture fixture, PlayerDataDTO playerData,
                                                    PlayerMatchData cachedData, List<EntityEventDTO> playerEvents,
                                                    OngoingMatchData cachedMatchData, Map<String, Number> mergedStats) {
    String playerId = playerData.getEntityId();
    String teamId = playerData.getParentEntityId();

    Map<String, Number> newStats = playerData.getStats();

    // processing events in cache for goals, assists and yellowcards and turning them into stats
    createLiveCumulativeStatsFromPlayerMatchEvents(fixture, playerData, cachedMatchData, mergedStats);

    // TODO atm this is only processing player offsides, subon and suboff, we could delete it
    Map<String, Number> computedStats = createCumulativeStatsFromPlayerEvents(playerId, cachedData, newStats,
      mergedStats, playerEvents, STATS_AGGREGATED_FROM_EVENTS);

    enrichMinsPlayed(computedStats);
    enrichMatchScoreFromCache(fixture, teamId, cachedMatchData, computedStats);

    log.info("enriched stats for playerId={} and fixtureId={} timestamp: {} stats: {}", playerId,
      fixture.getIdAsString(), playerData.getTimestamp(), computedStats);

    return computedStats;
  }

  private static void createLiveCumulativeStatsFromPlayerMatchEvents(Fixture fixture, PlayerDataDTO playerData,
                                                                     OngoingMatchData cachedMatchData,
                                                                     Map<String, Number> updatableStats) {
    if (nonNull(cachedMatchData)) {
      String playerId = playerData.getEntityId();
      String teamId = playerData.getParentEntityId();
      Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToPlayerEvents = cachedMatchData
        .getEventIdToPlayerMatchEvents()
        .values()
        .stream()
        .flatMap(Set::stream)
        .filter(not(PlayerMatchEventDTO::isIgnore))
        .filter(not(PlayerMatchEventDTO::isOnBench))
        .filter(event -> event.getEntityId().equals(playerId))
        .collect(groupingBy(PlayerMatchEventDTO::getEventType, toSet()));

      var eventTypeToEvents = cachedMatchData.getEventIdToPlayerMatchEvents().values()
        .stream()
        .flatMap(Set::stream)
        .collect(groupingBy(PlayerMatchEventDTO::getEventType, toSet()));

      boolean wasContestantSubOnDuringExtraTimes = wasPlayerSubOnDuringExtraTimes(eventTypeToEvents);

      STATS_AGGREGATED_FROM_EVENTS.forEach(
        event -> updateStatsFromEvents(event, event.getStatisticName(), updatableStats, eventTypeToPlayerEvents,
          wasContestantSubOnDuringExtraTimes));
      updateSentOff(fixture, teamId, updatableStats, eventTypeToEvents);
    }
  }

  private static void createStatsFromPlayerMatchEvents(FeedProvider provider, String playerId,
                                                       List<EntityEventDTO> aggregatedPlayerMatchEvents,
                                                       Map<String, Number> updatableStats) {
    // FIXME: [PROD-6548] This check must be removed once we use the supportedEventTypes in createStatsFromPlayerMatchEvents for both TeamStatsEnricher and PlayerStatsEnricher
    if (provider == MatchDataFeed.FeedProvider.WSF) {
      return;
    }

    Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(not(EntityEventDTO::isIgnore))
      .filter(not(EntityEventDTO::isOnBench))
      .filter(event -> event.wasMadeByPlayer(playerId))
      .collect(groupingBy(EntityEventDTO::getEventType, mapping(PlayerMatchEventDTO::fromMatchFeedEvent, toSet())));

    boolean wasContestantSubOnDuringExtraTimes = wasPlayerSubOnDuringExtraTimes(eventTypeToEvents);

    SUPPORTED_FINAL_EVENT_TYPES
      .stream()
      .filter(event -> !updatableStats.containsKey(event.getStatisticName()))
      .forEach(event -> updateStatsFromEvents(event, event.getStatisticName(), updatableStats, eventTypeToEvents,
        wasContestantSubOnDuringExtraTimes));
  }

  private static void enrichMatchScoreFromFeed(MatchDataFeed matchDataFeed, String teamId,
                                               Map<String, Number> updatableStats) {
    Score score = computeScoreFromFeed(matchDataFeed);
    boolean isTeamPlayingHome = matchDataFeed.isHomeTeam(teamId);

    enrichMatchScore(updatableStats, isTeamPlayingHome, score.getHomeScore(), score.getAwayScore());
  }

  private static void enrichMinsPlayed(Map<String, Number> stats) {
    stats.putIfAbsent(MINS_PLAYED, 0);
  }

  private static boolean wasPlayerSubOnDuringExtraTimes(
    Map<SoccerMatchEvent, Set<PlayerMatchEventDTO>> eventTypeToEvents) {
    return ofNullable(eventTypeToEvents.get(SUB_ON))
      .map(subOnEvents -> subOnEvents
        .stream()
        .anyMatch(x -> getMatchPeriod(x.getPeriodId()).areRegularTimesOver()))
      .orElse(false);
  }
}
