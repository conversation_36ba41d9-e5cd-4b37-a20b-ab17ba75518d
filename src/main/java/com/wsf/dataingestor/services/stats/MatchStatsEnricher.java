package com.wsf.dataingestor.services.stats;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;

import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@UtilityClass
public class MatchStatsEnricher {
  public static void updateTimeData(MatchDataFeed dataFeed, OngoingMatchData cachedMatchData) {
    ofNullable(dataFeed.getLatestUpdateTs())
      .map(Instant::toEpochMilli)
      .ifPresent(cachedMatchData::setTimestamp);
    ofNullable(dataFeed.getMatchTimeMin()).ifPresent(cachedMatchData::setMatchTime);
    ofNullable(dataFeed.getMatchPeriod()).ifPresent(cachedMatchData::setMatchPeriod);
    cachedMatchData.setHasStarted(dataFeed.isInProgress());
    cachedMatchData.setFixtureStatus(dataFeed.getFixtureStatus());

    validateMatchTime(dataFeed, cachedMatchData);
  }

  private static void validateMatchTime(MatchDataFeed dataFeed, OngoingMatchData cachedMatchData) {
    if (nonNull(dataFeed.getMatchTimeMin())) {
      String fixtureId = dataFeed.getFixture().getId().toString();
      if (dataFeed.getMatchTimeMin() == 0 && cachedMatchData.getMatchTime() != 0) {
        throw new IllegalArgumentException(
          format("Error updating cache for fixtureId=%s: matchTime: %s but feed had matchTime: %s", fixtureId, 0,
            cachedMatchData.getMatchTime()));
      }
      log.info("updating cache for fixtureId={} with matchTime: {} and matchPeriod: {}", fixtureId,
        cachedMatchData.getMatchTime(), cachedMatchData.getMatchPeriod());
    }
  }
}
