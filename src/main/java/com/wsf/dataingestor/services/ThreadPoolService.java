package com.wsf.dataingestor.services;

import io.micrometer.core.instrument.Counter;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.concurrent.Executor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.jano7.executor.KeyRunnable;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.config.ExecutorServiceConfig.FIXTURES_POOL_NAME;
import static com.wsf.dataingestor.config.ExecutorServiceConfig.NR_THREADS_FIXTURES;
import static com.wsf.dataingestor.config.ExecutorServiceConfig.NR_THREADS_PLAYERS;
import static com.wsf.dataingestor.config.ExecutorServiceConfig.PLAYERS_POOL_NAME;
import static java.time.Instant.now;
import static java.util.Collections.emptyList;

@Slf4j
@Service
public class ThreadPoolService {

  private final Executor playersPool;
  private final Executor fixturesPool;
  private final MetricsManager metricsManager;

  public ThreadPoolService(@Qualifier("playersPool") Executor playersPool,
                           @Qualifier("fixturesPool") Executor fixturesPool, MetricsManager metricsManager) {
    this.playersPool = playersPool;
    this.fixturesPool = fixturesPool;
    this.metricsManager = metricsManager;
  }

  public void processPlayersUpdateInThreadPool(String key, Runnable runnable) {
    processInThreadPool(PLAYERS_POOL_NAME, key.hashCode() % NR_THREADS_PLAYERS, runnable, playersPool);
  }

  public void processFixtureUpdateInThreadPool(String key, Runnable runnable) {
    processInThreadPool(FIXTURES_POOL_NAME, key.hashCode() % NR_THREADS_FIXTURES, runnable, fixturesPool);
  }

  private void processInThreadPool(String poolName, int key, Runnable runnable, Executor executor) {
    Instant start = now();

    Runnable exceptionSafeRunnable = () -> {
      try {
        metricsManager.measureThreadStartLatency(poolName, start, emptyList());
        runnable.run();
      } catch (Exception e) {
        Counter metric = metricsManager.RATINGS_FEED_PROCESSING_ERROR;
        log.error("Error running task in threadpool error={}", metric.getId().getName(), e);
        metric.increment();
      }
    };
    Runnable keyRunnable = new KeyRunnable<>(key, exceptionSafeRunnable);
    executor.execute(keyRunnable);
  }

  public static String getPlayersUpdatesKey(Fixture fixture, String playerId) {
    return String.format("%s-%s", fixture.getIdAsString(), playerId);
  }
}
