package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.domain.common.Player;

import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.partitioningBy;

@Slf4j
@Service
@RequiredArgsConstructor
public class LineUpService {

  private final PlayerService playerService;

  public void updatePlayersPosition(MatchDataFeed parsedFeed) {
    parsedFeed.getPlayersData()
      .stream()
      .filter(not(PlayerDataDTO::isUnknown))
      .forEach(playerDataDTO -> {
        Player player = playerDataDTO.getPlayer();
        Player.DetailedPosition newPosition = playerDataDTO.getMatchPosition();
        playerService.updatePlayerDetailedPosition(player, newPosition);
      });
  }

  public static Set<Player> retrievePlayers(MatchDataFeed parsedFeed) {
    Set<String> validTeamIds = Set.of(parsedFeed.getFixture().getHomeTeam().getIdAsString(),
      parsedFeed.getFixture().getAwayTeam().getIdAsString());

    Map<Boolean, List<Player>> playersPartitionedByTeamInFixture = parsedFeed.getPlayersData()
      .stream()
      .filter(PlayerDataDTO::isPlaying)
      .filter(not(PlayerDataDTO::isUnknown))
      .map(PlayerDataDTO::getPlayer)
      .collect(partitioningBy(player -> validTeamIds.contains(player.getTeam().getIdAsString())));

    List<Player> playersWhoDoNotBelongToTheFixture = playersPartitionedByTeamInFixture.get(false);

    playersWhoDoNotBelongToTheFixture.forEach(
      player -> log.warn("According to our DB, playerId={} belongs to teamId={}, which is not part of fixtureId={}",
        player.getId().toString(), player.getTeam().getIdAsString(), parsedFeed.getFixture().getIdAsString()));

    return new HashSet<>(playersPartitionedByTeamInFixture.get(true));
  }
}
