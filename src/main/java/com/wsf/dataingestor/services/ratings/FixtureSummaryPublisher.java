package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.kafka.domain.FixtureSummaryData;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixtureSummaryPublisher {
  private final KafkaService kafkaService;

  public void send(FixtureSummaryData fixtureSummary) {
    log.info("sending fixture summary data for fixtureId={}: {}", fixtureSummary.getFixtureId(), fixtureSummary);
    if (fixtureSummary.getIsFinal()) {
      kafkaService.sendFinalFixtureSummary(fixtureSummary);
    } else {
      kafkaService.sendTemporaryFixtureSummary(fixtureSummary);
    }
  }
  
}
