package com.wsf.dataingestor.services.ratings.player;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingsProcessor.isPlayerInCurrentLineup;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static java.util.stream.Collectors.toList;

@Service
@RequiredArgsConstructor
@Slf4j
public class PlayerLiveRatingFilter {
  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final PlayerService playerService;

  public boolean shouldProcessPlayerStatusOnThePitch(MatchDataFeed parsedFeed, OngoingMatchData cachedMatchData,
                                                     String playerId) {
    boolean isPlayerInCurrentLineup = isPlayerInCurrentLineup(cachedMatchData, playerId);

    boolean hasPlayerJustBeenSentOff = hasPlayerJustBeenSentOff(parsedFeed, playerId);
    return isPlayerInCurrentLineup || hasPlayerJustBeenSentOff;
  }

  public boolean hasLineUpChangeEventHappened(MatchDataFeed parsedFeed, Player player) {
    String playerId = player.getIdAsString();
    List<EntityEventDTO> playerFeedEvents = getEventsForPlayer(parsedFeed, playerId);
    PlayerMatchData playerCachedData = playerMatchDataCache.get(parsedFeed.getFixture().getIdAsString(), playerId);

    boolean hasPlayerEnteredThePitch = processSubOnEventIfItExists(playerFeedEvents, player, playerCachedData);
    boolean hasPlayerJustBeenSentOff = hasPlayerJustBeenSentOff(parsedFeed, playerId);
    return hasPlayerJustBeenSentOff || hasPlayerEnteredThePitch;
  }

  private boolean processSubOnEventIfItExists(List<EntityEventDTO> playerFeedEvents, Player player,
                                              PlayerMatchData playerCachedData) {
    return playerFeedEvents
      .stream()
      .filter(event -> event.getEventType() == SUB_ON)
      .filter(event -> hasEventNotAlreadyHappened(event, playerCachedData.getFixtureId()))
      .peek(event -> updatePlayerPosition(player, playerCachedData))
      .findFirst()
      .isPresent();
  }

  private void updatePlayerPosition(Player player, PlayerMatchData playerCachedData) {
    log.info("updating playerId={} position to {}", player.getIdAsString(), playerCachedData.getMatchPosition());
    playerService.updatePlayerDetailedPosition(player, playerCachedData.getMatchPosition());
  }

  private boolean hasPlayerJustBeenSentOff(MatchDataFeed parsedFeed, String playerId) {
    String fixtureId = parsedFeed.getFixture().getIdAsString();
    return parsedFeed.getFeedPlayerMatchEvents()
      .stream()
      .filter(e -> e.wasMadeByPlayer(playerId))
      .filter(event -> event.getEventType() == RED_CARD || event.getEventType() == SUB_OFF)
      .anyMatch(event -> hasEventNotAlreadyHappened(event, fixtureId));
  }

  private boolean hasEventNotAlreadyHappened(EntityEventDTO event, String fixtureId) {
    PlayerMatchData playerCachedData = playerMatchDataCache.get(fixtureId, event.getEntityId());
    return !playerCachedData.getProcessedEvents()
      .contains(event.getEventId());
  }

  private static List<EntityEventDTO> getEventsForPlayer(MatchDataFeed parsedFeed, String playerId) {
    return parsedFeed.getFeedPlayerMatchEvents()
      .stream()
      .filter(event -> event.wasMadeByPlayer(playerId))
      .collect(toList());
  }
}
