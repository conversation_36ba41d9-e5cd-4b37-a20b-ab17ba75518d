package com.wsf.dataingestor.services.ratings.player;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.metrics.TagHelper;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.services.LivePlayerRatingService;
import com.wsf.dataingestor.services.PlayerRatingService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.dataingestor.services.stats.StatsValidator;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.kafka.domain.metadata.KafkaMetadata;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.RATINGS_FEED_BUILD_AND_PUBLISH_LATENCY;
import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.RATINGS_FEED_RATING_FROM_RECEIVED_LATENCY;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater.toFixtureStatus;
import static com.wsf.dataingestor.services.ratings.Utils.buildEmptyRating;
import static java.time.Duration.between;
import static java.time.Instant.now;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlayerFinalRatingsProcessor {

  public final static Set<String> BYPASS_VALIDATION_FIXTURES = Set.of("67d45f29465a58121fbd6ddb");

  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final OngoingMatchDataCacheService ongoingMatchDataCacheService;
  private final PlayerRatingService playerRatingService;
  private final LivePlayerRatingService livePlayerRatingService;
  private final PlayerStatsEnricher playerStatsEnricher;
  private final PlayerService playerService;
  private final KafkaService kafkaService;
  @Qualifier("playerStatsValidator")
  private final StatsValidator statsValidator;
  private final Utils utils;
  private final MetricsManager metricsManager;
  private final CompetitionConfigRepository competitionConfigRepository;

  public List<PlayerRating> sendFinalRatingsPlayersWhoPlayed(MatchDataFeed parsedFeed, boolean settleOdds,
                                                             boolean forceSettlement) {

    var playerRatings = parsedFeed.getPlayersData()
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .map(playerData -> Pair.of(playerData, enrichStats(parsedFeed, playerData, settleOdds)))
      .filter(pair -> !pair.getLeft().isUnknown())
      .map(pair -> {
        var playerData = pair.getLeft();
        var updatedStats = pair.getRight();
        return buildPlayerRating(parsedFeed, playerData, updatedStats);
      })
      .filter(Objects::nonNull)
      .toList();

    String fixtureId = parsedFeed.getFixture().getIdAsString();
    boolean shouldSkipValidation = BYPASS_VALIDATION_FIXTURES.contains(fixtureId);
    if (!forceSettlement && settleOdds && !shouldSkipValidation) {
      validateStats(playerRatings, parsedFeed);
    }

    playerRatings.forEach(rating -> {
      storeAndPublishRatings(rating, parsedFeed, settleOdds);
      String playerId = rating.getPlayer().getIdAsString();
      String eventId = rating.getEventId();
      log.info("stats published and stored in the db for playerId={} in fixtureId={} eventId={}", playerId,
        parsedFeed.getFixture().getIdAsString(), eventId);
      clearCache(rating);
    });

    return playerRatings;
  }

  public void sendFinalRatingsPlayerWhoDidNotPlay(MatchDataFeed parsedFeed,
                                                  List<PlayerRating> ratingsPlayersOnThePitch) {
    Fixture fixture = parsedFeed.getFixture();
    String feedId = parsedFeed.getFeedId();
    Instant receivedTs = parsedFeed.getReceivedTs();
    FixtureStatus fixtureStatus = toFixtureStatus(parsedFeed.getFixtureStatus(), parsedFeed.getDate());

    List<String> onlyPlayersWithRatings = ratingsPlayersOnThePitch
      .stream()
      .map(rating -> rating.getPlayer().getIdAsString())
      .toList();

    var sportType = fixture.getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_PLAYER);

    playersWhoDidNotPlay(parsedFeed, onlyPlayersWithRatings).forEach(player -> {
      var metadata = metadataRetriever.getMetadata(player.getIdAsString(), parsedFeed.getFeedPlayerMatchEvents(),
        new MetadataRetrievalFlags(true, parsedFeed.isManualSettlement()));
      settlePlayer(fixture, fixtureStatus, player, feedId, receivedTs, metadata);
    });
  }

  public void settlePlayer(Fixture fixture, FixtureStatus fixtureStatus, Player player, String feedId,
                           Instant receivedTs, KafkaMetadata metadata) {
    String playerId = player.getId().toString();

    PlayerRating rating = buildEmptyRating(fixture, player, feedId, receivedTs);

    log.info("triggering settlement for playerId={} who didn't play fixtureId={}", playerId,
      fixture.getId().toString());
    clearCache(rating);

    kafkaService.sendFinalPlayerRating(rating, fixtureStatus, true, metadata);
  }

  private Map<String, Number> enrichStats(MatchDataFeed parsedFeed, PlayerDataDTO playerData, boolean settleOdds) {
    if (settleOdds) {
      return playerStatsEnricher.enrichFinalPlayerStats(parsedFeed, playerData);
    } else {
      var fixture = parsedFeed.getFixture();
      String fixtureId = fixture.getIdAsString();
      var cachedMatchData = ongoingMatchDataCacheService.get(fixtureId);
      var playerCachedData = playerMatchDataCache.get(fixtureId, playerData.getEntityId());
      return playerStatsEnricher.enrichLiveStats(fixture, playerData, playerCachedData, List.of(), cachedMatchData);
    }
  }

  private List<Player> playersWhoDidNotPlay(MatchDataFeed parsedFeed, List<String> playerIdsThatPlayed) {
    Fixture fixture = parsedFeed.getFixture();
    return playerService.getAllPlayersForFixtureId(fixture)
      .stream()
      .filter(player -> !playerIdsThatPlayed.contains(player.getIdAsString()))
      .toList();
  }

  private void clearCache(PlayerRating rating) {
    playerMatchDataCache.remove(rating.getFixture().getId().toString(), rating.getPlayer().getId().toString());
  }

  private PlayerRating buildPlayerRating(MatchDataFeed matchDataFeed, PlayerDataDTO playerData,
                                         Map<String, Number> updatedStats) {
    Instant start = now();
    String feedId = matchDataFeed.getFeedId();

    try {
      boolean isFinal = !playerData.isPlaying() || !matchDataFeed.isInProgress();
      Integer periodId = ofNullable(matchDataFeed.getMatchPeriod())
        .map(MatchPeriod::getPeriodId)
        .orElse(null);
      Integer matchTime = matchDataFeed.getMatchTimeMin();
      return utils.buildPlayerRating(matchDataFeed.getFixture(), playerData.getPlayer(), isFinal, updatedStats,
        periodId, matchTime, playerData.getEventId(), feedId, matchDataFeed.getReceivedTs());
    } catch (Exception e) {
      utils.handleGenericError(feedId, playerData, e);
      return null;
    } finally {
      metricsManager.recordTimer(RATINGS_FEED_BUILD_AND_PUBLISH_LATENCY.value(), between(start, now()),
        List.of(TagHelper.Tag.of("type", "final")));
      metricsManager.recordTimer(RATINGS_FEED_RATING_FROM_RECEIVED_LATENCY.value(),
        between(matchDataFeed.getReceivedTs(), now()), List.of(TagHelper.Tag.of("type", "final")));
    }
  }

  private void storeAndPublishRatings(PlayerRating rating, MatchDataFeed matchDataFeed, boolean settleOdds) {
    livePlayerRatingService.storeRating(rating, true);
    // it's a final rating, we can afford to first store it and then send it in order to maintain consistency
    playerRatingService.storeRating(rating);

    FixtureStatus fixtureStatus = toFixtureStatus(matchDataFeed.getFixtureStatus(), matchDataFeed.getDate());

    var sportType = matchDataFeed.getFixture().getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_PLAYER);
    var metadata = metadataRetriever.getMetadata(rating.getPlayer().getIdAsString(),
      matchDataFeed.getFeedPlayerMatchEvents(), new MetadataRetrievalFlags(true, matchDataFeed.isManualSettlement()));
    sendFinalRating(rating, fixtureStatus, settleOdds, metadata);
    metricsManager.RATINGS_FEED_RATING_PROCESSED.increment();
  }

  private void sendFinalRating(PlayerRating rating, FixtureStatus fixtureStatus, boolean settleOdds,
                               KafkaMetadata metadata) {
    kafkaService.sendFinalPlayerRating(rating, fixtureStatus, settleOdds, metadata);
  }

  private void validateStats(List<PlayerRating> playerRatings, MatchDataFeed parsedFeed) {
    Fixture fixture = parsedFeed.getFixture();
    CompetitionConfig competitionConfig = competitionConfigRepository.findByCompetitionId(
      fixture.getTournament().getCompetitionId());
    List<Map<String, Number>> contestantsStats = playerRatings
      .stream()
      .map(PlayerRating::getStats)
      .toList();
    statsValidator.validateStats(contestantsStats, competitionConfig);
  }
}
