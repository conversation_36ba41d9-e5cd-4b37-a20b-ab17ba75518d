package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.mappers.EntityDataDTOToContestantStats;
import com.wsf.dataingestor.mappers.IngestorEventToKafkaEventMapper;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryDto;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryMapper;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.ContestantType;
import com.wsf.kafka.domain.FixtureSummaryData;
import com.wsf.kafka.domain.FixtureSummaryData.ContestantStats;
import com.wsf.kafka.domain.FixtureSummaryData.Event;

import static com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryEventCreator.createFixtureSummary;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static java.util.stream.Stream.concat;

@Slf4j
@Service
@RequiredArgsConstructor
public class FinalFixtureSummaryNotifier {
  private final PlayerService playerService;
  private final FixtureSummaryPublisher fixtureSummaryPublisher;
  private final FixtureSummaryRetriever fixtureSummaryRetriever;

  public void sendSettlementSummary(MatchDataFeed feed) {
    FeedFixtureStatus feedFixtureStatus = feed.getFixtureStatus();
    String fixtureId = feed.getFixture().getIdAsString();

    List<Event> eventList = ofNullable(feed.getAggregatedPlayerMatchEvents())
      .map(FinalFixtureSummaryNotifier::mapEvents)
      .orElse(List.of());
    List<ContestantStats> contestantStats = getContestantStats(feed);

    FixtureSummaryData fixtureSummary = createFixtureSummary(fixtureId, feedFixtureStatus, true, eventList,
      contestantStats, feed.getSupportedEventTypes(), feed.getFeedId());
    FixtureSummaryDto fixtureSummaryDto = FixtureSummaryMapper.fromKafkaToDto(fixtureSummary);
    fixtureSummaryRetriever.put(fixtureId, fixtureSummaryDto);
    fixtureSummaryPublisher.send(fixtureSummary);
  }

  private List<ContestantStats> getContestantStats(MatchDataFeed feed) {
    boolean isFeedEmpty = feed.getTeamsData()
      .isEmpty() && feed.getPlayersData()
      .isEmpty();
    Stream<ContestantStats> playerStatsOfPlayersWhoDidNotPlay = getContestantStatsForPlayersWhoDidNotPlay(feed);
    if (isFeedEmpty) {
      return concat(playerStatsOfPlayersWhoDidNotPlay, getEmptyStatsForTeams(feed)).collect(toList());
    } else {
      Stream<ContestantStats> contestantStatsOfEntityWhoPlayed = getContestantStatsForEntityWhoPlayed(feed);
      return concat(contestantStatsOfEntityWhoPlayed, playerStatsOfPlayersWhoDidNotPlay).collect(toList());
    }
  }

  private static Stream<ContestantStats> getEmptyStatsForTeams(MatchDataFeed feed) {
    Fixture fixture = feed.getFixture();
    ContestantStats contestantStatsHome = new ContestantStats(fixture.getHomeTeam().getIdAsString(),
      ContestantType.SOCCER_TEAM, true, false, Map.of());

    ContestantStats contestantStatsAway = new ContestantStats(fixture.getAwayTeam().getIdAsString(),
      ContestantType.SOCCER_TEAM, true, false, Map.of());
    return Stream.of(contestantStatsHome, contestantStatsAway);
  }

  private static List<Event> mapEvents(List<EntityEventDTO> events) {
    return events
      .stream()
      .filter(not(EntityEventDTO::isIgnore))
      .filter(not(EntityEventDTO::isOnBench))
      .map(IngestorEventToKafkaEventMapper::mapEntityEventDtoToKafkaEvent)
      .collect(toList());
  }

  private static Stream<ContestantStats> getContestantStatsForEntityWhoPlayed(MatchDataFeed feed) {
    return concat(feed.getPlayersData()
      .stream(), feed.getTeamsData()
      .stream()).map(EntityDataDTOToContestantStats::map);
  }

  private Stream<ContestantStats> getContestantStatsForPlayersWhoDidNotPlay(MatchDataFeed feed) {
    List<Player> players = playerService.getAllPlayersForFixtureId(feed.getFixture());

    var playersWhoPlayed = feed.getPlayersData()
      .stream()
      .filter(not(PlayerDataDTO::isUnknown))
      .filter(PlayerDataDTO::isHasPlayed)
      .map(PlayerDataDTO::getPlayerId)
      .collect(toSet());

    return players
      .stream()
      .filter(player -> !playersWhoPlayed.contains(player.getIdAsString()))
      .map(EntityDataDTOToContestantStats::map);
  }
}
