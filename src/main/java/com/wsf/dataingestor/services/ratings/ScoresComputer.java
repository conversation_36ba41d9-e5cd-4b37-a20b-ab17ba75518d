package com.wsf.dataingestor.services.ratings;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Set;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.Fixture;

import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;

public class ScoresComputer {

  public static Score computeScoreFromCache(Fixture fixture, OngoingMatchData ongoingMatchData) {
    Set<PlayerMatchEventDTO> events = ongoingMatchData.getEventIdToPlayerMatchEvents().values()
      .stream()
      .flatMap(Set::stream)
      .collect(toSet());

    String homeTeamId = fixture.getHomeTeam().getIdAsString();
    String awayTeamId = fixture.getAwayTeam().getIdAsString();

    int homeScore = 0;
    int awayScore = 0;
    for (PlayerMatchEventDTO event : events) {
      if (event.getEvent() == GOAL) {
        boolean isOwnGoal = event.isIgnore();
        boolean homeTeamGoal = event.getTeamId().equals(homeTeamId);
        boolean awayTeamGoal = event.getTeamId().equals(awayTeamId);

        if ((homeTeamGoal && !isOwnGoal) || (awayTeamGoal && isOwnGoal)) {
          homeScore += 1;
        } else {
          awayScore += 1;
        }
      }
    }

    return new Score(homeScore, awayScore);
  }

  public static Score computeScoreFromFeed(MatchDataFeed parsedFeed) {
    var fixture = parsedFeed.getFixture();
    String homeTeamId = fixture.getHomeTeam().getIdAsString();
    String awayTeamId = fixture.getAwayTeam().getIdAsString();

    int homeScore = 0;
    int awayScore = 0;
    List<EntityEventDTO> events = ofNullable(parsedFeed.getAggregatedPlayerMatchEvents()).orElse(List.of());
    for (EntityEventDTO event : events) {
      if (event.getEventType() == GOAL) {
        boolean isOwnGoal = event.isIgnore();
        boolean homeTeamGoal = event.getTeamId().equals(homeTeamId);
        boolean awayTeamGoal = event.getTeamId().equals(awayTeamId);

        if ((homeTeamGoal && !isOwnGoal) || (awayTeamGoal && isOwnGoal)) {
          homeScore += 1;
        } else {
          awayScore += 1;
        }
      }
    }

    return new Score(homeScore, awayScore);
  }

  @Getter
  @RequiredArgsConstructor
  public static class Score {
    private final int homeScore;
    private final int awayScore;
  }
}
