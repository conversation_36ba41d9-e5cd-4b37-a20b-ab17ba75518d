package com.wsf.dataingestor.services.ratings;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.wsf.dataingestor.cache.models.BaseEntityMatchData;
import com.wsf.dataingestor.cache.models.EntityMatchData;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityDataDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;

import static com.google.common.collect.Sets.newHashSet;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.Optional.ofNullable;

@Slf4j
@RequiredArgsConstructor
public class Utils {

  private final IndexCalculator indexCalculator;
  private final MetricsManager metrics;

  public PlayerRating buildPlayerRating(Fixture fixture, Player player, boolean isFinal,
                                        Map<String, Number> updatedStats, Integer periodId, Integer matchTime,
                                        String eventId, String feedId, Instant receivedTime) {
    IndexPerformance indexPerformance = calculateIndex(player, updatedStats);

    return PlayerRating
      .builder()
      .isFinal(isFinal)
      .fixture(fixture)
      .fixtureTimeMin(matchTime)
      .periodId(periodId)
      .player(player)
      .stats(updatedStats)
      .indexPerformance(indexPerformance)
      .feedId(feedId)
      .eventId(eventId)
      .timestamp(receivedTime)
      .build();
  }

  public static PlayerRating buildEmptyRating(Fixture fixture, Player player, String feedId, Instant receivedTs) {
    return PlayerRating
      .builder().isFinal(true).fixture(fixture).player(player).stats(emptyMap()).events(emptyList()).feedId(feedId)
      .timestamp(receivedTs).build();
  }

  public static TeamRating buildTeamRating(Fixture fixture, Team team, Map<String, Number> stats, Integer periodId,
                                           Integer matchTime, boolean isFinal, String feedId, String eventId,
                                           Instant receivedTime) {
    return TeamRating
      .builder()
      .fixture(fixture)
      .team(team)
      .stats(stats)
      .isFinal(isFinal)
      .feedId(feedId)
      .eventId(eventId)
      .periodId(periodId)
      .fixtureTimeMin(matchTime)
      .timestamp(receivedTime)
      .build();
  }

  public static void updateEventsInCache(BaseEntityMatchData cachedData, List<EntityEventDTO> events) {
    events.forEach(event -> updateEventsInCache(cachedData, event));
  }

  public static void updateEventsInCache(BaseEntityMatchData cachedData, EntityEventDTO event) {
    Set<String> processedEvents = updateProcessedEvents(cachedData, event);
    cachedData.setProcessedEvents(processedEvents);
  }

  private static Set<String> updateProcessedEvents(BaseEntityMatchData cachedData, EntityEventDTO event) {
    Set<String> newProcessedEvents = newHashSet(ofNullable(cachedData)
      .map(EntityMatchData::getProcessedEvents)
      .orElse(emptySet()));
    newProcessedEvents.add(event.getEventId());
    return newProcessedEvents;
  }

  private IndexPerformance calculateIndex(Player player, Map<String, Number> stats) {
    Player.Position position = player.getPosition();
    return indexCalculator.calculate(position, stats);
  }

  public void handleGenericError(String feedId, EntityDataDTO entityDataDTO, Exception e) {
    Counter metric = metrics.RATINGS_FEED_PROCESSING_ERROR;
    log.error("Error while processing EntityData {} from feedId {} error={}", entityDataDTO, feedId,
      metric.getId().getName(), e);
    metric.increment();
  }

  public void handleGenericError(String feedId, Exception e) {
    Counter metric = metrics.RATINGS_FEED_PROCESSING_ERROR;
    log.error("Error processing feedId={} error={}", feedId, metric.getId().getName(), e);
    metric.increment();
  }

  public static MatchDataFeed.FeedFixtureStatus toFeedFixtureStatus(Fixture.FixtureStatus fixtureStatus) {
    return switch (fixtureStatus) {
      case FIXTURE -> MatchDataFeed.FeedFixtureStatus.FIXTURE;
      case LIVE -> MatchDataFeed.FeedFixtureStatus.LIVE;
      case PLAYED -> MatchDataFeed.FeedFixtureStatus.PLAYED;
      case CANCELLED -> MatchDataFeed.FeedFixtureStatus.CANCELLED;
      default -> throw new IllegalArgumentException("Unknown FixtureStatus " + fixtureStatus);
    };
  }

}
