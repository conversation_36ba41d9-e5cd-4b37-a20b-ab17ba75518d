package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.mappers.IngestorEventToKafkaEventMapper;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryDto;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryMapper;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;
import com.wsf.domain.soccer.SoccerMatchEvent;
import com.wsf.kafka.domain.FixtureSummaryData;

import static com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryEventCreator.createFixtureSummary;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.toList;

@Service
@RequiredArgsConstructor
public class LiveFixtureSummaryNotifier {

  public static final Set<SoccerMatchEvent> VALID_EVENTS = Set.of(SUB_OFF, SUB_ON, GOAL, ASSIST_GOAL, CORNER,
    YELLOW_CARD, RED_CARD);

  private final FixtureSummaryPublisher fixtureSummaryPublisher;
  private final FixtureSummaryRetriever fixtureSummaryRetriever;

  public void sendLiveSummary(OngoingMatchData ongoingMatchData) {
    var validEvents = ongoingMatchData.getEventIdToPlayerMatchEvents().values()
      .stream()
      .flatMap(Collection::stream)
      .filter(playerMatchEvent -> VALID_EVENTS.contains(playerMatchEvent.getEvent()))
      .filter(not(OngoingMatchData.PlayerMatchEventDTO::isIgnore))
      .filter(not(OngoingMatchData.PlayerMatchEventDTO::isOnBench))
      .toList();

    var fixtureId = ongoingMatchData.getMatchId();
    FeedFixtureStatus fixtureStatus = ongoingMatchData.getFixtureStatus();
    sendSummaryDataWithPlayerMatchEvents(fixtureId, fixtureStatus, validEvents);
  }

  private void sendSummaryDataWithPlayerMatchEvents(String fixtureId, FeedFixtureStatus feedFixtureStatus,
                                                    List<OngoingMatchData.PlayerMatchEventDTO> playerMatchEvents) {
    List<FixtureSummaryData.Event> events = ofNullable(playerMatchEvents)
      .map(LiveFixtureSummaryNotifier::mapEventsFromPlayerMatchEvents)
      .orElse(List.of());

    FixtureSummaryData fixtureSummary = createFixtureSummary(fixtureId, feedFixtureStatus, false, events, List.of(),
      VALID_EVENTS, "events_%s");
    FixtureSummaryDto fixtureSummaryDto = FixtureSummaryMapper.fromKafkaToDto(fixtureSummary);
    fixtureSummaryRetriever.put(fixtureId, fixtureSummaryDto);
    fixtureSummaryPublisher.send(fixtureSummary);
  }

  private static List<FixtureSummaryData.Event> mapEventsFromPlayerMatchEvents(
    List<OngoingMatchData.PlayerMatchEventDTO> events) {
    return events
      .stream()
      .map(IngestorEventToKafkaEventMapper::mapPlayerMatchEventDtoToKafkaEvent)
      .collect(toList());
  }
}
