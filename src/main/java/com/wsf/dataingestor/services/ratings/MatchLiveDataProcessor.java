package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Consumer;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.NewAndDeletedEvents;
import com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingsProcessor;
import com.wsf.dataingestor.services.ratings.team.TeamLiveRatingsProcessor;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import static com.wsf.dataingestor.services.ratings.ScoresComputer.computeScoreFromCache;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.DELETED_EVENT;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.UNKNOWN_EVENT;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Comparator.comparing;
import static java.util.Objects.nonNull;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class MatchLiveDataProcessor {

  private static final Set<SoccerMatchEvent> EVENTS_TO_PROCESS_FROM_MA18DP = Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD,
    RED_CARD, SHOT, SHOT_ON_GOAL, CORNER, GOALKICK, OFFSIDE, UNKNOWN_EVENT, DELETED_EVENT, SUB_OFF, SUB_ON, FOUL,
    TACKLE_WON);

  private final PlayerLiveRatingsProcessor playerLiveRatingsProcessor;
  private final TeamLiveRatingsProcessor teamLiveRatingsProcessor;

  public NewAndDeletedEvents processFeed(MatchDataFeed parsedFeed, OngoingMatchData ongoingMatchData,
                                         Consumer<NewAndDeletedEvents> preProcessEventsFn) {
    // updating cache with new goal, assist, shots, sog, yellowcard events
    NewAndDeletedEvents newAndDeletedEvents = updateCachedPlayerMatchEvents(parsedFeed, ongoingMatchData);
    enrichScore(parsedFeed.getFixture(), ongoingMatchData);
    processSubsOrRedCardForKnownPlayers(newAndDeletedEvents, ongoingMatchData);

    preProcessEventsFn.accept(newAndDeletedEvents);
    sendRatingsToEngine(parsedFeed, ongoingMatchData, newAndDeletedEvents);

    return newAndDeletedEvents;
  }

  private void sendRatingsToEngine(MatchDataFeed parsedFeed, OngoingMatchData ongoingMatchData,
                                   NewAndDeletedEvents newAndDeletedEvents) {
    if (parsedFeed.doesContainStats() || newAndDeletedEvents.doesContainEvents() ||
      doesFeedContainSupportedEvents(parsedFeed)) {
      playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);
      teamLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);
    }
  }

  public void processPullFeed(MatchDataFeed parsedFeed, OngoingMatchData ongoingMatchData) {
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, true);
    teamLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, true);
  }

  private static void processSubsOrRedCardForKnownPlayers(NewAndDeletedEvents newAndDeletedEvents,
                                                          OngoingMatchData ongoingMatchData) {
    newAndDeletedEvents.newEvents()
      .stream()
      .filter(not(PlayerMatchEventDTO::isUnknownEntity))
      .forEach(playerEvent -> {
        if (!playerEvent.isUnknownEntity() && playerEvent.getEntityId()
          .startsWith("UNKNOWN")) {
          log.info("Omar qui - playerEvent: {}", playerEvent);
        }

        String entityId = playerEvent.getEntityId();
        String teamId = playerEvent.getTeamId();
        switch (playerEvent.getEvent()) {
          case RED_CARD, SUB_OFF -> removePlayerFromLineUp(entityId, teamId, ongoingMatchData);
          case SUB_ON -> addPlayerToLineUp(entityId, teamId, ongoingMatchData);
        }
      });

    newAndDeletedEvents.deletedEvents()
      .stream()
      .filter(not(PlayerMatchEventDTO::isUnknownEntity))
      .forEach(playerEvent -> {
        String entityId = playerEvent.getEntityId();
        String teamId = playerEvent.getTeamId();
        switch (playerEvent.getEvent()) {
          case RED_CARD, SUB_OFF -> addPlayerToLineUp(entityId, teamId, ongoingMatchData);
          case SUB_ON -> removePlayerFromLineUp(entityId, teamId, ongoingMatchData);
        }
      });
  }

  private static void removePlayerFromLineUp(String playerId, String teamId, OngoingMatchData ongoingMatchData) {
    if (ongoingMatchData.getTeamIdToLineUp().containsKey(teamId)) {
      ongoingMatchData.getTeamIdToLineUp().get(teamId).remove(playerId);
      log.info("removing playerId={} from lineup for fixtureId={}, new lineup: {}", playerId,
        ongoingMatchData.getMatchId(), ongoingMatchData.getTeamIdToLineUp());
    }
  }

  private static void addPlayerToLineUp(String playerId, String teamId, OngoingMatchData ongoingMatchData) {
    if (ongoingMatchData.getTeamIdToLineUp().containsKey(teamId)) {
      ongoingMatchData.getTeamIdToLineUp().get(teamId).add(playerId);
      log.info("adding playerId={} to lineup for fixtureId={}, new lineup: {}", playerId, ongoingMatchData.getMatchId(),
        ongoingMatchData.getTeamIdToLineUp());
    }
  }

  private static void enrichScore(Fixture fixture, OngoingMatchData ongoingMatchData) {
    ScoresComputer.Score score = computeScoreFromCache(fixture, ongoingMatchData);
    ongoingMatchData.setHomeScore(score.getHomeScore());
    ongoingMatchData.setAwayScore(score.getAwayScore());
  }

  private static boolean doesFeedContainSupportedEvents(MatchDataFeed parsedFeed) {
    return parsedFeed.getFeedPlayerMatchEvents()
      .stream()
      .anyMatch(e -> !e.isUnknown() && e.getEventType() != DELETED_EVENT);
  }

  private static NewAndDeletedEvents updateCachedPlayerMatchEvents(MatchDataFeed parsedFeed,
                                                                   OngoingMatchData ongoingMatchData) {
    String fixtureId = parsedFeed.getFixture().getId().toString();
    Map<String, Set<PlayerMatchEventDTO>> cachedMatchPlayerEvents = ongoingMatchData.getEventIdToPlayerMatchEvents();

    List<Entry<String, List<EntityEventDTO>>> sortedEvents = parsedFeed.getFeedPlayerMatchEvents()
      .stream()
      .filter(event -> EVENTS_TO_PROCESS_FROM_MA18DP.contains(event.getEventType()))
      .collect(groupingBy(EntityEventDTO::getEventId)).entrySet()
      .stream()
      .sorted(comparing(entry -> entry.getValue().get(0).getTimestamp()))
      .toList();

    var newEvents = new ArrayList<PlayerMatchEventDTO>();
    var deletedEvents = new ArrayList<PlayerMatchEventDTO>();

    sortedEvents.forEach(entry -> {
      String eventId = entry.getKey();
      List<EntityEventDTO> events = entry.getValue();

      if (eventHasToBeRemoved(events)) {
        Set<PlayerMatchEventDTO> removedItems = cachedMatchPlayerEvents.remove(eventId);
        if (nonNull(removedItems)) {
          log.info(
            "deleted event with eventId={} received for fixtureId={}: events={} removed from cache: existing events: {}",
            eventId, fixtureId, removedItems, cachedMatchPlayerEvents);
          deletedEvents.addAll(removedItems);
        }

        Set<PlayerMatchEventDTO> relatedItemsToBeRemoved = cachedMatchPlayerEvents.values()
          .stream()
          .flatMap(Set::stream)
          .filter(playerMatchEventDTOS -> eventId.equals(playerMatchEventDTOS.getRelatedEventId()))
          .collect(toSet());

        if (!relatedItemsToBeRemoved.isEmpty()) {
          log.info("deleted event with eventId={} received for fixtureId={}: related events={} removed from cache",
            eventId, fixtureId, relatedItemsToBeRemoved);
          relatedItemsToBeRemoved.forEach(relatedItem -> cachedMatchPlayerEvents.remove(relatedItem.getEventId()));
          deletedEvents.addAll(relatedItemsToBeRemoved);
        }
      } else {
        var eventsToUpdate = events
          .stream()
          .filter(not(EntityEventDTO::isUnknown))
          .map(PlayerMatchEventDTO::fromMatchFeedEvent)
          .collect(toSet());
        log.info("event with eventId={} received for fixtureId={}: newEvents={}", eventId, fixtureId, eventsToUpdate);
        cachedMatchPlayerEvents.put(eventId, eventsToUpdate);
        newEvents.addAll(eventsToUpdate);
      }
    });
    return new NewAndDeletedEvents(newEvents, deletedEvents);
  }

  private static boolean eventHasToBeRemoved(List<EntityEventDTO> events) {
    return events.size() == 1 && (events.get(0).getEventType().equals(DELETED_EVENT) || events.get(0).isUnknown());
  }

}
