package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ratings.player.PlayerFinalRatingsProcessor;
import com.wsf.dataingestor.services.ratings.team.TeamFinalRatingsProcessor;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.PlayerRating;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.CANCELLED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.POSTPONED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.SUSPENDED;
import static java.lang.String.format;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class MatchSettlementProcessor {

  private final FixtureService fixtureService;
  private final PlayerFinalRatingsProcessor playerRatingProcessor;
  private final TeamFinalRatingsProcessor teamFinalRatingsProcessor;
  private final SettlementValidatorFactory settlementValidatorFactory;

  //TODO create a SettlementService.sendEmptySettlement, this is needed for the manual settlement and for the voidAll settlement in order to settle
  // match props with empty data
  public boolean processFeed(MatchDataFeed parsedFeed, boolean forceSettlement) {
    Fixture fixture = parsedFeed.getFixture();
    String fixtureId = fixture.getId().toString();
    FeedFixtureStatus fixtureStatus = parsedFeed.getFixtureStatus();

    boolean isFinalSettlement = fixtureStatus == PLAYED && parsedFeed.isFinalData();
    boolean isFixtureCancelled = fixtureStatus == CANCELLED;
    boolean isFixturePostponed = fixtureStatus == POSTPONED;
    boolean isFixtureSuspended = fixtureStatus == SUSPENDED;

    if (isFinalSettlement || isFixtureSuspended || isFixtureCancelled || isFixturePostponed || forceSettlement) {
      log.info("sending final ratings for settlement for fixtureId={}", fixtureId);

      validateSettlement(parsedFeed, forceSettlement);

      List<PlayerRating> ratingsPlayersWhoPlayed = playerRatingProcessor.sendFinalRatingsPlayersWhoPlayed(parsedFeed,
        true, forceSettlement);
      playerRatingProcessor.sendFinalRatingsPlayerWhoDidNotPlay(parsedFeed, ratingsPlayersWhoPlayed);

      teamFinalRatingsProcessor.sendFinalRatingsTeams(parsedFeed, true, forceSettlement);

      fixtureService.storeFixtureProcessStatusSettled(fixture);

      log.info("settlement for fixtureId={} processed", fixtureId);

      return true;
    } else if (!fixtureStatus.isFinished()) {
      throw new IllegalStateException(
        format("not settling fixtureId=%s that is in status %s and isFinalData %s", fixtureId, fixtureStatus,
          parsedFeed.isFinalData()));
    } else if (!parsedFeed.isFinalData()) {
      log.warn("not settling fixtureId={}, data is not final", fixtureId);
      return false;
    }
    return false;
  }

  private void validateSettlement(MatchDataFeed parsedFeed, boolean forceSettlement) {
    if (forceSettlement) {
      return;
    }

    var settlementValidator = settlementValidatorFactory.getSettlementValidator(parsedFeed.getProvider());
    if (nonNull(settlementValidator)) {
      settlementValidator.validate(parsedFeed);
    }
  }
}
