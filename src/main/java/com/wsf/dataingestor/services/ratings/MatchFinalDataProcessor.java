package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ratings.player.PlayerFinalRatingsProcessor;
import com.wsf.dataingestor.services.ratings.team.TeamFinalRatingsProcessor;

@Slf4j
@Service
@RequiredArgsConstructor
public class MatchFinalDataProcessor {

  private final PlayerFinalRatingsProcessor playerRatingsProcessor;
  private final TeamFinalRatingsProcessor teamFinalRatingsProcessor;

  public void processFeed(MatchDataFeed parsedFeed) {
    String fixtureId = parsedFeed.getFixture().getId().toString();
    log.info("fixtureId={} finished, processing final ratings", fixtureId);
    playerRatingsProcessor.sendFinalRatingsPlayersWhoPlayed(parsedFeed, false, false);
    teamFinalRatingsProcessor.sendFinalRatingsTeams(parsedFeed, false, false);
  }
}
