package com.wsf.dataingestor.services.ratings.player;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.metrics.TagHelper;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.publishers.RatingsPublisher;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.RATINGS_FEED_BUILD_AND_PUBLISH_LATENCY;
import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.RATINGS_FEED_RATING_FROM_RECEIVED_LATENCY;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.ThreadPoolService.getPlayersUpdatesKey;
import static com.wsf.dataingestor.services.ratings.Utils.updateEventsInCache;
import static com.wsf.dataingestor.services.stats.StatsUtils.isBetStop;
import static java.time.Duration.between;
import static java.time.Instant.now;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlayerLiveRatingsProcessor {

  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final PlayerStatsEnricher playerStatsEnricher;
  private final PlayerService playerService;
  private final RatingsPublisher ratingsPublisher;
  private final PlayerLiveRatingFilter playerLiveRatingFilter;
  private final Utils utils;
  private final ThreadPoolService threadPoolService;
  private final MetricsManager metricsManager;

  //  snapshot pull feeds (ma2) only update the cache
  public void processFeed(MatchDataFeed parsedFeed, OngoingMatchData ongoingMatchData, boolean onlyUpdateCache) {
    Fixture fixture = parsedFeed.getFixture();
    String fixtureId = fixture.getId().toString();

    if (parsedFeed.isPreMatch()) {
      log.info("fixtureId={} has not started yet", fixtureId);
      return;
    }

    List<PlayerDataDTO> playersData = parsedFeed.isSnapshot() ?
                                      // only for ma18dp snapshots
                                      allPlayersPlaying(fixture, parsedFeed.getPlayersData(), ongoingMatchData,
                                        parsedFeed.getFeedId()) : parsedFeed.getPlayersData();

    var playerTasks = playersData
      .stream()
      .filter(not(PlayerDataDTO::isUnknown))
      .map(playerData -> {
        CompletableFuture<Void> playerTask = new CompletableFuture<>();
        String key = getPlayersUpdatesKey(fixture, playerData.getEntityId());

        threadPoolService.processPlayersUpdateInThreadPool(key, () -> {
          try {
            if (onlyUpdateCache) {
              updatePlayerCache(fixture, ongoingMatchData, playerData);
            } else {
              updateCacheAndPublish(parsedFeed, ongoingMatchData, playerData);
            }
          } catch (Exception e) {
            utils.handleGenericError(parsedFeed.getFeedId(), e);
          } finally {
            playerTask.complete(null);
          }
        });
        return playerTask;
      })
      .toList();

    playerTasks.forEach(CompletableFuture::join);
  }

  private void updatePlayerCache(Fixture fixture, OngoingMatchData cachedMatchData, PlayerDataDTO playerData) {
    Player player = playerData.getPlayer();
    String playerId = player.getIdAsString();
    String fixtureId = fixture.getIdAsString();
    PlayerMatchData playerCachedData = playerMatchDataCache.get(fixtureId, playerId);

    if (!isPlayerInCurrentLineup(cachedMatchData, playerId)) {
      log.info("playerId={} is out for fixtureId={}, stop processing", playerId, fixtureId);
      return;
    }

    Map<String, Number> computedStats = playerStatsEnricher.enrichLiveStats(fixture, playerData, playerCachedData,
      List.of(), cachedMatchData);

    playerCachedData.setStats(computedStats);
    playerCachedData.setMatchPosition(playerData.getMatchPosition());

    updatePlayerCache(fixtureId, playerCachedData);
  }

  private void updateCacheAndPublish(MatchDataFeed parsedFeed, OngoingMatchData cachedMatchData,
                                     PlayerDataDTO playerData) {
    var start = Instant.now();
    Player player = playerData.getPlayer();
    String playerId = player.getIdAsString();
    Fixture fixture = parsedFeed.getFixture();
    String fixtureId = fixture.getIdAsString();
    String eventId = playerData.getEventId();

    PlayerMatchData playerCachedData = playerMatchDataCache.get(fixtureId, playerId);
    if (isBetStop(playerCachedData) && parsedFeed.isSnapshot()) {
      return;
    }

    if (!playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, cachedMatchData, playerId)) {
      log.info("playerId={} is not on the pitch for fixtureId={}, stop processing", playerId, fixtureId);
      return;
    }

    List<EntityEventDTO> playerFeedEvents = getEventsForPlayer(parsedFeed, playerId);

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixture, playerData, playerCachedData,
      playerFeedEvents, cachedMatchData);
    boolean didStatsChange = !updatedStats.equals(playerCachedData.getStats());
    updatePlayerCachedDataStats(playerCachedData, updatedStats, playerData.getMatchPosition());

    boolean hasLineUpChangeEventHappened = playerLiveRatingFilter.hasLineUpChangeEventHappened(parsedFeed, player);

    boolean shouldPublishRatings = didStatsChange || hasLineUpChangeEventHappened;

    if (shouldPublishRatings) {
      boolean isPlayerInCurrentLineup = isPlayerInCurrentLineup(cachedMatchData, playerId);
      boolean isFinal = !playerData.isPlaying() || !isPlayerInCurrentLineup;

      var sportType = fixture.getTournament().getCompetition().getSport().getType();
      var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_PLAYER);
      var metadata = metadataRetriever.getMetadata(playerId, cachedMatchData.getAllPlayerEvents(),
        new MetadataRetrievalFlags(isFinal, false));

      buildAndPublishRating(parsedFeed, cachedMatchData, playerCachedData, player, eventId, isFinal, metadata);
      updateEventsInCache(playerCachedData, playerFeedEvents);
      updatePlayerCache(fixtureId, playerCachedData);
      updateMetrics(parsedFeed.getReceivedTs(), start);
    } else {
      log.info("stats for playerId={} in fixtureId={} have not changed, not publishing", playerId, fixtureId);
    }
  }

  private void updatePlayerCachedDataStats(PlayerMatchData playerCachedData, Map<String, Number> updatedStats,
                                           Player.DetailedPosition matchPosition) {
    playerCachedData.setStats(updatedStats);
    playerCachedData.setMatchPosition(matchPosition);
  }

  private void updatePlayerCache(String fixtureId, PlayerMatchData cachedData) {
    var start = now();
    String playerId = cachedData.getPlayerId();
    cachedData.setTimestamp(now().toEpochMilli());
    playerMatchDataCache.set(fixtureId, playerId, cachedData);
    metricsManager.PLAYER_MATCH_CACHE_WRITE_LATENCY.record(between(start, now()));
  }

  // TODO can this be simplified?
  private List<PlayerDataDTO> allPlayersPlaying(Fixture fixture, List<PlayerDataDTO> playersData,
                                                OngoingMatchData ongoingMatchData, String feedId) {
    Set<String> lineupPlayerIds = ongoingMatchData.getLineupPlayerIds();

    log.info("feedId={} for fixtureId={} is a snapshot, sending ratings for all the players on the pitch: {}", feedId,
      fixture.getId().toString(), lineupPlayerIds);

    Map<String, List<PlayerDataDTO>> playerIdToData = playersData
      .stream()
      .collect(groupingBy(player -> player.getPlayer().getId().toString()));

    String fixtureId = fixture.getId().toString();
    String competitionId = fixture.getTournament().getCompetitionId();
    return lineupPlayerIds
      .stream()
      .map(playerId -> {
        return ofNullable(playerIdToData.get(playerId))
          .map(l -> l.get(0))
          .orElseGet(() -> getPlayerDTOFromCache(fixtureId, competitionId, playerId));
      })
      .collect(toList());
  }

  private PlayerRating buildPlayerRating(MatchDataFeed matchDataFeed, OngoingMatchData cachedMatchData, Player player,
                                         PlayerMatchData cachedData, boolean isFinal, String eventId) {
    boolean isRatingFinal = isFinal || !matchDataFeed.isInProgress();
    int periodId = cachedMatchData.getMatchPeriod().getPeriodId();
    int matchTime = cachedMatchData.getMatchTime();
    return utils.buildPlayerRating(matchDataFeed.getFixture(), player, isRatingFinal, cachedData.getStats(), periodId,
      matchTime, eventId, matchDataFeed.getFeedId(), matchDataFeed.getReceivedTs());
  }

  private void buildAndPublishRating(MatchDataFeed parsedFeed, OngoingMatchData cachedMatchData,
                                     PlayerMatchData cachedData, Player player, String eventId, boolean isFinal,
                                     KafkaMetadata metadata) {
    var start = now();
    var playerId = player.getIdAsString();
    var fixtureId = cachedData.getFixtureId();

    PlayerRating rating = buildPlayerRating(parsedFeed, cachedMatchData, player, cachedData, isFinal, eventId);
    ratingsPublisher.publishAndStore(rating, isFinal, metadata);

    log.info("data published and stored in db for playerId={} in fixtureId={} eventId={}", playerId, fixtureId,
      eventId);
    metricsManager.recordTimer(RATINGS_FEED_BUILD_AND_PUBLISH_LATENCY.value(), between(start, now()),
      List.of(TagHelper.Tag.of("type", "live")));
  }

  public static boolean isPlayerInCurrentLineup(OngoingMatchData ongoingMatchData, String playerId) {
    return ongoingMatchData.getLineupPlayerIds()
      .contains(playerId);
  }

  private PlayerDataDTO getPlayerDTOFromCache(String fixtureId, String competitionId, String playerId) {
    PlayerMatchData playerMatchData = playerMatchDataCache.get(fixtureId, playerId);
    Player player = playerService.getOrThrowById(competitionId, playerId);
    return PlayerDataDTO
      .builder().player(player).hasPlayed(true).isPlaying(true)
      .timestamp(Instant.ofEpochMilli(playerMatchData.getTimestamp())).stats(playerMatchData.getStats()).build();
  }

  private void updateMetrics(Instant receivedTs, Instant start) {
    metricsManager.RATINGS_FEED_RATING_PROCESSED.increment();
    metricsManager.recordTimer(RATINGS_FEED_RATING_FROM_RECEIVED_LATENCY.value(), between(receivedTs, now()),
      List.of(TagHelper.Tag.of("type", "live")));
    metricsManager.RATINGS_FEED_RATING_PROCESSING_LATENCY.record(between(start, now()));
  }

  private static List<EntityEventDTO> getEventsForPlayer(MatchDataFeed parsedFeed, String playerId) {
    return parsedFeed.getFeedPlayerMatchEvents()
      .stream()
      .filter(event -> event.wasMadeByPlayer(playerId))
      .collect(toList());
  }
}
