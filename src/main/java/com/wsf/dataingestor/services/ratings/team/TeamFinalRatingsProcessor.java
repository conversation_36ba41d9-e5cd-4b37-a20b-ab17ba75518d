package com.wsf.dataingestor.services.ratings.team;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.services.LiveTeamRatingService;
import com.wsf.dataingestor.services.TeamRatingService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.stats.StatsValidator;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;
import com.wsf.kafka.domain.metadata.KafkaMetadata;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater.toFixtureStatus;
import static com.wsf.dataingestor.services.ratings.player.PlayerFinalRatingsProcessor.BYPASS_VALIDATION_FIXTURES;
import static java.time.Instant.now;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamFinalRatingsProcessor {

  private final TeamStatsEnricher teamStatsEnricher;
  private final TeamRatingService teamRatingService;
  private final LiveTeamRatingService liveTeamRatingService;
  private final TeamMatchDataCacheService teamMatchDataCacheService;
  private final OngoingMatchDataCacheService ongoingMatchDataCacheService;
  private final KafkaService kafkaService;
  private final CompetitionConfigRepository competitionConfigRepository;
  @Qualifier("teamStatsValidator")
  private final StatsValidator statsValidator;

  public void sendFinalRatingsTeams(MatchDataFeed matchDataFeed, boolean settleOdds, boolean forceSettlement) {
    Fixture fixture = matchDataFeed.getFixture();

    Team homeTeam = fixture.getHomeTeam();
    Team awayTeam = fixture.getAwayTeam();

    Optional<TeamDataDTO> homeTeamDataOpt = retrieveTeamDataFromFeed(matchDataFeed, homeTeam.getIdAsString());
    Optional<TeamDataDTO> awayTeamDataOpt = retrieveTeamDataFromFeed(matchDataFeed, awayTeam.getIdAsString());

    var homeTeamRating = buildTeamRating(matchDataFeed, homeTeam, homeTeamDataOpt, settleOdds);
    var awayTeamRating = buildTeamRating(matchDataFeed, awayTeam, awayTeamDataOpt, settleOdds);

    String fixtureId = matchDataFeed.getFixture().getIdAsString();
    boolean shouldSkipValidation = BYPASS_VALIDATION_FIXTURES.contains(fixtureId);
    if (!forceSettlement && settleOdds && !shouldSkipValidation) {
      CompetitionConfig competitionConfig = competitionConfigRepository.findByCompetitionId(
        fixture.getTournament().getCompetitionId());
      statsValidator.validateStats(List.of(homeTeamRating.getStats(), awayTeamRating.getStats()), competitionConfig);
    }

    Fixture.FixtureStatus fixtureStatus = toFixtureStatus(matchDataFeed.getFixtureStatus(), matchDataFeed.getDate());

    var sportType = fixture.getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_TEAM);

    var homeTeamMetadata = metadataRetriever.getMetadata(homeTeam.getIdAsString(),
      matchDataFeed.getFeedPlayerMatchEvents(), new MetadataRetrievalFlags(true, matchDataFeed.isManualSettlement()));
    storeAndPublishRating(homeTeamRating, fixtureStatus, homeTeamDataOpt.isPresent(), settleOdds, homeTeamMetadata);

    var awayTeamMetadata = metadataRetriever.getMetadata(awayTeam.getIdAsString(),
      matchDataFeed.getFeedPlayerMatchEvents(), new MetadataRetrievalFlags(true, matchDataFeed.isManualSettlement()));
    storeAndPublishRating(awayTeamRating, fixtureStatus, awayTeamDataOpt.isPresent(), settleOdds, awayTeamMetadata);
  }

  private TeamRating buildTeamRating(MatchDataFeed matchDataFeed, Team team, Optional<TeamDataDTO> teamDataOpt,
                                     boolean settleOdds) {
    String teamId = team.getIdAsString();
    Fixture fixture = matchDataFeed.getFixture();
    String fixtureId = fixture.getIdAsString();

    if (teamDataOpt.isPresent()) {
      TeamDataDTO teamData = teamDataOpt.get();

      Map<String, Number> stats = getStats(matchDataFeed, teamData, settleOdds);

      Integer matchTime = matchDataFeed.getMatchTimeMin();
      Integer periodId = ofNullable(matchDataFeed.getMatchPeriod())
        .map(MatchPeriod::getPeriodId)
        .orElse(null);
      return Utils.buildTeamRating(fixture, teamData.getTeam(), stats, periodId, matchTime, true,
        matchDataFeed.getFeedId(), teamData.getEventId(), matchDataFeed.getReceivedTs());
    } else {
      // in case of postponed matches
      log.info("building empty final stats for teamId={} fixtureId={} feedId={}", teamId, fixtureId,
        matchDataFeed.getFeedId());
      return buildEmptyTeamRating(fixture, team, matchDataFeed.getFeedId());
    }
  }

  private Map<String, Number> getStats(MatchDataFeed matchDataFeed, TeamDataDTO teamData, boolean settleOdds) {
    if (settleOdds) {
      return teamStatsEnricher.enrichFinalStats(matchDataFeed, teamData);
    } else {
      var fixture = matchDataFeed.getFixture();
      String fixtureId = fixture.getIdAsString();
      var ongoingMatchData = ongoingMatchDataCacheService.get(fixtureId);
      var teamCachedData = teamMatchDataCacheService.get(fixtureId, teamData.getTeamId());
      return teamStatsEnricher.enrichLiveStats(fixture, teamData, teamCachedData, ongoingMatchData);
    }
  }

  private void storeAndPublishRating(TeamRating homeTeamRating, Fixture.FixtureStatus fixtureStatus,
                                     boolean hasToBeStored, boolean settleOdds, KafkaMetadata metadata) {
    log.info("sending final stats for teamId={} fixtureId={} eventId={} settleOdds={}",
      homeTeamRating.getTeam().getIdAsString(), homeTeamRating.getFixture().getIdAsString(),
      homeTeamRating.getEventId(), settleOdds);
    if (hasToBeStored) {
      teamRatingService.storeRating(homeTeamRating);
      liveTeamRatingService.storeRating(homeTeamRating, true);
    }
    kafkaService.sendFinalTeamRating(homeTeamRating, fixtureStatus, settleOdds, metadata);
  }

  private static TeamRating buildEmptyTeamRating(Fixture fixture, Team team, String feedId) {
    return TeamRating
      .builder().fixture(fixture).team(team).stats(emptyMap()).isFinal(true).feedId(feedId).fixtureTimeMin(0)
      .timestamp(now()).build();
  }

  private static Optional<TeamDataDTO> retrieveTeamDataFromFeed(MatchDataFeed matchDataFeed, String teamId) {
    return matchDataFeed.getTeamsData()
      .stream()
      .filter(t -> t.getTeamId().equals(teamId))
      .findFirst();
  }
}
