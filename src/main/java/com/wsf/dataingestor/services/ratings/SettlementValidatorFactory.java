package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;

import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class SettlementValidatorFactory {

  public SettlementValidator getSettlementValidator(MatchDataFeed.FeedProvider feedProvider) {
    return ofNullable(feedProvider)
      .map(provider -> switch (feedProvider) {
        case SPORTMONKS -> new SportmonksSettlementValidator();
        case RUNNINGBALL, OPTA, WSF -> null;
      })
      .orElse(null);
  }
}
