package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;

import static com.wsf.domain.common.Contestant.ContestantType.SOCCER_TEAM;
import static java.util.UUID.randomUUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PreFixtureDataProcessor {

  final PlayerService playerService;
  final KafkaService kafkaService;

  public void sendPreFixture(Fixture fixture) {
    log.info("sending pre-fixture for fixtureId={}", fixture.getId().toString());

    sendMatchEvent(fixture);

    fixture.getContestants(Optional.of(SOCCER_TEAM))
      .forEach(team -> processTeamInFixture(fixture, team));
  }

  private void processTeamInFixture(Fixture fixture, Fixture.FixtureContestant team) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getId().toString();
    String teamId = team.getContestant().getIdAsString();

    sendTeamEvent(fixture, team.getContestant());

    List<Player> teamPlayers = playerService.getActivePlayersByTeamIdAndTournamentId(competitionId, teamId,
      tournamentId);
    teamPlayers.forEach(player -> sendPlayerEvent(fixture, player));
  }

  private void sendPlayerEvent(Fixture fixture, Player player) {
    String eventId = randomUUID().toString();
    log.info("pre-fixture: playerId={} is involved in fixtureId={} eventId={}. sending to kafka.",
      player.getIdAsString(), fixture.getIdAsString(), eventId);

    kafkaService.sendPlayerPrematchEvent(player, fixture, eventId);
  }

  private void sendTeamEvent(Fixture fixture, Team team) {
    String eventId = randomUUID().toString();
    log.info("pre-fixture: teamId={} is involved in fixtureId={} eventId={}. sending to kafka.", team.getIdAsString(),
      fixture.getIdAsString(), eventId);

    kafkaService.sendTeamPrematchEvent(team, fixture, eventId);
  }

  private void sendMatchEvent(Fixture fixture) {
    String eventId = randomUUID().toString();
    log.info("pre-fixture: fixtureId={} eventId={}. sending to kafka.", fixture.getIdAsString(), eventId);

    kafkaService.sendMatchPrematchEvent(fixture, eventId);
  }

}
