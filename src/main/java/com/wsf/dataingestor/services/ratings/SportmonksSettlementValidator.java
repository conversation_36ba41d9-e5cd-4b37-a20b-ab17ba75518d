package com.wsf.dataingestor.services.ratings;

import lombok.extern.slf4j.Slf4j;

import com.wsf.dataingestor.models.MatchDataFeed;

import static java.lang.String.format;

@Slf4j
public final class SportmonksSettlementValidator implements SettlementValidator {

  @Override
  public void validate(MatchDataFeed matchDataFeed) {
    var fixtureId = matchDataFeed.getFixture().getId().toString();

    if (matchDataFeed.getFixture().getCanGoExtraTime()) {
      throw new IllegalStateException(
        format("not settling sportmonks fixtureId=%s because extra time possibility", fixtureId));
    }

    if (matchDataFeed.isExtraTimeHappened()) {
      throw new IllegalStateException(
        format("not settling sportmonks fixtureId=%s because extra time happened", fixtureId));
    }
  }
}
