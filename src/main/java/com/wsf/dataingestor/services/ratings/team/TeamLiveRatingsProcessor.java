package com.wsf.dataingestor.services.ratings.team;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.publishers.RatingsPublisher;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;

import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.ratings.Utils.buildTeamRating;
import static com.wsf.dataingestor.services.stats.StatsUtils.isBetStop;
import static java.time.Instant.now;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamLiveRatingsProcessor {

  private final TeamMatchDataCacheService teamMatchDataCache;
  private final TeamService teamService;
  private final TeamStatsEnricher teamStatsEnricher;
  private final RatingsPublisher ratingsPublisher;
  private final Utils utils;
  private final MetricsManager metricsManager;

  public void processFeed(MatchDataFeed matchDataFeed, OngoingMatchData ongoingMatchData, boolean onlyUpdateCache) {
    if (matchDataFeed.isPreMatch()) {
      return;
    }

    Fixture fixture = matchDataFeed.getFixture();

    List<TeamDataDTO> teamsData = matchDataFeed.isSnapshot() ?
                                  allTeamsForFixture(fixture, matchDataFeed.getTeamsData()) :
                                  matchDataFeed.getTeamsData();

    teamsData.forEach(teamData -> updateTeamDataAndPublish(matchDataFeed, ongoingMatchData, onlyUpdateCache, teamData));
  }

  private void updateTeamDataAndPublish(MatchDataFeed matchDataFeed, OngoingMatchData ongoingMatchData,
                                        boolean onlyUpdateCache, TeamDataDTO teamData) {
    try {
      var fixture = matchDataFeed.getFixture();
      var fixtureId = fixture.getIdAsString();
      String teamId = teamData.getTeamId();
      TeamMatchData cachedData = getTeamDataFromCache(fixtureId, teamId);

      if (isBetStop(cachedData) && matchDataFeed.isSnapshot()) {
        return;
      }

      Map<String, Number> cachedStats = cachedData.getStats();
      Map<String, Number> updatedStats = teamStatsEnricher.enrichLiveStats(fixture, teamData, cachedData,
        ongoingMatchData);
      cachedData.setStats(updatedStats);

      if (onlyUpdateCache) {
        updateTeamCache(teamId, fixtureId, cachedData);
        return;
      }

      boolean hasDataChanged = !updatedStats.equals(cachedStats);
      if (hasDataChanged) {
        boolean isFinal = !matchDataFeed.isInProgress();
        int periodId = ongoingMatchData.getMatchPeriod().getPeriodId();
        int matchTime = ongoingMatchData.getMatchTime();

        TeamRating teamRating = buildTeamRating(matchDataFeed.getFixture(), teamData.getTeam(), updatedStats, periodId,
          matchTime, isFinal, matchDataFeed.getFeedId(), teamData.getEventId(), matchDataFeed.getReceivedTs());

        var sportType = fixture.getTournament().getCompetition().getSport().getType();
        var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_TEAM);
        var metadata = metadataRetriever.getMetadata(teamId, ongoingMatchData.getAllPlayerEvents(),
          new MetadataRetrievalFlags(isFinal, false));
        ratingsPublisher.publishAndStore(teamRating, isFinal, metadata);
        metricsManager.RATINGS_FEED_RATING_PROCESSED.increment();
        updateTeamCache(teamId, fixtureId, cachedData);

        log.info("data published and stored in db for teamId={} in fixtureId={} eventId={}", teamId, fixtureId,
          teamData.getEventId());
      }
    } catch (Exception e) {
      utils.handleGenericError(matchDataFeed.getFeedId(), teamData, e);
    }
  }

  private List<TeamDataDTO> allTeamsForFixture(Fixture fixture, List<TeamDataDTO> teamsData) {
    String fixtureId = fixture.getId().toString();
    String competitionId = fixture.getTournament().getCompetitionId();
    return Stream
      .of(fixture.getHomeTeam(), fixture.getAwayTeam())
      .map(team -> {
        String teamId = team.getId().toString();
        return teamsData
          .stream()
          .filter(t -> t.getTeamId().equals(teamId))
          .findFirst()
          .orElseGet(() -> getTeamDTOFromCache(fixtureId, competitionId, teamId));
      })
      .collect(toList());
  }

  private TeamDataDTO getTeamDTOFromCache(String fixtureId, String competitionId, String teamId) {
    TeamMatchData teamMatchData = getTeamDataFromCache(fixtureId, teamId);
    Team team = teamService.getById(competitionId, teamId);
    return TeamDataDTO
      .builder().team(team)
      .timestamp(Instant.ofEpochMilli(teamMatchData.getTimestamp())).stats(teamMatchData.getStats()).build();
  }

  private TeamMatchData getTeamDataFromCache(String fixtureId, String teamId) {
    return teamMatchDataCache.get(fixtureId, teamId);
  }

  private void updateTeamCache(String teamId, String fixtureId, TeamMatchData cachedData) {
    cachedData.setTimestamp(now().toEpochMilli());
    teamMatchDataCache.set(fixtureId, teamId, cachedData);
  }
}
