package com.wsf.dataingestor.services.ratings;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityEventDTOFactory;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.parsers.ma18dp.ProviderPlayerKey;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.validation.PositiveMapValues;
import com.wsf.domain.common.BaseEntity;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedProvider.WSF;
import static com.wsf.dataingestor.models.MatchDataFeed.MANUAL_SETTLEMENT_FEED_ID;
import static java.time.Instant.now;
import static java.util.Objects.nonNull;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Service
@AllArgsConstructor
public class MatchDataFeedRequestMapper {
  private final FixtureService fixtureService;
  private final PlayerService playerService;

  public MatchDataFeed createMatchDataFeedForManualSettlement(String fixtureId, MatchStatsDto matchStatsDto) {
    Instant now = now();
    Fixture fixture = fixtureService.getFixture(fixtureId);
    MatchDataFeed.FeedFixtureStatus fixtureStatus = Utils.toFeedFixtureStatus(fixture.getStatus());
    String competitionId = fixture.getTournament().getCompetitionId();

    var playerIdToPlayer = playerService.getActivePlayersForFixture(fixture)
      .stream()
      .collect(toMap(BaseEntity::getIdAsString, identity()));

    var teamIdToTeam = Stream
      .of(fixture.getAwayTeam(), fixture.getHomeTeam())
      .collect(toMap(BaseEntity::getIdAsString, identity()));

    var playersDataDtos = matchStatsDto.getContestantStatsDtos()
      .stream()
      .filter(c -> SOCCER_PLAYER == c.getType())
      .map(playerStats -> {
        // FIXME: The teamId will be sent by the BO when tackling PROD-6559
        var providerPlayerKey = new ProviderPlayerKey(playerStats.getId(),
          "TODO-The BO must send it in ticket PROD-6559");

        return PlayerDataDTO.buildPlayerData(now(), playerStats.getStats(), playerIdToPlayer.get(playerStats.getId()),
          providerPlayerKey, null, false, playerStats.getHasPlayed(), MANUAL_SETTLEMENT_FEED_ID);
      })
      .collect(toList());

    var teamsDataDtos = matchStatsDto.getContestantStatsDtos()
      .stream()
      .filter(c -> SOCCER_TEAM == c.getType())
      .map(teamStatsDto -> TeamDataDTO
        .builder()
        .team(teamIdToTeam.get(teamStatsDto.getId()))
        .eventId(MANUAL_SETTLEMENT_FEED_ID)
        .stats(teamStatsDto.getStats())
        .build())
      .collect(toList());

    List<EntityEventDTO> playerEvents = matchStatsDto.getFixtureEvents()
      .stream()
      .map(event -> mapFixtureEventDtoToEntityEventDTO(event, competitionId))
      .toList();

    return MatchDataFeed
      .builder()
      .aggregatedPlayerMatchEvents(playerEvents)
      .supportedEventTypes(matchStatsDto.getSupportedEventTypes())
      .receivedTs(now)
      .latestUpdateTs(now)
      .feedId(MANUAL_SETTLEMENT_FEED_ID)
      .fixture(fixture)
      .fixtureStatus(fixtureStatus)
      .date(fixture.getDate())
      .playersData(playersDataDtos)
      .teamsData(teamsDataDtos)
      .matchTimeMin(90)
      .matchPeriod(MatchPeriod.END_MATCH)
      .isFinalData(true)
      .provider(WSF)
      .build();
  }

  private EntityEventDTO mapFixtureEventDtoToEntityEventDTO(FixtureEventDto event, String competitionId) {
    Player player = playerService.getOrThrowById(competitionId, event.getContestantId());
    SoccerMatchEvent eventType = SoccerMatchEvent.valueOf(event.getEventType());
    MatchPeriod matchPeriod = MatchPeriod.getMatchPeriod(event.getPeriod());
    String externalEntityId = null;
    String externalTeamId = null;

    // FIXME: The isUnknownEntity will be sent by the BO when tackling PROD-6559
    boolean isUnknownEntity = false;
    String eventId = nonNull(event.getRelatedEventId()) ? event.getRelatedEventId() : event.getId();

    return EntityEventDTOFactory.withRelatedEvent(eventId, event.getRelatedEventId(), event.getContestantId(),
      externalEntityId, player.getTeam().getIdAsString(), externalTeamId, isUnknownEntity, eventType, matchPeriod,
      event.getMatchTime(), false, false, false, now());
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class MatchStatsDto {
    @Valid
    List<ContestantStatsDto> contestantStatsDtos;
    List<FixtureEventDto> fixtureEvents;
    Set<SoccerMatchEvent> supportedEventTypes;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContestantStatsDto {
      @NotNull
      private String id;
      @NotNull
      private Boolean hasPlayed;
      @NotNull
      private ContestantType type;
      @NotNull
      @PositiveMapValues
      private Map<String, Number> stats;
    }
  }

  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class FixtureEventDto {
    @NotNull
    private String id;
    @NotNull
    private String eventType;
    @NotNull
    private String contestantId;
    private int matchTime;
    private int period;
    private String relatedEventId;
  }

}
