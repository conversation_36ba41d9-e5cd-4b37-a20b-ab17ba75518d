package com.wsf.dataingestor.services.ratings;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.crons.SettlementUpdater;
import com.wsf.domain.common.Fixture;

@Slf4j
@Service
@RequiredArgsConstructor
public class MatchSettlementLogic {

  private final SettlementUpdater settlementUpdater;

  public void processPlayedFixtureSettlement(Fixture fixture) {
    log.info("fixtureId={} is played, settling", fixture.getIdAsString());
    settlementUpdater.scheduleSettlementJob(fixture);
  }
}