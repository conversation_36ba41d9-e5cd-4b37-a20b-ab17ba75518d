package com.wsf.dataingestor.services.ratings;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.events.LiveCoverageService;
import com.wsf.dataingestor.services.events.PlayerEventFactory;
import com.wsf.dataingestor.services.events.TeamEventFactory;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.PlayerLineUp;
import com.wsf.kafka.domain.TeamEvent;
import com.wsf.kafka.domain.TeamMatchStart;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.cache.CacheUtils.buildOngoingMatchInitialPlayerData;
import static com.wsf.dataingestor.cache.CacheUtils.buildOngoingMatchInitialTeamData;
import static com.wsf.dataingestor.kafka.KafkaService.buildFixtureMatch;
import static com.wsf.dataingestor.kafka.KafkaService.buildPlayer;
import static com.wsf.dataingestor.kafka.KafkaService.buildTeam;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.events.Utils.isRunningBallSupported;
import static java.time.Instant.now;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixtureStartDataProcessor {

  private final PlayerService playerService;
  private final KafkaService kafkaService;
  private final FeedsConfigService feedsConfigService;
  private final PlayerStatsEnricher playerStatsEnricher;
  private final TeamStatsEnricher teamStatsEnricher;
  private final OngoingMatchDataCacheService ongoingMatchDataCacheService;
  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final TeamMatchDataCacheService teamMatchDataCache;
  private final MetricsManager metricsRegistry;
  private final LiveCoverageService liveCoverageService;

  public void processFeed(Fixture fixture, OngoingMatchData ongoingMatchData) {
    String fixtureId = fixture.getId().toString();
    try {
      FixtureStartDataRetriever fixtureStartDataRetriever = feedsConfigService.getFixtureStartDataRetriever(
        fixture.getTournament().getCompetitionId());
      MatchDataFeed matchDataFeed = fixtureStartDataRetriever.retrieveFixtureStartData(fixture);
      processFeed(fixture, matchDataFeed, ongoingMatchData);
    } catch (Exception e) {
      Counter metric = metricsRegistry.RATINGS_FEED_PROCESSING_ERROR;
      log.error("Error while executing fixture start for fixtureId={} error={}", fixtureId, metric.getId().getName(),
        e);
      metric.increment();
    }
  }

  public void processFeed(Fixture fixture, MatchDataFeed matchDataFeed, OngoingMatchData ongoingMatchData) {
    boolean shouldCheckRunningBallConnection = isRunningBallSupported(fixture) && fixture.getIsLiveEnabled();
    boolean noRunningBallConnection =
      fixture.getIsRunningBallConnected() == null || !fixture.getIsRunningBallConnected();
    boolean cancelLiveCoverage = shouldCheckRunningBallConnection && noRunningBallConnection;

    Fixture updatedFixture = cancelLiveCoverage ? liveCoverageService.cancelLiveCoverageForFixture(fixture) : fixture;

    sendLineUps(updatedFixture, ongoingMatchData, matchDataFeed);
    sendTeamsMatchStart(updatedFixture, ongoingMatchData, matchDataFeed);
    updateLineUpsInCache(matchDataFeed, ongoingMatchData);
  }

  private void updateLineUpsInCache(MatchDataFeed matchDataFeed, OngoingMatchData ongoingMatchData) {
    if (nonNull(ongoingMatchData)) {
      Map<String, Set<String>> teamIdToLineUpPlayerIds = LineUpService.retrievePlayers(matchDataFeed)
        .stream()
        .collect(groupingBy(player -> player.getTeam().getIdAsString(), collectingAndThen(toSet(), players -> players
          .stream()
          .map(Player::getIdAsString)
          .collect(toSet()))));

      String fixtureId = matchDataFeed.getFixture().getIdAsString();
      if (teamIdToLineUpPlayerIds.isEmpty()) {
        log.info("skipping lineups update in cache for fixtureId={} because lineups are empty", fixtureId);
        return;
      }

      teamIdToLineUpPlayerIds.forEach((teamId, teamLineupPlayerIds) -> {
        ongoingMatchData.getTeamIdToLineUp().put(teamId, teamLineupPlayerIds);
        log.info("updating teamId={} lineup in cache in fixtureId={}: {}", teamId, fixtureId, teamLineupPlayerIds);
      });

      ongoingMatchDataCacheService.set(fixtureId, ongoingMatchData);
    }
  }

  private void sendTeamsMatchStart(Fixture fixture, OngoingMatchData ongoingMatchData, MatchDataFeed matchDataFeed) {
    String fixtureId = fixture.getId().toString();
    String feedId = matchDataFeed.getFeedId();
    String tournamentId = fixture.getTournament().getIdAsString();

    var teamIdToTeamData = matchDataFeed.getTeamsData()
      .stream()
      .collect(toMap(TeamDataDTO::getTeamId, identity()));

    Stream
      .of(fixture.getHomeTeam(), fixture.getAwayTeam())
      .forEach(team -> {
        String teamId = team.getIdAsString();
        String eventId = UUID.randomUUID().toString();

        log.info("sending match start for fixtureId={} teamId={} eventId={}", fixtureId, teamId, eventId);

        teamMatchDataCache.remove(fixtureId, teamId);

        TeamMatchData teamCachedData = buildOngoingMatchInitialTeamData(Instant.now().toEpochMilli(), teamId,
          fixtureId);

        TeamDataDTO teamData = teamIdToTeamData.getOrDefault(teamId, TeamDataDTO
          .builder().team(team).eventId(eventId).build());
        Map<String, Number> computedStats = teamStatsEnricher.enrichLiveStats(fixture, teamData, teamCachedData,
          ongoingMatchData);

        MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_TEAM);
        MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
        List<OngoingMatchData.PlayerMatchEventDTO> allPlayerEvents = ofNullable(ongoingMatchData)
          .map(OngoingMatchData::getAllPlayerEvents)
          .orElse(List.of());
        KafkaMetadata metadata = metadataRetriever.getMetadata(team.getIdAsString(), allPlayerEvents, retrievalFlags);

        TeamMatchStart teamMatchStart = new TeamMatchStart(buildTeam(team, tournamentId), buildFixtureMatch(fixture),
          metadata, computedStats, now());
        TeamEvent teamEvent = TeamEventFactory.fromTeamMatchStart(teamMatchStart, eventId, feedId);
        kafkaService.sendTeamEventRating(teamEvent);

        teamCachedData.setStats(computedStats);
        teamMatchDataCache.set(fixtureId, teamId, teamCachedData);
      });
  }

  private void sendLineUps(Fixture fixture, OngoingMatchData ongoingMatchData, MatchDataFeed matchDataFeed) {
    log.info("sending lineups for fixtureId={} feedId={}", fixture.getId().toString(), matchDataFeed.getFeedId());
    String fixtureId = fixture.getId().toString();
    String feedId = matchDataFeed.getFeedId();

    Map<String, PlayerDataDTO> playerIdToPlayerData = matchDataFeed.getPlayersData()
      .stream()
      .collect(toMap(PlayerDataDTO::getPlayerId, identity(), (first, second) -> first));

    List<Player> matchPlayers = playerService.getActivePlayersForFixture(fixture);
    matchPlayers
      .stream()
      .peek(player -> playerMatchDataCache.remove(fixtureId, player.getId().toString()))
      .forEach(player -> {
        String eventId = UUID.randomUUID().toString();

        log.info("LineUps: playerId={} is involved in fixtureId={} eventId={}. sending to kafka.",
          player.getId().toString(), fixtureId, eventId);

        String playerId = player.getId().toString();
        try {
          PlayerDataDTO playerData = playerIdToPlayerData.get(playerId);
          if (nonNull(playerData)) {
            PlayerMatchData playerMatchData = buildOngoingMatchInitialPlayerData(Instant.now().toEpochMilli(), playerId,
              fixtureId);
            Map<String, Number> computedStats = playerStatsEnricher.enrichLiveStats(fixture, playerData,
              playerMatchData, List.of(), ongoingMatchData);
            //FIXME cant the enrichLiveStats also save it instead of merging and returning them?
            playerMatchData.setStats(computedStats);

            sendPlayerLineUp(fixture, player, eventId, feedId, playerMatchData.getStats(), true);

            playerMatchDataCache.set(fixtureId, playerId, playerMatchData);
          } else {
            sendPlayerLineUp(fixture, player, eventId, feedId, new HashMap<>(), false);
          }
        } catch (Exception e) {
          log.error("Error while processing start fixture for playerId={} fixtureId={}", player.getId().toString(),
            fixtureId, e);
        }
      });
  }

  private void sendPlayerLineUp(Fixture fixture, Player player, String eventId, String feedId,
                                Map<String, Number> stats, boolean isPlaying) {

    MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_PLAYER);
    MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
    KafkaMetadata metadata = metadataRetriever.getMetadata(player.getIdAsString(), List.of(), retrievalFlags);

    PlayerLineUp playerLineUp = new PlayerLineUp(buildPlayer(player), buildFixtureMatch(fixture), isPlaying, stats,
      metadata, now());
    PlayerEvent playerEvent = PlayerEventFactory.fromPlayerLineUp(playerLineUp, eventId, feedId);

    kafkaService.sendPlayerEventRating(playerEvent);
  }
}
