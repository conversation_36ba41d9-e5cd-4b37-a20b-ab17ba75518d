package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import org.springframework.stereotype.Service;
import com.wsf.domain.common.Competition;
import com.wsf.repository.common.CompetitionRepository;

import static java.lang.String.format;

@Slf4j
@RequiredArgsConstructor
@Service
public class CompetitionService {

  private final CompetitionRepository competitionRepository;

  public Competition findByCompetitionId(String id) {
    return competitionRepository.findById(id)
      .orElseThrow(() -> new IllegalArgumentException(format("Competition with id %s could not be found", id)));
  }

  public Competition findByOptaCompetitionId(String optaCompetitionId) {
    return competitionRepository.findByOptaCompetitionId(optaCompetitionId)
      .orElseThrow(() -> new IllegalArgumentException(
        format("Competition with OptaCompetitionId %s could not be found", optaCompetitionId)));
  }

  public Competition findBySportmonksCompetitionId(String sportmonksCompetitionId) {
    return competitionRepository.findBySportmonksCompetitionId(sportmonksCompetitionId)
      .orElseThrow(() -> new IllegalArgumentException(
        format("Competition with smCompetitionId %s could not be found", sportmonksCompetitionId)));
  }

  public List<Competition> findActiveCompetitions() {
    return competitionRepository.findActiveCompetitions();
  }

}
