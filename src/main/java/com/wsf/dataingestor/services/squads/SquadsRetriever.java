package com.wsf.dataingestor.services.squads;

import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

public interface SquadsRetriever {
  SquadsFeed retrieveSquadsFeed(Tournament tournament);

  MasterPlayer findMasterPlayer(SquadPlayerDTO squadPlayerDTO);

  Team findTeamOrCreateUnmapped(String competitionId, SquadPlayerDTO.TeamDTO teamDTO);

  Player.PlayerBuilder<?, ?> enrichPlayer(Player.PlayerBuilder<?, ?> playerBuilder, String externalPlayerId);

  String getExternalPlayerFieldName();
}
