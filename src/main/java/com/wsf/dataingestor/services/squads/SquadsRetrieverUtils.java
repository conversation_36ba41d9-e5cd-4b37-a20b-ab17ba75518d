package com.wsf.dataingestor.services.squads;

import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.wsf.dataingestor.clients.PullAPIClient;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.models.SquadsFeed.FeedProvider;
import com.wsf.dataingestor.parsers.ParsersUtils;

@UtilityClass
public class SquadsRetrieverUtils {

  public static SquadsFeed retrieveSquadsFeed(PullAPIClient<SquadsFeed> pullAPIClient, Set<String> externalIds,
                                              FeedProvider feedProvider) {
    List<SquadsFeed> squadsFeedsList = externalIds
      .stream()
      .map(pullAPIClient::retrieveParsedFeed)
      .toList();

    List<String> feedIdsList = squadsFeedsList
      .stream()
      .map(SquadsFeed::getFeedId)
      .collect(Collectors.toList());

    String feedId = ParsersUtils.buildFeedId(feedIdsList);

    List<SquadPlayerDTO> squadPlayerDTO = squadsFeedsList
      .stream()
      .map(SquadsFeed::getSquadPlayers)
      .flatMap(Collection::stream)
      .collect(Collectors.toList());
    return SquadsFeed
      .builder().feedId(feedId).squadPlayers(squadPlayerDTO).provider(feedProvider).build();
  }
}
