package com.wsf.dataingestor.services.squads;

import lombok.extern.slf4j.Slf4j;

import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.apache.commons.lang3.tuple.Pair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.events.PlayerEventFactory;
import com.wsf.domain.common.BaseEntity;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Sport;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;
import com.wsf.kafka.domain.PlayerChangedTeam;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.kafka.KafkaService.buildPlayer;

import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.DELETED;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.NEW;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.TEAM_CHANGED;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Slf4j
public class SquadsIngestionService {

  private final PlayerService playerService;
  private final KafkaService kafkaService;
  private final MetricsManager metrics;
  private final ObjectMapper objectMapper;

  public SquadsIngestionService(PlayerService playerService, KafkaService kafkaService, MetricsManager metrics,
                                ObjectMapper objectMapper) {
    this.playerService = playerService;
    this.kafkaService = kafkaService;
    this.metrics = metrics;
    this.objectMapper = objectMapper;
  }

  public void processSquadsFeed(SquadsRetriever squadsRetriever, Tournament tournament, boolean forceDeletePlayers) {
    SquadsFeed squadsFeed = squadsRetriever.retrieveSquadsFeed(tournament);
    processSquadsFeed(squadsRetriever, tournament, squadsFeed, forceDeletePlayers);
  }

  private void processSquadsFeed(SquadsRetriever squadsRetriever, Tournament tournament, SquadsFeed squadsFeed,
                                 boolean forceDeletePlayers) {
    List<SquadPlayerDTO> squadPlayers = squadsFeed.getSquadPlayers();

    if (squadPlayers.isEmpty()) {
      log.warn(format("no players returned in feed %s", squadsFeed.getFeedId()));
      return;
    }

    Map<String, Team> teamIdToTeam = ingestAndReturnTeams(squadsRetriever, tournament, squadsFeed);
    List<Player> savedPlayers = ingestAndReturnPlayers(squadsRetriever, tournament, squadsFeed, teamIdToTeam);
    deletePlayers(tournament, squadsFeed, savedPlayers, forceDeletePlayers);
    clearCache(tournament.getCompetitionId(), teamIdToTeam.keySet());
  }

  private Map<String, Team> ingestAndReturnTeams(SquadsRetriever squadsRetriever, Tournament tournament,
                                                 SquadsFeed squadsFeed) {
    List<SquadPlayerDTO> squadPlayers = squadsFeed.getSquadPlayers();
    return squadPlayers
      .stream()
      .map(SquadPlayerDTO::getTeam)
      .distinct()
      .map(team -> Pair.of(team.getExternalTeamId(), retrieveTeam(squadsRetriever, tournament, team)))
      .filter(p -> nonNull(p.getValue()))
      .collect(toMap(Pair::getKey, Pair::getValue));
  }

  private List<Player> ingestAndReturnPlayers(SquadsRetriever squadsRetriever, Tournament tournament,
                                              SquadsFeed squadsFeed, Map<String, Team> teamIdToTeam) {
    List<SquadPlayerDTO> squadPlayers = squadsFeed.getSquadPlayers();

    return squadPlayers
      .stream()
      .map(player -> ingestPlayer(squadsRetriever, tournament, squadsFeed, player, teamIdToTeam))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private Player ingestPlayer(SquadsRetriever squadsRetriever, Tournament tournament, SquadsFeed squadsFeed,
                              SquadPlayerDTO feedPlayer, Map<String, Team> teamIdToTeam) {
    String feedId = squadsFeed.getFeedId();

    try {
      MasterPlayer masterPlayer = squadsRetriever.findMasterPlayer(feedPlayer);

      if (isNull(masterPlayer)) {
        log.info("no MasterPlayer found for player {}. An unmapped player was created, skipping it for now.",
          feedPlayer.getMatchName());
        return null;
      }

      if (!isPlayerSupported(tournament, masterPlayer.getPlayerMasterId())) {
        log.info("MasterPlayer with id={} not supported for tournamentId={}", masterPlayer.getId().toString(),
          tournament.getIdAsString());
        return null;
      }

      Player player = checkCompetitionPlayerExistsAndUpdate(squadsRetriever, tournament, feedPlayer, teamIdToTeam,
        feedId, masterPlayer);
      if (nonNull(player)) {
        log.info("Saved playerId={} player: {}", player.getIdAsString(), player);
        metrics.SQUADS_FEED_PLAYER_PROCESSED.increment();
        return player;
      } else {
        return null;
      }
    } catch (DateTimeParseException e) {
      log.error("Error while parsing birthdate for player: {}", feedPlayer);
      return null;
    } catch (Exception e) {
      log.error("Error while ingesting player: {}", feedPlayer, e);
      metrics.SQUADS_FEED_PLAYER_ERROR.increment();
      return null;
    }
  }

  public Player checkCompetitionPlayerExistsAndUpdate(SquadsRetriever squadsRetriever, Tournament tournament,
                                                      SquadPlayerDTO feedPlayer, Map<String, Team> teamIdToTeam,
                                                      String feedId, MasterPlayer masterPlayer) {

    Team team = teamIdToTeam.get(feedPlayer.getTeam().getExternalTeamId());
    if (team == null) {
      return null;
    }

    String competitionId = tournament.getCompetitionId();
    Optional<Player> tournamentPlayerOpt = playerService.getById(competitionId, masterPlayer.getId().toString());
    String tournamentId = tournament.getIdAsString();

    String eventId = UUID.randomUUID().toString();
    if (tournamentPlayerOpt.isPresent()) {
      Player existingPlayer = tournamentPlayerOpt.get();
      String playerId = existingPlayer.getIdAsString();
      log.info("Found playerId={}, playerName={} in tournamentId={}, updating it.", playerId, masterPlayer.getName(),
        tournamentId);
      Player newPlayer = updatePlayer(squadsRetriever, masterPlayer, existingPlayer, feedPlayer, team, tournament);
      boolean playerDidChangeTeam = !existingPlayer.getTeam().getIdAsString().equals(team.getIdAsString());
      boolean playerDidChangeActiveStatus = !existingPlayer.getIsActive() && newPlayer.getIsActive();
      if (playerDidChangeTeam) {
        log.info("Sending PlayerTransferEvent TEAM_CHANGED for playerId={}, tournamentId={} eventId={}", playerId,
          tournamentId, eventId);

        Sport.SportType sportType = tournament.getCompetition().getSport().getType();
        MetadataRetriever metadataRetriever = getMetadataRetriever(sportType, SOCCER_PLAYER);
        MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
        KafkaMetadata metadata = metadataRetriever.getMetadata(playerId, List.of(), retrievalFlags);

        PlayerChangedTeam playerChangedTeam = new PlayerChangedTeam(buildPlayer(existingPlayer), metadata, now());
        PlayerEvent playerEvent = PlayerEventFactory.fromPlayerChangedTeam(playerChangedTeam, eventId, feedId);
        kafkaService.sendPlayerTransferAndTeamChangeEvents(existingPlayer, eventId, feedId, TEAM_CHANGED, competitionId,
          playerEvent);

      } else if (playerDidChangeActiveStatus) {
        log.info("Sending PlayerTransferEvent NEW for inactive playerId={}, tournamentId={}", playerId, tournamentId);
        kafkaService.sendPlayerTransferEvent(eventId, competitionId, playerId, NEW, feedId);
      }
      return newPlayer;

    } else {
      log.info("Sending PlayerTransferEvent NEW for not found playerId={} playerName={}, tournamentId={}",
        masterPlayer.getId().toString(), masterPlayer.getName(), tournamentId);
      Player player = createPlayer(squadsRetriever, masterPlayer, feedPlayer, team, tournament);
      kafkaService.sendPlayerTransferEvent(eventId, competitionId, player.getIdAsString(), NEW, feedId);
      return player;
    }
  }

  private Team retrieveTeam(SquadsRetriever squadsRetriever, Tournament tournament, SquadPlayerDTO.TeamDTO teamDTO) {
    return squadsRetriever.findTeamOrCreateUnmapped(tournament.getCompetitionId(), teamDTO);
  }

  private static boolean isPlayerSupported(Tournament tournament, String masterId) {
    return isNull(tournament.getSupportedPlayers()) || tournament.getSupportedPlayers()
      .isEmpty() || tournament.getSupportedPlayers()
      .contains(masterId);
  }

  private void deletePlayers(Tournament tournament, SquadsFeed squadsFeed, List<Player> savedPlayers,
                             boolean forceDeletePlayers) {
    String tournamentId = tournament.getIdAsString();
    String competitionId = tournament.getCompetitionId();
    Map<String, Player> playerIdToTournamentPlayers = playerService
      .getAllPlayersByTournamentId(competitionId, tournamentId)
      .stream()
      .collect(toMap(BaseEntity::getIdAsString, identity()));

    Set<String> savedPlayersIds = savedPlayers
      .stream()
      .map(BaseEntity::getIdAsString)
      .collect(toSet());

    playerIdToTournamentPlayers.entrySet().removeIf(e -> savedPlayersIds.contains(e.getKey()));

    log.info("Players to delete: {}", convertPlayersToJson(playerIdToTournamentPlayers));

    Map<Team, Long> teamWithTooManyDeletedPlayers = teamsWithDeletionCountOver(playerIdToTournamentPlayers, 15);
    boolean atLeastOneTeamWithTooManyDeletedPlayers = !teamWithTooManyDeletedPlayers.isEmpty();

    boolean doesTournamentStartInLessThan10Days = now().plus(10, ChronoUnit.DAYS).isAfter(tournament.getStartDate());
    boolean isTournamentOngoing = now().isAfter(tournament.getStartDate()) && now().isBefore(tournament.getEndDate());
    boolean isTournamentStartingSoonOrOngoing = doesTournamentStartInLessThan10Days || isTournamentOngoing;
    if (!forceDeletePlayers && atLeastOneTeamWithTooManyDeletedPlayers && isTournamentStartingSoonOrOngoing) {
      teamWithTooManyDeletedPlayers.forEach((team, count) -> {
        log.error("{} players would be deleted for team={} competition={} with id={}", count, team.getName(),
          tournament.getCompetition(), competitionId);
      });

      metrics.SQUADS_FEED_MANY_TEAM_PLAYERS_DELETED_ERROR.increment();
      return;
    }

    String feedId = squadsFeed.getFeedId();
    playerIdToTournamentPlayers.values()
      .forEach(player -> {
        String eventId = UUID.randomUUID().toString();
        log.info("playerId={} set to inactive, sending DELETED kafka events, eventId={}", player.getIdAsString(),
          eventId);

        Sport.SportType sportType = tournament.getCompetition().getSport().getType();
        MetadataRetriever metadataRetriever = getMetadataRetriever(sportType, SOCCER_PLAYER);
        MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
        KafkaMetadata metadata = metadataRetriever.getMetadata(player.getIdAsString(), List.of(), retrievalFlags);

        PlayerChangedTeam playerChangedTeam = new PlayerChangedTeam(buildPlayer(player), metadata, now());
        PlayerEvent playerEvent = PlayerEventFactory.fromPlayerChangedTeam(playerChangedTeam, eventId, feedId);
        kafkaService.sendPlayerTransferAndTeamChangeEvents(player, eventId, feedId, DELETED, competitionId,
          playerEvent);

        playerService.disablePlayer(player);
        metrics.SQUADS_FEED_PLAYER_DELETED.increment();
      });
  }

  private String convertPlayersToJson(Map<String, Player> playerIdToTournamentPlayers) {
    String loggablePlayers;
    try {
      loggablePlayers = objectMapper.writeValueAsString(playerIdToTournamentPlayers.values()
        .stream()
        .toList());
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
    return loggablePlayers;
  }

  private static Map<Team, Long> teamsWithDeletionCountOver(Map<String, Player> playerIdToTournamentPlayers,
                                                            int limit) {
    Map<Team, Long> teamsToPlayersDeletionCount = teamsToPlayersDeletionCount(playerIdToTournamentPlayers);

    return teamsToPlayersDeletionCount.entrySet()
      .stream()
      .filter(e -> e.getValue() > limit)
      .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  private static Map<Team, Long> teamsToPlayersDeletionCount(Map<String, Player> playerIdToTournamentPlayers) {

    return playerIdToTournamentPlayers.values()
      .stream()
      .collect(groupingBy(Player::getTeam, counting()));
  }

  private Player createPlayer(SquadsRetriever squadsRetriever, MasterPlayer masterPlayer, SquadPlayerDTO squadPlayer,
                              Team team, Tournament tournament) {

    updateMasterPlayer(squadPlayer, masterPlayer);
    playerService.updateMasterPlayer(masterPlayer);

    Player.PlayerBuilder<?, ?> playerBuilder = Player
      .builder()
      .id(masterPlayer.getId())
      .firstName(masterPlayer.getFirstName())
      .name(masterPlayer.getName())
      .matchName(masterPlayer.getMatchName())
      .dateOfBirth(masterPlayer.getDateOfBirth())
      .playerMasterId(masterPlayer.getPlayerMasterId())
      .position(squadPlayer.getPosition())
      .detailedPosition(squadPlayer.getDetailedPosition())
      .team(team)
      .tournament(tournament)
      .wyscoutPlayerId(masterPlayer.getWyscoutPlayerId())
      .betradarPlayerId(masterPlayer.getBetradarPlayerId())
      .toddId(masterPlayer.getToddId())
      .isActive(true);
    Player playerToSave = squadsRetriever.enrichPlayer(playerBuilder, squadPlayer.getPlayerId()).build();
    return playerService.savePlayer(playerToSave);
  }

  private Player updatePlayer(SquadsRetriever squadsRetriever, MasterPlayer masterPlayer, Player tournamentPlayer,
                              SquadPlayerDTO squadPlayer, Team team, Tournament tournament) {
    updateMasterPlayer(squadPlayer, masterPlayer);
    playerService.updateMasterPlayer(masterPlayer);

    var fieldsToUpdateMap = new HashMap<String, Object>();
    fieldsToUpdateMap.put("firstName", masterPlayer.getFirstName());
    fieldsToUpdateMap.put("name", masterPlayer.getName());
    fieldsToUpdateMap.put("matchName", masterPlayer.getMatchName());
    fieldsToUpdateMap.put("dateOfBirth", masterPlayer.getDateOfBirth());
    fieldsToUpdateMap.put("position", squadPlayer.getPosition());
    if (nonNull(squadPlayer.getDetailedPosition())) {
      fieldsToUpdateMap.put("detailedPosition", squadPlayer.getDetailedPosition());
    }
    fieldsToUpdateMap.put("team", team);
    fieldsToUpdateMap.put("tournament", tournament);
    fieldsToUpdateMap.put("isActive", true);
    fieldsToUpdateMap.put("wyscoutPlayerId", masterPlayer.getWyscoutPlayerId());
    fieldsToUpdateMap.put("betradarPlayerId", masterPlayer.getBetradarPlayerId());
    fieldsToUpdateMap.put("toddId", masterPlayer.getToddId());
    fieldsToUpdateMap.put(squadsRetriever.getExternalPlayerFieldName(), squadPlayer.getPlayerId());
    return playerService.update(tournamentPlayer, fieldsToUpdateMap);
  }

  private void clearCache(String competitionId, Set<String> teamIds) {
    teamIds.forEach(teamId -> playerService.evictActivePlayersByCompetitionIdAndTeamId(competitionId, teamId));
  }

  private static void updateMasterPlayer(SquadPlayerDTO squadPlayer, MasterPlayer masterPlayer) {
    masterPlayer.setFirstName(squadPlayer.getFirstName());
    masterPlayer.setName(squadPlayer.getLastName());
    masterPlayer.setMatchName(squadPlayer.getMatchName());
    masterPlayer.setDateOfBirth(squadPlayer.getBirthDate());
    masterPlayer.setIsActive(true);
  }

}
