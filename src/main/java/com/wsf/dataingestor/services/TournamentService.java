package com.wsf.dataingestor.services;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Service;
import com.wsf.domain.common.Tournament;
import com.wsf.repository.common.TournamentRepository;


@Slf4j
@RequiredArgsConstructor
@Service
public class TournamentService {

  private final TournamentRepository tournamentRepository;

  public Tournament findByIsActiveTrue(String competitionId) {
    return tournamentRepository.findActiveByCompetitionId(competitionId)
      .orElse(null);
  }

  public Tournament findByTournamentId(String tournamentId) {
    return tournamentRepository.findById(tournamentId)
      .orElseThrow();
  }

  public Optional<Tournament> findByOptaExternalId(String optaCalendarId) {
    return tournamentRepository.findByOptaExternalId(optaCalendarId);
  }

  public Optional<Tournament> findBySportmonksExternalId(String externalId) {
    return tournamentRepository.findBySportmonksExternalId(externalId);
  }

  public List<Tournament> findByCompetitionIdAndYear(String competitionId, String firstYear) {
    return tournamentRepository.findByCompetitionIdAndYearRegex(competitionId, firstYear);
  }

  public String buildExternalProviderIdFieldName(String providerIdFieldName) {
    return tournamentRepository.buildExternalProviderIdFieldName(providerIdFieldName);
  }

  public void updateToInactive(String tournamentId) {
    tournamentRepository.updateToInactive(tournamentId);
  }

  public void update(String tournamentId, Map<String, Object> keyToValueUpdates) {
    tournamentRepository.update(tournamentId, keyToValueUpdates);
  }

  public void save(Tournament tournament) {
    tournamentRepository.save(tournament);
  }
}
