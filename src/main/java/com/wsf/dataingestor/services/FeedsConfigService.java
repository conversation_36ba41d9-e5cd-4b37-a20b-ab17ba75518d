package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.opta.services.OptaCurrentTournamentRetriever;
import com.wsf.dataingestor.opta.services.OptaFixtureDataProcessor;
import com.wsf.dataingestor.opta.services.OptaFixtureStartDataRetriever;
import com.wsf.dataingestor.opta.services.OptaFixturesRetriever;
import com.wsf.dataingestor.opta.services.OptaSettlementDataRetriever;
import com.wsf.dataingestor.opta.services.OptaSquadsRetriever;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.services.ratings.FixtureStartDataRetriever;
import com.wsf.dataingestor.services.ratings.SettlementDataRetriever;
import com.wsf.dataingestor.services.squads.SquadsRetriever;
import com.wsf.dataingestor.services.tournaments.CurrentTournamentRetriever;
import com.wsf.dataingestor.sportmonks.services.SportmonksCurrentTournamentRetriever;
import com.wsf.dataingestor.sportmonks.services.SportmonksFixtureDataProcessor;
import com.wsf.dataingestor.sportmonks.services.SportmonksFixtureRetriever;
import com.wsf.dataingestor.sportmonks.services.SportmonksFixtureStartDataRetriever;
import com.wsf.dataingestor.sportmonks.services.SportmonksSettlementDataRetriever;
import com.wsf.dataingestor.sportmonks.services.SportmonksSquadsRetriever;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.CompetitionConfig.CompetitionMarketConfig;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Tournament;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.google.common.collect.Maps.newConcurrentMap;
import static java.lang.String.format;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@RequiredArgsConstructor
public class FeedsConfigService {

  private final ApplicationContext applicationContext;
  private final CompetitionConfigRepository competitionConfigRepository;

  private final Map<String, CompetitionConfig> competitionIdToConfig = newConcurrentMap();

  public CurrentTournamentRetriever getCurrentTournamentRetriever(String competitionId) {
    CompetitionConfig.Provider fixturesProvider = getFeedsConfig(competitionId).getFixturesProvider();
    return switch (fixturesProvider) {
      case OPTA -> applicationContext.getBean(OptaCurrentTournamentRetriever.class);
      case SPORTMONKS -> applicationContext.getBean(SportmonksCurrentTournamentRetriever.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for current tournament ingestion", fixturesProvider));
    };
  }

  public Set<String> getCompetitionExternalIds(Competition competition) {
    CompetitionConfig.Provider fixturesProvider = getFeedsConfig(competition.getIdAsString()).getFixturesProvider();

    return switch (fixturesProvider) {
      case OPTA -> competition.getExternalIds().getOptaIds();
      case SPORTMONKS -> competition.getExternalIds().getSportmonksIds();
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for current competition=%s", fixturesProvider, competition));
    };
  }

  public Set<String> getTournamentExternalIds(Tournament tournament) {
    CompetitionConfig.Provider fixturesProvider = getFeedsConfig(tournament.getCompetitionId()).getFixturesProvider();

    return switch (fixturesProvider) {
      case OPTA -> tournament.getExternalIds().getOptaIds();
      case SPORTMONKS -> tournament.getExternalIds().getSportmonksIds();
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for current tournament=%s", fixturesProvider, tournament));
    };
  }

  public FixturesRetriever getFixturesRetriever(String competitionId) {
    CompetitionConfig.Provider fixturesProvider = getFeedsConfig(competitionId).getFixturesProvider();
    return switch (fixturesProvider) {
      case SPORTMONKS -> applicationContext.getBean(SportmonksFixtureRetriever.class);
      case OPTA -> applicationContext.getBean(OptaFixturesRetriever.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for fixtures ingestion", fixturesProvider));
    };
  }

  public SquadsRetriever getSquadsRetriever(String competitionId) {
    CompetitionConfig.Provider playersProvider = getFeedsConfig(competitionId).getPlayersProvider();
    return switch (playersProvider) {
      case SPORTMONKS -> applicationContext.getBean(SportmonksSquadsRetriever.class);
      case OPTA -> applicationContext.getBean(OptaSquadsRetriever.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for squads ingestion", playersProvider));
    };
  }

  public FixtureStartDataRetriever getFixtureStartDataRetriever(String competitionId) {
    CompetitionConfig.Provider settlementProvider = getFeedsConfig(competitionId).getSettlementConfig().getProvider();
    return switch (settlementProvider) {
      case SPORTMONKS -> applicationContext.getBean(SportmonksFixtureStartDataRetriever.class);
      case OPTA -> applicationContext.getBean(OptaFixtureStartDataRetriever.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for fixture start data retriever", settlementProvider));
    };
  }

  public FixtureDataProcessor getFixtureDataProcessor(String competitionId) {
    CompetitionConfig.Provider settlementProvider = getFeedsConfig(competitionId).getSettlementConfig().getProvider();
    return switch (settlementProvider) {
      case SPORTMONKS -> applicationContext.getBean(SportmonksFixtureDataProcessor.class);
      case OPTA -> applicationContext.getBean(OptaFixtureDataProcessor.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for fixture data processor", settlementProvider));
    };
  }

  public SettlementDataRetriever getSettlementDataRetriever(String competitionId) {
    CompetitionConfig.Provider playersProvider = getFeedsConfig(competitionId).getSettlementConfig().getProvider();
    return switch (playersProvider) {
      case SPORTMONKS -> applicationContext.getBean(SportmonksSettlementDataRetriever.class);
      case OPTA -> applicationContext.getBean(OptaSettlementDataRetriever.class);
      default -> throw new IllegalArgumentException(
        format("Provider %s is not supported for settlement data retriever", playersProvider));
    };
  }

  public int getSettlementDelaySeconds(String competitionId) {
    return getFeedsConfig(competitionId).getSettlementConfig().getDelaySecs();
  }

  public boolean isProvisionalLineupFeedAvailable(String competitionId) {
    return getFeedsConfig(competitionId).getIsProvisionalLineupFeedAvailable();
  }

  // TODO maybe add cache? not necessary now as we're using it for Sportmonks postmatch
  public Set<String> getSupportedStatsForCompetitionId(String competitionId) {
    return getCompetitionConfig(competitionId).getMarkets()
      .stream()
      .map(CompetitionMarketConfig::getPropName)
      .collect(toSet());
  }

  public ExternalProvider getCompetitionProvider(String competitionId) {
    var fixturesProvider = getFeedsConfig(competitionId).getFixturesProvider();
    return switch (fixturesProvider) {
      case OPTA -> ExternalProvider.OPTA;
      case SPORTMONKS -> ExternalProvider.SPORTMONKS;
      case WYSCOUT -> ExternalProvider.WYSCOUT;
      case BETRADAR -> ExternalProvider.BETRADAR;
    };
  }

  private CompetitionConfig.FeedsConfig getFeedsConfig(String competitionId) {
    return getCompetitionConfig(competitionId).getFeedsConfig();
  }

  private CompetitionConfig getCompetitionConfig(String competitionId) {
    return competitionIdToConfig.computeIfAbsent(competitionId, competitionConfigRepository::findByCompetitionId);
  }
}
