package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.domain.common.LivePlayerRating;
import com.wsf.domain.common.PlayerRating;
import com.wsf.repository.common.LivePlayerRatingRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

@Slf4j
@RequiredArgsConstructor
@Service
public class LivePlayerRatingService {

  private final RepositoryFactory.LivePlayerRatingRepositoryFactory liveRatingRepositoryFactory;
  private final DBUtils dbUtils;

  @Retryable(value = DataAccessException.class, maxAttempts = 10, backoff = @Backoff(delay = 5000))
  public void storeRating(PlayerRating ratingToSave, boolean isFinal) {

    LivePlayerRating liveRating = buildLiveRating(ratingToSave, isFinal);

    log.debug("storing rating into the db: {}", liveRating);

    String competitionId = ratingToSave.getFixture().getTournament().getCompetitionId();
    LivePlayerRatingRepository liveRatingRepository = dbUtils.retrieveRepo(liveRatingRepositoryFactory, competitionId);
    liveRatingRepository.save(liveRating);
  }

  private static LivePlayerRating buildLiveRating(PlayerRating ratingToSave, boolean isFinal) {
    return LivePlayerRating
      .builder()
      .playerId(ratingToSave.getPlayer().getId().toString())
      .fixtureId(ratingToSave.getFixture().getId().toString())
      .indexPerformance(ratingToSave.getIndexPerformance())
      .stats(ratingToSave.getStats())
      .fixtureTimeMin(ratingToSave.getFixtureTimeMin())
      .periodId(ratingToSave.getPeriodId())
      .feedId(ratingToSave.getFeedId())
      .eventId(ratingToSave.getEventId())
      .isFinal(isFinal)
      .timestamp(Instant.now())
      .build();
  }
}
