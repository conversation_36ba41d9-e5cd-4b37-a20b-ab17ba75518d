package com.wsf.dataingestor.services.fixtures;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import com.wsf.dataingestor.clients.PullAPIClient;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.parsers.ParsersUtils;
import com.wsf.domain.common.Tournament;

@Slf4j
public class FixtureRetrieverUtils {

  public static FixturesFeed retrieveFixturesFeed(Tournament tournament, PullAPIClient<FixturesFeed> pullAPIClient,
                                                  Set<String> externalIds) {
    List<FixturesFeed> fixturesFeed = externalIds
      .stream()
      .map(pullAPIClient::retrieveParsedFeed)
      .collect(Collectors.toList());

    List<String> feedIdsList = fixturesFeed
      .stream()
      .map(FixturesFeed::getFeedId)
      .collect(Collectors.toList());

    String feedId = ParsersUtils.buildFeedId(feedIdsList);

    List<FixtureDTO> fixtureDTOs = fixturesFeed
      .stream()
      .map(FixturesFeed::getFixtures)
      .flatMap(Collection::stream)
      .collect(Collectors.toList());
    return FixturesFeed
      .builder().feedId(feedId).tournament(tournament).fixtures(fixtureDTOs).build();
  }
}
