package com.wsf.dataingestor.services.fixtures;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.crons.FixturesWatcherUpdater;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Tournament;
import com.wsf.domain.common.TournamentConfig;
import com.wsf.repository.common.TournamentConfigRepository;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.wsf.dataingestor.services.FixtureChangeProcessor.shouldRescheduleJob;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.HOURS;
import static java.util.Comparator.comparing;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixturesIngestionService {

  private final MetricsManager metrics;
  private final FixtureRelationshipService fixtureRelationshipService;
  private final FixtureInfoUpdater fixtureInfoUpdater;
  private final FixturesWatcherUpdater fixturesWatcherUpdater;
  private final CompetitionConfigRepository competitionConfigRepository;
  private final TournamentConfigRepository tournamentConfigRepository;

  public void processFixturesFeed(FixturesRetriever fixturesRetriever, FixturesFeed fixturesFeed) {
    Tournament tournament = fixturesFeed.getTournament();

    CompetitionConfig competitionConfig = competitionConfigRepository.findByCompetitionId(
      tournament.getCompetitionId());
    Boolean isLiveEnabledForCompetition = competitionConfig.getOddsGeneration().getIsLiveEnabled();

    String feedId = fixturesFeed.getFeedId();
    log.info("Processing matches feed feedId={}", feedId);

    Optional<TournamentConfig> optionalTournamentConfig = tournamentConfigRepository.findByTournamentId(
      tournament.getIdAsString());

    List<FixtureDTO> fixtures = fixturesFeed.getFixtures();

    var processedExternalFixtureIds = fixtures
      .stream()
      .filter(fixtureDTO -> isFixtureSupported(fixturesRetriever, optionalTournamentConfig, fixtureDTO, tournament))
      .sorted(comparing(FixtureDTO::getTime))
      .map(fixtureDTO -> {
        try {
          Fixture existingFixture = fixturesRetriever.findFixture(fixtureDTO, tournament);
          Fixture updatedFixture = fixtureInfoUpdater.processFixtureInfoDuringIngestion(existingFixture, fixtureDTO,
            fixturesRetriever, isLiveEnabledForCompetition, fixturesFeed.getFeedId());
          if (shouldRescheduleJob(existingFixture.getProcessStatus(), existingFixture.getStatus(), updatedFixture)) {
            fixturesWatcherUpdater.scheduleMatchProcess(updatedFixture, fixturesRetriever.getExternalFixtureProvider());
          }
          return fixtureDTO.getExternalFixtureId();
        } catch (FixtureNotFoundException e) {
          handleFixtureNotFound(fixtureDTO, competitionConfig, e.getMessage());
        } catch (Exception e) {
          Counter counter = metrics.MATCHES_FEED_PARSING_ERROR;
          log.error("error={} while ingesting fixtureDTO={}: {}", counter.getId().getName(), fixtureDTO,
            e.getMessage());
          counter.increment();
        }
        return null;
      })
      .filter(Objects::nonNull)
      .collect(Collectors.toList());

    fixtureRelationshipService.storeRelations(fixturesRetriever, tournament, fixtures, processedExternalFixtureIds);
  }

  private boolean isFixtureSupported(FixturesRetriever fixturesRetriever,
                                     Optional<TournamentConfig> optionalTournamentConfig, FixtureDTO fixtureDTO,
                                     Tournament tournament) {
    if (!fixturesRetriever.shouldProcessStages()) {
      return true;
    }
    return optionalTournamentConfig
      .map(tournamentConfig -> {
        try {
          Stage stage = fixturesRetriever.findStage(fixtureDTO, tournament.getCompetitionId());
          return tournamentConfig.getSupportedStages()
            .stream()
            .anyMatch(supportedStage -> supportedStage.getId().equals(stage.getId()));
        } catch (IllegalArgumentException exception) {
          log.info("Not found a stage with external id {} and name {}, sent to entity mapper.",
            fixtureDTO.getExternalStageId(), fixtureDTO.getExternalStageName(), exception);
          metrics.MATCHES_FEED_UNMAPPED_STAGE.increment();
          return false;
        }
      })
      .orElse(true);
  }

  private void handleFixtureNotFound(FixtureDTO fixtureDTO, CompetitionConfig competitionConfig,
                                     String exceptionMessage) {
    Instant fixtureTime = fixtureDTO.getTime();
    Optional<Integer> leadInHoursOpt = ofNullable(competitionConfig.getOddsGeneration().getLeadInHours());

    boolean fixtureIsGoingToBePlayedInTwoDaysFromLeadIn = leadInHoursOpt
      .map(leadInHours -> fixtureTime.isAfter(now()) && fixtureTime.isBefore(now().plus(leadInHours + 48, HOURS)))
      .orElse(true);

    if (fixtureIsGoingToBePlayedInTwoDaysFromLeadIn) {
      Counter counter = metrics.MATCHES_FEED_UNMAPPED_FIXTURES;
      log.error("Could not find match for fixtureDTO={} competitionId={}, leadIn={}, exception={}, error={}",
        fixtureDTO, competitionConfig.getCompetition().getIdAsString(), leadInHoursOpt.orElse(null), exceptionMessage,
        counter.getId().getName());
      counter.increment();
    } else {
      log.warn("Error ingesting fixtureDTO={}: {}", fixtureDTO, exceptionMessage);
    }
  }
}
