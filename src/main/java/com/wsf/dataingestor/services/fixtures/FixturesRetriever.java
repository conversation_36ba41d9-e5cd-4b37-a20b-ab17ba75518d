package com.wsf.dataingestor.services.fixtures;

import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

public interface FixturesRetriever {
  FixturesFeed retrieveFixturesFeed(Tournament tournament);

  Fixture findFixture(FixtureDTO fixtureDTO, Tournament tournament) throws FixtureNotFoundException;

  Fixture findRelatedFixture(FixtureDTO fixture, Tournament tournament) throws FixtureNotFoundException;

  Team findTeam(String externalTeamId, Tournament tournament);

  boolean shouldProcessStages();

  Stage findStage(FixtureDTO fixtureDTO, String competitionId);

  String getExternalFixtureFieldName();

  ExternalProvider getExternalFixtureProvider();
}
