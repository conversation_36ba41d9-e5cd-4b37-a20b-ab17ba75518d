package com.wsf.dataingestor.services.fixtures;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.mappers.FixtureRelationshipMapper;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.domain.common.Fixture.Relation;
import com.wsf.domain.common.Provider;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.mappers.ProviderMapper.mapProvider;
import static com.wsf.domain.common.Fixture.DB_FIELD_ACTIVE;
import static com.wsf.domain.common.Fixture.DB_FIELD_AWAY_TEAM;
import static com.wsf.domain.common.Fixture.DB_FIELD_CAN_GO_EXTRATIME;
import static com.wsf.domain.common.Fixture.DB_FIELD_DATE;
import static com.wsf.domain.common.Fixture.DB_FIELD_HOME_TEAM;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED_BY;
import static com.wsf.domain.common.Fixture.DB_FIELD_LEG;
import static com.wsf.domain.common.Fixture.DB_FIELD_PROCESS_STATUS;
import static com.wsf.domain.common.Fixture.DB_FIELD_STATUS;
import static com.wsf.domain.common.Fixture.DB_FIELD_WAS_SUSPENDED;
import static com.wsf.domain.common.Fixture.DB_IS_NEUTRAL_VENUE;
import static com.wsf.domain.common.Fixture.FixtureStatus.CANCELLED;
import static com.wsf.domain.common.Fixture.FixtureStatus.SUSPENDED;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@RequiredArgsConstructor
@Service
public class FixtureInfoUpdater {

  private final FixtureService fixtureService;
  private final KafkaService kafkaService;

  public Fixture processFixtureInfoDuringIngestion(Fixture existingFixture, FixtureDTO fixtureDTO,
                                                   FixturesRetriever fixturesRetriever,
                                                   Boolean isLiveEnabledForCompetition, String feedId) {

    boolean isBetradarFixture = nonNull(existingFixture.getBetradarFixtureId());
    Instant newFixtureDate = isBetradarFixture ? existingFixture.getDate() : fixtureDTO.getTime();
    var newFixtureStatus = toFixtureStatus(fixtureDTO.getFixtureStatus(), newFixtureDate);

    Fixture updatedFixture = updateFixtureInfoDuringFixturesIngestion(fixturesRetriever, fixtureDTO, existingFixture,
      newFixtureStatus, newFixtureDate, isLiveEnabledForCompetition, isBetradarFixture);

    boolean isDataChanged = !fixtureDTO.getTime().equals(existingFixture.getDate());
    boolean isStatusChanged = newFixtureStatus != existingFixture.getStatus();
    boolean isActiveChanged = existingFixture.getActive() != ofNullable(fixtureDTO.getIsActive()).orElse(true);

    if (!isBetradarFixture && (isDataChanged || isStatusChanged || isActiveChanged)) {
      kafkaService.sendFixtureChange(existingFixture, feedId);
    }

    return updatedFixture;
  }

  public void processFixtureInfoDuringLive(Fixture existingFixture, FixtureStatus newFixtureStatus,
                                           Instant newFixtureDate, String feedId) {

    boolean isDataChanged = !newFixtureDate.equals(existingFixture.getDate());
    boolean isStatusChanged = newFixtureStatus != existingFixture.getStatus();
    if (isDataChanged || isStatusChanged) {
      kafkaService.sendFixtureChange(existingFixture, feedId);
    }

    updateFixtureInfoDuringLive(existingFixture, newFixtureDate, newFixtureStatus);
  }

  public static FixtureStatus toFixtureStatus(MatchDataFeed.FeedFixtureStatus fixtureStatus, Instant fixtureDate) {
    Instant now = now();
    boolean wasMatchStartedLessThan6hAgo =
      fixtureDate.isAfter(now.minus(6, ChronoUnit.HOURS)) && fixtureDate.isBefore(now);
    switch (fixtureStatus) {
      case FIXTURE:
        return FixtureStatus.FIXTURE;
      case LIVE:
      case BET_END:
        return FixtureStatus.LIVE;
      case PLAYED:
        return FixtureStatus.PLAYED;
      case SUSPENDED:
        if (wasMatchStartedLessThan6hAgo) {
          return FixtureStatus.LIVE;
        } else {
          return SUSPENDED;
        }
      case CANCELLED:
      case POSTPONED:
        return CANCELLED;
      default:
        throw new IllegalArgumentException("Unknown FeedFixtureStatus " + fixtureStatus);
    }
  }

  private Fixture updateFixtureInfoDuringFixturesIngestion(FixturesRetriever fixturesRetriever, FixtureDTO fixtureDTO,
                                                           Fixture existingFixture, FixtureStatus newFixtureStatus,
                                                           Instant newFixtureDate, Boolean isLiveEnabledForCompetition,
                                                           boolean isBetradarFixture) {
    boolean hasDateBeenUpdated = !existingFixture.getDate().equals(newFixtureDate);
    Map<String, Object> fieldsToUpdateMap;
    if (hasDateBeenUpdated && existingFixture.getActive()) {
      fieldsToUpdateMap = getDateAndStatusUpdateFields(newFixtureDate, newFixtureStatus, isBetradarFixture);
    } else {
      fieldsToUpdateMap = getDefaultUpdateFields(fixturesRetriever, fixtureDTO, existingFixture, newFixtureStatus,
        newFixtureDate, isLiveEnabledForCompetition, isBetradarFixture);
    }

    var isFixtureActive = fixtureDTO.getIsActive() == null || fixtureDTO.getIsActive();
    fieldsToUpdateMap.put(DB_FIELD_ACTIVE, isFixtureActive);
    if (isNull(existingFixture.getLeg())) {
      Relation dbFixtureLeg = FixtureRelationshipMapper.mapDtoRelationToDbRelation(fixtureDTO.getLeg());
      fieldsToUpdateMap.put(DB_FIELD_LEG, dbFixtureLeg);
    }

    log.info("updating fixture with id={} with data: {}", existingFixture.getIdAsString(), fieldsToUpdateMap);
    return fixtureService.updateFixture(existingFixture, fieldsToUpdateMap);
  }

  private void updateFixtureInfoDuringLive(Fixture existingFixture, Instant newFixtureDate,
                                           FixtureStatus newFixtureStatus) {
    boolean hasDateBeenUpdated = !existingFixture.getDate().equals(newFixtureDate);
    boolean hasStatusBeenUpdated = existingFixture.getStatus() != newFixtureStatus;
    boolean hasFixtureBeenUpdated = hasDateBeenUpdated || hasStatusBeenUpdated;
    if (hasFixtureBeenUpdated) {
      var isBetradarFixture = nonNull(existingFixture.getBetradarFixtureId());
      Map<String, Object> fieldsToUpdateMap = getDateAndStatusUpdateFieldsForLive(newFixtureDate, newFixtureStatus,
        isBetradarFixture);
      log.info("fixture data has been updated for fixtureId={}: {}", existingFixture.getIdAsString(),
        fieldsToUpdateMap);
      fixtureService.updateFixture(existingFixture, fieldsToUpdateMap);
    }
  }


  private HashMap<String, Object> getDefaultUpdateFields(FixturesRetriever retriever, FixtureDTO fixtureDTO,
                                                         Fixture existingFixture, FixtureStatus newFixtureStatus,
                                                         Instant newFixtureDate, Boolean isLiveEnabledForCompetition,
                                                         boolean isBetradarFixture) {

    Tournament tournament = existingFixture.getTournament();
    var fieldsToUpdateMap = new HashMap<String, Object>();

    Team homeTeam = retriever.findTeam(fixtureDTO.getExternalHomeTeamId(), tournament);
    Team awayTeam = retriever.findTeam(fixtureDTO.getExternalAwayTeamId(), tournament);
    Boolean isNeutralVenue = fixtureDTO.getIsNeutralVenue();
    Map<Provider, Boolean> updatedIsLiveEnabledBy = updatedIsLiveEnabledBy(fixtureDTO, existingFixture,
      isLiveEnabledForCompetition);
    boolean isLiveEnabledForAllProviders = updatedIsLiveEnabledBy.values()
      .stream()
      .allMatch(Boolean::booleanValue);

    if (!isBetradarFixture) {
      fieldsToUpdateMap.put(DB_FIELD_DATE, newFixtureDate);
      fieldsToUpdateMap.put(DB_FIELD_HOME_TEAM, homeTeam);
      fieldsToUpdateMap.put(DB_FIELD_AWAY_TEAM, awayTeam);
    }

    fieldsToUpdateMap.put(DB_IS_NEUTRAL_VENUE, isNeutralVenue);
    fieldsToUpdateMap.put(DB_FIELD_CAN_GO_EXTRATIME, fixtureDTO.getCanGoExtraTime());
    fieldsToUpdateMap.put(retriever.getExternalFixtureFieldName(), fixtureDTO.getExternalFixtureId());
    fieldsToUpdateMap.put(DB_FIELD_IS_LIVE_ENABLED_BY, updatedIsLiveEnabledBy);
    fieldsToUpdateMap.put(DB_FIELD_IS_LIVE_ENABLED, isLiveEnabledForAllProviders);

    boolean hasToBePlayed = fixtureDTO.getFixtureStatus() != MatchDataFeed.FeedFixtureStatus.CANCELLED &&
      fixtureDTO.getFixtureStatus() != MatchDataFeed.FeedFixtureStatus.POSTPONED &&
      fixtureDTO.getFixtureStatus() != MatchDataFeed.FeedFixtureStatus.SUSPENDED;
    boolean isAfterNow = newFixtureDate.isAfter(now());

    if (isAfterNow && hasToBePlayed) {
      log.info("match time for fixtureId={}: {} after now, setting its status to FIXTURE",
        existingFixture.getId().toString(), newFixtureDate);
      fieldsToUpdateMap.put(DB_FIELD_STATUS, FixtureStatus.FIXTURE);
      Fixture.FixtureProcessStatus processStatus = existingFixture.getProcessStatus();
      boolean isAlreadySettled = processStatus == Fixture.FixtureProcessStatus.SETTLED;
      if (isAlreadySettled) {
        fieldsToUpdateMap.put(DB_FIELD_PROCESS_STATUS, null);
      }
    } else {
      fieldsToUpdateMap.put(DB_FIELD_STATUS, newFixtureStatus);
    }

    if (newFixtureStatus == SUSPENDED) {
      fieldsToUpdateMap.put(DB_FIELD_WAS_SUSPENDED, true);
    }

    return fieldsToUpdateMap;
  }

  private static Map<Provider, Boolean> updatedIsLiveEnabledBy(FixtureDTO fixtureDTO, Fixture existingFixture,
                                                               Boolean isLiveEnabledForCompetition) {
    boolean isLiveEnabledForCurrentProvider = fixtureDTO.getIsLiveSupported() && isLiveEnabledForCompetition;
    Map<Provider, Boolean> updatedIsLiveEnabledBy = new HashMap<>(existingFixture.getIsLiveEnabledBy());
    updatedIsLiveEnabledBy.put(mapProvider(fixtureDTO.getProvider()), isLiveEnabledForCurrentProvider);
    return updatedIsLiveEnabledBy;
  }

  private HashMap<String, Object> getDateAndStatusUpdateFields(Instant newFixtureDate, FixtureStatus newFixtureStatus,
                                                               boolean isBetradarFixture) {
    var fieldsToUpdateMap = new HashMap<String, Object>();

    fieldsToUpdateMap.put(DB_FIELD_STATUS, newFixtureStatus);

    if (!isBetradarFixture) {
      fieldsToUpdateMap.put(DB_FIELD_DATE, newFixtureDate);
    }

    switch (newFixtureStatus) {
      case FIXTURE:
        fieldsToUpdateMap.put(DB_FIELD_PROCESS_STATUS, null);
        break;
      case SUSPENDED:
        fieldsToUpdateMap.put(DB_FIELD_WAS_SUSPENDED, true);
        break;
    }
    return fieldsToUpdateMap;
  }

  private HashMap<String, Object> getDateAndStatusUpdateFieldsForLive(Instant newFixtureDate,
                                                                      FixtureStatus newFixtureStatus,
                                                                      boolean isBetradarFixture) {
    var fieldsToUpdateMap = new HashMap<String, Object>();
    fieldsToUpdateMap.put(DB_FIELD_STATUS, newFixtureStatus);

    if (!isBetradarFixture) {
      fieldsToUpdateMap.put(DB_FIELD_DATE, newFixtureDate);
    }

    if (SUSPENDED == newFixtureStatus) {
      fieldsToUpdateMap.put(DB_FIELD_WAS_SUSPENDED, true);
    }
    return fieldsToUpdateMap;
  }
}
