package com.wsf.dataingestor.services.fixtures;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.mappers.FixtureRelationshipMapper;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.Relation;
import com.wsf.domain.common.Tournament;

import static com.wsf.domain.common.Fixture.DB_FIELD_LEG;
import static com.wsf.domain.common.Fixture.DB_FIELD_RELATED_FIXTURE;
import static com.wsf.domain.common.Fixture.Relation.SINGLE;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixtureRelationshipService {

  private final FixtureService fixtureService;
  private final MetricsManager metrics;

  public void storeRelations(FixturesRetriever fixturesRetriever, Tournament tournament, List<FixtureDTO> fixtureDTOS,
                             List<String> processedExternalFixtureIds) {
    fixtureDTOS
      .stream()
      .filter(fixtureDTO -> processedExternalFixtureIds.contains(fixtureDTO.getExternalFixtureId()))
      .forEach(fixtureDTO -> storeFixtureRelation(fixturesRetriever, tournament, fixtureDTO));
  }

  private void storeFixtureRelation(FixturesRetriever fixturesRetriever, Tournament tournament, FixtureDTO fixtureDTO) {
    try {
      Fixture existingFixture = fixturesRetriever.findFixture(fixtureDTO, tournament);
      var fieldsToUpdateMap = new HashMap<String, Object>();
      Relation dbFixtureLeg = FixtureRelationshipMapper.mapDtoRelationToDbRelation(fixtureDTO.getLeg());
      fieldsToUpdateMap.put(DB_FIELD_LEG, dbFixtureLeg);

      if (nonNull(fixtureDTO.getExternalRelatedFixtureId())) {
        Fixture relatedFixture = getRelatedFixture(fixturesRetriever, tournament, fixtureDTO, existingFixture);
        if (isNull(relatedFixture)) {
          return;
        }
        fieldsToUpdateMap.put(DB_FIELD_RELATED_FIXTURE, relatedFixture.getId());
      } else if (dbFixtureLeg != SINGLE) {
        throw new FixtureNotFoundException("Error relating fixtures");
      }

      fixtureService.updateFixture(existingFixture, fieldsToUpdateMap);
    } catch (Exception exception) {
      handleError(tournament, fixtureDTO, exception);
    }
  }

  private Fixture getRelatedFixture(FixturesRetriever fixturesRetriever, Tournament tournament, FixtureDTO fixtureDTO,
                                    Fixture existingFixture) throws FixtureNotFoundException {
    try {
      return fixturesRetriever.findRelatedFixture(fixtureDTO, tournament);
    } catch (FixtureNotFoundException exception) {
      boolean isRelatedFixtureAlreadyMapped = ofNullable(existingFixture)
        .map(Fixture::getRelatedFixtureId)
        .isPresent();
      if (isRelatedFixtureAlreadyMapped) {
        log.warn(
          "related fixture with externalFixtureId={} not found for fixture={} but the fixture has already a related fixture in the db, skipping",
          fixtureDTO.getExternalRelatedFixtureId(), existingFixture);
        return null;
      } else {
        throw exception;
      }
    }
  }

  private void handleError(Tournament tournament, FixtureDTO fixtureDTO, Exception e) {
    Counter counter = metrics.MATCHES_FEED_PARSING_ERROR;
    log.error("error={} relating fixtureDTO={} and externalId={} for competition={}, {}", counter.getId().getName(),
      fixtureDTO, fixtureDTO.getExternalRelatedFixtureId(), tournament.getCompetition().getName(), e.getMessage());
    counter.increment();
  }

}
