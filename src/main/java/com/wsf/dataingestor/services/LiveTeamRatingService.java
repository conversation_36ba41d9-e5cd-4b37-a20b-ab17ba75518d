package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.domain.common.LiveTeamRating;
import com.wsf.domain.common.TeamRating;
import com.wsf.repository.common.LiveTeamRatingRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

@Slf4j
@RequiredArgsConstructor
@Service
public class LiveTeamRatingService {

  private final RepositoryFactory.LiveTeamRatingRepositoryFactory liveRatingRepositoryFactory;
  private final DBUtils dbUtils;

  @Retryable(value = DataAccessException.class, maxAttempts = 10, backoff = @Backoff(delay = 5000))
  public void storeRating(TeamRating ratingToSave, boolean isFinal) {

    LiveTeamRating liveRating = buildLiveRating(ratingToSave, isFinal);

    log.debug("Storing team rating into the db: {}", liveRating);

    String competitionId = ratingToSave.getFixture().getTournament().getCompetitionId();
    LiveTeamRatingRepository liveRatingRepository = dbUtils.retrieveRepo(liveRatingRepositoryFactory, competitionId);
    liveRatingRepository.save(liveRating);
  }

  private static LiveTeamRating buildLiveRating(TeamRating ratingToSave, boolean isFinal) {
    return LiveTeamRating
      .builder()
      .teamId(ratingToSave.getTeam().getId().toString())
      .fixtureId(ratingToSave.getFixture().getId().toString())
      .stats(ratingToSave.getStats())
      .fixtureTimeMin(ratingToSave.getFixtureTimeMin())
      .periodId(ratingToSave.getPeriodId())
      .feedId(ratingToSave.getFeedId())
      .eventId(ratingToSave.getEventId())
      .isFinal(isFinal)
      .timestamp(Instant.now())
      .build();
  }
}
