package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.domain.common.TeamRating;
import com.wsf.repository.common.TeamRatingRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

@Slf4j
@RequiredArgsConstructor
@Service
public class TeamRatingService {

  private final RepositoryFactory.TeamRatingRepositoryFactory ratingRepositoryFactory;
  private final DBUtils dbUtils;

  @Retryable(value = DataAccessException.class, maxAttempts = 100, backoff = @Backoff(delay = 5000))
  public void storeRating(TeamRating ratingToSave) {

    log.debug("upsert rating into the db: {}", ratingToSave);

    String competitionId = ratingToSave.getFixture().getTournament().getCompetitionId();
    TeamRatingRepository ratingRepository = dbUtils.retrieveRepo(ratingRepositoryFactory, competitionId);
    ratingRepository.upsert(ratingToSave);
  }
}
