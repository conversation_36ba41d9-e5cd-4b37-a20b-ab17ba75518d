package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.repository.common.MasterPlayerRepository;
import com.wsf.repository.common.PlayerRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static com.wsf.dataingestor.config.cache.CacheConstants.OPTA_PLAYERS_CACHE_NAME;
import static com.wsf.dataingestor.config.cache.CacheConstants.TEAM_ACTIVE_PLAYERS_CACHE_NAME;
import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.ListUtils.union;

@Slf4j
@RequiredArgsConstructor
@Service
public class PlayerService {

  private final RepositoryFactory.PlayerRepositoryFactory playerRepositoryFactory;
  private final MasterPlayerRepository masterPlayerRepository;
  private final UnmappedEntityClient unmappedEntityClient;
  private final DBUtils dbUtils;

  public Player getOrThrowById(String competitionId, String playerId) {
    return getById(competitionId, playerId).orElseThrow(
      () -> new NoSuchElementException(format("Can't find player with id %s", playerId)));
  }

  public Optional<Player> getById(String competitionId, String playerId) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findById(playerId);
  }

  public List<Player> getAllPlayersByTournamentId(String competitionId, String tournamentId) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findByTournamentIdAndIsActiveTrue(tournamentId);
  }

  public List<Player> getActivePlayersForFixture(Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getIdAsString();

    String homeTeamId = fixture.getHomeTeam().getIdAsString();
    String awayTeamId = fixture.getAwayTeam().getIdAsString();

    List<Player> homeTeamPlayers = getActivePlayersByTeamIdAndTournamentId(competitionId, homeTeamId, tournamentId);
    List<Player> awayTeamPlayers = getActivePlayersByTeamIdAndTournamentId(competitionId, awayTeamId, tournamentId);

    return union(homeTeamPlayers, awayTeamPlayers);
  }

  @Cacheable(value = TEAM_ACTIVE_PLAYERS_CACHE_NAME, unless = "#result == null", key = "{#competitionId + #teamId}")
  public List<Player> getActivePlayersByTeamIdAndTournamentId(String competitionId, String teamId,
                                                              String tournamentId) {
    log.debug("retrieving players for teamId={} from DB", teamId);
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findByTeamIdAndTournamentIdAndIsActive(teamId, tournamentId);
  }

  @CacheEvict(value = TEAM_ACTIVE_PLAYERS_CACHE_NAME, key = "{#competitionId + #teamId}")
  public void evictActivePlayersByCompetitionIdAndTeamId(String competitionId, String teamId) {}

  public List<Player> getAllPlayersForFixtureId(Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getId().toString();
    String homeTeamId = fixture.getHomeTeam().getId().toString();
    String awayTeamId = fixture.getAwayTeam().getId().toString();

    List<Player> allHomeTeamPlayers = getAllPlayersByTeamIdAndTournamentId(competitionId, homeTeamId, tournamentId);
    List<Player> allAwayTeamPlayers = getAllPlayersByTeamIdAndTournamentId(competitionId, awayTeamId, tournamentId);
    return union(allHomeTeamPlayers, allAwayTeamPlayers);
  }

  private List<Player> getAllPlayersByTeamIdAndTournamentId(String competitionId, String teamId, String tournamentId) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findByTeamIdAndTournamentIdAndIsActive(teamId, tournamentId);
  }

  @Cacheable(value = OPTA_PLAYERS_CACHE_NAME, unless = "#result == null", key = "{#competitionId + #optaPlayerId}")
  public Player findByOptaPlayerIdAndTournamentId(String competitionId, String optaPlayerId, String tournamentId) {
    log.debug("retrieving player for optaPlayerId={} from DB", optaPlayerId);
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findByOptaPlayerIdAndTournamentId(optaPlayerId, tournamentId)
      .orElse(null);
  }

  public Player findBySportmonksPlayerIdAndTournamentId(String competitionId, String tournamentId, String smPlayerId) {
    log.debug("retrieving player for sportmonksPlayerId={} from DB", smPlayerId);
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findBySportmonksPlayerIdAndTournamentId(smPlayerId, tournamentId)
      .orElse(null);
  }

  public Optional<MasterPlayer> findMasterPlayerByOptaPlayerId(String optaPlayerId) {
    return masterPlayerRepository.findByOptaPlayerId(optaPlayerId);
  }

  public Optional<MasterPlayer> findMasterPlayerBySportmonksPlayerId(String smPlayerId) {
    return masterPlayerRepository.findBySportmonksPlayerId(smPlayerId);
  }

  @CacheEvict(value = OPTA_PLAYERS_CACHE_NAME, key = "T(com.wsf.dataingestor.services.PlayerService).buildCacheKey(#player)", condition = "#player.optaPlayerId != null")
  public Player savePlayer(Player player) {
    String competitionId = player.getTournament().getCompetitionId();
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.save(player);
  }

  public void updateMasterPlayer(MasterPlayer masterPlayer) {
    masterPlayerRepository.save(masterPlayer);
  }

  public void disablePlayersForTournament(String competitionId, String tournamentId) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    playerRepository.disablePlayersForTournament(tournamentId);
  }

  public void disablePlayer(Player player) {
    player.setIsActive(false);

    String competitionId = player.getTournament().getCompetitionId();
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    playerRepository.save(player);
  }

  @CacheEvict(cacheNames = OPTA_PLAYERS_CACHE_NAME, key = "T(com.wsf.dataingestor.services.PlayerService).buildCacheKey(#player)", condition = "#player.optaPlayerId != null")
  public void updatePlayerDetailedPosition(Player player, Player.DetailedPosition newPosition) {
    Player.DetailedPosition oldPosition = player.getDetailedPosition();
    if (nonNull(newPosition) && (isNull(oldPosition) || !oldPosition.equals(newPosition))) {
      player.setDetailedPosition(newPosition);
      savePlayer(player);
      log.info("New position {} stored for playerId={}", newPosition, player.getId().toString());
    }
  }

  @CacheEvict(cacheNames = OPTA_PLAYERS_CACHE_NAME, key = "T(com.wsf.dataingestor.services.PlayerService).buildCacheKey(#player)", condition = "#player.optaPlayerId != null")
  public Player update(Player player, Map<String, Object> fieldsToUpdate) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory,
      player.getTournament().getCompetitionId());
    return playerRepository.update(player.getId().toString(), fieldsToUpdate);
  }

  public MasterPlayer findMasterPlayerByExternalIdOrCreateUnmapped(String externalFirstName, String externalLastName,
                                                                   String externalMatchName, LocalDate birthDate,
                                                                   String externalPlayerId, String providerField,
                                                                   ExternalProvider provider,
                                                                   Function<String, Optional<MasterPlayer>> masterPlayerFinder,
                                                                   Supplier<MasterPlayer> masterPlayerBuilder) {
    if (!isBirthDateValid(birthDate)) {
      throw new DateTimeParseException(format("Invalid birthDate %s", birthDate), ofNullable(birthDate)
        .map(LocalDate::toString)
        .orElse(""), 0);
    }

    return masterPlayerFinder.apply(externalPlayerId)
      .orElseGet(() -> {
        boolean wasUnmappedPlayerCreated = unmappedEntityClient.createUnmappedPlayer(externalFirstName,
          externalLastName, externalMatchName, birthDate, externalPlayerId, provider);
        if (wasUnmappedPlayerCreated) {
          return null;
        }

        MasterPlayer masterPlayer = masterPlayerBuilder.get();
        Optional<MasterPlayer> existingMasterPlayer = masterPlayerRepository.findByPlayerMasterId(
          masterPlayer.getPlayerMasterId());

        if (existingMasterPlayer.isPresent()) {
          return masterPlayerRepository.update(existingMasterPlayer.get().getPlayerMasterId(),
            Map.of(providerField, externalPlayerId));
        } else {
          return masterPlayerRepository.save(masterPlayer);
        }
      });
  }

  private static boolean isBirthDateValid(LocalDate birthDate) {
    return birthDate != null && LocalDate
      .of(1900, 1, 1).isBefore(birthDate) && LocalDate.now().isAfter(birthDate);
  }

  public static MasterPlayer.MasterPlayerBuilder buildMasterPlayer(String firstName, String lastName, String matchName,
                                                                   LocalDate birthDate) {
    String masterId = MasterPlayer.buildMasterId(firstName, lastName, birthDate);
    return MasterPlayer
      .builder().playerMasterId(masterId).firstName(firstName)
      .name(lastName).matchName(matchName).dateOfBirth(birthDate).createdAt(Instant.now()).isActive(true);
  }

  public Optional<Player> findPlayerByFullNameOrMatchName(String competitionId, String name, String teamId) {
    PlayerRepository playerRepository = dbUtils.retrieveRepo(playerRepositoryFactory, competitionId);
    return playerRepository.findPlayerByFullNameOrMatchName(name, teamId);
  }

  // DO NOT REMOVE, IT'S USED TO BUILD THE CACHE KEY
  public static String buildCacheKey(Player player) {
    return player.getTournament().getCompetitionId() + player.getOptaPlayerId();
  }
}
