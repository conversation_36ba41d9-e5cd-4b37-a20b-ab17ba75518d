package com.wsf.dataingestor.services;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.FeedStore;

@Slf4j
@Service
public class FeedStoreService {
  private final FeedStore feedStore;
  private final Executor feedsStoragePool;

  public FeedStoreService(FeedStore feedStore, @Qualifier("feedsStoragePool") Executor feedsStoragePool) {
    this.feedStore = feedStore;
    this.feedsStoragePool = feedsStoragePool;
  }

  public void storeFeed(byte[] content, String feedId, String name) {
    CompletableFuture.runAsync(() -> {
      try {
        feedStore.store(content, name);
      } catch (Exception e) {
        log.error("Error while storing feed {}", feedId, e);
      }
    }, feedsStoragePool);
  }
}
