package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.crons.FixturesWatcherUpdater;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.web.model.FixtureChangeDto;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.web.model.FixtureChangeDto.OperationType.UPDATE;
import static com.wsf.domain.common.ExternalProvider.OPTA;
import static com.wsf.domain.common.Fixture.DB_FIELD_ACTIVE;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static java.util.Optional.ofNullable;

@Service
@RequiredArgsConstructor
@Slf4j
public class FixtureChangeProcessor {

  private final FixtureService fixtureService;
  private final FixturesWatcherUpdater fixturesWatcherUpdater;
  private final FeedsConfigService feedsConfigService;

  public void process(String fixtureId, FixtureChangeDto fixtureChangeDto) {
    log.info("Processing fixture change event for fixtureId={}: {}", fixtureId, fixtureChangeDto);
    if (fixtureChangeDto.operationType() != UPDATE) {
      log.info("Fixture change event for fixtureId={} is not an update, ignoring it", fixtureId);
      return;
    }

    Fixture updatedFixture = fixtureService.getFixture(fixtureId);
    var fixturesRetriever = feedsConfigService.getFixturesRetriever(updatedFixture.getTournament().getCompetitionId());

    log.info("Fixture change event for fixtureId={} is an update, processing it", updatedFixture);
    clearCaches(updatedFixture);
    updateFixtureToInactiveIfReplaced(updatedFixture);
    rescheduleMatchJobIfNeeded(fixtureChangeDto, updatedFixture, fixturesRetriever);
  }

  private void clearCaches(Fixture updatedFixture) {
    Tournament tournament = updatedFixture.getTournament();
    // Since the betradar-ingestor already updated the fixture in db, we need to evict the old one from the OPTA cache
    if (feedsConfigService.getCompetitionProvider(tournament.getCompetitionId()) == OPTA) {
      fixtureService.evictByFixtureId(updatedFixture.getOptaFixtureId());
    }
  }

  private void rescheduleMatchJobIfNeeded(FixtureChangeDto fixtureChangeDto, Fixture updatedFixture,
                                          FixturesRetriever fixturesRetriever) {
    if (shouldRescheduleJob(fixtureChangeDto.originalProcessStatus(), fixtureChangeDto.originalFixtureStatus(),
      updatedFixture)) {
      fixturesWatcherUpdater.scheduleMatchProcess(updatedFixture, fixturesRetriever.getExternalFixtureProvider());
    }
  }

  private void updateFixtureToInactiveIfReplaced(Fixture updatedFixture) {
    if (ofNullable(updatedFixture.getWasReplaced()).orElse(false)) {
      fixtureService.updateFixture(updatedFixture, Map.of(DB_FIELD_ACTIVE, false));
    }
  }

  public static boolean shouldRescheduleJob(Fixture.FixtureProcessStatus originalProcessStatus,
                                            Fixture.FixtureStatus originalStatus, Fixture updatedFixture) {
    boolean isLive = updatedFixture.getStatus() == Fixture.FixtureStatus.LIVE;
    boolean isNotConnected = originalProcessStatus != Fixture.FixtureProcessStatus.CONNECTED;
    boolean existingFixtureIsNotLive = originalStatus != LIVE;
    return isLive && existingFixtureIsNotLive && isNotConnected;
  }
}
