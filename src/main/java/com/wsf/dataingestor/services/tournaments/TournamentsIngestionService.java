package com.wsf.dataingestor.services.tournaments;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Tournament;

import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class TournamentsIngestionService {

  private static final List<String> YEAR_POSSIBLE_SEPARATORS = List.of("/", "-");

  private final TournamentService tournamentService;
  private final PlayerService playerService;
  private final FeedsConfigService feedsConfigService;

  public void processTournamentFeed(List<CurrentTournamentFeed> currentTournaments) {
    List<Boolean> tournamentsActive = currentTournaments.stream()
      .sorted(TournamentsIngestionService::compareStartDates)
      .map(this::processTournamentFeedAndReturnIfActive)
      .toList();

    if (tournamentsActive.stream().noneMatch(val -> val)) {
      String competitionId = currentTournaments.get(0).getCompetition().getIdAsString();
      setActiveTournamentToInactive(competitionId);
    }
  }

  private boolean processTournamentFeedAndReturnIfActive(CurrentTournamentFeed currentTournamentFeed) {
    if (isNull(currentTournamentFeed)) {
      return false;
    }

    Competition competition = currentTournamentFeed.getCompetition();
    String competitionId = competition.getId().toString();

    if (isNull(currentTournamentFeed.getExternalSeasonId())) {
      log.warn("no current tournament retrieved from the provider for competitionId={}.", competitionId);
      return false;
    }

    boolean tournamentAlreadyExists = nonNull(currentTournamentFeed.getExistingTournament());
    if (tournamentAlreadyExists) {
      log.info("an active tournament for competitionId={} already exists and it's mapped, skipping", competitionId);
      return true;
    }

    String firstYear = getFirstYearOfTournament(currentTournamentFeed);
    List<Tournament> tournamentsForYear = tournamentService.findByCompetitionIdAndYear(competitionId, firstYear);

    if (shouldRollover(tournamentsForYear)) {
      log.info(
        "setting other tournaments to inactive. Found {} tournaments for competitionId={} and year={}. Provider={}",
        tournamentsForYear.size(), competitionId, firstYear, currentTournamentFeed.getProvider());
      setActiveTournamentToInactive(competitionId);
    }

    boolean shouldWaitForWyscoutRollover = tournamentsForYear.isEmpty();
    if (shouldWaitForWyscoutRollover) {
      String message = format(
        "current tournament for competitionId=%s competitionName=%s startDate=%s doesn't exist yet, skipping until it gets created",
        competitionId, competition.getName(), currentTournamentFeed.getStartDate());
      boolean shouldTriggerException = ofNullable(currentTournamentFeed.getStartDate())
        .map(date -> date.isBefore(now().plus(10, ChronoUnit.DAYS)))
        .orElse(false);
      if (shouldTriggerException) {
        throw new IllegalStateException(message);
      } else {
        log.warn(message);
        return true;
      }
    }

    String externalTournamentId = currentTournamentFeed.getExternalSeasonId();

    Tournament tournament = tournamentsForYear.get(0);

    Set<String> currentTournamentExternalProviderIds = feedsConfigService.getTournamentExternalIds(tournament);
    Set<String> newExternalProviderIds = Stream
      .concat(currentTournamentExternalProviderIds.stream(), Stream.of(externalTournamentId))
      .collect(Collectors.toSet());

    String externalIdKey = tournamentService.buildExternalProviderIdFieldName(
      currentTournamentFeed.getProvider().getExternalIdFieldName());

    log.info(
      "another tournament for the same competition and same year already exists in the db: {}. Updating {} to {}",
      tournament, externalIdKey, newExternalProviderIds);

    tournamentService.update(tournament.getId().toString(),
      Map.of(externalIdKey, newExternalProviderIds, "active", true, "current", true));
    return true;
  }

  private boolean shouldRollover(List<Tournament> tournamentsForYear) {
    if (tournamentsForYear.isEmpty()) {
      return true;
    }

    Tournament tournament = tournamentsForYear.get(0);
    Set<String> externalProviderIds = feedsConfigService.getTournamentExternalIds(tournament);

    return externalProviderIds.isEmpty();
  }

  private void setActiveTournamentToInactive(String competitionId) {
    Tournament activeTournament = tournamentService.findByIsActiveTrue(competitionId);
    if (nonNull(activeTournament)) {
      log.info("updating tournament {} and its players to inactive", activeTournament);
      tournamentService.updateToInactive(activeTournament.getId().toString());
      playerService.disablePlayersForTournament(competitionId, activeTournament.getId().toString());
    }
  }

  private static String getFirstYearOfTournament(CurrentTournamentFeed currentTournamentFeed) {
    String year = currentTournamentFeed.getYear();
    return YEAR_POSSIBLE_SEPARATORS
      .stream()
      .filter(year::contains)
      .findFirst()
      .map(sep -> year.split(sep)[0])
      .orElse(year);
  }

  private static int compareStartDates(CurrentTournamentFeed first, CurrentTournamentFeed second) {
    if (isNull(first.getExternalSeasonId()) || isNull(first.getStartDate())) {
      return -1;
    }
    if (isNull(second.getExternalSeasonId()) || isNull(second.getStartDate())) {
      return 1;
    }

    return first.getStartDate().compareTo(second.getStartDate());
  }
}
