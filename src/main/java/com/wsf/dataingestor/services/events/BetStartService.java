package com.wsf.dataingestor.services.events;


import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.EntityMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.PlayerBetStart;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.TeamBetStart;
import com.wsf.kafka.domain.TeamEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.kafka.KafkaService.buildLiveMatch;
import static com.wsf.dataingestor.kafka.KafkaService.buildPlayer;
import static com.wsf.dataingestor.kafka.KafkaService.buildTeam;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.ThreadPoolService.getPlayersUpdatesKey;
import static com.wsf.dataingestor.services.events.Utils.isOptaFeedAndCompetitionSupported;
import static com.wsf.dataingestor.services.events.Utils.isRunningballFeedAndCompetitionSupported;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
public class BetStartService {

  private final OngoingMatchDataCacheService ongoingMatchDataCache;
  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final TeamMatchDataCacheService teamMatchDataCacheService;
  private final FixtureService fixtureService;
  private final KafkaService kafkaService;
  private final LiveMatchUtils liveMatchUtils;
  private final ThreadPoolService threadPoolService;
  private final ScheduledExecutorService betStartScheduler;
  private final MatchLocker matchLocker;
  private final Utils utils;

  @Autowired
  public BetStartService(OngoingMatchDataCacheService ongoingMatchDataCache,
                         PlayerMatchDataCacheService playerMatchDataCache,
                         TeamMatchDataCacheService teamMatchDataCacheService, FixtureService fixtureService,
                         KafkaService kafkaService, LiveMatchUtils liveMatchUtils, ThreadPoolService threadPoolService,
                         @Qualifier("betStartScheduler") ScheduledExecutorService betStartScheduler,
                         MatchLocker matchLocker, Utils utils) {
    this.ongoingMatchDataCache = ongoingMatchDataCache;
    this.playerMatchDataCache = playerMatchDataCache;
    this.teamMatchDataCacheService = teamMatchDataCacheService;
    this.fixtureService = fixtureService;
    this.kafkaService = kafkaService;
    this.liveMatchUtils = liveMatchUtils;
    this.threadPoolService = threadPoolService;
    this.betStartScheduler = betStartScheduler;
    this.matchLocker = matchLocker;
    this.utils = utils;
  }

  public void processBetStart(MatchDataFeed feed, MatchEventDTO event) {
    String feedId = feed.getFeedId();
    Fixture fixture = feed.getFixture();

    if (isRunningballFeedAndCompetitionSupported(feed, fixture) || isOptaFeedAndCompetitionSupported(feed, fixture)) {
      String fixtureId = fixture.getId().toString();

      if (fixture.getStatus() != Fixture.FixtureStatus.LIVE) {
        log.warn("fixtureId={} is not live, not sending the betstart", fixtureId);
        return;
      }

      if (matchLocker.isMatchLocked(fixtureId)) {
        log.info("fixtureId={} is locked, not processing the betstart", fixtureId);
        return;
      }

      log.info("processing BETSTART: {} for fixtureId={}", event.getEvent(), fixtureId);

      OngoingMatchData cachedMatchData = ongoingMatchDataCache.get(fixtureId);

      Set<String> playersBetStopEventIds = processBetStartForPlayersInThePitch(feed, event, cachedMatchData);
      Set<String> homeTeamBetStopEventIds = processBetStartForTeam(feed, event, fixture.getHomeTeam());
      Set<String> awayTeamBetStopEventIds = processBetStartForTeam(feed, event, fixture.getAwayTeam());

      updateMatchCache(event, playersBetStopEventIds, homeTeamBetStopEventIds, awayTeamBetStopEventIds,
        cachedMatchData);
    } else {
      log.debug("feedId={} ignored for betstart: provider={}, competitionId={}", feedId, feed.getProvider(),
        fixture.getTournament().getCompetitionId());
    }
  }

  private void updateMatchCache(MatchEventDTO event, Set<String> playersBetStopEventIds,
                                Set<String> homeTeamBetStopEventIds, Set<String> awayTeamBetStopEventIds,
                                OngoingMatchData cachedMatchData) {
    Set<String> betStopEventIds = new HashSet<>();
    betStopEventIds.addAll(playersBetStopEventIds);
    betStopEventIds.addAll(homeTeamBetStopEventIds);
    betStopEventIds.addAll(awayTeamBetStopEventIds);
    cachedMatchData.getProcessedBetStopEventIds().addAll(betStopEventIds);
    cachedMatchData.setLatestBetStartTimestamp(event.getTimestamp());
    ongoingMatchDataCache.mergeIfExists(cachedMatchData.getMatchId(), cachedMatchData, false);
  }

  private Set<String> processBetStartForPlayersInThePitch(MatchDataFeed feed, MatchEventDTO event,
                                                          OngoingMatchData cachedMatchData) {
    var fixture = feed.getFixture();
    String fixtureId = fixture.getIdAsString();

    List<Player> playersOnThePitch = liveMatchUtils.findPlayersOnThePitch(fixture, cachedMatchData);
    var playerBetStopEventIds = playersOnThePitch
      .stream()
      .map(player -> {
        CompletableFuture<Set<String>> playerTask = new CompletableFuture<>();
        var key = getPlayersUpdatesKey(fixture, player.getIdAsString());
        threadPoolService.processPlayersUpdateInThreadPool(key, () -> {
          var betStopEventIds = processBetStartForPlayer(feed, event, player);
          playerTask.complete(betStopEventIds);
        });
        return playerTask;
      })
      .map(CompletableFuture::join)
      .filter(Objects::nonNull)
      .flatMap(Set::stream)
      .collect(toSet());

    if (!playerBetStopEventIds.isEmpty()) {
      Runnable fixtureBetstartRunnable = () -> threadPoolService.processFixtureUpdateInThreadPool(fixtureId,
        () -> sendPlayerRatings(fixtureId));
      betStartScheduler.schedule(fixtureBetstartRunnable, 10, TimeUnit.SECONDS);
    }
    return playerBetStopEventIds;
  }

  private void sendPlayerRatings(String fixtureId) {
    Fixture fixture = fixtureService.getFixture(fixtureId);

    if (fixture.getStatus() != Fixture.FixtureStatus.LIVE) {
      log.warn("fixtureId={} is not live, not sending players ratings after betstart", fixtureId);
      return;
    }

    var now = Instant.now();
    var sportType = fixture.getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, SOCCER_PLAYER);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixture.getIdAsString());
    liveMatchUtils.findPlayersOnThePitch(fixture, ongoingMatchData)
      .stream()
      .map(player -> {
        String playerId = player.getIdAsString();

        var playerMatchData = playerMatchDataCache.get(fixtureId, playerId);

        if (playerMatchData.isBetStop()) {
          log.info(
            "not sending update after betstart for playerId={} in fixtureId={} since there has been a betstop in the meantime",
            playerId, fixtureId);
          return null;
        }

        var playerRatingFeedId = buildFeedId(playerId, fixtureId, now);
        var playerRatingEventId = UUID.randomUUID().toString();

        return utils.buildPlayerRating(fixture, player, false, playerMatchData.getStats(),
          ongoingMatchData.getMatchPeriod().getPeriodId(), ongoingMatchData.getMatchTime(), playerRatingEventId,
          playerRatingFeedId, now);
      })
      .filter(Objects::nonNull)
      .peek(
        playerRating -> log.info("sending update after betstart for playerId={} in fixtureId={} eventId={} to kafka",
          playerRating.getPlayer().getIdAsString(), fixtureId, playerRating.getEventId()))
      .forEach(playerRating -> {
        var metadata = metadataRetriever.getMetadata(playerRating.getPlayer().getIdAsString(),
          ongoingMatchData.getAllPlayerEvents(), new MetadataRetrievalFlags(false, false));
        kafkaService.sendLivePlayerRating(playerRating, metadata);
      });
  }

  private void sendTeamRatings(MatchDataFeed feed, Team team) {
    Fixture fixture = fixtureService.getFixture(feed.getFixture().getIdAsString());
    String fixtureId = fixture.getIdAsString();

    if (fixture.getStatus() != Fixture.FixtureStatus.LIVE) {
      log.warn("fixtureId={} is not live, not sending teams ratings after betstart", fixtureId);
      return;
    }

    OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixture.getIdAsString());

    String teamId = team.getIdAsString();
    var teamMatchData = teamMatchDataCacheService.get(fixtureId, teamId);

    if (teamMatchData.isBetStop()) {
      log.info(
        "not sending update after betstart for teamId={} in fixtureId={} since there has been a betstop in the meantime",
        teamId, fixtureId);
      return;
    }

    var now = Instant.now();
    var teamRatingFeedId = buildFeedId(teamId, fixtureId, now);
    var teamEventId = UUID.randomUUID().toString();
    var teamRating = Utils.buildTeamRating(fixture, team, teamMatchData.getStats(),
      ongoingMatchData.getMatchPeriod().getPeriodId(), ongoingMatchData.getMatchTime(), false, teamRatingFeedId,
      teamEventId, now);
    log.info("sending update after betstart for teamId={} in fixtureId={} eventId={} to kafka", teamId, fixtureId,
      teamEventId);

    var sportType = fixture.getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, SOCCER_TEAM);
    var metadata = metadataRetriever.getMetadata(teamId, ongoingMatchData.getAllPlayerEvents(),
      new MetadataRetrievalFlags(false, false));
    kafkaService.sendLiveTeamRating(teamRating, metadata);
  }

  private Set<String> processBetStartForPlayer(MatchDataFeed feed, MatchEventDTO event, Player player) {
    String playerId = player.getId().toString();
    var fixture = feed.getFixture();
    String fixtureId = fixture.getIdAsString();

    try {
      PlayerMatchData cachedData = playerMatchDataCache.get(fixtureId, playerId);

      if (isEntityInBetStopStatus(event, cachedData)) {
        String eventId = UUID.randomUUID().toString();
        log.info("sending BETSTART - {} for playerId={} in fixtureId={} eventId={} to kafka", event.getEvent(),
          playerId, fixtureId, eventId);
        Set<EntityMatchData.BetStopInfo> betStopsInfo = new HashSet<>(cachedData.getBetStopsInfo());
        cachedData.clearBetStops();

        MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_PLAYER);
        MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
        List<OngoingMatchData.PlayerMatchEventDTO> allPlayerEvents = ongoingMatchDataCache
          .get(fixtureId)
          .getAllPlayerEvents();
        KafkaMetadata metadata = metadataRetriever.getMetadata(playerId, allPlayerEvents, retrievalFlags);

        PlayerBetStart playerBetStart = buildPlayerBetStart(player, fixture, metadata);
        PlayerEvent playerEvent = PlayerEventFactory.fromBetStart(playerBetStart, eventId, feed.getFeedId());
        kafkaService.sendPlayerEventRating(playerEvent);

        playerMatchDataCache.set(fixtureId, playerId, cachedData);
        return betStopsInfo
          .stream()
          .map(EntityMatchData.BetStopInfo::getEventId)
          .collect(toSet());
      }
    } catch (Exception e) {
      utils.handleGenericError(feed.getFeedId(), e);
    }
    return Set.of();
  }

  private Set<String> processBetStartForTeam(MatchDataFeed feed, MatchEventDTO event, Team team) {
    String teamId = team.getId().toString();
    var fixture = feed.getFixture();
    String fixtureId = fixture.getIdAsString();

    try {
      TeamMatchData cachedData = teamMatchDataCacheService.get(fixtureId, teamId);

      if (isEntityInBetStopStatus(event, cachedData)) {
        String eventId = UUID.randomUUID().toString();
        log.info("sending BETSTART - {} for teamId={} in fixtureId={} eventId={} to kafka", event.getEvent(), teamId,
          fixtureId, eventId);
        Set<EntityMatchData.BetStopInfo> betStopsInfo = new HashSet<>(cachedData.getBetStopsInfo());
        cachedData.clearBetStops();

        MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_TEAM);
        MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
        List<OngoingMatchData.PlayerMatchEventDTO> allPlayerEvents = ongoingMatchDataCache
          .get(fixtureId)
          .getAllPlayerEvents();
        KafkaMetadata metadata = metadataRetriever.getMetadata(teamId, allPlayerEvents, retrievalFlags);

        TeamBetStart teamBetStart = buildTeamBetStart(team, fixture, metadata);
        TeamEvent teamEvent = TeamEventFactory.fromBetStart(teamBetStart, eventId, feed.getFeedId());
        kafkaService.sendTeamEventRating(teamEvent);

        teamMatchDataCacheService.set(fixtureId, teamId, cachedData);
        var teamBetStopEventIds = betStopsInfo
          .stream()
          .map(EntityMatchData.BetStopInfo::getEventId)
          .collect(toSet());

        if (!teamBetStopEventIds.isEmpty()) {
          Runnable fixtureBetstartRunnable = () -> threadPoolService.processFixtureUpdateInThreadPool(fixtureId,
            () -> sendTeamRatings(feed, team));
          betStartScheduler.schedule(fixtureBetstartRunnable, 10, TimeUnit.SECONDS);
        }
        return teamBetStopEventIds;
      }
    } catch (Exception e) {
      utils.handleGenericError(feed.getFeedId(), e);
    }
    return Set.of();
  }

  private static boolean isEntityInBetStopStatus(MatchEventDTO event, EntityMatchData cachedData) {
    return cachedData.isBetStop() && event
      .getTimestamp()
      .isAfter(cachedData.getBetStopsInfo()
        .last().getTimestamp());
  }

  private static String buildFeedId(String entityId, String fixtureId, Instant now) {
    return format("betstart-%s-%s-%s", fixtureId, entityId, now.toEpochMilli());
  }

  private static PlayerBetStart buildPlayerBetStart(com.wsf.domain.common.Player player,
                                                    com.wsf.domain.common.Fixture fixture,
                                                    KafkaMetadata kafkaMetadata) {
    return new PlayerBetStart(buildPlayer(player), buildLiveMatch(fixture), kafkaMetadata, now(), "");
  }

  private static TeamBetStart buildTeamBetStart(com.wsf.domain.common.Team team, com.wsf.domain.common.Fixture fixture,
                                                KafkaMetadata kafkaMetadata) {
    return new TeamBetStart(buildTeam(team, fixture.getTournament().getId().toString()), buildLiveMatch(fixture),
      kafkaMetadata, now(), "");
  }

}
