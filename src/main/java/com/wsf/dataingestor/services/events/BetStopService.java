package com.wsf.dataingestor.services.events;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.BaseEntityMatchData;
import com.wsf.dataingestor.cache.models.EntityMatchData.BetStopInfo;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.modules.metadata.MetadataRetriever;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.PlayerBetStop;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.TeamBetStop;
import com.wsf.kafka.domain.TeamEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.kafka.KafkaService.buildLiveMatch;
import static com.wsf.dataingestor.kafka.KafkaService.buildPlayer;
import static com.wsf.dataingestor.kafka.KafkaService.buildTeam;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.services.ThreadPoolService.getPlayersUpdatesKey;
import static com.wsf.dataingestor.services.events.Utils.isOptaFeedAndCompetitionSupported;
import static com.wsf.dataingestor.services.events.Utils.isRunningballFeedAndCompetitionSupported;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class BetStopService {

  private final OngoingMatchDataCacheService ongoingMatchDataCache;
  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final TeamMatchDataCacheService teamMatchDataCache;
  private final LiveMatchUtils liveMatchUtils;
  private final KafkaService kafkaService;
  private final ThreadPoolService threadPoolService;
  private final Utils utils;

  public void processBetStop(MatchDataFeed feed, MatchEventDTO event) {
    String feedId = feed.getFeedId();
    Fixture fixture = feed.getFixture();
    if (isRunningballFeedAndCompetitionSupported(feed, fixture) || isOptaFeedAndCompetitionSupported(feed, fixture)) {
      String fixtureId = fixture.getId().toString();
      OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixtureId);

      if (isNull(ongoingMatchData)) {
        log.warn("fixtureId={} non present in the cache event={}", fixtureId, event.getEvent());
        return;
      }

      if (fixture.getStatus() != Fixture.FixtureStatus.LIVE) {
        log.warn("fixtureId={} is not live, not sending the betstop", fixtureId);
        return;
      }

      if (ongoingMatchData.getProcessedBetStopEventIds()
        .contains(event.getEventId())) {
        log.warn("BETSTOP event={} for fixtureId={} has already been processed, ignoring it", event.getEventId(),
          fixtureId);
        return;
      }

      MatchPeriod currentMatchPeriod = ongoingMatchData.getMatchPeriod();
      if (isMatchInPeriodBreak(currentMatchPeriod)) {
        log.warn("BETSTOP event={} for fixtureId={} during period break={}, ignoring it", event.getEventId(), fixtureId,
          currentMatchPeriod);
        return;
      }

      if (isBetStopBeforeLatestBetStart(ongoingMatchData, event)) {
        log.warn("BETSTOP event={} with timestamp={} for fixtureId={} is past the latest betstart timestamp={}",
          event.getEventId(), event.getTimestamp(), fixtureId, ongoingMatchData.getLatestBetStartTimestamp());
        return;
      }

      log.info("processing BETSTOP - {} for fixtureId={}", event.getEvent(), fixtureId);
      processPlayersBetStop(feedId, fixture, ongoingMatchData, event);
      processTeamsBetStop(feed, event);
    } else {
      log.debug("feedId={} ignored for betstop: provider {}, competitionId={}", feedId, feed.getProvider(),
        fixture.getTournament().getCompetitionId());
    }
  }

  public void processBetStopOnlyTeams(MatchDataFeed feed, MatchEventDTO event) {
    String feedId = feed.getFeedId();
    Fixture fixture = feed.getFixture();
    if (isRunningballFeedAndCompetitionSupported(feed, fixture) || isOptaFeedAndCompetitionSupported(feed, fixture)) {
      String fixtureId = feed.getFixture().getId().toString();
      OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixtureId);

      if (ongoingMatchData.getProcessedBetStopEventIds()
        .contains(event.getEventId())) {
        log.warn("BETSTOP event={} for fixtureId={} has already been processed, ignoring it", event.getEventId(),
          fixtureId);
        return;
      }

      MatchPeriod currentMatchPeriod = ongoingMatchData.getMatchPeriod();
      if (isMatchInPeriodBreak(currentMatchPeriod)) {
        log.warn("BETSTOP event={} for fixtureId={} during period break={}, ignoring it", event.getEventId(), fixtureId,
          currentMatchPeriod);
        return;
      }

      if (isBetStopBeforeLatestBetStart(ongoingMatchData, event)) {
        log.warn("BETSTOP event={} with timestamp={} for fixtureId={} is past the latest betstart timestamp={}",
          event.getEventId(), fixtureId, event.getTimestamp(), ongoingMatchData.getLatestBetStartTimestamp());
        return;
      }

      log.info("processing BETSTOP - {} for fixtureId={}", event.getEvent(), fixtureId);
      processTeamsBetStop(feed, event);
    } else {
      log.debug("feedId={} ignored for betstop: provider={}, competitionId={}", feedId, feed.getProvider(),
        fixture.getTournament().getCompetitionId());
    }
  }

  public void processLiveCoverageCancelledBetStop(Fixture fixture, MatchEventDTO event, String feedId) {
    OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixture.getIdAsString());

    if (isNull(ongoingMatchData)) {
      log.warn("OngoingMatchData not found in cache for fixtureId={}, skipping bet stops for live coverage cancelled",
        fixture.getIdAsString());
      return;
    }
    processPlayersBetStop(feedId, fixture, ongoingMatchData, event);
    processBetStopForTeam(fixture, event, fixture.getHomeTeam(), feedId);
    processBetStopForTeam(fixture, event, fixture.getAwayTeam(), feedId);
  }

  private void processTeamsBetStop(MatchDataFeed feed, MatchEventDTO event) {
    String feedId = feed.getFeedId();
    Fixture fixture = feed.getFixture();
    processBetStopForTeam(fixture, event, fixture.getHomeTeam(), feedId);
    processBetStopForTeam(fixture, event, fixture.getAwayTeam(), feedId);
  }

  private void processPlayersBetStop(String feedId, Fixture fixture, OngoingMatchData ongoingMatchData,
                                     MatchEventDTO event) {
    liveMatchUtils.findPlayersOnThePitch(fixture, ongoingMatchData)
      .forEach(player -> {
        var key = getPlayersUpdatesKey(fixture, player.getIdAsString());
        threadPoolService.processPlayersUpdateInThreadPool(key,
          () -> processBetStopForPlayer(fixture, event, player, feedId));
      });
  }

  private void processBetStopForPlayer(Fixture fixture, MatchEventDTO event, Player player, String feedId) {
    String playerId = player.getId().toString();
    String fixtureId = fixture.getIdAsString();

    try {
      PlayerMatchData cachedData = playerMatchDataCache.get(fixtureId, playerId);

      if (doesBetStopEventAlreadyExist(cachedData, event)) {
        log.warn("playerId={} in fixtureId={} is already in betstop for the same event={}: {}", playerId, fixtureId,
          event, cachedData);
        return;
      }
      String eventId = UUID.randomUUID().toString();
      log.info("sending BETSTOP - {} for playerId={} in fixtureId={} eventId={} to kafka", event.getEvent(), playerId,
        fixtureId, eventId);

      MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_PLAYER);
      MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);
      List<OngoingMatchData.PlayerMatchEventDTO> allPlayerEvents = ongoingMatchDataCache
        .get(fixtureId)
        .getAllPlayerEvents();
      KafkaMetadata metadata = metadataRetriever.getMetadata(playerId, allPlayerEvents, retrievalFlags);

      PlayerBetStop playerBetStop = buildPlayerBetStop(player, fixture, metadata, event.getEvent().toString());
      PlayerEvent playerEvent = PlayerEventFactory.fromBetStop(playerBetStop, eventId, feedId);
      kafkaService.sendPlayerEventRating(playerEvent);

      updatePlayerCacheWithBetStop(playerId, fixtureId, cachedData, event);
    } catch (Exception e) {
      utils.handleGenericError(feedId, e);
    }
  }

  private void processBetStopForTeam(Fixture fixture, MatchEventDTO event, Team team, String feedId) {
    String teamId = team.getId().toString();
    String fixtureId = fixture.getIdAsString();

    try {
      TeamMatchData cachedData = teamMatchDataCache.get(fixtureId, teamId);

      if (doesBetStopEventAlreadyExist(cachedData, event)) {
        log.warn("teamId={} in fixtureId={} is already in betstop for the same event={}: {}", teamId, fixtureId, event,
          cachedData);
        return;
      }
      String eventId = UUID.randomUUID().toString();
      log.info("sending BETSTOP - {} for teamId={} in fixtureId={} eventId={} to kafka", event.getEvent(), teamId,
        fixtureId, eventId);

      MetadataRetriever metadataRetriever = getMetadataRetriever(fixture, SOCCER_TEAM);
      MetadataRetrievalFlags retrievalFlags = new MetadataRetrievalFlags(false, false);

      List<OngoingMatchData.PlayerMatchEventDTO> allPlayerEvents = ongoingMatchDataCache
        .get(fixtureId)
        .getAllPlayerEvents();

      KafkaMetadata metadata = metadataRetriever.getMetadata(teamId, allPlayerEvents, retrievalFlags);

      TeamBetStop teamBetStop = buildTeamBetStop(team, fixture, metadata, event.getEvent().toString());
      TeamEvent teamEvent = TeamEventFactory.fromBetStop(teamBetStop, eventId, feedId);
      kafkaService.sendTeamEventRating(teamEvent);

      updateTeamCacheWithBetStop(teamId, fixtureId, cachedData, event);
    } catch (Exception e) {
      utils.handleGenericError(feedId, e);
    }
  }

  private void updatePlayerCacheWithBetStop(String playerId, String fixtureId, PlayerMatchData cachedData,
                                            MatchEventDTO event) {
    cachedData.addBetStop(BetStopInfo.of(event.getEvent().toString(), event.getEventId(), event.getTimestamp()));
    playerMatchDataCache.set(fixtureId, playerId, cachedData);
  }

  private void updateTeamCacheWithBetStop(String teamId, String fixtureId, TeamMatchData cachedData,
                                          MatchEventDTO event) {
    cachedData.addBetStop(BetStopInfo.of(event.getEvent().toString(), event.getEventId(), event.getTimestamp()));
    teamMatchDataCache.set(fixtureId, teamId, cachedData);
  }

  private static boolean doesBetStopEventAlreadyExist(BaseEntityMatchData cachedData, MatchEventDTO event) {
    return cachedData.getBetStopsInfo()
      .stream()
      .anyMatch(betStopInfo -> betStopInfo.getEventId().equals(event.getEventId()));
  }

  private static boolean isBetStopBeforeLatestBetStart(OngoingMatchData ongoingMatchData, MatchEventDTO event) {
    return nonNull(ongoingMatchData.getLatestBetStartTimestamp()) &&
      event.getTimestamp().isBefore(ongoingMatchData.getLatestBetStartTimestamp());
  }

  private boolean isMatchInPeriodBreak(MatchPeriod matchPeriod) {
    return switch (matchPeriod) {
      case HALF_TIME, EXTRA_HALF_TIME, END_REGULAR_TIMES -> true;
      default -> false;
    };
  }

  private static PlayerBetStop buildPlayerBetStop(com.wsf.domain.common.Player player,
                                                  com.wsf.domain.common.Fixture fixture, KafkaMetadata kafkaMetadata,
                                                  String event) {
    return new PlayerBetStop(buildPlayer(player), buildLiveMatch(fixture), kafkaMetadata, now(), event);
  }

  private static TeamBetStop buildTeamBetStop(com.wsf.domain.common.Team team, com.wsf.domain.common.Fixture fixture,
                                              KafkaMetadata kafkaMetadata, String event) {
    return new TeamBetStop(buildTeam(team, fixture.getTournament().getId().toString()), buildLiveMatch(fixture),
      kafkaMetadata, now(), event);
  }
}
