package com.wsf.dataingestor.services.events;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.ratings.FixtureStartDataProcessor;
import com.wsf.dataingestor.services.ratings.LineUpService;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
public class MatchEventsProcessor {

  private final Map<SoccerMatchEvent, BiConsumer<EventInfo, OngoingMatchData>> eventsToConsumer;
  private final BetStopService betStopService;
  private final BetStartService betStartService;
  private final LineUpService lineUpService;
  private final LiveFixtureSummaryNotifier liveFixtureSummaryNotifier;
  private final FixtureStartDataProcessor fixtureStartDataProcessor;
  private final ThreadPoolService threadPoolService;
  private final MatchLocker matchLocker;

  @Autowired
  public MatchEventsProcessor(BetStopService betStopService, BetStartService betStartService,
                              LineUpService lineUpService, LiveFixtureSummaryNotifier liveFixtureSummaryNotifier,
                              FixtureStartDataProcessor fixtureStartDataProcessor, ThreadPoolService threadPoolService,
                              MatchLocker matchLocker) {
    this.betStopService = betStopService;
    this.betStartService = betStartService;
    this.lineUpService = lineUpService;
    this.liveFixtureSummaryNotifier = liveFixtureSummaryNotifier;
    this.fixtureStartDataProcessor = fixtureStartDataProcessor;
    this.threadPoolService = threadPoolService;
    this.matchLocker = matchLocker;
    this.eventsToConsumer = buildEventToConsumer();
  }

  public void processMatchEvents(MatchDataFeed matchDataFeed, OngoingMatchData cachedData) {
    matchDataFeed.getMatchEvents()
      .stream()
      .filter(matchEvent -> eventsToConsumer.containsKey(matchEvent.getEvent()))
      .forEach(event -> eventsToConsumer.get(event.getEvent()).accept(new EventInfo(matchDataFeed, event), cachedData));
  }

  private void processMatchAboutToStart(EventInfo eventInfo, OngoingMatchData cachedData) {
    var fixture = eventInfo.feed().getFixture();
    fixtureStartDataProcessor.processFeed(fixture, cachedData);
  }

  private void processBetStop(EventInfo eventInfo, OngoingMatchData cachedData) {
    threadPoolService.processFixtureUpdateInThreadPool(cachedData.getMatchId(), () -> {
      betStopService.processBetStop(eventInfo.feed(), eventInfo.event());
    });
  }

  private void processTeamsBetStop(EventInfo eventInfo, OngoingMatchData cachedData) {
    threadPoolService.processFixtureUpdateInThreadPool(cachedData.getMatchId(), () -> {
      betStopService.processBetStopOnlyTeams(eventInfo.feed(), eventInfo.event());
    });
  }

  private void processBetStart(EventInfo eventInfo, OngoingMatchData cachedData) {
    threadPoolService.processFixtureUpdateInThreadPool(cachedData.getMatchId(), () -> {
      betStartService.processBetStart(eventInfo.feed(), eventInfo.event());
    });
  }

  private void processLineUps(EventInfo eventInfo, OngoingMatchData cachedMatchData) {
    String teamId = eventInfo.event().getTeamId();
    // we only want to store lineups from the ma18dp if they were not stored before match start by the FixtureStartDataProcessor class
    // the reason for this is that lineups sent by ma18dp are not reliable and most of the time outdated
    if (!cachedMatchData.getTeamIdToLineUp().containsKey(teamId)) {
      Fixture fixture = eventInfo.feed().getFixture();
      String fixtureId = fixture.getIdAsString();
      log.info("processing LINEUPS event={} for fixtureId={} and teamId={}", eventInfo.event(), fixtureId, teamId);
      updateLineUpInCache(eventInfo, fixture, cachedMatchData);
      lineUpService.updatePlayersPosition(eventInfo.feed());
      liveFixtureSummaryNotifier.sendLiveSummary(cachedMatchData);
    }
  }

  private void processPossibleVar(EventInfo eventInfo, OngoingMatchData cachedData) {
    matchLocker.lockMatch(cachedData.getMatchId());
    processBetStop(eventInfo, cachedData);
  }

  private void processVarCheckStart(EventInfo eventInfo, OngoingMatchData cachedData) {
    matchLocker.unlockMatch(cachedData.getMatchId());
    processBetStop(eventInfo, cachedData);
  }

  private void processNoVarCheck(EventInfo eventInfo, OngoingMatchData cachedData) {
    matchLocker.unlockMatch(cachedData.getMatchId());
  }

  private void updateLineUpInCache(EventInfo eventInfo, Fixture fixture, OngoingMatchData cachedMatchData) {
    String fixtureId = fixture.getIdAsString();
    String teamId = eventInfo.event().getTeamId();
    Set<Player> lineUpPlayers = LineUpService.retrievePlayers(eventInfo.feed());
    Set<String> teamPlayersIds = lineUpPlayers
      .stream()
      .filter(player -> player.getTeam().getIdAsString().equals(teamId))
      .map(Player::getIdAsString)
      .collect(toSet());
    cachedMatchData.getTeamIdToLineUp().put(teamId, teamPlayersIds);
    log.info("lineup for fixtureId={} teamId={} updated in the cache: {}", fixtureId, teamId, teamPlayersIds);
  }

  private Map<SoccerMatchEvent, BiConsumer<EventInfo, OngoingMatchData>> buildEventToConsumer() {
    Map<SoccerMatchEvent, BiConsumer<EventInfo, OngoingMatchData>> eventsToConsumer = newHashMap();
    eventsToConsumer.put(SoccerMatchEvent.BET_STOP, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.LIVE_COVERAGE_CANCELLED, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.GOAL, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.FOUL, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.CARD, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.PENALTY, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.BREAKAWAY, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.DANGEROUS_FREEKICK, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.POSSIBLE_CORNER, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.GOALKICK, this::processTeamsBetStop);
    eventsToConsumer.put(SoccerMatchEvent.SHOT, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.OFFSIDE, this::processTeamsBetStop);

    eventsToConsumer.put(SoccerMatchEvent.POSSIBLE_VAR, this::processPossibleVar);
    eventsToConsumer.put(SoccerMatchEvent.VAR_CHECK_START, this::processVarCheckStart);
    eventsToConsumer.put(SoccerMatchEvent.NO_VAR_CHECK, this::processNoVarCheck);

    eventsToConsumer.put(SoccerMatchEvent.BET_START, this::processBetStart);
    eventsToConsumer.put(SoccerMatchEvent.RESTART, this::processBetStart);
    eventsToConsumer.put(SoccerMatchEvent.KICKOFF, this::processBetStart);
    eventsToConsumer.put(SoccerMatchEvent.SAFE, this::processBetStart);
    eventsToConsumer.put(SoccerMatchEvent.NO_PENALTY, this::processBetStart);

    eventsToConsumer.put(SoccerMatchEvent.MATCH_ABOUT_TO_START, this::processMatchAboutToStart);
    eventsToConsumer.put(SoccerMatchEvent.LINEUPS_AVAILABLE, this::processLineUps);
    eventsToConsumer.put(SoccerMatchEvent.MATCH_FINISHED, this::processBetStop);
    eventsToConsumer.put(SoccerMatchEvent.MATCH_SUSPENDED, this::processBetStop);

    return eventsToConsumer;
  }

  public record EventInfo(MatchDataFeed feed, MatchEventDTO event) {}

}
