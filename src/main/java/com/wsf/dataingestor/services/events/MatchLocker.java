package com.wsf.dataingestor.services.events;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import static com.wsf.dataingestor.config.cache.CacheConstants.MATCH_LOCKS_CACHE_NAME;

@Slf4j
@Service
public class MatchLocker {

  @CachePut(cacheNames = MATCH_LOCKS_CACHE_NAME)
  public boolean lockMatch(String fixtureId) {
    log.info("Locking fixtureId={}", fixtureId);
    return true;
  }

  @CachePut(cacheNames = MATCH_LOCKS_CACHE_NAME)
  public boolean unlockMatch(String fixtureId) {
    log.info("Unlocking fixtureId={}", fixtureId);
    return false;
  }

  @Cacheable(cacheNames = MATCH_LOCKS_CACHE_NAME)
  public boolean isMatchLocked(String fixtureId) {
    log.info("Match lock info not present for fixtureId={}", fixtureId);
    return false;
  }
}
