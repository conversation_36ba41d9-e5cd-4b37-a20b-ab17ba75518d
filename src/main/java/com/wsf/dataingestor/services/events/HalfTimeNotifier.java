package com.wsf.dataingestor.services.events;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.UUID;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.modules.metadata.MetadataRetrievalFlags;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.ratings.Utils;

import static com.wsf.dataingestor.modules.metadata.MetadataRetrieverFactory.getMetadataRetriever;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;

@Service
@AllArgsConstructor
@Slf4j
public class HalfTimeNotifier {
  public static final String HALF_TIME_PREFIX = "half-time";
  private final LiveMatchUtils liveMatchUtils;
  private final Utils utils;
  private final PlayerMatchDataCacheService playerMatchDataCache;
  private final KafkaService kafkaService;

  public void notifyHalfTime(MatchDataFeed feed, OngoingMatchData cachedMatchData) {
    var fixture = feed.getFixture();
    String fixtureId = fixture.getIdAsString();

    var sportType = fixture.getTournament().getCompetition().getSport().getType();
    var metadataRetriever = getMetadataRetriever(sportType, ContestantType.SOCCER_PLAYER);

    liveMatchUtils.findPlayersOnThePitch(fixture, cachedMatchData)
      .stream()
      .map(player -> {
        Instant now = Instant.now();
        String playerId = player.getIdAsString();
        var playerRatingEventId = UUID.randomUUID().toString();
        String playerRatingFeedId = buildFeedId(HALF_TIME_PREFIX, playerId, abs(feed.hashCode()), now.toEpochMilli());
        var playerMatchData = playerMatchDataCache.get(fixtureId, playerId);

        return utils.buildPlayerRating(fixture, player, false, playerMatchData.getStats(),
          cachedMatchData.getMatchPeriod().getPeriodId(), cachedMatchData.getMatchTime(), playerRatingEventId,
          playerRatingFeedId, now);
      })
      .peek(playerRating -> log.info("sending update at half time for playerId={} in fixtureId={} eventId={}",
        playerRating.getPlayer().getIdAsString(), fixtureId, playerRating.getEventId()))
      .forEach(playerRating -> {
        var metadata = metadataRetriever.getMetadata(playerRating.getPlayer().getIdAsString(),
          cachedMatchData.getAllPlayerEvents(), new MetadataRetrievalFlags(false, false));
        kafkaService.sendLivePlayerRating(playerRating, metadata);
      });
  }

}
