package com.wsf.dataingestor.services.events;

import com.wsf.kafka.domain.EventInfo;
import com.wsf.kafka.domain.TeamBetStart;
import com.wsf.kafka.domain.TeamBetStop;
import com.wsf.kafka.domain.TeamEvent;
import com.wsf.kafka.domain.TeamMatchStart;

import static java.time.Instant.now;

public class TeamEventFactory {
  public static TeamEvent fromBetStart(TeamBetStart teamBetStart, String eventId, String feedId) {
    return new TeamEvent(teamBetStart, null, null, null, null, null, null, buildEventInfo(eventId, feedId));
  }

  public static TeamEvent fromBetStop(TeamBetStop teamBetStop, String eventId, String feedId) {
    return new TeamEvent(null, teamBetStop, null, null, null, null, null, buildEventInfo(eventId, feedId));
  }

  public static TeamEvent fromTeamMatchStart(TeamMatchStart teamMatchStart, String eventId, String feedId) {
    return new TeamEvent(null, null, null, null, teamMatchStart, null, null, buildEventInfo(eventId, feedId));
  }

  private static EventInfo buildEventInfo(String eventId, String feedId) {
    return new EventInfo(now(), now(), eventId, feedId);
  }
}
