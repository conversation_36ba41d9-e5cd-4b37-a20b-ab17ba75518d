package com.wsf.dataingestor.services.events;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Provider;

import static com.wsf.domain.soccer.SoccerMatchEvent.LIVE_COVERAGE_CANCELLED;
import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiveCoverageService {
  private final FixtureService fixtureService;
  private final BetStopService betStopService;

  public void suspendLiveCoverageForFixture(Fixture fixture, MatchEventDTO matchEventDTO, String feedId) {
    Fixture updatedFixture = fixtureService.storeFixtureAsLiveCancelled(fixture, Provider.RUNNINGBALL);
    log.info("Fixture {} is not live enabled anymore - Live Coverage Canceled, suspending Odds",
      updatedFixture.getIdAsString());
    betStopService.processLiveCoverageCancelledBetStop(fixture, toLiveCoverageCancelled(matchEventDTO), feedId);
  }

  public Fixture cancelLiveCoverageForFixture(Fixture fixture) {
    Fixture updatedFixture = fixtureService.storeFixtureAsLiveCancelled(fixture, Provider.RUNNINGBALL);
    log.info("Fixture {} is not live enabled anymore - Live Coverage Canceled", updatedFixture.getIdAsString());
    return updatedFixture;
  }

  private static MatchEventDTO toLiveCoverageCancelled(MatchEventDTO matchEventDTO) {
    String eventId = ofNullable(matchEventDTO)
      .map(MatchEventDTO::getEventId)
      .orElse(null);
    String teamId = ofNullable(matchEventDTO)
      .map(MatchEventDTO::getTeamId)
      .orElse(null);
    Instant timestamp = ofNullable(matchEventDTO)
      .map(MatchEventDTO::getTimestamp)
      .orElse(Instant.now());
    return new MatchEventDTO(eventId, LIVE_COVERAGE_CANCELLED, teamId, timestamp);
  }
}
