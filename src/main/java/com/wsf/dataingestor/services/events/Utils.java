package com.wsf.dataingestor.services.events;

import java.util.Set;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.Fixture;

public class Utils {

  public static final String CARABAO_CUP_COMPETITION_ID = "6538f2889ad49ebcbd92e063";
  public static final String COMMUNITY_SHIELD_COMPETITION_ID = "64b7f0695a597fb44158d8f6";
  public static final String PREMIER_LEAGUE_COMPETITION_ID = "5d3a33fe999fc4072ab589b1";
  public static final Set<String> OPTA_BETSTOP_SUPPORTED_COMPETITIONS = Set.of(CARABAO_CUP_COMPETITION_ID,
    PREMIER_LEAGUE_COMPETITION_ID, COMMUNITY_SHIELD_COMPETITION_ID);

  //  in case we want to test the opta betstop for ligue1 in sandbox
  //  public static final String PREMIER_LEAGUE_COMPETITION_ID = "60461381dc15001e84d80b92";

  public static boolean isOptaFeedAndCompetitionSupported(MatchDataFeed feed, Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    return feed.getProvider() == MatchDataFeed.FeedProvider.OPTA &&
      OPTA_BETSTOP_SUPPORTED_COMPETITIONS.contains(competitionId);
  }

  public static boolean isRunningballFeedAndCompetitionSupported(MatchDataFeed feed, Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    return feed.getProvider() == MatchDataFeed.FeedProvider.RUNNINGBALL &&
      !OPTA_BETSTOP_SUPPORTED_COMPETITIONS.contains(competitionId);
  }

  public static boolean isRunningBallSupported(Fixture fixture) {
    return !OPTA_BETSTOP_SUPPORTED_COMPETITIONS.contains(fixture.getTournament().getCompetitionId());
  }
}
