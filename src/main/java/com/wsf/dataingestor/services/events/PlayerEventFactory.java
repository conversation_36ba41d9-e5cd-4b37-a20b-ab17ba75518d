package com.wsf.dataingestor.services.events;

import com.wsf.kafka.domain.EventInfo;
import com.wsf.kafka.domain.PlayerBetStart;
import com.wsf.kafka.domain.PlayerBetStop;
import com.wsf.kafka.domain.PlayerChangedTeam;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.PlayerLineUp;

import static java.time.Instant.now;

public class PlayerEventFactory {
  public static PlayerEvent fromBetStart(PlayerBetStart playerBetStart, String eventId, String feedId) {
    return new PlayerEvent(null, null, null, null, playerBetStart, null, null, null, buildEventInfo(eventId, feedId));
  }

  public static PlayerEvent fromBetStop(PlayerBetStop playerBetStop, String eventId, String feedId) {
    return new PlayerEvent(null, null, null, playerBetStop, null, null, null, null, buildEventInfo(eventId, feedId));
  }

  public static PlayerEvent fromPlayerChangedTeam(PlayerChangedTeam playerChangedTeam, String eventId, String feedId) {
    return new PlayerEvent(null, null, null, null, null, null, null, playerChangedTeam,
      buildEventInfo(eventId, feedId));
  }

  public static PlayerEvent fromPlayerLineUp(PlayerLineUp PlayerLineUp, String eventId, String feedId) {
    return new PlayerEvent(null, null, PlayerLineUp, null, null, null, null, null, buildEventInfo(eventId, feedId));
  }

  private static EventInfo buildEventInfo(String eventId, String feedId) {
    return new EventInfo(now(), now(), eventId, feedId);
  }
}
