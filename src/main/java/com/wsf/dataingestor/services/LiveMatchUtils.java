package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static org.apache.commons.collections4.ListUtils.union;

@Service
@RequiredArgsConstructor
public class LiveMatchUtils {

  private final PlayerService playerService;

  public List<Player> findPlayersOnThePitch(Fixture fixture, OngoingMatchData ongoingMatchData) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getId().toString();
    String homeTeamId = fixture.getHomeTeam().getId().toString();
    String awayTeamId = fixture.getAwayTeam().getId().toString();

    if (isNull(ongoingMatchData)) {
      return emptyList();
    }

    Set<String> lineupPlayerIds = ongoingMatchData.getLineupPlayerIds();

    List<Player> allHomeTeamPlayers = playerService.getActivePlayersByTeamIdAndTournamentId(competitionId, homeTeamId,
      tournamentId);
    List<Player> allAwayTeamPlayers = playerService.getActivePlayersByTeamIdAndTournamentId(competitionId, awayTeamId,
      tournamentId);
    return union(allHomeTeamPlayers, allAwayTeamPlayers)
      .stream()
      .filter(p -> lineupPlayerIds.contains(p.getId().toString()))
      .collect(Collectors.toList());
  }
}
