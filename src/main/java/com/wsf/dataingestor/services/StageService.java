package com.wsf.dataingestor.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Stage;
import com.wsf.repository.common.StageRepository;

@Slf4j
@RequiredArgsConstructor
@Service
public class StageService {

  private final StageRepository stageRepository;
  private final UnmappedEntityClient unmappedEntityClient;

  public Stage findBySportmonksStageNameOrCreateUnmapped(String sportmonksStageId, String sportmonksStageName,
                                                         String competitionId, ExternalProvider provider) {
    return stageRepository.findBySportmonksStageNameAndCompetitionId(sportmonksStageName, competitionId)
      .orElseGet(() -> {
        createUnmappedStage(sportmonksStageId, sportmonksStageName, competitionId, provider);
        return null;
      });
  }

  private void createUnmappedStage(String externalStageId, String externalStageName, String competitionId,
                                   ExternalProvider provider) {
    log.info("Creating unmapped stage {} for competitionId {}", externalStageName, competitionId);
    unmappedEntityClient.createUnmappedStage(externalStageName, externalStageId, competitionId, provider);
  }
}