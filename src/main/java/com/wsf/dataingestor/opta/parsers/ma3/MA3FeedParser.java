package com.wsf.dataingestor.opta.parsers.ma3;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.opta.models.ma3.MA3Feed;
import com.wsf.dataingestor.opta.models.ma3.MA3Feed.MatchInfo;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;


@Slf4j
public class MA3FeedParser implements FeedParser<MatchEventsFeed> {
  private static final String FEED_CODE = "ma3";
  private final ObjectReader reader;
  private final OptaFeedParserUtils optaFeedParserUtils;
  private final MA3EventsMapper eventsMapper;

  public MA3FeedParser(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils,
                       MA3EventsMapper eventsMapper) {
    this.reader = jsonObjectMapper.readerFor(MA3Feed.class);
    this.optaFeedParserUtils = optaFeedParserUtils;
    this.eventsMapper = eventsMapper;
  }

  @Override
  public MatchEventsFeed parseFeed(byte[] feed) throws IOException {
    if (log.isTraceEnabled()) {
      log.trace("Parsing MA3 feed: {}", new String(feed));
    }
    Instant ts = Instant.now();
    MA3Feed matchStats = reader.readValue(feed);
    return parseFeed(matchStats, ts);
  }

  @Override
  public MatchEventsFeed parseFeed(String feed) throws IOException {
    log.trace("Parsing MA3 feed: {}", feed);
    Instant ts = Instant.now();
    MA3Feed matchStats = reader.readValue(feed);
    return parseFeed(matchStats, ts);
  }

  private MatchEventsFeed parseFeed(MA3Feed matchEvents, Instant receivedTs) {
    try {
      MatchInfo matchInfo = matchEvents.getMatchInfo();
      MA3Feed.LiveData liveData = matchEvents.getLiveData();

      String matchExternalId = matchInfo.getId();
      Fixture fixture = optaFeedParserUtils.getFixtureOrThrow(matchExternalId);

      String feedId = buildFeedId(FEED_CODE, matchExternalId, abs(matchEvents.hashCode()), receivedTs.toEpochMilli());

      Instant lastUpdatedTs = matchInfo.getLastUpdated();

      var events = liveData.getEvents()
        .stream()
        .filter(event -> nonNull(event.getPlayerId()))
        .collect(groupingBy(MA3Feed.Event::getPlayerId)).entrySet()
        .stream()
        .flatMap(entry -> {
          String optaPlayerId = entry.getKey();
          List<MA3Feed.Event> playerEvents = entry.getValue();
          Player player = optaFeedParserUtils.getPlayer(optaPlayerId, fixture);

          String playerId;
          String teamId;
          boolean isPlayerUnknown = false;

          if (isNull(player)) {
            isPlayerUnknown = true;
            playerId = format("UNKNOWN_PLAYER_OPTA_%s", optaPlayerId);

            String optaTeamId = playerEvents.get(0).getContestantId();
            Team team = optaFeedParserUtils.getTeam(fixture.getTournament().getCompetitionId(), optaTeamId);
            teamId = team.getIdAsString();
            log.error("could not find a match for player with optaId={}. Replacing with a fake id {}", optaPlayerId,
              playerId);
          } else {
            playerId = player.getIdAsString();
            teamId = player.getTeam().getId().toString();
          }
          return eventsMapper.translatePlayerEvents(playerId, teamId, isPlayerUnknown, playerEvents)
            .stream();
        })
        .collect(toList());

      Set<SoccerMatchEvent> supportedEventTypes = eventsMapper.getSupportedEventTypes();

      return MatchEventsFeed
        .builder()
        .receivedTs(receivedTs)
        .latestUpdateTs(lastUpdatedTs)
        .fixture(fixture)
        .feedId(feedId)
        .events(events)
        .supportedEventTypes(supportedEventTypes)
        .build();

    } catch (IllegalStateException e) {
      log.error("Can't process the feed", e);
      throw e;
    }
  }
}
