package com.wsf.dataingestor.opta.parsers.ma3;

import java.util.Set;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.wsf.dataingestor.opta.parsers.events.EntityEventFilter;

import static com.wsf.dataingestor.opta.parsers.events.EventInfo.defaultQualifierBuilder;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_FOUL;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_MISSED_SHOT;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_OFFSIDE;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_PASS;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_SHOT_ON_POST;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_SHOT_SAVED;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TACKLE;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_BLOCKED_SHOT;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_CORNER_TAKEN;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_DIRECT_CORNER_ATTEMPT_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_GOAL_KICK;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_OWN_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_SAVED_OFF_LINE;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;

public class MA3ValidEvents {
  public static final Multimap<Integer, EntityEventFilter> eventTypeIdToFilters = ArrayListMultimap.create();

  static {
    var goalKickEventFilter = EntityEventFilter
      .builder().stat(GOALKICK).qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_GOAL_KICK)).build();
    var shotsEventFilter = EntityEventFilter
      .builder().stat(SHOT).qualifiersIdToExclude(Set.of(QUALIFIER_ID_OWN_GOAL)).build();
    var cornersWithPassEventFilterForCorners = EntityEventFilter
      .builder()
      .stat(CORNER)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_CORNER_TAKEN))
      .build();
    var goalFromCornerEventFilterForCorners = EntityEventFilter
      .builder()
      .stat(CORNER)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_DIRECT_CORNER_ATTEMPT_GOAL))
      .build();
    var goalToShotsOnGoalEventFilter = EntityEventFilter
      .builder().stat(SHOT_ON_GOAL).qualifiersIdToExclude(Set.of(QUALIFIER_ID_OWN_GOAL)).build();
    var clearedOffTheLineToShotsOnGoalEventFilter = EntityEventFilter
      .builder()
      .stat(SHOT_ON_GOAL)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_SAVED_OFF_LINE))
      .build();
    var shotsSavedToShotsOnGoalEventFilter = EntityEventFilter
      .builder().stat(SHOT_ON_GOAL).qualifiersIdToExclude(Set.of(QUALIFIER_ID_BLOCKED_SHOT)).build();
    var offsideEventFilter = EntityEventFilter
      .builder()
      .stat(OFFSIDE)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE))
      .build();
    var foulEventFilter = EntityEventFilter
      .builder().stat(FOUL).outcomeHasToBeZero(true).build();

    EntityEventFilter tacklesWonEventFilter = EntityEventFilter
      .builder().stat(TACKLE_WON).build();

    eventTypeIdToFilters.put(EVENT_ID_PLAYER_PASS,
      goalKickEventFilter); // this won't work in sandbox PSG-STR since Opta introduced it in 2023
    eventTypeIdToFilters.put(EVENT_ID_MISSED_SHOT, shotsEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_SHOT_ON_POST, shotsEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, shotsEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_GOAL, shotsEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, shotsSavedToShotsOnGoalEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, clearedOffTheLineToShotsOnGoalEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_GOAL, goalToShotsOnGoalEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_PLAYER_PASS, cornersWithPassEventFilterForCorners);
    eventTypeIdToFilters.put(EVENT_ID_GOAL, goalFromCornerEventFilterForCorners);
    eventTypeIdToFilters.put(EVENT_ID_OFFSIDE, offsideEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_FOUL, foulEventFilter);
    eventTypeIdToFilters.put(EVENT_ID_TACKLE, tacklesWonEventFilter);
  }
}
