package com.wsf.dataingestor.opta.parsers;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed;
import com.wsf.domain.common.Player;

import static com.google.common.collect.ImmutableList.of;
import static com.google.common.collect.Maps.newHashMap;
import static java.lang.String.format;
import static java.util.Objects.isNull;

@Slf4j
public class OptaPositionUtils {

  private static final int QUALIFIER_ID_DETAILED_POSITION_SUBON = 292;

  public static Player.DetailedPosition parseDetailedPlayerPositionForSubOn(MA18DPFeed.Event subOnEvent) {
    String position = subOnEvent.getQualifiers().get(QUALIFIER_ID_DETAILED_POSITION_SUBON);
    if (isNull(position)) {
      return null;
    }
    switch (position) {
      case "1":
        return Player.DetailedPosition.GOALKEEPER;
      case "2":
        return Player.DetailedPosition.WING_BACK;
      case "3":
        return Player.DetailedPosition.WING_MIDFIELDER;
      case "4":
        return Player.DetailedPosition.CENTRE_BACK;
      case "5":
        return Player.DetailedPosition.DEFENSIVE_MIDFIELDER;
      case "6":
        return Player.DetailedPosition.ATTACKING_MIDFIELDER;
      case "7":
        return Player.DetailedPosition.CENTRE_MIDFIELDER;
      case "8":
        return Player.DetailedPosition.WING_FORWARD;
      case "9":
        return Player.DetailedPosition.STRIKER;
      case "10":
        return Player.DetailedPosition.ATTACKING_MIDFIELDER;
      default:
        log.info("could not parse position {}", position);
        return null;
    }
  }

  public static Player.Position parsePlayerPosition(String position) {
    switch (position) {
      case "Goalkeeper":
        return Player.Position.GOALKEEPER;
      case "Defender":
      case "Wing Back":
        return Player.Position.DEFENDER;
      case "Unknown":
      case "Midfielder":
      case "Defensive Midfielder":
      case "Attacking Midfielder":
        return Player.Position.MIDFIELDER;
      case "Striker":
      case "Forward":
      case "Attacker":
        return Player.Position.FORWARD;
      default:
        throw new IllegalArgumentException(format("%s position not supported", position));
    }
  }

  public static Player.DetailedPosition parseDetailedPlayerPosition(String formation, int playerPosition) {
    return strFormationToOptaPositionToPosition.get(formation).get(playerPosition);
  }

  public static Player.DetailedPosition parseDetailedPlayerPosition(int formation, int playerPosition) {
    return formationToOptaPositionToPosition.get(formation).get(playerPosition);
  }

  private final static Map<Integer, Map<Integer, Player.DetailedPosition>> formationToOptaPositionToPosition = newHashMap();
  private final static Map<String, Map<Integer, Player.DetailedPosition>> strFormationToOptaPositionToPosition = newHashMap();

  static {
    Map<Player.DetailedPosition, List<Integer>> form442 = newHashMap();
    form442.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form442.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form442.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form442.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(4, 8));
    form442.put(Player.DetailedPosition.WING_MIDFIELDER, of(7, 11));
    form442.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition442 = buildOptaPositionToPosition(form442);

    Map<Player.DetailedPosition, List<Integer>> form41212 = newHashMap();
    form41212.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form41212.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form41212.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form41212.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form41212.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 11));
    form41212.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form41212.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition41212 = buildOptaPositionToPosition(form41212);

    Map<Player.DetailedPosition, List<Integer>> form433 = newHashMap();
    form433.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form433.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form433.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form433.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form433.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form433.put(Player.DetailedPosition.WING_FORWARD, of(10, 11));
    form433.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition433 = buildOptaPositionToPosition(form433);

    Map<Player.DetailedPosition, List<Integer>> form451 = newHashMap();
    form451.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form451.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form451.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form451.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(4, 8, 10));
    form451.put(Player.DetailedPosition.WING_FORWARD, of(7, 11));
    form451.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition451 = buildOptaPositionToPosition(form451);

    Map<Player.DetailedPosition, List<Integer>> form4411 = newHashMap();
    form4411.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4411.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4411.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4411.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(4, 8));
    form4411.put(Player.DetailedPosition.WING_MIDFIELDER, of(7, 11));
    form4411.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10));
    form4411.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4411 = buildOptaPositionToPosition(form4411);

    Map<Player.DetailedPosition, List<Integer>> form4141 = newHashMap();
    form4141.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4141.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4141.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4141.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form4141.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(8, 10));
    form4141.put(Player.DetailedPosition.WING_FORWARD, of(7, 11));
    form4141.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4141 = buildOptaPositionToPosition(form4141);

    Map<Player.DetailedPosition, List<Integer>> form4231 = newHashMap();
    form4231.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4231.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4231.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4231.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(4, 8));
    form4231.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10));
    form4231.put(Player.DetailedPosition.WING_FORWARD, of(7, 11));
    form4231.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4231 = buildOptaPositionToPosition(form4231);

    Map<Player.DetailedPosition, List<Integer>> form4321 = newHashMap();
    form4321.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4321.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4321.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4321.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form4321.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form4321.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10, 11));
    form4321.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4321 = buildOptaPositionToPosition(form4321);

    Map<Player.DetailedPosition, List<Integer>> form532 = newHashMap();
    form532.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form532.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form532.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form532.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(8));
    form532.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 11));
    form532.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition532 = buildOptaPositionToPosition(form532);

    Map<Player.DetailedPosition, List<Integer>> form541 = newHashMap();
    form541.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form541.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form541.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form541.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(8, 10));
    form541.put(Player.DetailedPosition.WING_MIDFIELDER, of(7, 11));
    form541.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition541 = buildOptaPositionToPosition(form541);

    Map<Player.DetailedPosition, List<Integer>> form352 = newHashMap();
    form352.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form352.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form352.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(11));
    form352.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form352.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form352.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition352 = buildOptaPositionToPosition(form352);

    Map<Player.DetailedPosition, List<Integer>> form343 = newHashMap();
    form343.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form343.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form343.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form343.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form343.put(Player.DetailedPosition.WING_FORWARD, of(10, 11));
    form343.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition343 = buildOptaPositionToPosition(form343);

    Map<Player.DetailedPosition, List<Integer>> form31312 = newHashMap();
    form31312.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form31312.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form31312.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form31312.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(8));
    form31312.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form31312.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form31312.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> optaPosToPosition31312 = buildOptaPositionToPosition(form31312);

    Map<Player.DetailedPosition, List<Integer>> form4222 = newHashMap();
    form4222.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4222.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4222.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4222.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4, 8));
    form4222.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(7, 11));
    form4222.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4222 = buildOptaPositionToPosition(form4222);

    Map<Player.DetailedPosition, List<Integer>> form3511 = newHashMap();
    form3511.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3511.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3511.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(11));
    form3511.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form3511.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form3511.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10));
    form3511.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3511 = buildOptaPositionToPosition(form3511);

    Map<Player.DetailedPosition, List<Integer>> form3421 = newHashMap();
    form3421.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3421.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3421.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form3421.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form3421.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10, 11));
    form3421.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3421 = buildOptaPositionToPosition(form3421);

    Map<Player.DetailedPosition, List<Integer>> form3412 = newHashMap();
    form3412.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3412.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3412.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form3412.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form3412.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form3412.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3412 = buildOptaPositionToPosition(form3412);

    Map<Player.DetailedPosition, List<Integer>> form3142 = newHashMap();
    form3142.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3142.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3142.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(8));
    form3142.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 11));
    form3142.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form3142.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3142 = buildOptaPositionToPosition(form3142);

    Map<Player.DetailedPosition, List<Integer>> form343d = newHashMap();
    form343d.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form343d.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form343d.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(8));
    form343d.put(Player.DetailedPosition.WING_MIDFIELDER, of(2, 3));
    form343d.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(7));
    form343d.put(Player.DetailedPosition.WING_FORWARD, of(10, 11));
    form343d.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition343d = buildOptaPositionToPosition(form343d);

    Map<Player.DetailedPosition, List<Integer>> form4132 = newHashMap();
    form4132.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4132.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4132.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4132.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form4132.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form4132.put(Player.DetailedPosition.WING_FORWARD, of(7, 11));
    form4132.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4132 = buildOptaPositionToPosition(form4132);

    Map<Player.DetailedPosition, List<Integer>> form4240 = newHashMap();
    form4240.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4240.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4240.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4240.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4, 8));
    form4240.put(Player.DetailedPosition.WING_FORWARD, of(7, 11));
    form4240.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4240 = buildOptaPositionToPosition(form4240);

    Map<Player.DetailedPosition, List<Integer>> form4312 = newHashMap();
    form4312.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4312.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4312.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4312.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form4312.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 11));
    form4312.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form4312.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> optaPosToPosition4312 = buildOptaPositionToPosition(form4312);

    Map<Player.DetailedPosition, List<Integer>> form3241 = newHashMap();
    form3241.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3241.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3241.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(2, 3));
    form3241.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(7, 8));
    form3241.put(Player.DetailedPosition.WING_FORWARD, of(10, 11));
    form3241.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3241 = buildOptaPositionToPosition(form3241);

    Map<Player.DetailedPosition, List<Integer>> form3331 = newHashMap();
    form3331.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3331.put(Player.DetailedPosition.CENTRE_BACK, of(4, 5, 6));
    form3331.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(8));
    form3331.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(2, 3));
    form3331.put(Player.DetailedPosition.WING_MIDFIELDER, of(10, 11));
    form3331.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(7));
    form3331.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> optaPosToPosition3331 = buildOptaPositionToPosition(form3331);

    formationToOptaPositionToPosition.put(2, optaPosToPosition442);
    formationToOptaPositionToPosition.put(3, optaPosToPosition41212);
    formationToOptaPositionToPosition.put(4, optaPosToPosition433);
    formationToOptaPositionToPosition.put(5, optaPosToPosition451);
    formationToOptaPositionToPosition.put(6, optaPosToPosition4411);
    formationToOptaPositionToPosition.put(7, optaPosToPosition4141);
    formationToOptaPositionToPosition.put(8, optaPosToPosition4231);
    formationToOptaPositionToPosition.put(9, optaPosToPosition4321);
    formationToOptaPositionToPosition.put(10, optaPosToPosition532);
    formationToOptaPositionToPosition.put(11, optaPosToPosition541);
    formationToOptaPositionToPosition.put(12, optaPosToPosition352);
    formationToOptaPositionToPosition.put(13, optaPosToPosition343);
    formationToOptaPositionToPosition.put(14, optaPosToPosition31312);
    formationToOptaPositionToPosition.put(15, optaPosToPosition4222);
    formationToOptaPositionToPosition.put(16, optaPosToPosition3511);
    formationToOptaPositionToPosition.put(17, optaPosToPosition3421);
    formationToOptaPositionToPosition.put(18, optaPosToPosition3412);
    formationToOptaPositionToPosition.put(19, optaPosToPosition3142);
    formationToOptaPositionToPosition.put(20, optaPosToPosition343d);
    formationToOptaPositionToPosition.put(21, optaPosToPosition4132);
    formationToOptaPositionToPosition.put(22, optaPosToPosition4240);
    formationToOptaPositionToPosition.put(23, optaPosToPosition4312);
    formationToOptaPositionToPosition.put(24, optaPosToPosition3241);
    formationToOptaPositionToPosition.put(25, optaPosToPosition3331);

    strFormationToOptaPositionToPosition.put("442", optaPosToPosition442);
    strFormationToOptaPositionToPosition.put("41212", optaPosToPosition41212);
    strFormationToOptaPositionToPosition.put("433", optaPosToPosition433);
    strFormationToOptaPositionToPosition.put("451", optaPosToPosition451);
    strFormationToOptaPositionToPosition.put("4411", optaPosToPosition4411);
    strFormationToOptaPositionToPosition.put("4141", optaPosToPosition4141);
    strFormationToOptaPositionToPosition.put("4231", optaPosToPosition4231);
    strFormationToOptaPositionToPosition.put("4321", optaPosToPosition4321);
    strFormationToOptaPositionToPosition.put("532", optaPosToPosition532);
    strFormationToOptaPositionToPosition.put("541", optaPosToPosition541);
    strFormationToOptaPositionToPosition.put("352", optaPosToPosition352);
    strFormationToOptaPositionToPosition.put("343", optaPosToPosition343);
    strFormationToOptaPositionToPosition.put("31312", optaPosToPosition31312);
    strFormationToOptaPositionToPosition.put("4222", optaPosToPosition4222);
    strFormationToOptaPositionToPosition.put("3511", optaPosToPosition3511);
    strFormationToOptaPositionToPosition.put("3421", optaPosToPosition3421);
    strFormationToOptaPositionToPosition.put("3412", optaPosToPosition3412);
    strFormationToOptaPositionToPosition.put("3142", optaPosToPosition3142);
    strFormationToOptaPositionToPosition.put("343d", optaPosToPosition343d);
    strFormationToOptaPositionToPosition.put("4132", optaPosToPosition4132);
    strFormationToOptaPositionToPosition.put("4240", optaPosToPosition4240);
    strFormationToOptaPositionToPosition.put("4312", optaPosToPosition4312);
    strFormationToOptaPositionToPosition.put("3241", optaPosToPosition3241);
    strFormationToOptaPositionToPosition.put("3331", optaPosToPosition3331);
  }

  public static Map<Integer, Player.DetailedPosition> buildOptaPositionToPosition(
    Map<Player.DetailedPosition, List<Integer>> positionToOptaPositions) {
    Map<Integer, Player.DetailedPosition> optaPositionToPosition = newHashMap();
    IntStream
      .range(1, 12)
      .forEach(optaPosition -> {
        Player.DetailedPosition position = positionToOptaPositions.entrySet()
          .stream()
          .map(entry -> {
            Player.DetailedPosition pos = entry.getKey();
            List<Integer> optaPositions = entry.getValue();
            return optaPositions.contains(optaPosition) ? pos : null;
          })
          .filter(Objects::nonNull)
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException(
            format("could not find optaPosition %s in map %s", optaPosition, positionToOptaPositions)));
        optaPositionToPosition.put(optaPosition, position);
      });
    return optaPositionToPosition;
  }

}
