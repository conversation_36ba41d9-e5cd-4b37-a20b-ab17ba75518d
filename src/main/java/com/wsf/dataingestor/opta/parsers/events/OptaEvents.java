package com.wsf.dataingestor.opta.parsers.events;

import lombok.experimental.UtilityClass;

@UtilityClass
public class OptaEvents {
  public static final int EVENT_ID_PLAYER_PASS = 1;
  public static final int EVENT_ID_OFFSIDE = 2;
  public static final int EVENT_ID_FOUL = 4;
  public static final int EVENT_ID_BALL_OUT = 5;
  public static final int EVENT_ID_CORNER_AWARDED_TEAM_CORNER = 6;
  public static final int EVENT_ID_TACKLE = 7;
  public static final int EVENT_ID_MISSED_SHOT = 13;
  public static final int EVENT_ID_SHOT_ON_POST = 14;
  public static final int EVENT_ID_SHOT_SAVED = 15;
  public static final int EVENT_ID_GOAL = 16;
  public static final int EVENT_ID_CARD = 17;
  public static final int EVENT_ID_PLAYER_SUB_OFF = 18;
  public static final int EVENT_ID_PLAYER_SUB_ON = 19;
  public static final int EVENT_ID_PERIOD_FINISHED = 30;
  public static final int EVENT_ID_MATCH_DELAY_GAME_STOPPED = 27;
  public static final int EVENT_ID_PERIOD_START = 32;
  public static final int EVENT_ID_TEAM_SETUP_LINEUP = 34;
  public static final int EVENT_ID_TEMPORARY_GOAL = 38;
  public static final int EVENT_ID_TEMPORARY_ATTEMPT_SHOT = 39;
  public static final int EVENT_ID_TEMPORARY_CARD = 78;
  public static final int EVENT_DELETED_EVENT_HAPPENED = 43;
  public static final int EVENT_DELETED_EVENT_AFTER_REVIEW = 84;
  public static final int EVENT_ID_TEMPORARY_CORNER = 85;
  public static final int EVENT_ID_TEMPORARY_FREE_KICK = 86;
  public static final int EVENT_ID_TEMPORARY_OFFSIDE = 87;
}
