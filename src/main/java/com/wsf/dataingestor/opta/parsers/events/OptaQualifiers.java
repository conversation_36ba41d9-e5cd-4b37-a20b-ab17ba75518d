package com.wsf.dataingestor.opta.parsers.events;

public class OptaQualifiers {
  public static final int QUALIFIER_ID_CORNER_TAKEN = 6;
  public static final int QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE = 7;
  public static final int QUALIFIER_ID_PENALTY = 9;
  public static final int QUALIFIER_ID_FOUL = 13;
  public static final int QUALIFIER_ID_OWN_GOAL = 28;
  public static final int QUALIFIER_ID_BLOCKED_SHOT = 82;
  public static final int QUALIFIER_ID_PLAYER_LIST_IDS = 30;
  public static final int QUALIFIER_ID_YELLOW_CARD = 31;
  public static final int QUALIFIER_ID_SECOND_YELLOW_CARD = 32;
  public static final int QUALIFIER_ID_RED_CARD = 33;
  public static final int QUALIFIER_ID_RELATED_EVENT_ID = 55;
  public static final int QUALIFIER_ID_ZONE_OF_THE_PITCH = 56;
  public static final int QUALIFIER_ID_SAVED_OFF_LINE = 101;
  public static final int QUALIFIER_ID_GOAL_KICK = 124;
  public static final int QUALIFIER_ID_TEAM_FORMATION = 130;
  public static final int QUALIFIER_ID_TEAM_PLAYERS_FORMATION_NUMBERS = 131;
  public static final int QUALIFIER_ID_PLAYER_OFF_PITCH = 172;
  public static final int QUALIFIER_ID_TEMPORARY_SHOT = 249;
  public static final int QUALIFIER_ID_TEMPORARY_SHOTS_BLOCKED = 250;
  public static final int QUALIFIER_ID_TEMPORARY_SHOT_MISSED = 252;
  public static final int QUALIFIER_ID_TEMPORARY_SHOT_MISSED_NOT_PASSED_GOAL_LINE = 253;
  public static final int QUALIFIER_ID_DIRECT_CORNER_ATTEMPT_GOAL = 263;
  public static final int QUALIFIER_ID_TEAM_GOAL_KICK = 346;
}
