package com.wsf.dataingestor.opta.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixtureDTO.Relation;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.opta.models.ma1.MA1Feed;
import com.wsf.dataingestor.opta.models.ma1.MA1Feed.LiveData;
import com.wsf.dataingestor.opta.models.ma1.MA1Feed.Match;
import com.wsf.dataingestor.opta.models.ma1.MA1Feed.MatchInfo;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed.MatchDetails;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.models.FixtureDTO.Provider.OPTA;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.FIRST_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SECOND_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SINGLE;
import static com.wsf.dataingestor.opta.parsers.Utils.convertMatchStatus;
import static com.wsf.dataingestor.opta.parsers.Utils.parseMatchDate;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;


@Slf4j
@Service
@RequiredArgsConstructor
public class MA1FeedParser implements FeedParser<FixturesFeed> {

  private static final String FEED_CODE = "ma1";

  private final ObjectMapper mapper;
  private final TournamentService tournamentService;

  @Override
  public FixturesFeed parseFeed(byte[] feed) throws IOException {
    log.debug("Parsing MA1 feed: {}", feed);
    return parseFeed(mapper.readValue(feed, MA1Feed.class));
  }

  @Override
  public FixturesFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing MA1 feed: {}", feed);
    return parseFeed(mapper.readValue(feed, MA1Feed.class));
  }

  private FixturesFeed parseFeed(MA1Feed feed) {

    if (feed.getMatchList()
      .isEmpty()) {
      throw new IllegalStateException("Match list empty");
    }

    String optaCalendarId = feed.getMatchList().get(0).getMatchInfo().getTournamentCalendar().getId();
    Tournament tournament = tournamentService.findByOptaExternalId(optaCalendarId)
      .orElseThrow(
        () -> new IllegalArgumentException(format("Tournament with optaCalendarId %s not found", optaCalendarId)));

    List<FixtureDTO> matchesDTOs = feed.getMatchList()
      .stream()
      .map(match -> {
        MatchInfo matchInfo = match.getMatchInfo();
        String optaFixtureId = matchInfo.getId();

        if (isNotEmpty(matchInfo.getContestantList())) {
          try {

            MA1Feed.Contestant optaHomeTeam = matchInfo.getContestantList()
              .stream()
              .filter(contestant -> contestant.getPosition().equals("home"))
              .findFirst()
              .orElseThrow(() -> new IllegalArgumentException("couldn't find home team in the feed"));

            MA1Feed.Contestant optaAwayTeam = matchInfo.getContestantList()
              .stream()
              .filter(contestant -> contestant.getPosition().equals("away"))
              .findFirst()
              .orElseThrow(() -> new IllegalArgumentException("couldn't find away team in the feed"));

            Instant matchDate = getFixtureDate(match);

            LiveData liveData = match.getLiveData();

            boolean neutralVenue = ofNullable(matchInfo.getVenue())
              .map(val -> val.getNeutral().equals("yes"))
              .orElse(false);

            boolean isLiveSupported = ofNullable(matchInfo.getOptaBetting())
              .map(val -> val.equals(1))
              .orElse(false);

            FeedFixtureStatus fixtureStatus = convertMatchStatus(liveData.getMatchDetails());

            Integer week = ofNullable(matchInfo.getWeek())
              .map(Integer::parseInt)
              .orElse(null);

            Pair<Relation, String> relation = isNull(week) ? getRelationType(match) : null;
            String relatedOptaFixtureId = ofNullable(relation)
              .map(Pair::getRight)
              .orElse(null);

            Relation leg = ofNullable(relation)
              .map(Pair::getLeft)
              .orElse(null);

            return FixtureDTO
              .builder()
              .externalFixtureId(optaFixtureId)
              .externalRelatedFixtureId(relatedOptaFixtureId)
              .externalHomeTeamId(optaHomeTeam.getId())
              .externalHomeTeamName(optaHomeTeam.getName())
              .externalHomeTeamAbbreviation(optaHomeTeam.getCode())
              .externalAwayTeamId(optaAwayTeam.getId())
              .externalAwayTeamName(optaAwayTeam.getName())
              .externalAwayTeamAbbreviation(optaAwayTeam.getCode())
              .isNeutralVenue(neutralVenue)
              .week(week)
              .leg(leg)
              .time(matchDate)
              .fixtureStatus(fixtureStatus)
              .lastUpdated(matchInfo.getLastUpdated())
              .canGoExtraTime(matchInfo.getOvertimeLength() != null && FIRST_LEG != leg)
              .isLiveSupported(isLiveSupported)
              .provider(OPTA)
              .build();
          } catch (IllegalArgumentException e) {
            log.warn("Error parsing Opta fixture {}: {}", optaFixtureId, e.getMessage());
            return null;
          }
        } else {
          return null;
        }

      })
      .filter(Objects::nonNull)
      .collect(toList());

    String tournamentCalendarId = feed.getMatchList()
      .stream()
      .map(Match::getMatchInfo)
      .map(info -> info.getTournamentCalendar().getId())
      .findFirst()
      .orElse("");

    String feedId = buildFeedId(FEED_CODE, tournamentCalendarId, abs(feed.hashCode()), now().toEpochMilli());
    return FixturesFeed
      .builder().feedId(feedId).tournament(tournament).fixtures(matchesDTOs).build();
  }

  private static Pair<Relation, String> getRelationType(Match fixture) {
    MatchDetails matchDetails = fixture.getLiveData().getMatchDetails();
    if (isNull(matchDetails)) {
      return Pair.of(null, null);
    } else {
      Integer leg = matchDetails.getLeg();
      String relatedMatchId = matchDetails.getRelatedMatchId();
      if (nonNull(leg)) {
        return Pair.of(extractLegFromInteger(leg), relatedMatchId);
      }
      return Pair.of(SINGLE, null);
    }
  }

  private static Relation extractLegFromInteger(int leg) {
    return switch (leg) {
      case 1 -> FIRST_LEG;
      case 2 -> SECOND_LEG;
      default -> SINGLE;
    };
  }

  private static Instant getFixtureDate(Match fixture) {
    MatchInfo fixtureInfo = fixture.getMatchInfo();
    return parseMatchDate(fixtureInfo.getDate(), fixtureInfo.getTime());
  }
}
