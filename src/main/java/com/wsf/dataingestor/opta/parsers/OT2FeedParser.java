package com.wsf.dataingestor.opta.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.opta.models.ot2.OT2Feed;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.time.Instant.now;


@Slf4j
@Service
@RequiredArgsConstructor
public class OT2FeedParser implements FeedParser<CurrentTournamentFeed> {

  private static final String FEED_CODE = "ot2";
  private final OptaFeedParserUtils optaFeedParserUtils;
  private final ObjectMapper mapper;

  @Override
  public CurrentTournamentFeed parseFeed(byte[] feed) throws IOException {
    log.debug("Parsing {} feed: {}", FEED_CODE, feed);
    return parseFeed(mapper.readValue(feed, OT2Feed.class));
  }

  @Override
  public CurrentTournamentFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing {} feed: {}", FEED_CODE, feed);
    return parseFeed(mapper.readValue(feed, OT2Feed.class));
  }

  private CurrentTournamentFeed parseFeed(OT2Feed feed) {
    OT2Feed.Competition optaCompetition = feed.getCompetition().get(0);
    String feedId = buildFeedId(FEED_CODE, optaCompetition.getId(), abs(feed.hashCode()), now().toEpochMilli());
    Optional<OT2Feed.TournamentCalendar> optTournamentCalendar = optaCompetition.getTournamentCalendar()
      .stream()
      .filter(t -> t.getActive().equals("yes"))
      .findFirst();

    Competition competition = optaFeedParserUtils.getCompetition(optaCompetition.getId());

    if (optTournamentCalendar.isEmpty()) {
      return CurrentTournamentFeed
        .builder()
        .competition(competition)
        .provider(CurrentTournamentFeed.Provider.OPTA)
        .feedId(feedId)
        .build();
    }

    var tournamentCalendar = optTournamentCalendar.get();

    String seasonId = tournamentCalendar.getId();
    Tournament existingTournament = optaFeedParserUtils.getTournament(seasonId)
      .orElse(null);

    return CurrentTournamentFeed
      .builder()
      .externalSeasonId(seasonId)
      .year(getYear(tournamentCalendar))
      .startDate(tournamentCalendar.getStartDate())
      .competition(competition)
      .existingTournament(existingTournament)
      .provider(CurrentTournamentFeed.Provider.OPTA)
      .feedId(feedId)
      .build();
  }

  private String getYear(OT2Feed.TournamentCalendar tournamentCalendar) {
    Matcher matcher = Pattern.compile("\\d+").matcher(tournamentCalendar.getName());
    List<String> year = new ArrayList<>();
    while (matcher.find()) {
      year.add(matcher.group(0));
    }
    return String.join("-", year);
  }
}
