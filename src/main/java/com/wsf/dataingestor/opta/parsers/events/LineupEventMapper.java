package com.wsf.dataingestor.opta.parsers.events;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed;

import static java.lang.Integer.parseInt;

@Slf4j
@UtilityClass
public class LineupEventMapper {

  public static int getTeamFormation(FeedEvent lineupEvent) {
    return parseInt(lineupEvent.getQualifiers().get(OptaQualifiers.QUALIFIER_ID_TEAM_FORMATION));
  }

  public static String[] getPlayersNums(FeedEvent lineupEvent) {
    return lineupEvent.getQualifiers().get(OptaQualifiers.QUALIFIER_ID_TEAM_PLAYERS_FORMATION_NUMBERS).split(",");
  }

  public static String[] getPlayersIds(FeedEvent lineupEvent) {
    return lineupEvent.getQualifiers().get(OptaQualifiers.QUALIFIER_ID_PLAYER_LIST_IDS).split(",");
  }

  public static Optional<MA18DPFeed.Event> getSubOnEvent(List<MA18DPFeed.Event> events) {
    return getEvents(events, OptaEvents.EVENT_ID_PLAYER_SUB_ON).stream().findFirst();
  }

  public static List<MA18DPFeed.Event> getLineUpEvents(List<MA18DPFeed.Event> events) {
    return getEvents(events, OptaEvents.EVENT_ID_TEAM_SETUP_LINEUP);
  }

  private static List<MA18DPFeed.Event> getEvents(List<MA18DPFeed.Event> events, int eventTypeId) {
    return events
      .stream()
      .filter(event -> event.getTypeId() == eventTypeId)
      .toList();
  }

}
