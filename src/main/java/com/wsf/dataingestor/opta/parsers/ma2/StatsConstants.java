package com.wsf.dataingestor.opta.parsers.ma2;

import java.util.Map;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.sports.soccer.Constants.GK_PENALTIES_SAVED;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOAL_KICKS;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;

public class StatsConstants {

  public static final Map<String, String> OPTA_TO_WSF_PLAYER_STATS_LIVE = newHashMap();
  public static final Map<String, String> OPTA_TO_WSF_PLAYER_STATS_FINAL = newHashMap();

  static {
    OPTA_TO_WSF_PLAYER_STATS_LIVE.put("duelWon", "duelsWon");
    OPTA_TO_WSF_PLAYER_STATS_LIVE.put("unsuccessfulTouch", "missedBalls");
    OPTA_TO_WSF_PLAYER_STATS_LIVE.put("possLostAll", "lostBalls");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("saves", "gkSaves");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("accuratePass", "completedPasses");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("totalPass", "passes");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("totalCross", "crosses");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("redCard", RED_CARD.getStatisticName());
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("penaltySave", GK_PENALTIES_SAVED);
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("ownGoals", "ownGoals");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("interception", "interceptions");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("duelWon", "duelsWon");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("unsuccessfulTouch", "missedBalls");
    OPTA_TO_WSF_PLAYER_STATS_FINAL.put("possLostAll", "lostBalls");
  }

  public static final Map<String, String> OPTA_TO_WSF_TEAM_STATS_LIVE = newHashMap();

  static {
    // we only want to get the stats we're doing markets on from a fast feed (eg. ma18dp)
    // if we get them from the ma2, we might store outdated information in the cache
  }

  public static final Map<String, String> OPTA_TO_WSF_TEAM_STATS_FINAL = newHashMap();

  static {
    OPTA_TO_WSF_TEAM_STATS_FINAL.put("goals", TEAM_GOALS);
    OPTA_TO_WSF_TEAM_STATS_FINAL.put("cornerTaken", TEAM_CORNERS);
    OPTA_TO_WSF_TEAM_STATS_FINAL.put("goalKicks", TEAM_GOAL_KICKS);
  }
}
