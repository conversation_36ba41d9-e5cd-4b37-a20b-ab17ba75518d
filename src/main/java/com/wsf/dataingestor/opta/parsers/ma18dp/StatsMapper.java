package com.wsf.dataingestor.opta.parsers.ma18dp;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.sports.soccer.Constants.CROSSES;
import static com.wsf.dataingestor.sports.soccer.Constants.GK_PENALTIES_SAVED;
import static com.wsf.dataingestor.sports.soccer.Constants.INTERCEPTIONS;
import static com.wsf.domain.soccer.SoccerMatchEvent.COMPLETED_PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static java.lang.Integer.parseInt;

@Slf4j
public class StatsMapper {
  static final Map<String, String> OPTA_TO_WSF_PLAYER_STATS = newHashMap();

  static {
    OPTA_TO_WSF_PLAYER_STATS.put("accuratePass", COMPLETED_PASS.getStatisticName());
    OPTA_TO_WSF_PLAYER_STATS.put("totalPass", PASS.getStatisticName());
    OPTA_TO_WSF_PLAYER_STATS.put("totalCross", CROSSES);
    OPTA_TO_WSF_PLAYER_STATS.put("penaltySave", GK_PENALTIES_SAVED);
    OPTA_TO_WSF_PLAYER_STATS.put("ownGoals", "ownGoals");
    OPTA_TO_WSF_PLAYER_STATS.put("interception", INTERCEPTIONS);
  }

  static final Map<String, String> OPTA_TO_WSF_TEAM_STATS = newHashMap();


  public static Map<String, Number> translateTeamStats(Map<String, String> stats, boolean isSnapshot) {
    Map<String, Number> mappedStats = newHashMap();
    processStats(OPTA_TO_WSF_TEAM_STATS, stats, isSnapshot, mappedStats);
    return mappedStats;
  }

  public static Map<String, Number> translatePlayerStats(Map<String, String> stats, boolean isSnapshot) {
    Map<String, Number> mappedStats = newHashMap();
    processStats(OPTA_TO_WSF_PLAYER_STATS, stats, isSnapshot, mappedStats);
    return mappedStats;
  }

  private static void processStats(Map<String, String> baseStats, Map<String, String> matchStats, boolean isSnapshot,
                                   Map<String, Number> mappedStats) {
    if (isSnapshot) {
      baseStats.forEach((statName, mappedName) -> {
        String value = matchStats.getOrDefault(statName, "0");
        int valInt = parseInt(value);
        mappedStats.put(mappedName, Math.max(valInt, 0));
      });
    } else {
      matchStats.forEach((key, value) -> {
        if (baseStats.containsKey(key)) {
          int valInt = parseInt(value);
          if (valInt > 0) {
            mappedStats.put(baseStats.get(key), valInt);
          }
        }
      });
    }
  }
}
