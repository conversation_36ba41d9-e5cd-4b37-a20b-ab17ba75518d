package com.wsf.dataingestor.opta.parsers.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityEventDTOFactory;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Sets.intersection;
import static com.wsf.dataingestor.opta.parsers.Utils.PENALTY_SHOOTOUT_OPTA_PERIOD_ID;
import static com.wsf.dataingestor.opta.parsers.Utils.convertOptaPeriodId;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_OFF;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_ON;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_RELATED_EVENT_ID;
import static java.lang.Integer.parseInt;
import static java.lang.String.format;
import static java.util.stream.Collectors.toSet;

@Slf4j
@AllArgsConstructor
@SuperBuilder
public abstract class AbstractEventsMapper {

  protected static final List<Integer> SUBS_TYPE_IDS = List.of(EVENT_ID_PLAYER_SUB_OFF, EVENT_ID_PLAYER_SUB_ON);
  @Builder.Default
  protected final Multimap<Integer, EntityEventFilter> playerEventTypeIdToQualifiersId = ArrayListMultimap.create();
  @Builder.Default
  protected final Multimap<Integer, MatchEventFilter> matchEventTypeIdToQualifiersId = ArrayListMultimap.create();
  protected final MetricsManager metricsManager;

  public Set<SoccerMatchEvent> getSupportedEventTypes() {
    return playerEventTypeIdToQualifiersId.values()
      .stream()
      .map(EntityEventFilter::getStat)
      .collect(toSet());
  }

  public Stream<MatchDataFeed.MatchEventDTO> translateMatchEvents(List<? extends FeedEvent> events, String teamId) {
    return events
      .stream()
      .flatMap(event -> matchEventTypeIdToQualifiersId.get(event.getTypeId())
        .stream()
        .filter(eventInfo -> eventInfo.includeEvent(event))
        .map(eventInfo -> {
          String eventId = format("%s_%s_%s", event.getEventId(), eventInfo.getEvent(), teamId);
          return new MatchDataFeed.MatchEventDTO(eventId, eventInfo.getEvent(), teamId, event.getTimestamp());
        }));
  }

  protected List<EntityEventDTO> translateSingleEntityEventToManyWsfEvents(
    Multimap<Integer, EntityEventFilter> eventTypeIdToEventFilter, String entityId, String teamId,
    boolean isPlayerUnknown, FeedEvent event) {

    boolean isSubstitutionEvent = SUBS_TYPE_IDS.contains(event.getTypeId());
    boolean hasRelatedEventQualifier = event.hasQualifier(QUALIFIER_ID_RELATED_EVENT_ID);
    if (isSubstitutionEvent && !hasRelatedEventQualifier) {
      return List.of();
    }

    boolean isPenaltyShootout = PENALTY_SHOOTOUT_OPTA_PERIOD_ID.equals(event.getPeriodId());
    if (isPenaltyShootout) {
      return List.of();
    }

    Integer typeId = event.getTypeId();
    Collection<EntityEventFilter> eventsInfo = eventTypeIdToEventFilter.get(typeId);

    List<EntityEventDTO> parsedEvents = eventsInfo
      .stream()
      .map(eventInfo -> handleEventMapping(event, teamId, entityId, isPlayerUnknown, eventInfo))
      .filter(Objects::nonNull)
      .collect(Collectors.toList());

    return getStreamEvents(event, teamId, entityId, isPlayerUnknown, parsedEvents);
  }

  protected abstract EntityEventDTO handleEventMapping(FeedEvent event, String teamId, String entityId,
                                                       boolean isPlayerUnknown, EntityEventFilter eventInfo);

  protected abstract List<EntityEventDTO> getStreamEvents(FeedEvent event, String teamId, String entityId,
                                                          boolean isUnknownEntity, List<EntityEventDTO> parsedEvents);

  protected EntityEventDTO mapEventToEntityEventDto(FeedEvent event, String teamId, String entityId,
                                                    boolean isUnknownEntity, EntityEventFilter eventInfo,
                                                    String relatedEventExternalId) {
    MatchPeriod matchPeriod = convertOptaPeriodId(event.getPeriodId());
    Set<Integer> eventQualifiers = event.getQualifiers().keySet();
    boolean hasToBeIgnored = !intersection(eventQualifiers, eventInfo.getQualifiersToIgnore()).isEmpty();
    String externalTeamId = event.getContestantId();

    return EntityEventDTOFactory.withRelatedEvent(event.getId(), relatedEventExternalId, entityId, event.getPlayerId(),
      teamId, externalTeamId, isUnknownEntity, eventInfo.getStat(), matchPeriod, parseInt(event.getTimeMin()),
      hasToBeIgnored, false, false, event.getTimestamp());
  }
}
