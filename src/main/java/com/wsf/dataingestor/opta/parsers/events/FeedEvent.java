package com.wsf.dataingestor.opta.parsers.events;

import java.time.Instant;
import java.util.Map;
import java.util.Optional;

public interface FeedEvent {

  String getId();

  String getEventId();

  Integer getTypeId();

  String getPlayerId();

  String getContestantId();

  String getTimeMin();

  Integer getPeriodId();

  Short getOutcome();

  Map<Integer, String> getQualifiers();

  Instant getTimestamp();

  Instant getLastModified();

  Optional<String> getRelatedQualifierValue();

  default boolean hasQualifier(int qualifier) {
    return getQualifiers().containsKey(qualifier);
  }
}
