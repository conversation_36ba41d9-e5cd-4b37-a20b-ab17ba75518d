package com.wsf.dataingestor.opta.parsers.ma18dp;

import java.util.Map;
import java.util.Set;
import java.util.function.BiPredicate;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.wsf.dataingestor.opta.parsers.events.EntityEventFilter;
import com.wsf.dataingestor.opta.parsers.events.EventInfo;
import com.wsf.dataingestor.opta.parsers.events.FeedEvent;
import com.wsf.dataingestor.opta.parsers.events.MatchEventFilter;
import com.wsf.dataingestor.opta.parsers.events.OptaQualifiers;

import static com.wsf.dataingestor.opta.parsers.events.EventInfo.defaultQualifierBuilder;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_DELETED_EVENT_AFTER_REVIEW;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_DELETED_EVENT_HAPPENED;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_BALL_OUT;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_CARD;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_CORNER_AWARDED_TEAM_CORNER;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_FOUL;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_MATCH_DELAY_GAME_STOPPED;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_MISSED_SHOT;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_OFFSIDE;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PERIOD_FINISHED;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PERIOD_START;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_PASS;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_OFF;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_ON;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_SHOT_ON_POST;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_SHOT_SAVED;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TACKLE;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEAM_SETUP_LINEUP;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_ATTEMPT_SHOT;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_CARD;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_CORNER;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_FREE_KICK;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_TEMPORARY_OFFSIDE;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_BLOCKED_SHOT;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_FOUL;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_OWN_GOAL;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_PENALTY;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_PLAYER_OFF_PITCH;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_RELATED_EVENT_ID;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_SAVED_OFF_LINE;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_TEAM_GOAL_KICK;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_ZONE_OF_THE_PITCH;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.BET_START;
import static com.wsf.domain.soccer.SoccerMatchEvent.BET_STOP;
import static com.wsf.domain.soccer.SoccerMatchEvent.CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.DANGEROUS_FREEKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.DELETED_EVENT;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.LINEUPS_AVAILABLE;
import static com.wsf.domain.soccer.SoccerMatchEvent.MATCH_FINISHED;
import static com.wsf.domain.soccer.SoccerMatchEvent.MATCH_STARTED;
import static com.wsf.domain.soccer.SoccerMatchEvent.MATCH_SUSPENDED;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.PENALTY;
import static com.wsf.domain.soccer.SoccerMatchEvent.POSSIBLE_CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Collections.emptySet;

public class MA18ValidEvents {
  public static final Multimap<Integer, EntityEventFilter> playerEventTypeIdToFilters = ArrayListMultimap.create();
  public static final Multimap<Integer, MatchEventFilter> matchEventTypeIdToFilters = ArrayListMultimap.create();

  static {
    var shotEventFilterForShots = EntityEventFilter
      .builder().stat(SHOT).qualifiersIdToExclude(Set.of(QUALIFIER_ID_OWN_GOAL)).build();

    var tempShotEventFilterForShots = EntityEventFilter
      .builder()
      .stat(SHOT)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(OptaQualifiers.QUALIFIER_ID_TEMPORARY_SHOT,
        OptaQualifiers.QUALIFIER_ID_TEMPORARY_SHOTS_BLOCKED, OptaQualifiers.QUALIFIER_ID_TEMPORARY_SHOT_MISSED,
        OptaQualifiers.QUALIFIER_ID_TEMPORARY_SHOT_MISSED_NOT_PASSED_GOAL_LINE))
      .build();
    var tempShotOnGoalEventFilterForShotsOnGoal = EntityEventFilter
      .builder()
      .stat(SHOT_ON_GOAL)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(OptaQualifiers.QUALIFIER_ID_TEMPORARY_SHOT))
      .build();
    var attemptSavedEventFilterForShotsOnGoal = EntityEventFilter
      .builder()
      .stat(SHOT_ON_GOAL)
      .qualifiersIdToExclude(Set.of(QUALIFIER_ID_OWN_GOAL, QUALIFIER_ID_BLOCKED_SHOT))
      .build();
    var attemptOffTheLineForShotsOnGoal = EntityEventFilter
      .builder()
      .stat(SHOT_ON_GOAL)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_SAVED_OFF_LINE))
      .build();
    var goalEventFilterForShotsOnGoal = EntityEventFilter
      .builder().stat(SHOT_ON_GOAL).qualifiersIdToExclude(Set.of(QUALIFIER_ID_OWN_GOAL)).build();

    EntityEventFilter substitutionOutEventFilter = EntityEventFilter
      .builder()
      .stat(SUB_OFF)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_RELATED_EVENT_ID))
      .shouldMainEventIncludeRelatedEventId(true)
      .build();
    EntityEventFilter relatedSubstitutionInEventFilter = EntityEventFilter
      .builder()
      .stat(SUB_ON)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_RELATED_EVENT_ID))
      .parseRelatedEventInsteadOfMain(true)
      .build();
    EntityEventFilter substitutionInEventFilter = EntityEventFilter
      .builder()
      .stat(SUB_ON)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_RELATED_EVENT_ID))
      .shouldMainEventIncludeRelatedEventId(true)
      .build();
    EntityEventFilter relatedSubstitutionOutEventFilter = EntityEventFilter
      .builder()
      .stat(SUB_OFF)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_RELATED_EVENT_ID))
      .parseRelatedEventInsteadOfMain(true)
      .build();

    var yellowCardEventFilter = EntityEventFilter
      .builder()
      .stat(YELLOW_CARD)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(OptaQualifiers.QUALIFIER_ID_YELLOW_CARD))
      .qualifiersIdToExclude(Set.of(QUALIFIER_ID_PLAYER_OFF_PITCH))
      .build();
    var redCardEventFilter = EntityEventFilter
      .builder()
      .stat(RED_CARD)
      .qualifiersIdAndValuesToInclude(
        defaultQualifierBuilder(OptaQualifiers.QUALIFIER_ID_SECOND_YELLOW_CARD, OptaQualifiers.QUALIFIER_ID_RED_CARD))
      .qualifiersIdToExclude(Set.of(QUALIFIER_ID_PLAYER_OFF_PITCH))
      .build();

    var goalEventFilterForGoals = EntityEventFilter
      .builder().stat(GOAL).outcomeHasToBeOne(true).qualifiersToIgnore(Set.of(QUALIFIER_ID_OWN_GOAL)).build();

    var goalEventFilterForAssistsGoals = EntityEventFilter
      .builder()
      .stat(ASSIST_GOAL)
      .outcomeHasToBeOne(true)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_RELATED_EVENT_ID))
      .parseRelatedEventInsteadOfMain(true)
      // This is needed cause an assist event does not exist on its own, but it can only exist as related event of a goal event.
      // The reason for this is that Opta does not clearly define an assist event, if not minutes after the goal is scored.
      // If we used the assist eventId as the eventId, all the later events with eventId=assistEventId would
      // override the assist event as they would not be parsed as assists but rather as unknown events.
      .shouldUseMainEventId(true)
      .build();

    var deletedEventFilter = EntityEventFilter
      .builder().stat(DELETED_EVENT).build();

    var cornersEventFilter = EntityEventFilter
      .builder()
      .stat(CORNER)
      .outcomeHasToBeOne(false)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(OptaQualifiers.QUALIFIER_ID_CORNER_TAKEN))
      .build();

    var goalKicksEventFilter = EntityEventFilter
      .builder()
      .stat(GOALKICK)
      .outcomeHasToBeOne(true)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_TEAM_GOAL_KICK))
      .build();

    var offsideEventFilter = EntityEventFilter
      .builder()
      .stat(OFFSIDE)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE))
      .build();

    EntityEventFilter committedFoulsEventFilter = EntityEventFilter
      .builder().stat(FOUL).outcomeHasToBeZero(true).build();

    EntityEventFilter tacklesWonEventFilter = EntityEventFilter
      .builder().stat(TACKLE_WON).build();

    playerEventTypeIdToFilters.put(EVENT_ID_MISSED_SHOT, shotEventFilterForShots);
    playerEventTypeIdToFilters.put(EVENT_ID_SHOT_ON_POST, shotEventFilterForShots);
    playerEventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, shotEventFilterForShots);
    playerEventTypeIdToFilters.put(EVENT_ID_GOAL, shotEventFilterForShots);

    playerEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_ATTEMPT_SHOT, tempShotEventFilterForShots);
    playerEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_ATTEMPT_SHOT, tempShotOnGoalEventFilterForShotsOnGoal);
    playerEventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, attemptSavedEventFilterForShotsOnGoal);
    playerEventTypeIdToFilters.put(EVENT_ID_SHOT_SAVED, attemptOffTheLineForShotsOnGoal);
    playerEventTypeIdToFilters.put(EVENT_ID_GOAL, goalEventFilterForShotsOnGoal);
    playerEventTypeIdToFilters.put(EVENT_ID_GOAL, goalEventFilterForGoals);
    playerEventTypeIdToFilters.put(EVENT_ID_GOAL, goalEventFilterForAssistsGoals);
    playerEventTypeIdToFilters.put(EVENT_ID_CARD, yellowCardEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_CARD, redCardEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_PLAYER_SUB_OFF, substitutionOutEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_PLAYER_SUB_OFF, relatedSubstitutionInEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_PLAYER_SUB_ON, substitutionInEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_PLAYER_SUB_ON, relatedSubstitutionOutEventFilter);

    playerEventTypeIdToFilters.put(EVENT_DELETED_EVENT_HAPPENED, deletedEventFilter);
    playerEventTypeIdToFilters.put(EVENT_DELETED_EVENT_AFTER_REVIEW, deletedEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_PLAYER_PASS, cornersEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_BALL_OUT, goalKicksEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_OFFSIDE, offsideEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_FOUL, committedFoulsEventFilter);
    playerEventTypeIdToFilters.put(EVENT_ID_TACKLE, tacklesWonEventFilter);
  }

  static {
    BiPredicate<EventInfo, FeedEvent> isFirstHalf = (eventInfo, feedEvent) -> feedEvent.getPeriodId() == 1;
    var matchStartedEventFilter = MatchEventFilter
      .builder().event(MATCH_STARTED).includeEventFn(isFirstHalf).build();
    var matchFinishedEventFilter = MatchEventFilter
      .builder().event(MATCH_FINISHED).includeEventFn(((eventInfo, event) -> event.getPeriodId() == 2)).build();
    var lineUpsAvailableEventFilter = MatchEventFilter
      .builder().event(LINEUPS_AVAILABLE).build();

    matchEventTypeIdToFilters.put(EVENT_ID_TEAM_SETUP_LINEUP, lineUpsAvailableEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_PERIOD_START, matchStartedEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_PERIOD_FINISHED, matchFinishedEventFilter);

    /* BETSTOP events */
    var tempGoalEventFilter = MatchEventFilter
      .builder().event(GOAL).outcomeHasToBeOne(true).build();
    var tempShotEventFilter = MatchEventFilter
      .builder().event(SHOT).outcomeHasToBeOne(true).build();

    var foulEventFilter = MatchEventFilter
      .builder()
      .event(FOUL)
      .outcomeHasToBeOne(true)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_FOUL))
      .build();
    var temporaryFreeKickEventFilter = MatchEventFilter
      .builder().event(FOUL).build();
    var dangerousFreeKickEventFilter = MatchEventFilter
      .builder()
      .event(DANGEROUS_FREEKICK)
      .outcomeHasToBeOne(true)
      .qualifiersIdAndValuesToInclude(
        Map.of(QUALIFIER_ID_FOUL, emptySet(), QUALIFIER_ID_ZONE_OF_THE_PITCH, Set.of("Left", "Center", "Right")))
      .build();

    var penaltyEventFilter = MatchEventFilter
      .builder().event(PENALTY).qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_PENALTY)).build();
    var varGenericMatchBreakEventFilter = MatchEventFilter
      .builder().event(BET_STOP).outcomeHasToBeOne(true).build();
    var cornerAwardedEventFilter = MatchEventFilter
      .builder().event(POSSIBLE_CORNER).outcomeHasToBeOne(true).build();
    var temporaryCornerEventFilter = MatchEventFilter
      .builder().event(POSSIBLE_CORNER).build();
    var goalKickEventFilter = MatchEventFilter
      .builder()
      .event(GOALKICK)
      .outcomeHasToBeOne(true)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_TEAM_GOAL_KICK))
      .build();

    var offsideEventFilter = MatchEventFilter
      .builder()
      .event(OFFSIDE)
      .qualifiersIdAndValuesToInclude(defaultQualifierBuilder(QUALIFIER_ID_OFFSIDE_PLAYER_CAUGHT_OFFSIDE))
      .build();

    var temporaryOffsideEventFilter = MatchEventFilter
      .builder().event(OFFSIDE).build();

    var matchSuspendedEventFilter = MatchEventFilter
      .builder().event(MATCH_SUSPENDED).outcomeHasToBeOne(false).build();

    var tempCardEventFilter = MatchEventFilter
      .builder().event(CARD).outcomeHasToBeOne(false).build();

    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_GOAL, tempGoalEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_ATTEMPT_SHOT, tempShotEventFilter);

    matchEventTypeIdToFilters.put(EVENT_ID_FOUL, foulEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_FOUL, dangerousFreeKickEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_FOUL, penaltyEventFilter);

    matchEventTypeIdToFilters.put(EVENT_ID_CORNER_AWARDED_TEAM_CORNER, cornerAwardedEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_BALL_OUT, goalKickEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_MATCH_DELAY_GAME_STOPPED, varGenericMatchBreakEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_MATCH_DELAY_GAME_STOPPED, matchSuspendedEventFilter);

    matchEventTypeIdToFilters.put(EVENT_ID_OFFSIDE, offsideEventFilter);

    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_CARD, tempCardEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_CORNER, temporaryCornerEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_OFFSIDE, temporaryOffsideEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_TEMPORARY_FREE_KICK, temporaryFreeKickEventFilter);

    /* BETSTART events */
    var passEventFilter = MatchEventFilter
      .builder().event(BET_START).outcomeHasToBeOne(true).build();
    var endPeriodEventFilter = MatchEventFilter
      .builder().event(BET_START).build();

    matchEventTypeIdToFilters.put(EVENT_ID_PLAYER_PASS, passEventFilter);
    matchEventTypeIdToFilters.put(EVENT_ID_PERIOD_FINISHED, endPeriodEventFilter);
  }


}
