package com.wsf.dataingestor.opta.parsers.ma18dp;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Set;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.OptaPositionUtils;
import com.wsf.dataingestor.opta.parsers.events.LineupEventMapper;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.opta.parsers.OptaPositionUtils.parseDetailedPlayerPosition;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static java.lang.Integer.parseInt;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Comparator.comparing;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;


@Slf4j
@Service
public class MA18DPFeedParser implements FeedParser<MatchDataFeed> {

  private static final String FEED_CODE = "ma18dp";
  private final ObjectReader reader;
  private final OptaFeedParserUtils optaFeedParserUtils;
  private final MA18DPEventsMapper eventsMapper;

  @Autowired
  public MA18DPFeedParser(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils,
                          MA18DPEventsMapper eventsMapper) {
    this.reader = jsonObjectMapper.readerFor(MA18DPFeed.class);
    this.optaFeedParserUtils = optaFeedParserUtils;
    this.eventsMapper = eventsMapper;
  }

  @Override
  public MatchDataFeed parseFeed(byte[] feed) throws IOException {
    if (log.isTraceEnabled()) {
      log.trace("Parsing MA18DP feed: {}", new String(feed));
    }
    Instant ts = Instant.now();
    MA18DPFeed matchEventsStats = reader.readValue(feed);
    return parseFeed(matchEventsStats, ts);
  }

  @Override
  public MatchDataFeed parseFeed(String feed) throws IOException {
    log.trace("Parsing MA18DP feed: {}", feed);
    Instant ts = Instant.now();
    MA18DPFeed matchEventsStats = reader.readValue(feed, MA18DPFeed.class);
    return parseFeed(matchEventsStats, ts);
  }

  private MatchDataFeed parseFeed(MA18DPFeed feed, Instant receivedTs) {
    MA18DPFeed.LiveData liveData = feed.getContent().getLiveData();
    String optaFixtureId = getFixtureId(feed);
    String feedId = buildFeedId(FEED_CODE, optaFixtureId, abs(feed.hashCode()), receivedTs.toEpochMilli());
    Fixture fixture = optaFeedParserUtils.getFixtureOrThrow(optaFixtureId);
    if (isNull(liveData)) {
      return buildEmptyResponse(receivedTs, fixture, feedId);
    }

    MA18DPFeed.Stats stats = liveData.getMatchDetails().getStats();
    List<MA18DPFeed.Event> events = liveData.getMatchDetails().getEvents();

    Instant timestamp = null;
    List<PlayerDataDTO> playerDataDTOS;
    List<TeamDataDTO> teamDataDTOS;
    List<EntityEventDTO> playerEvents = null;
    boolean isSnapshot = false;
    Integer seqId = null;
    List<MA18DPFeed.Event> lineUpEvents = LineupEventMapper.getLineUpEvents(events);
    boolean isLineupEvent = !lineUpEvents.isEmpty() && events.size() == lineUpEvents.size();
    if (isLineupEvent) {
      MA18DPFeed.Event lastEvent = findLastEvent(events);
      if (nonNull(lastEvent)) {
        timestamp = lastEvent.getTimestamp();
        seqId = lastEvent.getSeqId();
      }
      playerDataDTOS = getLineUps(timestamp, fixture, lineUpEvents);
      teamDataDTOS = emptyList();
    } else if (nonNull(stats) && events.isEmpty()) {
      // snapshot
      playerDataDTOS = processPlayerStats(fixture, stats);
      teamDataDTOS = processTeamEventsAndStats(fixture, stats, true, timestamp);
      isSnapshot = true;
    } else if (!events.isEmpty()) {
      MA18DPFeed.Event lastEvent = findLastEvent(events);
      if (nonNull(lastEvent)) {
        timestamp = lastEvent.getTimestamp();
        seqId = lastEvent.getSeqId();
      }

      Map<ProviderPlayerKey, List<EntityEventDTO>> playerIdToEvents = extractEventsByPlayerId(events, fixture);

      playerEvents = playerIdToEvents.values()
        .stream()
        .flatMap(List::stream)
        .collect(toList());
      playerDataDTOS = processPlayerEvents(fixture, stats, playerIdToEvents, timestamp);
      teamDataDTOS = processTeamEventsAndStats(fixture, stats, false, timestamp);
    } else {
      // null stats and null events
      return buildEmptyResponse(receivedTs, fixture, feedId);
    }

    Map<String, List<MA18DPFeed.Event>> contestantIdToEvents = events
      .stream()
      .filter(event -> nonNull(event.getContestantId()))
      .collect(groupingBy(MA18DPFeed.Event::getContestantId));

    List<MatchDataFeed.MatchEventDTO> matchEvents = processMatchEvents(fixture.getTournament().getCompetitionId(),
      contestantIdToEvents);

    MatchDataFeed.FeedFixtureStatus status = !lineUpEvents.isEmpty() ?
                                             MatchDataFeed.FeedFixtureStatus.FIXTURE :
                                             MatchDataFeed.FeedFixtureStatus.LIVE;

    Set<SoccerMatchEvent> supportedEventTypes = eventsMapper.getSupportedEventTypes();

    return MatchDataFeed
      .builder()
      .receivedTs(receivedTs)
      .latestUpdateTs(timestamp)
      .fixture(fixture)
      .feedId(feedId)
      .fixtureStatus(status)
      .teamsData(teamDataDTOS)
      .playersData(playerDataDTOS)
      .feedPlayerMatchEvents(playerEvents)
      .supportedEventTypes(supportedEventTypes)
      .matchEvents(matchEvents)
      .isSnapshot(isSnapshot)
      .isSingleEventFeed(true)
      .seqId(seqId)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
  }

  private Map<ProviderPlayerKey, List<EntityEventDTO>> extractEventsByPlayerId(List<MA18DPFeed.Event> events,
                                                                               Fixture fixture) {

    return events
      .stream()
      .filter(event -> nonNull(event.getPlayerId()) && nonNull(event.getContestantId()))
      .collect(groupingBy(event -> new ProviderPlayerKey(event.getPlayerId(), event.getContestantId()))).entrySet()
      .stream()
      .flatMap(entry -> {
        String optaPlayerId = entry.getKey().id();
        String optaTeamId = entry.getKey().teamId();
        List<MA18DPFeed.Event> playerEvents = entry.getValue();

        Player player = optaFeedParserUtils.getPlayer(optaPlayerId, fixture);

        String playerId;
        String teamId;
        boolean isPlayerUnknown = false;

        if (isNull(player)) {
          isPlayerUnknown = true;
          playerId = format("UNKNOWN_PLAYER_OPTA_%s", optaPlayerId);
          Team team = optaFeedParserUtils.getTeam(fixture.getTournament().getCompetitionId(), optaTeamId);
          teamId = team.getIdAsString();
          log.error("could not find a match for player with optaId={}. Replacing with a fake id {}", optaPlayerId,
            playerId);
        } else {
          playerId = player.getId().toString();
          teamId = player.getTeam().getId().toString();
        }

        var eventsDto = eventsMapper.translatePlayerEvents(playerId, teamId, isPlayerUnknown, playerEvents)
          .stream()
          .toList();

        // Continue here: filter isUnknownEntity=false and playerID.startsWith("UNKNOWN")
        log.info("Omar3 - eventsDto: {}", eventsDto);

        return eventsDto.stream();
      })
      .collect(groupingBy(entityEventDTO -> new ProviderPlayerKey(entityEventDTO.getExternalEntityId(),
        entityEventDTO.getExternalTeamId())));
  }

  private List<PlayerDataDTO> processPlayerEvents(Fixture fixture, MA18DPFeed.Stats stats,
                                                  Map<ProviderPlayerKey, List<EntityEventDTO>> playerIdToEvents,
                                                  Instant timestamp) {
    List<PlayerDataDTO> playerDataDTOS;
    if (nonNull(stats)) {
      Map<ProviderPlayerKey, Map<String, String>> optaPlayerIdToProviderPlayer = stats.getTeams()
        .stream()
        .flatMap(team -> team.getPlayers()
          .stream()
          .map(player -> new ProviderPlayerStats(new ProviderPlayerKey(player.getPlayerId(), team.getContestantId()),
            player.getStats())))
        .collect(toMap(ProviderPlayerStats::key, ProviderPlayerStats::stats));

      playerDataDTOS = Stream
        .concat(optaPlayerIdToProviderPlayer.keySet()
          .stream(), playerIdToEvents.keySet()
          .stream())
        .distinct()
        .map(providerPlayerKey -> {
          List<EntityEventDTO> playerEvents = playerIdToEvents.get(providerPlayerKey);

          Map<String, String> playerStats = ofNullable(optaPlayerIdToProviderPlayer.get(providerPlayerKey)).orElse(
            emptyMap());

          return getPlayerDataDTO(fixture, timestamp, playerEvents, false, providerPlayerKey, playerStats);
        })
        .filter(Objects::nonNull)
        .collect(toList());
    } else {
      playerDataDTOS = playerIdToEvents.keySet()
        .stream()
        .map(providerPlayerKey -> {
          List<EntityEventDTO> playerEvents = playerIdToEvents.get(providerPlayerKey);

          return buildPlayerDataWithEvents(providerPlayerKey, fixture, timestamp, emptyList(), playerEvents);
        })
        .collect(toList());
    }
    return playerDataDTOS;
  }

  private List<TeamDataDTO> processTeamEventsAndStats(Fixture fixture, MA18DPFeed.Stats stats, boolean isSnapshot,
                                                      Instant timestamp) {
    Map<String, List<MA18DPFeed.Team>> teamIdToStats = nonNull(stats) ? groupStatsByTeam(stats) : emptyMap();

    return Stream
      .of(fixture.getHomeTeam().getOptaId(), fixture.getAwayTeam().getOptaId())
      .map(optaTeamId -> {
        Map<String, String> teamStats = ofNullable(teamIdToStats.get(optaTeamId))
          .map(t -> t.get(0))
          .map(MA18DPFeed.Team::getStats)
          .orElseGet(Collections::emptyMap);

        Team team = ofNullable(
          optaFeedParserUtils.getTeam(fixture.getTournament().getCompetitionId(), optaTeamId)).orElseThrow(
          () -> new IllegalArgumentException(
            format("could not find team with OptaId %s in fixture %s", optaTeamId, fixture)));

        return buildTeamData(team, teamStats, isSnapshot, timestamp);
      })
      .collect(toList());
  }

  private TeamDataDTO buildTeamData(Team team, Map<String, String> stats, boolean isSnapshot, Instant lastUpdatedTs) {
    Map<String, Number> teamStats = StatsMapper.translateTeamStats(stats, isSnapshot);
    return TeamDataDTO
      .builder().team(team).stats(teamStats)
      .timestamp(lastUpdatedTs).build();
  }

  private List<PlayerDataDTO> processPlayerStats(Fixture fixture, MA18DPFeed.Stats stats) {
    return stats.getTeams()
      .stream()
      .flatMap(lineup -> getPlayersData(fixture, lineup))
      .collect(toList());
  }

  private List<MatchDataFeed.MatchEventDTO> processMatchEvents(String competitionId,
                                                               Map<String, List<MA18DPFeed.Event>> contestantIdToEvents) {
    return contestantIdToEvents.entrySet()
      .stream()
      .flatMap(entry -> {
        String externalTeamId = entry.getKey();
        Team team = optaFeedParserUtils.getTeam(competitionId, externalTeamId);
        List<MA18DPFeed.Event> eventList = entry.getValue();
        return eventsMapper.translateMatchEvents(eventList, team.getIdAsString());
      })
      .collect(toList());
  }

  private Stream<PlayerDataDTO> getPlayersData(Fixture fixture, MA18DPFeed.Team teamLineUp) {
    return teamLineUp.getPlayers()
      .stream()
      .map(playerFeed -> {
        String optaPlayerId = playerFeed.getPlayerId();
        Map<String, String> playerStats = playerFeed.getStats();
        ProviderPlayerKey providerPlayerKey = new ProviderPlayerKey(optaPlayerId, teamLineUp.getContestantId());

        return getPlayerDataDTO(fixture, null, emptyList(), true, providerPlayerKey, playerStats);
      })
      .filter(Objects::nonNull);
  }

  private PlayerDataDTO getPlayerDataDTO(Fixture fixture, Instant timestamp, List<EntityEventDTO> parsedEvents,
                                         boolean isSnapshot, ProviderPlayerKey providerPlayerKey,
                                         Map<String, String> stats) {

    Player player = optaFeedParserUtils.getPlayer(providerPlayerKey.id(), fixture);

    Map<String, Number> translatedStats = nonNull(stats) ?
                                          StatsMapper.translatePlayerStats(stats, isSnapshot) :
                                          emptyMap();

    if (translatedStats.isEmpty() && (ofNullable(parsedEvents)
      .map(List::isEmpty)
      .orElse(true))) {
      return null;
    }

    return PlayerDataDTO.buildPlayerData(timestamp, translatedStats, player, providerPlayerKey, null, true, true,
      randomUUID().toString());
  }

  private PlayerDataDTO buildPlayerDataWithEvents(ProviderPlayerKey providerPlayerKey, Fixture fixture,
                                                  Instant timestamp, List<MA18DPFeed.Event> events,
                                                  List<EntityEventDTO> parsedEvents) throws NoSuchElementException {

    Player player = optaFeedParserUtils.getPlayer(providerPlayerKey.id(), fixture);

    Player.DetailedPosition position = getPosition(events);
    boolean isPlaying = parsedEvents
      .stream()
      .noneMatch(p -> p.getEventType() == SUB_OFF || p.getEventType() == RED_CARD);

    return PlayerDataDTO.buildPlayerData(timestamp, newHashMap(), player, providerPlayerKey, position, isPlaying, true,
      randomUUID().toString());
  }

  private Player.DetailedPosition getPosition(List<MA18DPFeed.Event> events) {
    return LineupEventMapper.getSubOnEvent(events)
      .map(OptaPositionUtils::parseDetailedPlayerPositionForSubOn)
      .orElse(null);
  }

  private List<PlayerDataDTO> getLineUps(Instant timestamp, Fixture fixture, List<MA18DPFeed.Event> lineupEvents) {
    return lineupEvents
      .stream()
      .flatMap(lineupEvent -> getLineUpForTeam(timestamp, fixture, lineupEvent))
      .toList();
  }

  private Stream<PlayerDataDTO> getLineUpForTeam(Instant timestamp, Fixture fixture, MA18DPFeed.Event lineupEvent) {
    int teamFormation = LineupEventMapper.getTeamFormation(lineupEvent);
    String[] playerNums = LineupEventMapper.getPlayersNums(lineupEvent);
    String[] playerIds = LineupEventMapper.getPlayersIds(lineupEvent);

    return IntStream
      .range(0, 11).mapToObj(i -> {
        String playerNum = playerNums[i].trim();
        String playerId = playerIds[i].trim();

        Player player = optaFeedParserUtils.getPlayer(playerId, fixture);
        Player.DetailedPosition position = parseDetailedPlayerPosition(teamFormation, parseInt(playerNum));
        ProviderPlayerKey providerPlayerKey = new ProviderPlayerKey(playerId, lineupEvent.getContestantId());

        return PlayerDataDTO.buildPlayerData(timestamp, emptyMap(), player, providerPlayerKey, position, true, true,
          randomUUID().toString());
      });
  }

  private static MA18DPFeed.Event findLastEvent(List<MA18DPFeed.Event> events) {
    return events
      .stream()
      .filter(e -> nonNull(e.getTimestamp()))
      .max(comparing(MA18DPFeed.Event::getTimestamp))
      .orElse(null);
  }

  private static String getFixtureId(MA18DPFeed matchStats) {
    MA18DPFeed.LiveData liveData = matchStats.getContent().getLiveData();
    return ofNullable(liveData)
      .map(ld -> ld.getMatchDetails().getId())
      .orElseGet(() -> matchStats.getContent().getFixture());
  }

  private static MatchDataFeed buildEmptyResponse(Instant receivedTs, Fixture fixture, String feedId) {
    return MatchDataFeed
      .builder()
      .receivedTs(receivedTs)
      .latestUpdateTs(receivedTs)
      .feedId(feedId)
      .fixture(fixture)
      .playersData(emptyList())
      .isSnapshot(false)
      .isSingleEventFeed(true)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
  }

  private static Map<String, List<MA18DPFeed.Team>> groupStatsByTeam(MA18DPFeed.Stats stats) {
    return stats.getTeams()
      .stream()
      .collect(groupingBy(MA18DPFeed.Team::getContestantId));
  }

  private record ProviderPlayerStats(ProviderPlayerKey key, Map<String, String> stats) {}
}
