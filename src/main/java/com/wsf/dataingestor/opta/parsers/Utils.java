package com.wsf.dataingestor.opta.parsers;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed.MatchDetails;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.CANCELLED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.FIXTURE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.POSTPONED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.SUSPENDED;
import static com.wsf.domain.common.MatchPeriod.END_MATCH;
import static com.wsf.domain.common.MatchPeriod.END_REGULAR_TIMES;
import static com.wsf.domain.common.MatchPeriod.EXTRA_FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.EXTRA_HALF_TIME;
import static com.wsf.domain.common.MatchPeriod.EXTRA_SECOND_HALF;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.HALF_TIME;
import static com.wsf.domain.common.MatchPeriod.SECOND_HALF;
import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class Utils {

  private static final String DEFAULT_MATCH_TIME = "15:00:00Z";
  private static final String PLAYED_MATCH_STATUS = "Played";
  private static final String AWARDED_MATCH_STATUS = "Awarded";
  /**
   * “Suspended” games: If a match begins and is stopped by the referee and we are unsure if it will be resumed later
   * that day, it bust be temporarily “suspended”.
   */
  private static final String SUSPENDED_MATCH_STATUS = "Suspended";
  private static final String CANCELLED_MATCH_STATUS = "Cancelled";
  /**
   * “Postponed” games: If a game has not started and is canceled (and will not start that day) the fixture status must
   * be updated to “postponed”.
   */
  private static final String POSTPONED_MATCH_STATUS = "Postponed";
  private static final String PLAYING_MATCH_STATUS = "Playing";

  // OPTA CONSTANTS FOR PERIOD ID
  private static final Integer FIRST_HALF_OPTA_PERIOD_ID = 1;
  private static final Integer SECOND_HALF_OPTA_PERIOD_ID = 2;
  private static final Integer ET_FIRST_HALF_OPTA_PERIOD_ID = 3;
  private static final Integer ET_SECOND_HALF_OPTA_PERIOD_ID = 4;
  public static final Integer PENALTY_SHOOTOUT_OPTA_PERIOD_ID = 5;
  private static final Integer HALF_TIME_OPTA_PERIOD_ID = 10;
  private static final Integer END_OF_SECOND_HALF_OPTA_PERIOD_ID = 11;
  private static final Integer ET_HALF_TIME_OPTA_PERIOD_ID = 12;
  private static final Integer END_OF_ET_OPTA_PERIOD_ID = 13;
  private static final Integer FULL_TIME_OPTA_PERIOD_ID = 14;
  private static final Integer PRE_MATCH_OPTA_PERIOD_ID = 16;

  private static final List<Integer> FIRST_HALF_PERIOD_IDS = List.of(FIRST_HALF_OPTA_PERIOD_ID,
    PRE_MATCH_OPTA_PERIOD_ID);
  private static final List<Integer> END_FIRST_HALF_PERIOD_IDS = List.of(HALF_TIME_OPTA_PERIOD_ID);
  private static final List<Integer> SECOND_HALF_PERIOD_IDS = List.of(SECOND_HALF_OPTA_PERIOD_ID);
  private static final List<Integer> END_SECOND_HALF_PERIOD_IDS = List.of(END_OF_SECOND_HALF_OPTA_PERIOD_ID);
  private static final List<Integer> EXTRA_FIRST_HALF_PERIOD_IDS = List.of(ET_FIRST_HALF_OPTA_PERIOD_ID);

  private static final List<Integer> END_EXTRA_FIRST_HALF_PERIOD_IDS = List.of(ET_HALF_TIME_OPTA_PERIOD_ID);
  private static final List<Integer> EXTRA_SECOND_HALF_PERIOD_IDS = List.of(ET_SECOND_HALF_OPTA_PERIOD_ID);
  public static final Set<Integer> END_MATCH_PERIOD_IDS = Set.of(PENALTY_SHOOTOUT_OPTA_PERIOD_ID,
    END_OF_ET_OPTA_PERIOD_ID, FULL_TIME_OPTA_PERIOD_ID);


  public static final List<Integer> PLAYING_PERIOD_IDS = new ArrayList<>();

  static {
    PLAYING_PERIOD_IDS.addAll(FIRST_HALF_PERIOD_IDS);
    PLAYING_PERIOD_IDS.addAll(SECOND_HALF_PERIOD_IDS);
    PLAYING_PERIOD_IDS.addAll(EXTRA_FIRST_HALF_PERIOD_IDS);
    PLAYING_PERIOD_IDS.addAll(EXTRA_SECOND_HALF_PERIOD_IDS);
  }

  public static Instant parseMatchDate(String matchDate, String matchTime) {
    String matchTimeOut = isNotBlank(matchTime) ? matchTime : DEFAULT_MATCH_TIME;
    String dateTime = matchDate.replace("Z", "T") + matchTimeOut;
    return Instant.parse(dateTime);
  }
  
  public static FeedFixtureStatus convertMatchStatus(MatchDetails matchDetails) {
    if (nonNull(matchDetails)) {
      Integer periodId = matchDetails.getPeriodId(); // end of second half
      String matchStatus = matchDetails.getMatchStatus();
      //Fixture | Playing | Played | Cancelled | Postponed | Suspended | Awarded
      if ((nonNull(periodId) && END_MATCH_PERIOD_IDS.contains(periodId)) || PLAYED_MATCH_STATUS.equals(matchStatus) ||
        AWARDED_MATCH_STATUS.equals(matchStatus)) {
        return PLAYED;
      } else if (SUSPENDED_MATCH_STATUS.equals(matchStatus)) {
        return SUSPENDED;
      } else if (CANCELLED_MATCH_STATUS.equals(matchStatus)) {
        return CANCELLED;
      } else if (POSTPONED_MATCH_STATUS.equals(matchStatus)) {
        return POSTPONED;
      } else if (PLAYING_MATCH_STATUS.equals(matchStatus)) {
        return LIVE;
      }
    }
    return FIXTURE;
  }

  public static MatchPeriod convertOptaPeriodId(Integer periodId) {
    if (isNull(periodId)) {
      // this should only happen if the match is cancelled, in which case Opta does not send a periodId
      // what do we do? for now it should be ok...
      return SECOND_HALF;
    }

    if (FIRST_HALF_PERIOD_IDS.contains(periodId)) {
      return FIRST_HALF;
    } else if (END_FIRST_HALF_PERIOD_IDS.contains(periodId)) {
      return HALF_TIME;
    } else if (SECOND_HALF_PERIOD_IDS.contains(periodId)) {
      return SECOND_HALF;
    } else if (END_SECOND_HALF_PERIOD_IDS.contains(periodId)) {
      return END_REGULAR_TIMES;
    } else if (EXTRA_FIRST_HALF_PERIOD_IDS.contains(periodId)) {
      return EXTRA_FIRST_HALF;
    } else if (END_EXTRA_FIRST_HALF_PERIOD_IDS.contains(periodId)) {
      return EXTRA_HALF_TIME;
    } else if (EXTRA_SECOND_HALF_PERIOD_IDS.contains(periodId)) {
      return EXTRA_SECOND_HALF;
    } else if (END_MATCH_PERIOD_IDS.contains(periodId)) {
      return END_MATCH;
    } else {
      throw new IllegalArgumentException(format("PeriodId %s not supported", periodId));
    }
  }
}
