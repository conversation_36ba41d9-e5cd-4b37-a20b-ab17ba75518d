package com.wsf.dataingestor.opta.parsers;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.models.ma3.MatchEvents;
import com.wsf.dataingestor.parsers.FeedParser;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static java.util.Collections.emptyMap;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class MA3DPFeedParser implements FeedParser {

  public static final String POSITIONS_QUALIFIER_ID = "44";
  public static final String LINEUP_QUALIFIER_ID = "30";
  public static final String SETUP_EVENT_ID = "34";

  private final XmlMapper mapper;

  public MA3DPFeedParser() {
    this.mapper = new XmlMapper();
  }

  @Override
  public List<PlayerDataDTO> parseFeed(String ma3dpFeed) throws IOException {

    MatchEvents matchEvents = mapper.readValue(ma3dpFeed, MatchEvents.class);

    String competitionCode = matchEvents.getMatchInfo().getCompetition().getCode();

    Integer matchDay = matchEvents.getMatchInfo().getWeek();

    int goalHome = Integer.parseInt(matchEvents.getLiveData().getMatchDetails().getScores().getTotal().getHome());
    int goalAway = Integer.parseInt(matchEvents.getLiveData().getMatchDetails().getScores().getTotal().getHome());

    String homeTeamId = null;
    String awayTeamId = null;

    for (MatchEvents.Contestant contestant : matchEvents.getMatchInfo().getContestants().getContestantList()) {
      if (contestant.getPosition().equals("home")) {
        homeTeamId = contestant.getId();
      } else if (contestant.getPosition().equals("away")) {
        awayTeamId = contestant.getId();
      }
    }

    if (isNull(homeTeamId) || isNull(awayTeamId)) {
      throw new IllegalStateException("Either HomeTeam or AwayTeam is missing");
    }

    List<MatchEvents.Event> events = matchEvents.getLiveData().getEvents().getEventList();

    Map<String, List<MatchEvents.Event>> playerIdToEvents = newHashMap();

    for (MatchEvents.Event event : events) {

      if (nonNull(event.getPlayerId())) {
        playerIdToEvents.merge(event.getPlayerId(), newArrayList(event), (oldValue, newValue) -> {
          oldValue.addAll(newValue);
          return oldValue;
        });
      }
    }

    PlayerIndexBuilder playerIndexBuilder = PlayerIndexBuilder
      .builder().competitionCode(competitionCode).matchDay(matchDay).goalHome(goalHome).goalAway(goalAway).build();

    return playerIdToEvents.entrySet()
      .stream()
      .map(entry -> playerIndexBuilder.buildPlayerIndexDTO(entry.getKey().trim(), entry.getValue()))
      .collect(toList());
  }

  @Slf4j
  @Builder
  static class PlayerIndexBuilder {

    final String competitionCode;
    final Integer matchDay;
    final int goalHome;
    final int goalAway;

    PlayerDataDTO buildPlayerIndexDTO(String playerId, List<MatchEvents.Event> events) {
      return PlayerDataDTO
        .builder().player(null).stats(emptyMap()).build();
    }
  }
}
