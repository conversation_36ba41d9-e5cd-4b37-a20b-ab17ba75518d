package com.wsf.dataingestor.opta.parsers.events;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Map;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.function.Function;
import com.google.common.collect.Sets;

import static com.google.common.collect.Sets.intersection;
import static java.util.Arrays.stream;
import static java.util.Collections.disjoint;
import static java.util.stream.Collectors.toMap;

@Data
@AllArgsConstructor
@SuperBuilder
public abstract class EventInfo {

  private final boolean outcomeHasToBeZero;
  private final boolean outcomeHasToBeOne;
  @Builder.Default
  private final Map<Integer, Set<String>> qualifiersIdAndValuesToInclude = Map.of();
  @Builder.Default
  private final Set<Integer> qualifiersIdToExclude = Set.of();
  @Builder.Default
  private final Set<Integer> qualifiersToIgnore = Set.of();
  @Builder.Default
  private final BiPredicate<EventInfo, FeedEvent> includeEventFn = EventInfo::defaultIncludeEventFn;
  private final boolean parseRelatedEventInsteadOfMain;
  private final boolean shouldUseMainEventId;
  private final boolean shouldMainEventIncludeRelatedEventId;

  public boolean includeEvent(FeedEvent event) {
    return includeEventFn.test(this, event);
  }

  public static boolean defaultIncludeEventFn(EventInfo eventInfo, FeedEvent event) {
    Map<Integer, String> qualifiers = event.getQualifiers();
    boolean qualifierCheckToExcludeCheck =
      eventInfo.qualifiersIdToExclude.isEmpty() || disjoint(qualifiers.keySet(), eventInfo.qualifiersIdToExclude);
    boolean qualifiersToIncludeCheck =
      eventInfo.qualifiersIdAndValuesToInclude.isEmpty() || hasSupportedQualifier(eventInfo, qualifiers);
    boolean outcomeZeroCheck = !eventInfo.outcomeHasToBeZero || event.getOutcome() == 0;
    boolean outcomeOneCheck = !eventInfo.outcomeHasToBeOne || event.getOutcome() == 1;
    boolean outcomeCheck = outcomeZeroCheck && outcomeOneCheck;
    return outcomeCheck && qualifierCheckToExcludeCheck && qualifiersToIncludeCheck;
  }

  private static boolean hasSupportedQualifier(EventInfo eventInfo, Map<Integer, String> qualifiers) {
    Map<Integer, Set<String>> qualifiersIdAndValuesToInclude = eventInfo.qualifiersIdAndValuesToInclude;

    Sets.SetView<Integer> commonQualifiers = intersection(qualifiers.keySet(), qualifiersIdAndValuesToInclude.keySet());

    if (commonQualifiers.isEmpty()) {
      return false;
    }

    Integer commonQualifier = commonQualifiers
      .stream()
      .findFirst().get();
    String qualifierValue = qualifiers.get(commonQualifier);
    Set<String> qualifierExpectedValues = qualifiersIdAndValuesToInclude.get(commonQualifier);
    return qualifierExpectedValues.isEmpty() || qualifierExpectedValues.contains(qualifierValue);
  }

  public static Map<Integer, Set<String>> defaultQualifierBuilder(int... qualifiers) {
    return stream(qualifiers).boxed()
      .collect(toMap(Function.identity(), i -> Set.of()));
  }
}
