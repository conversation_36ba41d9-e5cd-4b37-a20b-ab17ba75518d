package com.wsf.dataingestor.opta.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.opta.models.tm3.TeamSquads;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.models.SquadsFeed.FeedProvider.OPTA;
import static com.wsf.dataingestor.opta.parsers.OptaPositionUtils.parsePlayerPosition;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
public class TM3FeedParser implements FeedParser<SquadsFeed> {

  private static final String FEED_CODE = "tm3";
  private static final DateTimeFormatter BIRTH_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

  private final ObjectMapper mapper;
  private final OptaFeedParserUtils optaFeedParserUtils;

  @Override
  public SquadsFeed parseFeed(byte[] squadsFeed) throws IOException {
    log.debug("Parsing TM3 feed: {}", squadsFeed);

    TeamSquads teamSquads = mapper.readValue(squadsFeed, TeamSquads.class);

    return parseFeed(teamSquads);
  }

  @Override
  public SquadsFeed parseFeed(String squadsFeed) throws IOException {
    log.debug("Parsing TM3 feed: {}", squadsFeed);

    TeamSquads teamSquads = mapper.readValue(squadsFeed, TeamSquads.class);

    return parseFeed(teamSquads);
  }

  private SquadsFeed parseFeed(TeamSquads teamSquads) {
    List<TeamSquads.Squad> squads = teamSquads.getSquadList();

    if (teamSquads.getSquadList()
      .size() > 0) {

      String lastUpdatedTs = String.valueOf(teamSquads.getLastUpdated());

      List<SquadPlayerDTO> squadPlayers = squads
        .stream()
        .map(optaTeam -> processTeam(lastUpdatedTs, optaTeam))
        .flatMap(List::stream)
        .collect(toList());

      String tournamentCalendarId = teamSquads.getSquadList()
        .stream()
        .map(TeamSquads.Squad::getTournamentCalendarId)
        .findFirst()
        .orElse("");

      String feedId = buildFeedId(FEED_CODE, tournamentCalendarId, abs(teamSquads.hashCode()), now().toEpochMilli());

      Tournament tournament = optaFeedParserUtils.getTournament(tournamentCalendarId)
        .orElseThrow(() -> new IllegalArgumentException(
          format("Tournament with optaCalendarId %s not found", tournamentCalendarId)));

      if (isNull(tournament)) {
        throw new IllegalArgumentException(
          format("could not find tournament with optaCalendarId %s", tournamentCalendarId));
      }

      return SquadsFeed
        .builder().feedId(feedId).squadPlayers(squadPlayers).provider(OPTA).build();
    } else {
      return SquadsFeed
        .builder().feedId("").squadPlayers(emptyList()).provider(OPTA).build();
    }
  }

  private List<SquadPlayerDTO> processTeam(String lastUpdatedTs, TeamSquads.Squad optaTeam) {
    SquadPlayerDTO.TeamDTO teamDTO = SquadPlayerDTO.TeamDTO.of(optaTeam.getContestantId(), optaTeam.getContestantName(),
      optaTeam.getContestantCode());

    return optaTeam.getPersonList()
      .stream()
      .filter(player -> player.getType().equals("player"))
      .filter(player -> player.getActive().equals("yes"))
      .filter(player -> player.getStatus().equals("active"))
      .map(player -> {
        try {
          Player.Position position = parsePlayerPosition(player.getPosition());
          String playerId = player.getId();
          String firstName = player.getFirstName();
          String lastName = player.getLastName();
          String matchName = player.getMatchName();
          String birthDateStr = player.getDateOfBirth();

          LocalDate birthDate = ofNullable(birthDateStr)
            .map(bd -> LocalDate.parse(bd, BIRTH_DATE_FORMAT))
            .orElse(null);

          return SquadPlayerDTO
            .builder()
            .feedId(lastUpdatedTs)
            .playerId(playerId)
            .firstName(firstName)
            .lastName(lastName)
            .matchName(matchName)
            .birthDate(birthDate)
            .team(teamDTO)
            .position(position)
            .provider(SquadPlayerDTO.Provider.OPTA)
            .build();
        } catch (DateTimeParseException e) {
          log.error("Skipping player {}. Error parsing birthDate: {}", player, e.getMessage());
          return null;
        } catch (Exception e) {
          log.error("Skipping player {}. Error: {}", player, e);
          return null;
        }
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

}
