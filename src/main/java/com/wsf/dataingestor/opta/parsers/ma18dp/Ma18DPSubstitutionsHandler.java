package com.wsf.dataingestor.opta.parsers.ma18dp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.cache.OptaCachedEventService.EventEntity;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.parsers.events.FeedEvent;

import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_OFF;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_ON;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_RELATED_EVENT_ID;
import static java.time.Duration.between;
import static java.time.Instant.now;
import static java.util.Optional.ofNullable;

@Service
@RequiredArgsConstructor
@Slf4j
public class Ma18DPSubstitutionsHandler {
  private static final List<Integer> substitutionEventTypes = List.of(EVENT_ID_PLAYER_SUB_ON, EVENT_ID_PLAYER_SUB_OFF);

  private final OptaCachedEventService optaCachedEventService;
  private final MetricsManager metricsManager;

  public void handle(FeedEvent feedEvent, String playerTeamId) {
    boolean isASubEvent = substitutionEventTypes.contains(feedEvent.getTypeId());
    boolean isFirstEventOccurrence = Objects.isNull(optaCachedEventService.get(playerTeamId, feedEvent.getEventId()));
    if (isASubEvent && isFirstEventOccurrence) {
      log.info("Handling substitution event: {}", feedEvent.getEventId());
      metricsManager.SUBSTITUTIONS_RECEIVED.increment();

      getRelatedEventEntity(feedEvent, playerTeamId).ifPresent(relatedEventEntity -> {
        log.info("Related event found for substitution: {}", relatedEventEntity.getExternalPlayerId());
        metricsManager.RELATED_SUBSTITUTIONS_RECEIVED.increment();
        metricsManager.RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY.record(between(relatedEventEntity.getTimestamp(), now()));
      });
    }
  }

  private Optional<EventEntity> getRelatedEventEntity(FeedEvent event, String playerTeamId) {
    return ofNullable(event.getQualifiers())
      .map(eventQualifiersMap -> eventQualifiersMap.get(QUALIFIER_ID_RELATED_EVENT_ID))
      .map(relatedEventId -> optaCachedEventService.get(playerTeamId, relatedEventId));
  }
}
