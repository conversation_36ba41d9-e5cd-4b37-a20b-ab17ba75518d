package com.wsf.dataingestor.opta.parsers;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.models.ma2.MA2DPFeed;
import com.wsf.dataingestor.opta.parsers.ma18dp.ProviderPlayerKey;
import com.wsf.dataingestor.opta.parsers.ma2.StatsConstants;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.parsers.ParsersUtils;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static com.google.common.collect.Maps.newHashMap;
import static java.lang.Math.abs;
import static java.time.Instant.now;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.toList;


@Slf4j
@Service
public class MA2DPFeedParser implements FeedParser<MatchDataFeed> {

  private static final String FEED_CODE = "ma2dp";

  private final ObjectReader reader;
  private final OptaFeedParserUtils optaFeedParserUtils;

  @Autowired
  public MA2DPFeedParser(ObjectMapper jsonObjectMapper, OptaFeedParserUtils optaFeedParserUtils) {
    this.reader = jsonObjectMapper.readerFor(MA2DPFeed.class);
    this.optaFeedParserUtils = optaFeedParserUtils;
  }

  @Override
  public MatchDataFeed parseFeed(byte[] feed) throws IOException {
    if (log.isDebugEnabled()) {
      log.debug("Parsing MA2 feed: {}", new String(feed));
    }
    Instant ts = now();
    MA2DPFeed matchStats = reader.readValue(feed);
    return parseFeed(matchStats, ts);
  }

  @Override
  public MatchDataFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing MA2 feed: {}", feed);
    Instant ts = now();
    MA2DPFeed matchStats = reader.readValue(feed, MA2DPFeed.class);
    return parseFeed(matchStats, ts);
  }

  private MatchDataFeed parseFeed(MA2DPFeed matchStats, Instant receivedTs) {
    try {
      MA2DPFeed.LiveData liveData = matchStats.getContent().getLiveData();
      String fixtureId = liveData.getMatchDetails().getId();
      Fixture fixture = optaFeedParserUtils.getFixtureOrThrow(fixtureId);
      MA2DPFeed.Stats stats = liveData.getMatchDetails().getStats();

      List<PlayerDataDTO> playerDataDTOS = stats.getTeams()
        .stream()
        .map((teamLineUp) -> getPlayersData(fixture, teamLineUp))
        .flatMap(List::stream)
        .collect(toList());

      return MatchDataFeed
        .builder()
        .receivedTs(receivedTs)
        .latestUpdateTs(receivedTs)
        .fixture(fixture)
        .feedId(ParsersUtils.buildFeedId(FEED_CODE, fixtureId, abs(matchStats.hashCode()), receivedTs.toEpochMilli()))
        .fixtureStatus(FeedFixtureStatus.LIVE)
        .playersData(playerDataDTOS)
        .provider(MatchDataFeed.FeedProvider.OPTA)
        .build();
    } catch (IllegalStateException e) {
      log.error("Can't process the feed", e);
      throw e;
    }
  }

  private List<PlayerDataDTO> getPlayersData(Fixture fixture, MA2DPFeed.Team teamLineUp) {
    return teamLineUp.getPlayers()
      .stream()
      .map(player -> {
        Map<String, Number> translatedStats = translateStats(player.getStats());
        if (translatedStats.isEmpty()) {
          return null;
        }

        ProviderPlayerKey providerPlayerKey = new ProviderPlayerKey(player.getPlayerId(), teamLineUp.getContestantId());

        return getPlayerData(providerPlayerKey, fixture, translatedStats);
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private PlayerDataDTO getPlayerData(ProviderPlayerKey providerPlayerKey, Fixture fixture,
                                      Map<String, Number> translatedStats) {
    Player player = optaFeedParserUtils.getPlayer(providerPlayerKey.id(), fixture);

    return PlayerDataDTO.buildPlayerData(now(), translatedStats, player, providerPlayerKey, null, true, true,
      randomUUID().toString());
  }

  static Map<String, Number> translateStats(Map<String, String> stats) {
    Map<String, Number> mappedStats = newHashMap();

    // in case of red card as a result of 2 yellow cards, ma2dp sends 1 red card, 1 secondYellow and 0 yellowcard
    // which is different than what the ma2 is sending
    int yellowCard = Integer.parseInt(stats.getOrDefault("secondYellow", stats.getOrDefault("yellowCard", "0")));

    if (yellowCard != 0) {
      mappedStats.put("yellowCards", yellowCard);
    }

    int penaltiesMissed = Stream
      .of(stats.get("attPenMiss"), stats.get("attPenPost"), stats.get("attPenTarget"))
      .filter(Objects::nonNull)
      .mapToInt(Integer::parseInt).sum();

    if (penaltiesMissed != 0) {
      mappedStats.put("penaltiesMissed", penaltiesMissed);
    }

    int penaltyGoals = Integer.parseInt(stats.getOrDefault("attPenGoal", "0"));

    if (penaltyGoals != 0) {
      mappedStats.put("penalties", penaltyGoals + penaltiesMissed);
    }

    //    int totalAttAssist = Integer.parseInt(stats.getOrDefault("totalAttAssist", "0"));
    //    int goalAssist = Integer.parseInt(stats.getOrDefault("goalAssist", "0"));
    //
    //    int keyPasses = totalAttAssist - goalAssist;
    //
    //    if (keyPasses != 0) {
    //      mappedStats.put("keyPasses", keyPasses);
    //    }

    stats.forEach((key, value) -> {
      if (StatsConstants.OPTA_TO_WSF_PLAYER_STATS_LIVE.containsKey(key)) {
        mappedStats.put(StatsConstants.OPTA_TO_WSF_PLAYER_STATS_LIVE.get(key), Integer.parseInt(value));
      }
    });

    return mappedStats;
  }
}
