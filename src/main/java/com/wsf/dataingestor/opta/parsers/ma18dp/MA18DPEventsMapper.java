package com.wsf.dataingestor.opta.parsers.ma18dp;

import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.cache.OptaCachedEventService.EventEntity;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityEventDTOFactory;
import com.wsf.dataingestor.opta.parsers.errors.MissingRelatedQualifierValueException;
import com.wsf.dataingestor.opta.parsers.events.AbstractEventsMapper;
import com.wsf.dataingestor.opta.parsers.events.EntityEventFilter;
import com.wsf.dataingestor.opta.parsers.events.FeedEvent;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.opta.parsers.Utils.convertOptaPeriodId;
import static java.lang.Integer.parseInt;
import static java.util.Comparator.comparing;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toList;

@Slf4j
@SuperBuilder
public class MA18DPEventsMapper extends AbstractEventsMapper {

  private final OptaCachedEventService optaCachedEventService;
  private final Ma18DPSubstitutionsHandler ma18DPSubstitutionsHandler;

  public List<EntityEventDTO> translatePlayerEvents(String playerId, String playerTeamId, boolean isPlayerUnknown,
                                                    List<? extends FeedEvent> events) {
    return events
      .stream()
      .sorted(comparing(FeedEvent::getLastModified))
      .peek(event -> ma18DPSubstitutionsHandler.handle(event, playerTeamId))
      .peek(event -> optaCachedEventService.put(playerTeamId, event, playerId))
      .map(event -> translateSingleEntityEventToManyWsfEvents(playerEventTypeIdToQualifiersId, playerId, playerTeamId,
        isPlayerUnknown, event))
      .flatMap(List::stream)
      .collect(toList());
  }

  @Override
  protected EntityEventDTO handleEventMapping(FeedEvent event, String teamId, String entityId, boolean isUnknownEntity,
                                              EntityEventFilter eventInfo) {
    try {
      if (eventInfo.isParseRelatedEventInsteadOfMain()) {
        return mapRelatedEventToEntityEventDto(event, teamId, isUnknownEntity, eventInfo);
      }

      if (eventShouldBeProcessed(teamId, event, eventInfo)) {
        String relatedEventExternalId = getRelatedEventExternalIdForMainEvent(teamId, event, eventInfo);
        return mapEventToEntityEventDto(event, teamId, entityId, isUnknownEntity, eventInfo, relatedEventExternalId);
      }
    } catch (MissingRelatedQualifierValueException e) {
      log.error("error=no_event_info_error - Empty relation found for Opta event of type {} towards {} for eventId={}",
        event.getTypeId(), eventInfo.getStat(), event.getId());
      metricsManager.NO_EVENT_INFO_ERROR.increment();
    }
    return null;
  }

  @Override
  protected List<EntityEventDTO> getStreamEvents(FeedEvent event, String teamId, String entityId,
                                                 boolean isUnknownEntity, List<EntityEventDTO> parsedEvents) {
    if (parsedEvents.isEmpty()) {
      return List.of(buildUnknownEvent(event, entityId, teamId, isUnknownEntity));
    }
    return parsedEvents;
  }

  private boolean eventShouldBeProcessed(String teamId, FeedEvent event, EntityEventFilter eventInfo) {
    if (eventInfo.isShouldMainEventIncludeRelatedEventId()) {

      Optional<String> relatedEventId = event.getRelatedQualifierValue();
      if (relatedEventId.isEmpty()) {
        return false;
      }

      EventEntity relatedEvent = optaCachedEventService.get(teamId, relatedEventId.get());
      return nonNull(relatedEvent) && eventInfo.includeEvent(event);
    } else {
      return eventInfo.includeEvent(event);
    }
  }

  private String getRelatedEventExternalIdForMainEvent(String teamId, FeedEvent event, EntityEventFilter eventInfo) {
    if (!eventInfo.isShouldMainEventIncludeRelatedEventId()) {
      return null;
    }

    return event.getRelatedQualifierValue()
      .map(relatedEventId -> optaCachedEventService.get(teamId, relatedEventId))
      .map(EventEntity::getExternalEventId)
      .orElse(null);
  }

  private EntityEventDTO mapRelatedEventToEntityEventDto(FeedEvent event, String teamId, boolean isUnknownEntity,
                                                         EntityEventFilter eventInfo) {
    String eventId = event.getEventId();

    String relatedEventId = event.getRelatedQualifierValue()
      .orElse(null);
    if (isNull(relatedEventId)) {
      return null;
    }

    log.info("Event of type {} - Looking the {} relation with relatedEventId {}, teamId={} eventId={}",
      event.getTypeId(), eventInfo.getStat(), relatedEventId, teamId, eventId);

    EventEntity cachedRelatedEntity = optaCachedEventService.get(teamId, relatedEventId);

    if (isNull(cachedRelatedEntity)) {
      log.warn("error=no_event_info_error - did not find {} relation with relatedEventId {} for eventId={}, teamId={}",
        eventInfo.getStat(), relatedEventId, eventId, teamId);
      metricsManager.NO_EVENT_INFO_ERROR.increment();
      return null;
    }

    log.info("Found the {} relation with relatedEventId {} for teamId={} eventId={}, relatedEvent={}",
      eventInfo.getStat(), relatedEventId, teamId, eventId, cachedRelatedEntity);
    String newEventId = getEventId(event, eventInfo, cachedRelatedEntity);
    MatchPeriod matchPeriod = convertOptaPeriodId(event.getPeriodId());
    String externalTeamId = event.getContestantId();

    return EntityEventDTOFactory.withRelatedEvent(newEventId, event.getId(), cachedRelatedEntity.getPlayerId(),
      cachedRelatedEntity.getExternalPlayerId(), teamId, externalTeamId, isUnknownEntity, eventInfo.getStat(),
      matchPeriod, parseInt(event.getTimeMin()), false, false, false, event.getTimestamp());
  }

  private String getEventId(FeedEvent mainEvent, EntityEventFilter eventInfo, EventEntity cachedRelatedEntity) {
    boolean shouldUseMainEventId = eventInfo.isShouldUseMainEventId();
    return shouldUseMainEventId ? mainEvent.getId() : cachedRelatedEntity.getExternalEventId();
  }

  private static EntityEventDTO buildUnknownEvent(FeedEvent event, String entityId, String teamId,
                                                  boolean isUnknownEntity) {
    MatchPeriod matchPeriod = convertOptaPeriodId(event.getPeriodId());
    String externalTeamId = event.getContestantId();

    return EntityEventDTOFactory.standaloneEvent(event.getId(), entityId, event.getPlayerId(), teamId, externalTeamId,
      isUnknownEntity, SoccerMatchEvent.UNKNOWN_EVENT, matchPeriod, parseInt(event.getTimeMin()), false, true, false,
      event.getTimestamp());
  }
}
