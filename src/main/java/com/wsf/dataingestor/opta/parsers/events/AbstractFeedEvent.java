package com.wsf.dataingestor.opta.parsers.events;

import java.util.Map;
import java.util.Optional;
import com.wsf.dataingestor.opta.parsers.errors.MissingRelatedQualifierValueException;

import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_RELATED_EVENT_ID;
import static java.util.Objects.isNull;

public abstract class AbstractFeedEvent implements FeedEvent {
  @Override
  public Optional<String> getRelatedQualifierValue() {
    Map<Integer, String> qualifiers = getQualifiers();

    if (isNull(qualifiers)) {
      return Optional.empty();
    }

    if (!qualifiers.containsKey(QUALIFIER_ID_RELATED_EVENT_ID)) {
      return Optional.empty();
    }

    String value = qualifiers.get(QUALIFIER_ID_RELATED_EVENT_ID);
    if (value == null || value.isEmpty() || value.equals("NULL")) {
      throw new MissingRelatedQualifierValueException(this.getEventId());
    }
    return Optional.of(value);
  }
}
