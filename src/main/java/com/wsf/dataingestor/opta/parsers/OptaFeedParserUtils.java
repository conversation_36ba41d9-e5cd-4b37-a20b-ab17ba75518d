package com.wsf.dataingestor.opta.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.Optional;
import java.util.function.Supplier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.services.PlayerService.buildMasterPlayer;
import static com.wsf.domain.common.ExternalProvider.OPTA;
import static java.lang.String.format;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class OptaFeedParserUtils {

  private final CompetitionService competitionService;
  private final TournamentService tournamentService;
  private final PlayerService playerService;
  private final TeamService teamService;
  private final FixtureService fixtureService;

  public Competition getCompetition(String optaCompetitionId) {
    return competitionService.findByOptaCompetitionId(optaCompetitionId);
  }

  public Optional<Tournament> getTournament(String optaCalendarId) {
    return tournamentService.findByOptaExternalId(optaCalendarId);
  }

  public Team getTeamOrCreateUnmapped(String competitionId, String teamId, String teamName, String teamAbbreviation) {
    return teamService.findByExternalIdOrCreateUnmapped(competitionId, teamId, teamName, teamAbbreviation, OPTA,
      teamService::findMasterTeamByOptaId);
  }

  public Team getTeam(String competitionId, String teamId) {
    return teamService.findTeamByOptaId(competitionId, teamId);
  }

  public MasterPlayer getMasterPlayerOrCreateUnmapped(String externalFirstName, String externalLastName,
                                                      String externalMatchName, LocalDate birthDate,
                                                      String externalPlayerId) throws DateTimeParseException {
    Supplier<MasterPlayer> masterPlayerBuilder = () -> buildMasterPlayerFromOpta(externalFirstName, externalLastName,
      externalMatchName, birthDate, externalPlayerId);
    return playerService.findMasterPlayerByExternalIdOrCreateUnmapped(externalFirstName, externalLastName,
      externalMatchName, birthDate, externalPlayerId, "optaPlayerId", OPTA,
      playerService::findMasterPlayerByOptaPlayerId, masterPlayerBuilder);
  }

  public Fixture getFixtureOrThrow(String optaFixtureId) {
    return ofNullable(getFixtureByOptaFixtureId(optaFixtureId)).orElseThrow(
      () -> new IllegalArgumentException(format("could not find fixture %s in db", optaFixtureId)));
  }

  public Fixture getFixtureByOptaFixtureId(String optaFixtureId) {
    return fixtureService.findByOptaFixtureId(optaFixtureId);
  }

  public Fixture getFixtureByOptaIdOrTournamentAndTeamsAndDate(String optaFixtureId, String tournamentId,
                                                               String homeTeamId, String awayTeamId, Instant time) {
    return fixtureService.findByOptaFixtureIdOrTournamentAndTeamsAndDate(optaFixtureId, tournamentId, homeTeamId,
      awayTeamId, time);
  }

  public Player getPlayer(String playerId, Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getIdAsString();
    return playerService.findByOptaPlayerIdAndTournamentId(competitionId, playerId, tournamentId);
  }

  public Player getPlayerFromTournament(String optaPlayerId, String optaCalendarId) {
    var currentTournament = getTournament(optaCalendarId).orElseThrow();
    return playerService.findByOptaPlayerIdAndTournamentId(currentTournament.getCompetitionId(), optaPlayerId,
      currentTournament.getIdAsString());
  }

  private static MasterPlayer buildMasterPlayerFromOpta(String firstName, String lastName, String matchName,
                                                        LocalDate birthDate, String optaId) {
    return buildMasterPlayer(firstName, lastName, matchName, birthDate).optaPlayerId(optaId).build();
  }
}
