package com.wsf.dataingestor.opta.parsers.ma3;

import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.events.AbstractEventsMapper;
import com.wsf.dataingestor.opta.parsers.events.EntityEventFilter;
import com.wsf.dataingestor.opta.parsers.events.FeedEvent;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;

@Slf4j
@SuperBuilder
public class MA3EventsMapper extends AbstractEventsMapper {

  private final OptaCachedEventService optaCachedEventService;
  private final OptaFeedParserUtils optaFeedParserUtils;

  public List<EntityEventDTO> translatePlayerEvents(String playerId, String playerTeamId, boolean isUnknownPlayer,
                                                    List<? extends FeedEvent> events) {
    return events
      .stream()
      .sorted(comparing(FeedEvent::getTimestamp))
      .map(event -> translateSingleEntityEventToManyWsfEvents(playerEventTypeIdToQualifiersId, playerId, playerTeamId,
        isUnknownPlayer, event))
      .flatMap(List::stream)
      .collect(toList());
  }

  @Override
  protected EntityEventDTO handleEventMapping(FeedEvent event, String teamId, String entityId, boolean isUnknownEntity,
                                              EntityEventFilter eventInfo) {
    if (eventInfo.isParseRelatedEventInsteadOfMain()) {
      return null;
    }

    if (eventInfo.includeEvent(event)) {
      return mapEventToEntityEventDto(event, teamId, entityId, isUnknownEntity, eventInfo, null);
    }
    return null;
  }

  @Override
  protected List<EntityEventDTO> getStreamEvents(FeedEvent event, String teamId, String entityId,
                                                 boolean isUnknownEntity, List<EntityEventDTO> parsedEvents) {
    return parsedEvents;
  }
}
