package com.wsf.dataingestor.opta.parsers.ma2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Stream;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityEventDTOFactory;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed.MatchInfo;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed.Scores;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.ma18dp.ProviderPlayerKey;
import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser.SubData.SubInOrOut;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Lists.newArrayList;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.FIXTURE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.POSTPONED;
import static com.wsf.dataingestor.opta.parsers.OptaPositionUtils.parseDetailedPlayerPosition;
import static com.wsf.dataingestor.opta.parsers.Utils.convertMatchStatus;
import static com.wsf.dataingestor.opta.parsers.Utils.convertOptaPeriodId;
import static com.wsf.dataingestor.opta.parsers.Utils.parseMatchDate;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.lang.Math.abs;
import static java.lang.Math.max;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Collections.emptyMap;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.UUID.randomUUID;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;


@Slf4j
public class MA2FeedParser implements FeedParser<MatchDataFeed> {

  private static final String FEED_CODE = "ma2";
  private static final String SUBSTITUTE = "Substitute";

  private static final String YELLOW_CARDS_TYPE = "YC";
  private static final List<String> SENT_OFF_TYPES = newArrayList("Y2C", "RC");
  private static final String OWN_GOAL_TYPE = "OG";

  private final ObjectReader reader;
  private final StatsMapper statsMapper;
  private final OptaFeedParserUtils optaFeedParserUtils;

  public MA2FeedParser(ObjectMapper jsonObjectMapper, StatsMapper statsMapper,
                       OptaFeedParserUtils optaFeedParserUtils) {
    this.reader = jsonObjectMapper.readerFor(MA2Feed.class);
    this.optaFeedParserUtils = optaFeedParserUtils;
    this.statsMapper = statsMapper;
  }

  @Override
  public MatchDataFeed parseFeed(byte[] feed) throws IOException {
    if (log.isTraceEnabled()) {
      log.trace("Parsing MA2 feed: {}", new String(feed));
    }
    Instant ts = now();
    MA2Feed matchStats = reader.readValue(feed);
    return parseFeed(matchStats, ts);
  }

  @Override
  public MatchDataFeed parseFeed(String feed) throws IOException {
    log.trace("Parsing MA2 feed: {}", feed);
    Instant ts = now();
    MA2Feed matchStats = reader.readValue(feed, MA2Feed.class);
    return parseFeed(matchStats, ts);
  }

  private MatchDataFeed parseFeed(MA2Feed matchStats, Instant receivedTs) {
    try {
      MatchInfo matchInfo = matchStats.getMatchInfo();
      MA2Feed.LiveData liveData = matchStats.getLiveData();

      String fixtureId = matchInfo.getId();
      Fixture fixture = optaFeedParserUtils.getFixtureOrThrow(fixtureId);

      FeedFixtureStatus fixtureStatus = convertMatchStatus(liveData.getMatchDetails());

      String feedId = buildFeedId(FEED_CODE, fixtureId, abs(matchStats.hashCode()), receivedTs.toEpochMilli());

      Instant lastUpdatedTs = matchInfo.getLastUpdated();

      if (liveData.getLineUps()
        .isEmpty() || liveData.getLineUps()
        .size() == 1 || fixtureStatus == POSTPONED) {
        return buildEmptyFeed(matchInfo, liveData, receivedTs, fixture, fixtureStatus, feedId, lastUpdatedTs);
      } else {
        if (FIXTURE == fixtureStatus) {
          return processLineUps(receivedTs, fixture, matchInfo, liveData, fixtureStatus, feedId, lastUpdatedTs);
        } else {
          return processLiveOrFinishedFeed(receivedTs, fixture, matchInfo, liveData, fixtureStatus, feedId,
            lastUpdatedTs);
        }
      }
    } catch (IllegalStateException e) {
      log.error("Can't process the feed", e);
      throw e;
    }
  }

  private static Instant getMatchDate(MatchInfo matchInfo) {
    return parseMatchDate(matchInfo.getDate(), matchInfo.getTime());
  }

  private MatchDataFeed processLiveOrFinishedFeed(Instant receivedTs, Fixture fixture, MatchInfo matchInfo,
                                                  MA2Feed.LiveData liveData, FeedFixtureStatus fixtureStatus,
                                                  String feedId, Instant lastUpdatedTs) {

    // in case periodId is not returned (eg. cancelled matches)
    MA2Feed.MatchDetails matchDetails = liveData.getMatchDetails();
    MatchPeriod matchPeriod = convertOptaPeriodId(matchDetails.getPeriodId());

    boolean isFinalData = ofNullable(matchInfo.getPostMatch())
      .map(postMatch -> postMatch == 1)
      .orElse(false);

    Integer matchTime =
      fixtureStatus == LIVE ? maxOrNull(matchDetails.getMatchTime()) : maxOrNull(matchDetails.getMatchLengthMin());

    boolean extraTimeHappened = getExtraTimeHappenedFromScoreList(matchDetails.getScores());

    List<EntityEventDTO> substituteOffEvents = getSubstituteOffEvents(liveData, fixture);
    List<EntityEventDTO> substituteOnEvents = getSubstituteOnEvents(liveData, fixture);
    List<EntityEventDTO> playerMatchEvents = newArrayList();
    playerMatchEvents.addAll(getScorers(liveData, fixture));
    playerMatchEvents.addAll(getAssistPlayersEvents(liveData, fixture));
    playerMatchEvents.addAll(getYellowCardsEvents(liveData, substituteOffEvents, substituteOnEvents, fixture));
    playerMatchEvents.addAll(getSentOffEvents(liveData, substituteOffEvents, substituteOnEvents, fixture));
    playerMatchEvents.addAll(substituteOffEvents);
    playerMatchEvents.addAll(substituteOnEvents);

    Set<SoccerMatchEvent> supportedEventTypes = Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, RED_CARD, SUB_OFF, SUB_ON);

    List<MA2Feed.LineUp> lineUps = liveData.getLineUps();

    MA2Feed.LineUp firstTeamLineUp = lineUps.get(0);
    MA2Feed.LineUp secondTeamLineUp = lineUps.get(1);

    Map<String, Team> optaIdToTeam = Stream
      .of(fixture.getHomeTeam(), fixture.getAwayTeam())
      .collect(toMap(Team::getOptaId, identity()));

    TeamDataDTO firstTeamData = buildTeamData(fixture, firstTeamLineUp, optaIdToTeam, lastUpdatedTs);
    TeamDataDTO secondTeamData = buildTeamData(fixture, secondTeamLineUp, optaIdToTeam, lastUpdatedTs);

    List<MA2Feed.Substitute> substitutes = liveData.getSubstitutes();
    List<PlayerDataDTO> playerDataDTOS = getPlayersData(fixture, matchTime, firstTeamLineUp, substitutes,
      lastUpdatedTs);
    playerDataDTOS.addAll(getPlayersData(fixture, matchTime, secondTeamLineUp, substitutes, lastUpdatedTs));

    return MatchDataFeed
      .builder()
      .date(getMatchDate(matchInfo))
      .receivedTs(receivedTs)
      .latestUpdateTs(lastUpdatedTs)
      .fixture(fixture)
      .feedId(feedId)
      .matchTimeMin(matchTime)
      .matchPeriod(matchPeriod)
      .fixtureStatus(fixtureStatus)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .playersData(playerDataDTOS)
      .aggregatedPlayerMatchEvents(playerMatchEvents)
      .supportedEventTypes(supportedEventTypes)
      .isSnapshot(false)
      .isFinalData(isFinalData)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .extraTimeHappened(extraTimeHappened)
      .build();
  }

  private boolean getExtraTimeHappenedFromScoreList(Scores scores) {
    return ofNullable(scores)
      .map(Scores::getEt)
      .isPresent();
  }

  private TeamDataDTO buildTeamData(Fixture fixture, MA2Feed.LineUp teamLineUp, Map<String, Team> optaIdToTeam,
                                    Instant lastUpdatedTs) {
    String teamId = teamLineUp.getContestantId();
    Map<String, Number> teamStats = statsMapper.translateTeamStats(teamLineUp.getStats());
    Team team = ofNullable(optaIdToTeam.get(teamId)).orElseThrow(
      () -> new IllegalArgumentException(format("could not find team with OptaId %s in fixture %s", teamId, fixture)));
    return TeamDataDTO
      .builder().team(team).stats(teamStats)
      .timestamp(lastUpdatedTs).build();
  }

  private List<EntityEventDTO> getScorers(MA2Feed.LiveData liveData, Fixture fixture) {
    return liveData.getGoals()
      .stream()
      .map(goal -> {
        boolean ignore = goal.getType().equals(OWN_GOAL_TYPE);
        return getEventData(fixture, goal, goal.getScorerId(), GOAL, ignore, false);
      })
      .collect(toList());
  }

  private List<EntityEventDTO> getAssistPlayersEvents(MA2Feed.LiveData liveData, Fixture fixture) {
    return liveData.getGoals()
      .stream()
      .map(goal -> {
        boolean ignore = goal.getType().equals(OWN_GOAL_TYPE);
        return getEventData(fixture, goal, goal.getAssistPlayerId(), ASSIST_GOAL, ignore, false);
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getYellowCardsEvents(MA2Feed.LiveData liveData, List<EntityEventDTO> substituteOffEvents,
                                                    List<EntityEventDTO> substituteOnEvents, Fixture fixture) {
    return getCardEvents(liveData, substituteOffEvents, substituteOnEvents, fixture, YELLOW_CARD,
      event -> YELLOW_CARDS_TYPE.equals(event.getType()));
  }

  private List<EntityEventDTO> getSentOffEvents(MA2Feed.LiveData liveData, List<EntityEventDTO> substituteOffEvents,
                                                List<EntityEventDTO> substituteOnEvents, Fixture fixture) {
    return getCardEvents(liveData, substituteOffEvents, substituteOnEvents, fixture, RED_CARD,
      event -> SENT_OFF_TYPES.contains(event.getType()));
  }

  private List<EntityEventDTO> getCardEvents(MA2Feed.LiveData liveData, List<EntityEventDTO> substituteOffEvents,
                                             List<EntityEventDTO> substituteOnEvents, Fixture fixture,
                                             SoccerMatchEvent eventToMap, Predicate<MA2Feed.Card> eventFilter) {

    Set<String> startingElevenPlayerIds = liveData.getLineUps()
      .stream()
      .flatMap(lineup -> lineup.getPlayers()
        .stream())
      .filter(player -> !SUBSTITUTE.equals(player.getPosition()))
      .map(MA2Feed.Player::getPlayerId)
      .collect(toSet());

    var optaPlayerIdToSubOffEvent = substituteOffEvents
      .stream()
      .collect(toMap(EntityEventDTO::getExternalEntityId, identity()));

    var optaPlayerIdToSubOnEvent = substituteOnEvents
      .stream()
      .collect(toMap(EntityEventDTO::getExternalEntityId, identity()));

    List<MA2Feed.Card> cards = liveData.getCards();
    return cards
      .stream()
      .filter(eventFilter)
      .map(card -> {
        boolean isOnBench = isPlayerOnTheBench(card, optaPlayerIdToSubOffEvent, optaPlayerIdToSubOnEvent,
          startingElevenPlayerIds);
        return getEventData(fixture, card, card.getPlayerId(), eventToMap, false, isOnBench);
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getSubstituteOffEvents(MA2Feed.LiveData liveData, Fixture fixture) {
    List<MA2Feed.Substitute> substitutes = liveData.getSubstitutes();
    return substitutes
      .stream()
      .map(substitute -> getEventData(fixture, substitute, substitute.getPlayerOffId(), SUB_OFF, false, false))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getSubstituteOnEvents(MA2Feed.LiveData liveData, Fixture fixture) {
    List<MA2Feed.Substitute> substitutes = liveData.getSubstitutes();
    return substitutes
      .stream()
      .map(substitute -> getEventData(fixture, substitute, substitute.getPlayerOnId(), SUB_ON, false, false))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private EntityEventDTO getEventData(Fixture fixture, MA2Feed.Event event, String optaPlayerId,
                                      SoccerMatchEvent statName, boolean ignore, boolean isOnBench) {
    if (isNull(optaPlayerId)) {
      log.warn("OptaPlayerId is null for event {}", event);
      return null;
    }
    Player player = optaFeedParserUtils.getPlayer(optaPlayerId, fixture);
    String playerId;
    String teamId;
    boolean isPlayerUnknown = false;

    if (isNull(player)) {
      isPlayerUnknown = true;
      playerId = format("UNKNOWN_PLAYER_OPTA_%s", optaPlayerId);
      Team team = optaFeedParserUtils.getTeam(fixture.getTournament().getCompetitionId(), event.getContestantId());
      teamId = team.getIdAsString();
      log.error("could not find a match for player with optaId={}. Replacing with a fake id {}", optaPlayerId,
        playerId);
    } else {
      playerId = player.getId().toString();
      teamId = player.getTeam().getId().toString();
    }
    MatchPeriod matchPeriodType = convertOptaPeriodId(event.getPeriodId());

    String eventId = event.getEventId();
    String externalTeamId = event.getContestantId();

    return EntityEventDTOFactory.standaloneEvent(eventId, playerId, optaPlayerId, teamId, externalTeamId,
      isPlayerUnknown, statName, matchPeriodType, event.getTimeMin(), ignore, false, isOnBench, now());
  }

  private static MatchDataFeed buildEmptyFeed(MatchInfo matchInfo, MA2Feed.LiveData liveData, Instant receivedTs,
                                              Fixture fixture, FeedFixtureStatus fixtureStatus, String feedId,
                                              Instant lastUpdatedTs) {
    // in case periodId is not returned (eg. cancelled matches)
    int periodId = ofNullable(liveData.getMatchDetails().getPeriodId()).orElse(2);
    MatchPeriod matchPeriod = convertOptaPeriodId(periodId);
    return MatchDataFeed
      .builder()
      .date(getMatchDate(matchInfo))
      .receivedTs(receivedTs)
      .latestUpdateTs(lastUpdatedTs)
      .feedId(feedId)
      .fixture(fixture)
      .fixtureStatus(fixtureStatus)
      .matchPeriod(matchPeriod)
      .aggregatedPlayerMatchEvents(newArrayList())
      .isSnapshot(false)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
  }

  private MatchDataFeed processLineUps(Instant receivedTs, Fixture fixture, MatchInfo matchInfo,
                                       MA2Feed.LiveData liveData, FeedFixtureStatus fixtureStatus, String feedId,
                                       Instant lastUpdatedTs) {
    List<PlayerDataDTO> playerDataDTOS = retrieveLineups(fixture, liveData.getLineUps(), lastUpdatedTs);

    List<MA2Feed.LineUp> lineUps = liveData.getLineUps();

    MA2Feed.LineUp firstTeamLineUp = lineUps.get(0);
    MA2Feed.LineUp secondTeamLineUp = lineUps.get(1);

    Map<String, Team> optaIdToTeam = Stream
      .of(fixture.getHomeTeam(), fixture.getAwayTeam())
      .collect(toMap(Team::getOptaId, identity()));

    TeamDataDTO firstTeamData = buildTeamData(fixture, firstTeamLineUp, optaIdToTeam, lastUpdatedTs);
    TeamDataDTO secondTeamData = buildTeamData(fixture, secondTeamLineUp, optaIdToTeam, lastUpdatedTs);

    return MatchDataFeed
      .builder()
      .date(getMatchDate(matchInfo))
      .receivedTs(receivedTs)
      .latestUpdateTs(lastUpdatedTs)
      .fixture(fixture)
      .feedId(feedId)
      .matchTimeMin(0)
      .matchPeriod(FIRST_HALF)
      .fixtureStatus(fixtureStatus)
      .playersData(playerDataDTOS)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .aggregatedPlayerMatchEvents(newArrayList())
      .isSnapshot(true)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
  }

  private List<PlayerDataDTO> retrieveLineups(Fixture fixture, List<MA2Feed.LineUp> lineUps, Instant lastUpdatedTs) {
    return lineUps
      .stream()
      .map(lineup -> {
        return lineup.getPlayers()
          .stream()
          .filter(player -> !SUBSTITUTE.equals(player.getPosition()))
          .map(player -> buildPlayerData(new ProviderPlayerKey(player.getPlayerId(), lineup.getContestantId()), fixture,
            lastUpdatedTs, emptyMap(), null, false, true))
          .filter(Objects::nonNull)
          .collect(toList());
      })
      .flatMap(List::stream)
      .collect(toList());
  }

  private List<PlayerDataDTO> getPlayersData(Fixture fixture, Integer matchTime, MA2Feed.LineUp teamLineUp,
                                             List<MA2Feed.Substitute> substitutes, Instant lastUpdatedTs) {

    String formationUsed = teamLineUp.getFormationUsed();

    return teamLineUp.getPlayers()
      .stream()
      .map(player -> {
        String providerPlayerId = player.getPlayerId();

        Integer formationPlace = player.getFormationPlace();
        Player.DetailedPosition matchPosition = nonNull(formationUsed) && nonNull(formationPlace) ?
                                                parseDetailedPlayerPosition(formationUsed, formationPlace) :
                                                null;

        List<SubData> subData = calculateSubsData(substitutes, providerPlayerId);

        Integer minsPlayed = calculateMinsPlayed(player, subData, matchTime); // could be null
        Map<String, Number> translatedStats = statsMapper.translatePlayerStats(player.getPosition(), player.getStats(),
          minsPlayed);

        boolean hasPlayed = hasPlayed(player.getPosition(), subData);
        boolean isPlaying = isPlaying(player.getPosition(), subData, translatedStats);

        return buildPlayerData(new ProviderPlayerKey(providerPlayerId, teamLineUp.getContestantId()), fixture,
          lastUpdatedTs, translatedStats, matchPosition, hasPlayed, isPlaying);
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private PlayerDataDTO buildPlayerData(ProviderPlayerKey providerPlayerKey, Fixture fixture, Instant lastUpdatedTs,
                                        Map<String, Number> translatedStats, Player.DetailedPosition matchPosition,
                                        boolean hasPlayed, boolean isPlaying) {

    if (!isPlaying && !hasPlayed) {
      return null;
    }

    Player player = optaFeedParserUtils.getPlayer(providerPlayerKey.id(), fixture);

    return PlayerDataDTO.buildPlayerData(lastUpdatedTs, translatedStats, player, providerPlayerKey, matchPosition,
      isPlaying, hasPlayed, randomUUID().toString());
  }

  private static boolean hasPlayed(String position, List<SubData> subData) {
    boolean wasPlayerSubOn = subData
      .stream()
      .anyMatch(sd -> SubInOrOut.SUB_IN == sd.subInOrOut);
    return !SUBSTITUTE.equals(position) || wasPlayerSubOn;
  }

  private static boolean isPlaying(String position, List<SubData> subData, Map<String, Number> stats) {

    boolean gotRedCard = ofNullable(stats.get("redCard"))
      .map(val -> val.equals(1D))
      .orElse(false);

    boolean isPlaying;
    if (gotRedCard) {
      isPlaying = false;
    } else if (subData.size() == 2) { // sub in and sub out
      isPlaying = false;
    } else if (subData.size() == 1) {
      isPlaying = SubInOrOut.SUB_IN == subData.get(0).subInOrOut;
    } else {
      isPlaying = !SUBSTITUTE.equals(position);
    }
    return isPlaying;
  }

  private static List<SubData> calculateSubsData(List<MA2Feed.Substitute> substitutes, String playerId) {
    return substitutes
      .stream()
      .map(sub -> {
        if (sub.getPlayerOnId().equals(playerId)) {
          return new SubData(SubInOrOut.SUB_IN, sub.getTimeMin());
        } else if (sub.getPlayerOffId().equals(playerId)) {
          return new SubData(SubInOrOut.SUB_OUT, sub.getTimeMin());
        } else {
          return null;
        }
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private static boolean isPlayerOnTheBench(MA2Feed.Card card, Map<String, EntityEventDTO> optaPlayerIdToSubOffEvent,
                                            Map<String, EntityEventDTO> optaPlayerIdToSubOnEvent,
                                            Set<String> startingElevenPlayerIds) {
    EntityEventDTO subOffEvent = optaPlayerIdToSubOffEvent.get(card.getPlayerId());
    EntityEventDTO subOnEvent = optaPlayerIdToSubOnEvent.get(card.getPlayerId());
    boolean wasPlayerOnTheBench = !startingElevenPlayerIds.contains(card.getPlayerId()) && isNull(subOnEvent);
    boolean playerGotCardAfterSubOff = nonNull(subOffEvent) && subOffEvent.getTimeMin() < card.getTimeMin();
    boolean playerGotCardBeforeSubOn = nonNull(subOnEvent) && subOnEvent.getTimeMin() > card.getTimeMin();
    return wasPlayerOnTheBench || playerGotCardAfterSubOff || playerGotCardBeforeSubOn;
  }

  private static Integer calculateMinsPlayed(MA2Feed.Player player, List<SubData> subData, Integer matchTime) {

    String minsPlayed = player.getStats().get(MINS_PLAYED);
    if (nonNull(minsPlayed)) {
      return Integer.parseInt(minsPlayed);
    }

    if (!SUBSTITUTE.equals(player.getPosition()) && subData.isEmpty()) {
      return matchTime;
    } else if (SUBSTITUTE.equals(player.getPosition())) {
      return null;
    }

    if (subData.size() == 1) {
      if (isNull(matchTime)) {
        return null;
      } else {
        SubData data = subData.get(0);
        if (SubInOrOut.SUB_IN == data.subInOrOut) {
          int subTime = data.matchTime;
          return abs(matchTime - 1 - subTime); // we subtract 1 cause if the sub happened at minute 66:15, Opta sends 67
        } else {
          return data.matchTime;
        }
      }
    } else if (subData.size() == 2) {
      SubData subDataFirst = subData.get(0);
      SubData subDataSecond = subData.get(1);
      SubData subOn = SubInOrOut.SUB_IN == subDataFirst.subInOrOut ? subDataFirst : subDataSecond;
      SubData subOut = SubInOrOut.SUB_OUT == subDataFirst.subInOrOut ? subDataFirst : subDataSecond;
      return abs(subOut.matchTime - subOn.matchTime);
    } else {
      log.error("Subdata is not supported: {}", subData);
      return null;
    }
  }

  private static Integer maxOrNull(Integer matchTime) {
    return nonNull(matchTime) ? max(0, matchTime) : null;
  }

  @RequiredArgsConstructor
  static class SubData {

    final SubInOrOut subInOrOut;
    final int matchTime;

    enum SubInOrOut {
      SUB_IN,
      SUB_OUT
    }
  }
}
