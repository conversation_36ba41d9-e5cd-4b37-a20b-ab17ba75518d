package com.wsf.dataingestor.opta.parsers.ma2;

import lombok.RequiredArgsConstructor;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import com.wsf.dataingestor.parsers.ParsersUtils;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static java.util.Optional.ofNullable;

@RequiredArgsConstructor
public class StatsMapper {

  private static final String GOALKEEPER = "Goalkeeper";

  private final Map<String, String> optaToWSFPlayerStatsMapping;
  private final Map<String, String> optaToWSFTeamStatsMapping;

  public Map<String, Number> translateTeamStats(Map<String, String> stats) {
    return ParsersUtils.translateStatsWith0Fallback(stats, optaToWSFTeamStatsMapping);
  }

  public Map<String, Number> translatePlayerStats(String position, Map<String, String> stats, Integer minsPlayed) {
    Map<String, Number> mappedStats = newHashMap();

    ofNullable(minsPlayed).ifPresent(min -> mappedStats.putIfAbsent(MINS_PLAYED, min));

    int gkGoalsConceded = position.equals(GOALKEEPER) ? Integer.parseInt(stats.getOrDefault("goalsConceded", "0")) : 0;

    mappedStats.put("gkGoalsConceded", gkGoalsConceded);

    int penaltiesMissed = Stream
      .of(stats.get("attPenMiss"), stats.get("attPenPost"), stats.get("attPenTarget"))
      .filter(Objects::nonNull)
      .mapToInt(Integer::parseInt).sum();

    mappedStats.put("penaltiesMissed", penaltiesMissed);

    int penaltyGoals = Integer.parseInt(stats.getOrDefault("attPenGoal", "0"));

    mappedStats.put("penalties", penaltyGoals + penaltiesMissed);

    int totalAttAssist = Integer.parseInt(stats.getOrDefault("totalAttAssist", "0"));
    int goalAssist = Integer.parseInt(stats.getOrDefault("goalAssist", "0"));

    mappedStats.put("keyPasses", totalAttAssist - goalAssist);

    int duelsWon = Integer.parseInt(stats.getOrDefault("duelWon", "0"));
    int duelsLost = Integer.parseInt(stats.getOrDefault("duelLost", "0"));

    mappedStats.put("duels", duelsWon + duelsLost);

    optaToWSFPlayerStatsMapping.forEach((statName, mappedName) -> {
      if (stats.containsKey(statName)) {
        String value = stats.get(statName);
        int valInt = Integer.parseInt(value);
        mappedStats.put(mappedName, Math.max(valInt, 0));
      } else {
        mappedStats.put(mappedName, 0);
      }
    });
    return mappedStats;
  }

}
