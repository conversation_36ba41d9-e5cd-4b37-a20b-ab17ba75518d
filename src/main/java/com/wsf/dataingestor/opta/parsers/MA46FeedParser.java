package com.wsf.dataingestor.opta.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.opta.models.ma46.MA46Feed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedContestantUnavailability;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedUnavailability;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason;
import com.wsf.dataingestor.parsers.FeedParser;

import static com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY;
import static com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.time.Instant.now;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;

@Service
@Slf4j
@RequiredArgsConstructor
public class MA46FeedParser implements FeedParser<ContestantUnavailabilitiesDataFeed> {

  private static final String FEED_CODE = "ma46";

  private final ObjectMapper objectMapper;
  private final OptaFeedParserUtils optaFeedParserUtils;

  @Override
  public ContestantUnavailabilitiesDataFeed parseFeed(byte[] feed) throws IOException {
    if (log.isTraceEnabled()) {
      log.trace("Parsing {} feed: {}", FEED_CODE, new String(feed));
    }
    Instant ts = now();
    MA46Feed matchStats = objectMapper.readValue(feed, MA46Feed.class);
    return parseFeed(matchStats, ts);
  }

  @Override
  public ContestantUnavailabilitiesDataFeed parseFeed(String feed) throws IOException {
    log.trace("Parsing {} feed: {}", FEED_CODE, feed);
    Instant ts = now();
    MA46Feed matchStats = objectMapper.readValue(feed, MA46Feed.class);
    return parseFeed(matchStats, ts);
  }

  private ContestantUnavailabilitiesDataFeed parseFeed(MA46Feed feed, Instant receivedTs) {
    try {
      var tournament = feed.getMatchInfo().getTournamentCalendar();

      Stream<PlayerUnavailabilityDto> injuries = getInjuries(feed.getProvisionalLineUps().getLineUp());

      Stream<PlayerUnavailabilityDto> suspensions = getSuspensions(feed.getProvisionalLineUps().getLineUp());

      Set<FeedContestantUnavailability> unavailabilities = mapToContestantUnavailabilities(injuries, suspensions,
        tournament);

      String feedId = buildFeedId(FEED_CODE, feed.getMatchInfo().getId(), abs(feed.hashCode()),
        receivedTs.toEpochMilli());
      return new ContestantUnavailabilitiesDataFeed(feedId, unavailabilities);
    } catch (IllegalStateException e) {
      log.error("Can't process the feed", e);
      throw e;
    }
  }

  private Stream<PlayerUnavailabilityDto> getInjuries(List<MA46Feed.LineUp> lineUps) {
    return lineUps
      .stream()
      .map(MA46Feed.LineUp::getInjuries)
      .filter(Objects::nonNull)
      .map(MA46Feed.Injuries::getInjury)
      .flatMap(Collection::stream)
      .map(injury -> new PlayerUnavailabilityDto(injury.getPlayerId(), INJURY, injury.getType(), injury.getStartDate(),
        injury.getExpectedEndDate()));
  }

  private Stream<PlayerUnavailabilityDto> getSuspensions(List<MA46Feed.LineUp> lineUps) {
    return lineUps
      .stream()
      .map(MA46Feed.LineUp::getSuspensions)
      .filter(Objects::nonNull)
      .map(MA46Feed.Suspensions::getSuspension)
      .flatMap(Collection::stream)
      .map(suspension -> new PlayerUnavailabilityDto(suspension.getPlayerId(), SUSPENSION, suspension.getDescription(),
        suspension.getStartDate(), suspension.getEndDate()));
  }

  private Set<FeedContestantUnavailability> mapToContestantUnavailabilities(Stream<PlayerUnavailabilityDto> injuries,
                                                                            Stream<PlayerUnavailabilityDto> suspensions,
                                                                            MA46Feed.TournamentCalendar tournament) {

    Map<String, Set<FeedUnavailability>> optaIdToUnavailabilities = Stream
      .concat(injuries, suspensions)
      .collect(groupingBy(PlayerUnavailabilityDto::externalPlayerId,
        mapping(MA46FeedParser::mapToDomainObject, Collectors.toSet())));

    return optaIdToUnavailabilities.entrySet()
      .stream()
      .map(entry -> new FeedContestantUnavailability(
        optaFeedParserUtils.getPlayerFromTournament(entry.getKey(), tournament.getId()), entry.getValue()))
      .filter(unavailability -> Objects.nonNull(unavailability.player()))
      .collect(Collectors.toSet());
  }

  public static FeedUnavailability mapToDomainObject(PlayerUnavailabilityDto dto) {
    return new FeedUnavailability(dto.type(), dto.description(), dto.startDate(), dto.endDate());
  }

  public record PlayerUnavailabilityDto(String externalPlayerId,
                                        UnavailabilityReason type,
                                        String description,
                                        Instant startDate,
                                        Instant endDate) {}
}