package com.wsf.dataingestor.opta.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.opta.clients.http.OT2HttpClient;
import com.wsf.dataingestor.opta.parsers.OT2FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

@Service
public class OT2CurrentTournamentClient extends BasePullAPIClient<CurrentTournamentFeed> {

  private static final String STORAGE_PATH = "opta/ot2/";

  @Autowired
  public OT2CurrentTournamentClient(OT2HttpClient httpClient, OT2FeedParser ot2FeedParser,
                                    FeedStoreService feedStoreService) {
    super(httpClient, ot2FeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(CurrentTournamentFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.getFeedId() + ".json";
  }

}
