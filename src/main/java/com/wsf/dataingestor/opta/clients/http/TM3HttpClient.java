package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.google.common.collect.ImmutableMap;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public final class TM3HttpClient extends SDAPIClient {

  private static final String TM3_FEED_ENDPOINT = "/soccerdata/squads/";

  @Autowired
  public TM3HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                       @Value("${opta.sdapi.port}") int port,
                       @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    super(httpClient, host, port, outletAuthKey, TM3_FEED_ENDPOINT);
  }

  public byte[] getFeed(String tournamentCalendarId) throws IOException {
    Map<String, String> params = ImmutableMap.of("tmcl", tournamentCalendarId, "detailed", "yes", "_pgSz", "1500");
    return super.retrieveFeed(params, "json");
  }
}
