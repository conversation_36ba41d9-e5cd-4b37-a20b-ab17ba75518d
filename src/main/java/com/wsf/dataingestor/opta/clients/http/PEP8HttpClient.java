package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.google.common.collect.ImmutableMap;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

public final class PEP8HttpClient extends SDAPIClient {

  private static final String PEP8_FEED_ENDPOINT = "/soccerdata/suspensions/";

  @Autowired
  public PEP8HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                        @Value("${opta.sdapi.port}") int port,
                        @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    super(httpClient, host, port, outletAuthKey, PEP8_FEED_ENDPOINT);
  }

  //Or do we want this guy to work for match?
  public byte[] getFeed(String tournamentCalendarId) throws IOException {
    Map<String, String> params = ImmutableMap.of("tmcl", tournamentCalendarId);
    return super.retrieveFeed(params, "json");
  }
}
