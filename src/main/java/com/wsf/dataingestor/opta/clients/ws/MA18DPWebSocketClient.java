package com.wsf.dataingestor.opta.clients.ws;

import java.util.concurrent.ExecutorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.ws.factories.WebSocketConnectionFactory;
import com.wsf.dataingestor.clients.ws.factories.WebSocketHandlerFactory;

@Service
public class MA18DPWebSocketClient extends SDDPClient {

  private static final String FEED_NAME = "matchEventStats";

  @Autowired
  public MA18DPWebSocketClient(@Value("${opta.sddp.url}") String url,
                               @Value("${opta.sddp.outlet-auth-key}") String outletAuthKey,
                               WebSocketConnectionFactory webSocketConnectionFactory,
                               WebSocketHandlerFactory webSocketHandlerFactory,
                               @Qualifier("websocketReconnectPool") ExecutorService executorService) {
    super(webSocketConnectionFactory, webSocketHandlerFactory, executorService, url, outletAuthKey);
  }

  @Override
  protected String getFeedName() {
    return FEED_NAME;
  }
}
