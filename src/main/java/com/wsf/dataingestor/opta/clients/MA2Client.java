package com.wsf.dataingestor.opta.clients;

import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.http.MA2HttpClient;
import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

public class MA2Client extends BasePullAPIClient<MatchDataFeed> {

  private static final String STORAGE_PATH = "opta/ma2/";

  public MA2Client(MA2HttpClient httpClient, MA2FeedParser ma2FeedParser, FeedStoreService feedStoreService) {
    super(httpClient, ma2FeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(MatchDataFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.getFeedId() + ".json";
  }

}
