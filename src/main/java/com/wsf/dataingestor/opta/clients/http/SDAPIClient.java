package com.wsf.dataingestor.opta.clients.http;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpRequest;
import java.time.Duration;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.web.util.UriComponentsBuilder;
import com.wsf.dataingestor.clients.http.HttpFeedClient;
import com.wsf.dataingestor.exceptions.ResourceNotFoundException;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

import static java.lang.String.format;
import static java.time.temporal.ChronoUnit.SECONDS;

@Slf4j
@RequiredArgsConstructor
public sealed abstract class SDAPIClient implements HttpFeedClient permits MA1HttpClient,
                                                                           MA2HttpClient,
                                                                           MA3HttpClient,
                                                                           MA46HttpClient,
                                                                           OT2HttpClient,
                                                                           PEP7HttpClient,
                                                                           PEP8HttpClient,
                                                                           TM3HttpClient {

  private static final Map<String, String> COMMON_PARAMS = Map.of("_rt", "b");

  private final RetryableHttpClient retryableHttpClient;

  private final String apiHost;

  private final int apiPort;

  private final String outletAuthKey;

  private final String apiEndpoint;

  public byte[] retrieveFeed(Map<String, String> params, String responseFormat) throws IOException {

    UriComponentsBuilder uriBuilder = UriComponentsBuilder
      .newInstance()
      .scheme("http")
      .host(apiHost)
      .port(apiPort)
      .path(apiEndpoint)
      .path(outletAuthKey)
      .queryParam("_fmt", responseFormat);

    COMMON_PARAMS.forEach(uriBuilder::queryParam);
    params.forEach(uriBuilder::queryParam);

    URI uri = uriBuilder.build().toUri();

    var sdapiRequest = HttpRequest.newBuilder()
      .timeout(Duration.of(10, SECONDS)).uri(uri).build();

    try {

      var response = retryableHttpClient.send(sdapiRequest);

      if (response.statusCode() == HttpStatus.OK.value()) {
        return response.body();
      } else {
        String body = new String(response.body());
        String errorDescription = "Error while calling url: %s. HttpCode: %s Body: %s".formatted(uri,
          response.statusCode(), body);

        if (response.statusCode() == HttpStatus.NOT_FOUND.value()) {
          throw new ResourceNotFoundException(errorDescription);
        }

        throw new IOException(errorDescription);
      }
    } catch (InterruptedException e) {
      throw new IOException(format("Error while calling url: %s", uri.toString()), e);
    }
  }

}
