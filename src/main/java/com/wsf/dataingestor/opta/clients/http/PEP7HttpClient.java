package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.google.common.collect.ImmutableMap;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

public final class PEP7HttpClient extends SDAPIClient {

  private static final String PEP7_FEED_ENDPOINT = "/soccerdata/injuries/";

  @Autowired
  public PEP7HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                        @Value("${opta.sdapi.port}") int port,
                        @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    super(httpClient, host, port, outletAuthKey, PEP7_FEED_ENDPOINT);
  }

  public byte[] getFeed(String tournamentCalendarId) throws IOException {
    Map<String, String> params = ImmutableMap.of("tmcl", tournamentCalendarId);
    return super.retrieveFeed(params, "json");
  }
}
