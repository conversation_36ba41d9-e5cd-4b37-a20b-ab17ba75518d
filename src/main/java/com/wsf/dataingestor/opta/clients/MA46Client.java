package com.wsf.dataingestor.opta.clients;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.opta.clients.http.MA46HttpClient;
import com.wsf.dataingestor.opta.parsers.MA46FeedParser;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.services.FeedStoreService;

@Service
public class MA46Client extends BasePullAPIClient<ContestantUnavailabilitiesDataFeed> {

  private static final String STORAGE_PATH = "opta/ma46/";

  public MA46Client(MA46HttpClient httpClient, MA46FeedParser parser, FeedStoreService feedStoreService) {
    super(httpClient, parser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(ContestantUnavailabilitiesDataFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.feedId() + ".json";
  }
}
