package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.google.common.collect.ImmutableMap;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public final class OT2HttpClient extends SDAPIClient {

  private static final String OT2_FEED_ENDPOINT = "/soccerdata/tournamentcalendar/";

  @Autowired
  public OT2HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                       @Value("${opta.sdapi.port}") int port,
                       @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    super(httpClient, host, port, outletAuthKey, OT2_FEED_ENDPOINT);
  }

  public byte[] getFeed(String optaCompetitionId) throws IOException {
    Map<String, String> params = ImmutableMap.of("comp", optaCompetitionId, "stages", "yes");
    return super.retrieveFeed(params, "json");
  }
}
