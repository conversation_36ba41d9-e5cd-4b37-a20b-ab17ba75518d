package com.wsf.dataingestor.opta.clients;

import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.opta.clients.http.MA3HttpClient;
import com.wsf.dataingestor.opta.parsers.ma3.MA3FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

public class MA3Client extends BasePullAPIClient<MatchEventsFeed> {

  private static final String STORAGE_PATH = "opta/ma3/";

  public MA3Client(MA3HttpClient httpClient, MA3FeedParser ma3FeedParser, FeedStoreService feedStoreService) {
    super(httpClient, ma3FeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(MatchEventsFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.getFeedId() + ".json";
  }
}
