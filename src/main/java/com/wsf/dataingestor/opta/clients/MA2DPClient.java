package com.wsf.dataingestor.opta.clients;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.neovisionaries.ws.client.WebSocketException;
import com.wsf.dataingestor.clients.PushAPIClient;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.ws.MA2DPWebSocketClient;
import com.wsf.dataingestor.opta.parsers.MA2DPFeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

@Slf4j
@Service
public class MA2DPClient implements PushAPIClient {

  private static final String STORAGE_PATH = "opta/ma2dp/";

  private final MA2DPWebSocketClient webSocketClient;
  private final MA2DPFeedParser ma2dpFeedParser;
  private final FeedStoreService feedStoreService;

  @Autowired
  public MA2DPClient(MA2DPWebSocketClient webSocketClient, MA2DPFeedParser ma2dpFeedParser,
                     FeedStoreService feedStoreService) {
    this.webSocketClient = webSocketClient;
    this.ma2dpFeedParser = ma2dpFeedParser;
    this.feedStoreService = feedStoreService;
  }

  @Override
  public void startConnectionAndProcessFeed(String optaFixtureId, int seqId,
                                            Consumer<MatchDataFeed> parsedFeedProcessor,
                                            BiConsumer<String, Exception> onErrorHandler,
                                            Consumer<WebSocketManager.WSData> onSubscribeHandler,
                                            Runnable onUnsubscribeHandler) {

    try {
      Consumer<byte[]> rawFeedProcessor = (bytes) -> {
        try {
          MatchDataFeed matchDataFeed = ma2dpFeedParser.parseFeed(bytes);

          String feedId = matchDataFeed.getFeedId();

          feedStoreService.storeFeed(bytes, feedId, getFeedName(feedId));

          if (matchDataFeed.getPlayersData()
            .size() > 0) {
            parsedFeedProcessor.accept(matchDataFeed);
          } else {
            log.debug("feed {} has empty player data (or contains stats we don't process)", feedId);
          }
        } catch (IOException e) {
          onErrorHandler.accept("Parse feed error", e);
        }
      };

      webSocketClient.connectWebsocket(optaFixtureId, seqId, rawFeedProcessor, onSubscribeHandler, onUnsubscribeHandler,
        onErrorHandler);

    } catch (IOException | WebSocketException e) {
      onErrorHandler.accept("Error connecting to the websocket", e);
    }
  }

  private String getFeedName(String feedId) {
    return STORAGE_PATH + feedId + ".json";
  }
}
