package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public final class MA3HttpClient extends SDAPIClient {

  private static final String MA3_FEED_ENDPOINT = "/soccerdata/matchevent/";

  @Autowired
  public MA3HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                       @Value("${opta.sdapi.port}") int port,
                       @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey) {
    super(httpClient, host, port, outletAuthKey, MA3_FEED_ENDPOINT);
  }

  public byte[] getFeed(String fixtureId) throws IOException {
    Map<String, String> params = Map.of("fx", fixtureId);
    return super.retrieveFeed(params, "json");
  }
}
