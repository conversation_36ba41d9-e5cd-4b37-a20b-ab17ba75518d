package com.wsf.dataingestor.opta.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.opta.clients.http.TM3HttpClient;
import com.wsf.dataingestor.opta.parsers.TM3FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

@Service
public class TM3Client extends BasePullAPIClient<SquadsFeed> {

  private static final String STORAGE_PATH = "opta/tm3/";

  @Autowired
  public TM3Client(TM3HttpClient httpClient, TM3FeedParser tm3FeedParser, FeedStoreService feedStoreService) {
    super(httpClient, tm3FeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(SquadsFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.getFeedId() + ".json";
  }

}
