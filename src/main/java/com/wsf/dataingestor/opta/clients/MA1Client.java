package com.wsf.dataingestor.opta.clients;

import org.springframework.beans.factory.annotation.Autowired;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.opta.clients.http.MA1HttpClient;
import com.wsf.dataingestor.opta.parsers.MA1FeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

public class MA1Client extends BasePullAPIClient<FixturesFeed> {

  private static final String STORAGE_PATH = "opta/ma1/";

  @Autowired
  public MA1Client(MA1HttpClient httpClient, MA1FeedParser ma1FeedParser, FeedStoreService feedStoreService) {
    super(httpClient, ma1FeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(FixturesFeed parsedFeed) {
    return STORAGE_PATH + parsedFeed.getFeedId() + ".json";
  }

}
