package com.wsf.dataingestor.opta.clients.ws;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.json.JSONObject;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketException;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.factories.WebSocketConnectionFactory;
import com.wsf.dataingestor.clients.ws.factories.WebSocketHandlerFactory;
import com.wsf.dataingestor.clients.ws.handlers.ReconnectHandler;
import com.wsf.dataingestor.opta.clients.handlers.SDDPHandler;

@Slf4j
public abstract class SDDPClient {

  private final WebSocketConnectionFactory webSocketConnectionFactory;
  private final WebSocketHandlerFactory webSocketHandlerFactory;

  private final ExecutorService reconnectHandlerExecutorService;

  private final String url;
  private final String outlet;

  protected SDDPClient(WebSocketConnectionFactory webSocketConnectionFactory,
                       WebSocketHandlerFactory webSocketHandlerFactory, ExecutorService reconnectHandlerExecutorService,
                       String url, String outletId) {
    this.webSocketConnectionFactory = webSocketConnectionFactory;
    this.webSocketHandlerFactory = webSocketHandlerFactory;
    this.reconnectHandlerExecutorService = reconnectHandlerExecutorService;
    this.url = url;
    this.outlet = buildOutletObj(outletId);
  }

  protected abstract String getFeedName();

  public void connectWebsocket(String optaFixtureId, int seqId, Consumer<byte[]> feedConsumer,
                               Consumer<WebSocketManager.WSData> onSubscribeHandler, Runnable onUnsubscribeHandler,
                               BiConsumer<String, Exception> onErrorHandler) throws IOException, WebSocketException {

    ReconnectHandler reconnectHandler = new ReconnectHandler(optaFixtureId, reconnectHandlerExecutorService);

    SDDPHandler webSocketHandler = webSocketHandlerFactory.buildHandler(optaFixtureId, seqId, outlet, getFeedName(),
      feedConsumer, onSubscribeHandler, onUnsubscribeHandler, onErrorHandler, reconnectHandler);

    WebSocket webSocketConnection = webSocketConnectionFactory.createWebSocketConnection(url, webSocketHandler);

    webSocketConnection.connect();
  }

  private static String buildOutletObj(String outletId) {
    return new JSONObject()
      .put("outlet", new JSONObject().put("OutletKeyService", new JSONObject().put("outletid", outletId)))
      .toString();
  }

}
