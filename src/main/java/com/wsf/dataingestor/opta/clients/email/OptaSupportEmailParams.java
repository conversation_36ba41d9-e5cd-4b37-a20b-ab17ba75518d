package com.wsf.dataingestor.opta.clients.email;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "email.opta")
public class OptaSupportEmailParams {

  private String recipient;
  private List<String> subscribers;
}