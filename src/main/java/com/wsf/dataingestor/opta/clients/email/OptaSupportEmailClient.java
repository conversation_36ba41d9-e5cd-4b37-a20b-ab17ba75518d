package com.wsf.dataingestor.opta.clients.email;

import lombok.RequiredArgsConstructor;

import java.util.Set;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import static java.util.stream.Collectors.joining;

@Service
@RequiredArgsConstructor
public class OptaSupportEmailClient {

  @Value("${email.username}")
  private String fromMail;
  private final OptaSupportEmailParams optaSupportEmailParams;

  private final JavaMailSender mailSender;

  public void sendMissingMA46FeedMail(Set<String> optaFixtureIdsWithMissingFeeds) throws MailException {
    String[] allRecipients = Stream
      .concat(optaSupportEmailParams.getSubscribers()
        .stream(), Stream.of(optaSupportEmailParams.getRecipient()))
      .toArray(String[]::new);

    SimpleMailMessage simpleMailMessage = new SimpleMailMessage();

    simpleMailMessage.setFrom(fromMail);
    simpleMailMessage.setTo(allRecipients);

    String subject = "Missing MA46 Provisional Line-ups Feed(s) for Fixture(s)";
    simpleMailMessage.setSubject(subject);

    simpleMailMessage.setText(buildMA46MailBody(optaFixtureIdsWithMissingFeeds));

    mailSender.send(simpleMailMessage);
  }

  private String buildMA46MailBody(Set<String> optaFixtureIdsWithMissingFeeds) {
    String feedsList = optaFixtureIdsWithMissingFeeds
      .stream()
      .map(feed -> "- " + feed)
      .collect(joining("\n"));

    return """
      Hello Team,
      
      we noticed that the MA46 feed is missing provisional lineups for the following fixture(s) that will be played in the next 72h:
      
      %s
      
      Can you please provide the missing feed(s)?
      Thank you very much.
      
      Best regards,
      ----
      WSF Tech Team
      """.formatted(feedsList);
  }
}