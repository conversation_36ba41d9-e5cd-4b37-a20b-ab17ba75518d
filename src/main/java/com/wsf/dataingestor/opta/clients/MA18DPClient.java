package com.wsf.dataingestor.opta.clients;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.neovisionaries.ws.client.WebSocketException;
import com.wsf.dataingestor.clients.PushAPIClient;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.ws.MA18DPWebSocketClient;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPFeedParser;
import com.wsf.dataingestor.services.FeedStoreService;

@Slf4j
@Service
public class MA18DPClient implements PushAPIClient {

  private static final String STORAGE_PATH = "opta/ma18dp/";

  private final MA18DPWebSocketClient webSocketClient;
  private final MA18DPFeedParser ma18dpFeedParser;
  private final FeedStoreService feedStoreService;

  @Autowired
  public MA18DPClient(MA18DPWebSocketClient webSocketClient, MA18DPFeedParser ma18dpFeedParser,
                      FeedStoreService feedStoreService) {
    this.webSocketClient = webSocketClient;
    this.ma18dpFeedParser = ma18dpFeedParser;
    this.feedStoreService = feedStoreService;
  }

  @Override
  public void startConnectionAndProcessFeed(String optaFixtureId, int seqId,
                                            Consumer<MatchDataFeed> parsedFeedProcessor,
                                            BiConsumer<String, Exception> onErrorHandler,
                                            Consumer<WebSocketManager.WSData> onSubscribeHandler,
                                            Runnable onUnsubscribeHandler) {

    Consumer<byte[]> rawFeedProcessor = (bytes) -> feedConsumer(parsedFeedProcessor, onErrorHandler, bytes);

    try {
      webSocketClient.connectWebsocket(optaFixtureId, seqId, rawFeedProcessor, onSubscribeHandler, onUnsubscribeHandler,
        onErrorHandler);

    } catch (IOException | WebSocketException e) {
      onErrorHandler.accept("Error connecting to the websocket", e);
    }
  }

  private void feedConsumer(Consumer<MatchDataFeed> parsedFeedProcessor, BiConsumer<String, Exception> onErrorHandler,
                            byte[] bytes) {
    try {
      MatchDataFeed matchDataFeed = ma18dpFeedParser.parseFeed(bytes);

      String feedId = matchDataFeed.getFeedId();

      feedStoreService.storeFeed(bytes, feedId, getFeedName(feedId));

      if (!matchDataFeed.getMatchEvents()
        .isEmpty() || !matchDataFeed.getPlayersData()
        .isEmpty() || !matchDataFeed.getFeedPlayerMatchEvents().isEmpty()) {
        parsedFeedProcessor.accept(matchDataFeed);
      } else {
        log.info("feedId={} fixtureId={} has empty player data (or contains stats we don't process)", feedId,
          matchDataFeed.getFixture().getId().toString());
      }
    } catch (Exception e) {
      onErrorHandler.accept(new String(bytes), e);
    }
  }

  private String getFeedName(String feedId) {
    return STORAGE_PATH + feedId + ".json";
  }
}
