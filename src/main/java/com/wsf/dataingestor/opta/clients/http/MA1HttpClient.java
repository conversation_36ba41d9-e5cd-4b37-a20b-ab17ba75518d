package com.wsf.dataingestor.opta.clients.http;

import java.io.IOException;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

public final class MA1HttpClient extends SDAPIClient {

  private static final String MA1_FEED_ENDPOINT = "/soccerdata/match/";
  private final boolean shouldFetchOnlyBettingAllowedFixtures;

  @Autowired
  public MA1HttpClient(RetryableHttpClient httpClient, @Value("${opta.sdapi.host}") String host,
                       @Value("${opta.sdapi.port}") int port,
                       @Value("${opta.sdapi.outlet-auth-key}") String outletAuthKey,
                       boolean shouldFetchOnlyBettingAllowedFixtures) {
    super(httpClient, host, port, outletAuthKey, MA1_FEED_ENDPOINT);
    this.shouldFetchOnlyBettingAllowedFixtures = shouldFetchOnlyBettingAllowedFixtures;
  }

  public byte[] getFeed(String tournamentCalendarId) throws IOException {
    Map<String, String> params;
    if (shouldFetchOnlyBettingAllowedFixtures) {
      //@formatter:off
      params = Map.of(
        "tmcl", tournamentCalendarId,
        "cvlv", "13,15",
        "optaBetting", "yes",
        "_pgSz", "1000",
        "_ordSrt","asc",
        "live", "yes");
    } else {
      params = Map.of(
        "tmcl", tournamentCalendarId,
        "_fld","main.da,main.ti,main.we,main.laup,main.con.con.id,main.con.con.po,main.con.con.na,main.sta.foid,main.sta.na,main.toca.id,lida.made.mast,lida.made.remaid,main.ovle,main.ven.ne",
        "_pgSz", "1000",
        "_ordSrt", "asc",
        "live", "yes");
      //@formatter:on
    }
    return super.retrieveFeed(params, "json");
  }
}
