package com.wsf.dataingestor.opta.clients.handlers;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import javax.naming.AuthenticationException;
import org.json.JSONArray;
import org.json.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketAdapter;
import com.neovisionaries.ws.client.WebSocketException;
import com.neovisionaries.ws.client.WebSocketFrame;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.handlers.ReconnectHandler;
import com.wsf.dataingestor.clients.ws.handlers.WSHandler;
import com.wsf.dataingestor.metrics.MetricsManager;

import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;

@Slf4j
@Builder
public class SDDPHandler extends WebSocketAdapter implements WSHandler {

  private static final String IS_SUBSCRIBED = "is_subscribed";
  private static final String IS_AUTHORISED = "is_authorised";
  private static final String MATCH_FINISHED_CLOSE_REASON = "match_finished";

  private final ObjectMapper jsonObjectMapper;
  private final ReconnectHandler reconnectHandler;
  private final MetricsManager metricsManager;

  private final String optaFixtureId;
  private final String outlet;
  private final String feedName;
  private final Consumer<byte[]> feedHandler;
  private final Consumer<WebSocketManager.WSData> onSubscribeHandler;
  private final Runnable onUnsubscribeHandler;
  private final BiConsumer<String, Exception> onErrorHandler;

  private final AtomicBoolean isSubscribed = new AtomicBoolean(false);
  private volatile int seqId;

  @Override
  public void onError(WebSocket websocket, WebSocketException cause) {
    processError(cause.getMessage(), cause);
  }

  @Override
  public void onConnected(WebSocket ws, Map<String, List<String>> headers) {
    log.info("websocket connected, authenticating now");
    ws.sendText(outlet);
  }

  @Override
  public void onDisconnected(WebSocket websocket, WebSocketFrame serverCloseFrame, WebSocketFrame clientCloseFrame,
                             boolean closedByServer) {
    if (closedByServer) {
      log.warn("websocket disconnected: {}", serverCloseFrame.getCloseReason());
      reconnect(websocket);
    } else if (isNull(clientCloseFrame) || !clientCloseFrame.getCloseReason().equals(MATCH_FINISHED_CLOSE_REASON)) {
      log.warn("websocket disconnected by the client: serverCloseFrame: {} - {} clientCloseFrame {} - {}",
        serverCloseFrame, ofNullable(serverCloseFrame).map(WebSocketFrame::getPayloadText), clientCloseFrame,
        ofNullable(clientCloseFrame).map(WebSocketFrame::getPayloadText));
      reconnect(websocket);
    }
  }

  @Override
  public void onTextFrame(WebSocket websocket, WebSocketFrame frame) {
    processMessage(websocket, frame.getPayload());
  }

  @Override
  public void onTextMessage(WebSocket ws, byte[] message) {
    processMessage(ws, message);
  }

  @Override
  public void requestStatsSnapshot(WebSocket ws) {
    JSONObject snapshotObj = new JSONObject();
    snapshotObj.put("feed", "stats");
    JSONObject content = new JSONObject()
      .put("name", "subscribe")
      .put("feed", new JSONArray().put(feedName))
      .put("fixtureUuid", optaFixtureId)
      .put("snapShot", new JSONArray().put(snapshotObj));

    JSONObject subscribeRequest = new JSONObject().put("content", content);

    log.debug("snapshot request for fixtureId={}: {}", optaFixtureId, subscribeRequest.toString());

    ws.sendText(subscribeRequest.toString());
  }

  @Override
  public void unsubscribeFromFixture(WebSocket ws) {
    JSONObject unsubscribeRequest = new JSONObject().put("name", "unsubscribe").put("fixtureUuid", optaFixtureId);

    ws.sendText(unsubscribeRequest.toString()).disconnect(MATCH_FINISHED_CLOSE_REASON);

    onUnsubscribeHandler.run();
  }

  @Override
  public void updateSeqId(int seqId) {
    this.seqId = seqId;
  }

  private void processMessage(WebSocket ws, byte[] message) {
    // Received a response. Print the received message.
    if (log.isDebugEnabled()) {
      log.debug("response: {}", new String(message));
    }

    try {
      if (!isSubscribed.get()) {
        JsonNode response = jsonObjectMapper.readTree(message);

        if (response.has("outlet")) {
          // auth data
          processAuthResponse(ws, response);
        } else if (response.has("content") && response.get("content").has("msg")) {
          processSubscribeResponse(ws, response);
        } else if (response.has("welcome")) {
          log.info("welcome message: {}: ", new String(message));
        } else if (response.has("content")) {
          processData(message);
          isSubscribed.compareAndSet(false, true);
        } else {
          log.warn("message not supported: {}", new String(message));
        }
      } else {
        // feed data
        processData(message);
      }
    } catch (IOException e) {
      ws.disconnect();
      String error = format("could not parse json feed: %s", new String(message));
      processErrorAndTryReconnect(error, e, ws);
    } catch (Exception e) {
      ws.disconnect();
      String error = format("error in the websocket connection with fixtureUuid: %s", optaFixtureId);
      processErrorAndTryReconnect(error, e, ws);
    }
  }

  private void processError(String message, Exception e) {
    onErrorHandler.accept(message, e);
  }

  private void processErrorAndTryReconnect(String message, Exception e, WebSocket ws) {
    onErrorHandler.accept(message, e);
    reconnect(ws);
  }

  private void processSubscribeResponse(WebSocket ws, JsonNode response) {
    JsonNode content = response.get("content");
    if (content.has("msg") && content.get("msg").asText().equals(IS_SUBSCRIBED)) {
      log.info("subscribed to optaFixtureId={}", optaFixtureId);
      isSubscribed.compareAndSet(false, true);
      onSubscribeHandler.accept(new WebSocketManager.WSData(ws, this));
    } else {
      String message = format("not subscribed to fixture Id %s, response %s", optaFixtureId, response);
      onErrorHandler.accept(message, new IllegalStateException()); // TODO improve this
    }
  }

  private void processData(byte[] message) {
    feedHandler.accept(message);
  }

  private void processAuthResponse(WebSocket ws, JsonNode response) {
    if (isAuthorized(response)) {
      log.info("response from authentication: authenticated");
      subscribeToFixture(ws);
    } else {
      ws.disconnect();
      log.error("response from authentication: not authenticated");
      onErrorHandler.accept("not authenticated", new AuthenticationException());
    }
  }

  private void subscribeToFixture(WebSocket ws) {
    JSONObject content = buildSubscribeRequest();
    JSONObject subscribeRequest = new JSONObject().put("content", content);

    log.info("subscribe request: {}", subscribeRequest.toString());

    ws.sendText(subscribeRequest.toString());
  }

  private JSONObject buildSubscribeRequest() {
    JSONObject content = new JSONObject()
      .put("name", "subscribe")
      .put("feed", new JSONArray().put(feedName))
      .put("fixtureUuid", optaFixtureId);
    JSONObject snapshotObj = new JSONObject().put("feed", "matchEvent").put("seqId", seqId);
    return content.put("snapShot", new JSONArray().put(snapshotObj));
  }

  private void reconnect(WebSocket websocket) {
    try {
      if (websocket.isOpen()) {
        websocket.disconnect();
      }
      isSubscribed.set(false);
      reconnectHandler.reconnect(websocket);
    } catch (ReconnectHandler.MaxNrRetriesExceeded e) {
      metricsManager.MA2DP_WEBSOCKET_MAX_RETRIES_REACHED.increment();
      String message = format("max nr of retries for feed %s and fixture %s", feedName, optaFixtureId);
      onErrorHandler.accept(message, e);
      onUnsubscribeHandler.run();
    } catch (IOException e) {
      String message = format("error on disconnection from fixtureId %s", optaFixtureId);
      onErrorHandler.accept(message, e);
      onUnsubscribeHandler.run();
    }
  }

  private static boolean isAuthorized(JsonNode response) {
    // response to auth message
    String resp = response.get("outlet").get("msg").asText();

    return resp.equals(IS_AUTHORISED);
  }
}
