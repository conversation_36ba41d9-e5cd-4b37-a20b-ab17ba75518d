package com.wsf.dataingestor.opta.models.runningball;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "keep_alive")
@RequiredArgsConstructor
public class KeepAlive {

  @JacksonXmlProperty(localName = "date_generated", isAttribute = true)
  private final Instant timestamp = null;

}
