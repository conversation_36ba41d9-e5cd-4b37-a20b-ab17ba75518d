package com.wsf.dataingestor.opta.models.f09;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "SoccerFeed")
@RequiredArgsConstructor
public class SoccerFeed {

  @JacksonXmlProperty(localName = "TimeStamp", isAttribute = true)
  private final String timestamp = null;

  @JacksonXmlProperty(localName = "SoccerDocument")
  private final SoccerDocument soccerDocument = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class SoccerDocument {
    @JacksonXmlProperty(localName = "uID", isAttribute = true)
    private String uId = null;
    @JacksonXmlProperty(localName = "Competition")
    private final Competition competition = null;
    @JacksonXmlProperty(localName = "MatchData")
    private final MatchData matchData = null;
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private String type = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    @JacksonXmlProperty(isAttribute = true)
    private String uID = null;

    @JacksonXmlProperty(localName = "Stat")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Stat> statList = newArrayList();

    @JsonSetter
    public void setStatList(Stat stat) {
      this.statList.add(stat);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stat {
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private final String type = null;
    @JacksonXmlText
    private final String value = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Substitution {
    @JacksonXmlProperty(localName = "Time", isAttribute = true)
    private final String time = null;
    @JacksonXmlProperty(localName = "SubOff", isAttribute = true)
    private final String subOff = null;
    @JacksonXmlProperty(localName = "SubOn", isAttribute = true)
    private final String subOn = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchData {

    @JacksonXmlProperty(localName = "MatchInfo")
    private MatchInfo matchInfo = null;

    @JacksonXmlProperty(localName = "TeamData")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TeamData> teamDataList = newArrayList();

    @JacksonXmlProperty(localName = "Stat")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Stat> statList = newArrayList();

    @JsonSetter
    public void setTeamDataList(TeamData team) {
      this.teamDataList.add(team);
    }

    @JsonSetter
    public void setStatList(Stat stats) {
      this.statList.add(stats);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    @JacksonXmlProperty(localName = "Period", isAttribute = true)
    private String period = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TeamData {
    @JacksonXmlProperty(localName = "PlayerLineUp")
    private final PlayerLineUp playerLineUp = null;

    @JacksonXmlProperty(localName = "Score", isAttribute = true)
    private final String score = null;

    @JacksonXmlProperty(localName = "Side", isAttribute = true)
    private final String side = null;

    @JacksonXmlProperty(localName = "TeamRef", isAttribute = true)
    private final String teamRef = null;

    @JacksonXmlProperty(localName = "Substitution")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Substitution> substitutions = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PlayerLineUp {
    @JacksonXmlProperty(localName = "MatchPlayer")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MatchPlayer> matchPlayers = newArrayList();

    @JsonSetter
    public void setMatchPlayers(MatchPlayer matchPlayer) {
      this.matchPlayers.add(matchPlayer);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchPlayer {

    @JacksonXmlProperty(localName = "PlayerRef", isAttribute = true)
    private final String playerRef = null;

    @JacksonXmlProperty(localName = "Position", isAttribute = true)
    private final String position = null;

    @JacksonXmlProperty(localName = "SubPosition", isAttribute = true)
    private final String subPosition = null;

    @JacksonXmlProperty(localName = "Stat")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Stat> stats = newArrayList();

    @JsonSetter
    public void setStats(Stat stat) {
      this.stats.add(stat);
    }

  }
}
