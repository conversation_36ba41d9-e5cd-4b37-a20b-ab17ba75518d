package com.wsf.dataingestor.opta.models.converters;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import static java.time.ZoneOffset.UTC;

public class LocalDateToInstantDeserializer extends JsonDeserializer<Instant> {

  @Override
  public Instant deserialize(JsonParser jsonParser,
                             DeserializationContext deserializationContext) throws IOException, JacksonException {

    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);

    String parsedLocalDateStringWithoutTimeZone = node.asText().replace("Z", "");

    return LocalDate.parse(parsedLocalDateStringWithoutTimeZone).atStartOfDay(UTC).toInstant();
  }
}