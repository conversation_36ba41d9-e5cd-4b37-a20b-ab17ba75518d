package com.wsf.dataingestor.opta.models.ma2;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.ContestantsDeserializer;
import com.wsf.dataingestor.opta.models.converters.StatDeserializer;
import com.wsf.dataingestor.opta.models.converters.UnconfirmedDeserializer;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static java.lang.String.format;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class MA2Feed {

  private final MatchInfo matchInfo = null;
  private final LiveData liveData = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    private final String id = null;
    private final String date = null;
    private final String time = null;
    private final Integer week = null;
    private final Instant lastUpdated = null;
    private final TournamentCalendar tournamentCalendar = null;
    private final Integer postMatch = null;

    @JsonProperty("contestant")
    @JsonDeserialize(using = ContestantsDeserializer.class)
    private final Pair<String, String> homeAwayContestantIds = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    @Getter
    private final String id = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestant {
    private final String id = null;
    private final String position = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {
    @JsonProperty("lineUp")
    private final List<LineUp> lineUps = newArrayList();
    @JsonProperty("substitute")
    private final List<Substitute> substitutes = newArrayList();
    @JsonProperty("card")
    private final List<Card> cards = newArrayList();
    @JsonProperty("goal")
    private final List<Goal> goals = newArrayList();
    private final MatchDetails matchDetails = null;
  }

  @Data
  public abstract static class Event {
    protected final String contestantId = null;
    protected final Integer timeMin = null;
    protected final Integer periodId = null;
    protected final String optaEventId = null;
    protected final String type = null;

    public abstract String getPlayerId();

    public String getEventId() {
      return format("%s_%s", getOptaEventId(), getContestantId());
    }
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Substitute extends Event {
    private final Integer timeMin = null;
    private final String playerOnId = null;
    private final String playerOffId = null;
    private final Integer periodId = null;

    @Override
    public String getPlayerId() {
      return playerOffId;
    }

    @Override
    public String getEventId() {
      return format("substitution_%s_%s", playerOffId, contestantId);
    }
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Card extends Event {
    private final String playerId = null;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Goal extends Event {
    private final String scorerId = null;
    private final String assistPlayerId = null;
    @JsonDeserialize(using = UnconfirmedDeserializer.class)
    private final Boolean unconfirmed = false;

    @Override
    public String getPlayerId() {
      return scorerId;
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDetails {
    private final String matchStatus = null;
    private final String relatedMatchId = null;
    private final Integer matchLengthMin = null;
    private final Integer matchTime = null;
    private final Integer periodId = null;
    private final Integer leg = null;
    private final Scores scores = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Scores {
    private final PeriodScore total = null;
    private final PeriodScore et = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PeriodScore {
    private final Integer home = null;
    private final Integer away = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LineUp {
    private final String contestantId = null;
    private final String formationUsed = null;
    @JsonProperty("player")
    private final List<Player> players = newArrayList();
    @JsonProperty("stat")
    @JsonDeserialize(using = StatDeserializer.class)
    private final Map<String, String> stats = newHashMap();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Player {
    private final String playerId = null;
    private final String position = null;
    private final Integer formationPlace = null;

    @JsonProperty("stat")
    @JsonDeserialize(using = StatDeserializer.class)
    private final Map<String, String> stats = newHashMap();
  }
}
