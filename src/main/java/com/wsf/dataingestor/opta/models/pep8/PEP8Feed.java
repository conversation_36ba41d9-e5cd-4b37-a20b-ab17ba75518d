package com.wsf.dataingestor.opta.models.pep8;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.LocalDateToInstantDeserializer;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class PEP8Feed {

  private Competition competition;
  private TournamentCalendar tournamentCalendar;
  private List<Person> person = new ArrayList<>();
  private String lastUpdated;

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    private String id;
    private String name;
    private String knownName;
    private String sponsorName;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    private String id;
    private String value;
    private String startDate;
    private String endDate;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Person {
    private String id;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String matchName;
    private String type;
    private String position;
    private String nationalityId;
    private String nationality;
    private String secondNationalityId;
    private String secondNationality;
    private String contestantId;
    private String contestantName;
    private List<Suspension> suspension = new ArrayList<>();
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Suspension {
    private String type;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant endDate;
    private Integer matches;
    private String description;
  }
}
