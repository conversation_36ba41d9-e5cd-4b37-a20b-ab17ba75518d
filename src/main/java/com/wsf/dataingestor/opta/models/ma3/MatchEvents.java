package com.wsf.dataingestor.opta.models.ma3;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "matchEvents")
@RequiredArgsConstructor
public class MatchEvents {

  @JacksonXmlProperty(localName = "matchInfo")
  private final MatchInfo matchInfo = null;

  @JacksonXmlProperty(localName = "liveData")
  private final LiveData liveData = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    @JacksonXmlProperty(isAttribute = true)
    private Integer week = null;

    @JacksonXmlProperty(localName = "competition")
    private final Competition competition = null;

    @JacksonXmlProperty(localName = "contestants")
    private final Contestants contestants = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    @JacksonXmlProperty(localName = "competitionCode", isAttribute = true)
    private String code = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestants {
    @JacksonXmlProperty(localName = "contestant")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Contestant> contestantList = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestant {
    @JacksonXmlProperty(isAttribute = true)
    private String id = null;
    @JacksonXmlProperty(isAttribute = true)
    private String position = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {

    @JacksonXmlProperty(localName = "events")
    private final Events events = null;

    @JacksonXmlProperty(localName = "matchDetails")
    private final MatchDetails matchDetails = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDetails {

    @JacksonXmlProperty(localName = "scores")
    private final Scores scores = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Scores {

    @JacksonXmlProperty(localName = "total")
    private final Total total = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Total {

    @JacksonXmlProperty(isAttribute = true)
    private final String home = null;

    @JacksonXmlProperty(isAttribute = true)
    private final String away = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Events {

    @JacksonXmlProperty(localName = "event")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Event> eventList = newArrayList();

    @JsonSetter
    public void setEventList(Event event) {
      this.eventList.add(event);
    }

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Event {

    @JacksonXmlProperty(isAttribute = true)
    private String typeId = null;

    @JacksonXmlProperty(isAttribute = true)
    private String playerId = null;

    @JacksonXmlProperty(isAttribute = true)
    private String contestantId = null;

    @JacksonXmlProperty(localName = "qualifier")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Qualifier> qualifierList = newArrayList();

    @JsonSetter
    public void setQualifierList(Qualifier qualifier) {
      this.qualifierList.add(qualifier);
    }

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Qualifier {
    @JacksonXmlProperty(isAttribute = true)
    private String qualifierId = null;
    @JacksonXmlProperty(isAttribute = true)
    private String value = null;
  }

}
