package com.wsf.dataingestor.opta.models.ma3;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.ContestantsDeserializer;
import com.wsf.dataingestor.opta.models.converters.InstantDeserializer;
import com.wsf.dataingestor.opta.models.converters.QualifierDeserializer;
import com.wsf.dataingestor.opta.parsers.events.AbstractFeedEvent;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class MA3Feed {

  private final MatchInfo matchInfo = null;
  private final LiveData liveData = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    private final String id = null;
    private final Integer week = null;
    private final Instant lastUpdated = null;
    private final TournamentCalendar tournamentCalendar = null;
    private final Competition competition = null;

    @JsonProperty("contestant")
    @JsonDeserialize(using = ContestantsDeserializer.class)
    private final Pair<String, String> homeAwayContestantIds = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    @Getter
    private final String id = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    @Getter
    private final String id = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestant {
    private final String id = null;
    private final String position = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {
    private final MatchDetails matchDetails = null;
    @JsonProperty("event")
    private final List<Event> events = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDetails {
    private final String matchStatus = null;
    private final String relatedMatchId = null;
    private final Integer matchLengthMin = null;
    private final Integer matchTime = null;
    private final Integer periodId = null;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Event extends AbstractFeedEvent {

    private final String id = null;
    private final String eventId = null;
    private final Integer typeId = null;
    private final String playerId = null;
    private final String contestantId = null;
    private final String timeMin = null;
    private final String timeSec = null;
    private final Integer periodId = null;
    private final Short outcome = null;

    @JsonProperty("timeStamp")
    @JsonDeserialize(using = InstantDeserializer.class)
    private Instant timestamp = null;

    @JsonDeserialize(using = InstantDeserializer.class)
    private Instant lastModified = null;

    @JsonProperty("qualifier")
    @JsonDeserialize(using = QualifierDeserializer.class)
    private final Map<Integer, String> qualifiers = newHashMap();
  }
}
