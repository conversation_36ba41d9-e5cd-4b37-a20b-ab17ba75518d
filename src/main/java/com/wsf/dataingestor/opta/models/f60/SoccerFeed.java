package com.wsf.dataingestor.opta.models.f60;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "SoccerFeed")
@RequiredArgsConstructor
public class SoccerFeed {
  @JacksonXmlProperty(localName = "SoccerDocument")
  private final SoccerDocument soccerDocument = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class SoccerDocument {
    @JacksonXmlProperty(localName = "Competition")
    private final Competition competition = null;
    private final MatchData matchData = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    @JacksonXmlProperty(isAttribute = true)
    private String uID = null;

    @JacksonXmlProperty(localName = "Stat")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Stat> statList = newArrayList();

    @JsonSetter
    public void setStatList(Stat stat) {
      this.statList.add(stat);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stat {
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private final String type = null;
    @JacksonXmlText
    private final String value = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchData {
    @JacksonXmlProperty(localName = "playerRatings")
    private final PlayerRatings playerRatings = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PlayerRatings {
    @JacksonXmlProperty(localName = "team")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Team> teamList = newArrayList();

    @JsonSetter
    public void setTeamList(Team team) {
      this.teamList.add(team);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Team {
    @JacksonXmlProperty(localName = "matchPlayer")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MatchPlayer> matchPlayers = newArrayList();
    @JacksonXmlProperty(isAttribute = true)
    private final String uID = null;

    @JsonSetter
    public void setMatchPlayers(MatchPlayer matchPlayer) {
      this.matchPlayers.add(matchPlayer);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchPlayer {
    @JacksonXmlProperty(localName = "matchDataScore")
    private final MatchDataScore matchDataScore = null;
    @JacksonXmlProperty(isAttribute = true)
    private final String playerRef = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDataScore {
    @JacksonXmlProperty(localName = "IndexScore")
    private Float indexScore = null;

    private void setIndexScore(String indexScore) {
      this.indexScore = Float.valueOf(indexScore);
    }
  }
}
