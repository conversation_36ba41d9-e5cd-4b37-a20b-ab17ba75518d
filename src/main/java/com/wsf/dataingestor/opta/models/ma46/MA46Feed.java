package com.wsf.dataingestor.opta.models.ma46;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.LocalDateToInstantDeserializer;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class MA46Feed {

  private MatchInfo matchInfo;
  private ProvisionalLineUps provisionalLineUps;

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    private String id;
    private String coverageLevel;
    private String date;
    private String time;
    private Integer week;
    private Instant lastUpdated;
    private Sport sport;
    private Ruleset ruleset;
    private Competition competition;
    private TournamentCalendar tournamentCalendar;
    private Stage stage;
    private List<Contestant> contestant = new ArrayList<>();
    private Venue venue;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ProvisionalLineUps {
    private List<LineUp> lineUp = new ArrayList<>();
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LineUp {
    private String contestantId;
    private String formationUsed;
    private List<Player> player = new ArrayList<>();
    private TeamOfficial teamOfficial;
    private Injuries injuries;
    private Suspensions suspensions;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Injuries {
    private List<Injury> injury = new ArrayList<>();
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Suspensions {
    private List<Suspension> suspension = new ArrayList<>();
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Player {
    private String playerId;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String matchName;
    private Integer shirtNumber;
    private String position;
    private String positionSide;
    private String positionX;
    private String positionY;
    private Integer formationPlace;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TeamOfficial {
    private String id;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String type;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Injury {
    private String playerId;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String matchName;
    private String position;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant expectedEndDate;
    private String type;
    private String status;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Suspension {
    private String playerId;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String matchName;
    private String position;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant endDate;
    private String type;
    private Integer matches;
    private String description;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Sport {
    private String id;
    private String name;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Ruleset {
    private String id;
    private String name;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    private String id;
    private String name;
    private String knownName;
    private String sponsorName;
    private String competitionCode;
    private String competitionFormat;
    private Country country;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    private String id;
    private String name;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant endDate;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stage {
    private String id;
    private String formatId;
    private String name;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant endDate;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestant {
    private String id;
    private String name;
    private String shortName;
    private String officialName;
    private String code;
    private String position;
    private Country country;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Venue {
    private String id;
    private String neutral;
    private String longName;
    private String shortName;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Country {
    private String id;
    private String name;
  }
}
