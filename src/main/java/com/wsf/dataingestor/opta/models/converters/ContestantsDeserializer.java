package com.wsf.dataingestor.opta.models.converters;

import java.io.IOException;
import org.apache.commons.lang3.tuple.Pair;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

public class ContestantsDeserializer extends JsonDeserializer<Pair<String, String>> {

  @Override
  public Pair<String, String> deserialize(JsonParser jsonParser,
                                          DeserializationContext deserializationContext) throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);

    String homeTeamId = "";
    String awayTeamId = "";
    for (JsonNode next : node) {
      if (next.get("position").asText().equals("home")) {
        homeTeamId = next.get("id").asText();
      } else {
        awayTeamId = next.get("id").asText();
      }
    }

    return Pair.of(homeTeamId, awayTeamId);
  }

}
