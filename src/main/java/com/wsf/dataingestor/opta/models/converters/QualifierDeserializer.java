package com.wsf.dataingestor.opta.models.converters;

import java.io.IOException;
import java.util.Map;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.Optional.ofNullable;

public class QualifierDeserializer extends JsonDeserializer<Map<Integer, String>> {
  @Override
  public Map<Integer, String> deserialize(JsonParser jsonParser,
                                          DeserializationContext deserializationContext) throws IOException {
    Map<Integer, String> qualifiers = newHashMap();

    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);

    for (JsonNode next : node) {
      if (next.has("qualifierId")) {
        qualifiers.put(next.get("qualifierId").asInt(), ofNullable(next.get("value"))
          .map(JsonNode::asText)
          .orElse(""));
      }
    }

    return qualifiers;
  }
}
