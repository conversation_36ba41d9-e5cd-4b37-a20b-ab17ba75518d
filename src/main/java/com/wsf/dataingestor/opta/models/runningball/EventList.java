package com.wsf.dataingestor.opta.models.runningball;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "event_list")
@RequiredArgsConstructor
public class EventList {

  @JacksonXmlProperty(localName = "date_generated", isAttribute = true)
  private final Instant dateGenerated = null;

  @JacksonXmlProperty(localName = "event")
  @JacksonXmlElementWrapper(useWrapping = false)
  private List<Event> events = newArrayList();

  @JsonSetter
  public void setEvents(Event event) {
    this.events.add(event);
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Event {
    @JacksonXmlProperty(localName = "game_id", isAttribute = true)
    private final String gameId = null;
    @JacksonXmlProperty(localName = "minute", isAttribute = true)
    private final String minute = null;
    @JacksonXmlProperty(localName = "event_number", isAttribute = true)
    private final String eventNumber = null;
    @JacksonXmlProperty(localName = "event_code_id", isAttribute = true)
    private final String eventCodeId = null;
    @JacksonXmlProperty(localName = "message_id", isAttribute = true)
    private final String messageId = null;
    @JacksonXmlProperty(localName = "date", isAttribute = true)
    private final Instant timestamp = null;
    @JacksonXmlProperty(localName = "score_home", isAttribute = true)
    private final String scoreHome = null;
    @JacksonXmlProperty(localName = "score_away", isAttribute = true)
    private final String scoreAway = null;
    @JacksonXmlProperty(localName = "imported_game_id", isAttribute = true)
    private final String optaMatchId = null;
  }


}
