package com.wsf.dataingestor.opta.models.ma1;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wsf.dataingestor.opta.models.ma2.MA2Feed;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class MA1Feed {

  @JsonProperty("match")
  private List<Match> matchList = newArrayList();

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Match {

    private final MatchInfo matchInfo = null;
    private final LiveData liveData = null;

  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchInfo {
    private String id = null;
    private String date = null;
    private String time = null;
    private String week = null;
    private Stage stage = null;
    private TournamentCalendar tournamentCalendar = null;
    private Integer overtimeLength = null;
    private Venue venue = null;
    private Integer optaBetting = null;

    @JsonProperty("contestant")
    private List<Contestant> contestantList = newArrayList();

    private Instant lastUpdated = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Contestant {
    private String id = null;
    private String name = null;
    private String position = null;
    private String code = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stage {
    private String formatId = null;
    private String name = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    private String id = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {
    private final MA2Feed.MatchDetails matchDetails = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Venue {
    private String neutral = null;
  }

}
