package com.wsf.dataingestor.opta.models.ma18dp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.InstantDeserializer;
import com.wsf.dataingestor.opta.models.converters.QualifierDeserializer;
import com.wsf.dataingestor.opta.models.converters.StatDeserializer;
import com.wsf.dataingestor.opta.parsers.events.AbstractFeedEvent;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class MA18DPFeed {

  private final Content content = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Content {
    private final LiveData liveData = null;
    private final String msg = null;
    private final String fixture = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {
    private final MatchDetails matchDetails = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDetails {
    private final String id = null;
    private final Stats stats = null;
    @JsonProperty("event")
    private final List<Event> events = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stats {
    @JsonProperty("team")
    private final List<Team> teams = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Team {
    private final String contestantId = null;
    @JsonProperty("stat")
    @JsonDeserialize(using = StatDeserializer.class)
    private final Map<String, String> stats = newHashMap();
    @JsonProperty("player")
    private final List<Player> players = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Player {
    private final String playerId = null;

    @JsonProperty("stat")
    @JsonDeserialize(using = StatDeserializer.class)
    private final Map<String, String> stats = newHashMap();
  }

  @Data
  @Builder
  @EqualsAndHashCode(callSuper = true)
  @NoArgsConstructor
  @AllArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Event extends AbstractFeedEvent {
    private String id = null;
    private Integer seqId = null;
    private String eventId = null;
    private Integer typeId = null;
    private String playerId = null;
    private String contestantId = null;
    private String timeMin = null;
    private Integer periodId = null;
    private Short outcome = null;

    @JsonProperty("timeStamp")
    @JsonDeserialize(using = InstantDeserializer.class)
    private Instant timestamp = null;

    @JsonDeserialize(using = InstantDeserializer.class)
    private Instant lastModified = null;

    @JsonProperty("qualifier")
    @JsonDeserialize(using = QualifierDeserializer.class)
    private Map<Integer, String> qualifiers = newHashMap();
  }

}
