package com.wsf.dataingestor.opta.models.pep7;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.LocalDateToInstantDeserializer;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class PEP7Feed {

  private Competition competition;
  private TournamentCalendar tournamentCalendar;
  private List<Person> person = new ArrayList<>();

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    private String id;
    private String name;
    private String knownName;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    private String id;
    private String name;
    private String startDate;
    private String endDate;
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Person {
    private String id;
    private String firstName;
    private String lastName;
    private String shortFirstName;
    private String shortLastName;
    private String matchName;
    private String type;
    private String position;
    private List<Injury> injury = new ArrayList<>();
  }

  @Data
  @NoArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Injury {
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant startDate;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private Instant endDate;
    private String type;
  }
}