package com.wsf.dataingestor.opta.models.converters;

import java.io.IOException;
import java.util.Map;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import static com.google.common.collect.Maps.newHashMap;

public class StatDeserializer extends JsonDeserializer<Map<String, String>> {
  @Override
  public Map<String, String> deserialize(JsonParser jsonParser,
                                         DeserializationContext deserializationContext) throws IOException {
    Map<String, String> stats = newHashMap();

    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);

    for (JsonNode next : node) {
      if (next.has("type")) {
        stats.put(next.get("type").asText(), next.get("value").asText());
      }
    }

    return stats;
  }
}

