package com.wsf.dataingestor.opta.models.ot2;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.LocalDateToInstantDeserializer;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class OT2Feed {

  List<Competition> competition = new ArrayList<>();

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Competition {
    private final String id = null;
    private final String name = null;
    private final List<TournamentCalendar> tournamentCalendar = new ArrayList<>();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TournamentCalendar {
    private final String id = null;
    private final String name = null;
    private final String active = null;
    @JsonDeserialize(using = LocalDateToInstantDeserializer.class)
    private final Instant startDate = null;
    private final List<Stage> stage = new ArrayList<>();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stage {
    private final String name = null;
    private final Integer vertical = null;
    private final Integer phase = null;
  }
}
