package com.wsf.dataingestor.opta.models.f40;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JacksonXmlRootElement(localName = "SoccerFeed")
@RequiredArgsConstructor
public class SoccerFeed {
  @JacksonXmlProperty(localName = "timestamp", isAttribute = true)
  private String timestamp = null;
  @JacksonXmlProperty(localName = "SoccerDocument")
  private final SoccerDocument soccerDocument = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class SoccerDocument {
    @JacksonXmlProperty(localName = "competition_id", isAttribute = true)
    private String competitionId = null;

    @JacksonXmlProperty(localName = "Team")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Team> teamList = newArrayList();

    @JsonSetter
    public void setTeamList(Team team) {
      this.teamList.add(team);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Team {
    @JacksonXmlProperty(localName = "uID", isAttribute = true)
    private String uId = null;
    @JacksonXmlProperty(localName = "Name")
    private final String name = null;

    @JacksonXmlProperty(localName = "Player")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Player> playerList = newArrayList();

    @JsonSetter
    public void setPlayerList(Player player) {
      this.playerList.add(player);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Player {
    @JacksonXmlProperty(localName = "uID", isAttribute = true)
    private String uId = null;
    @JacksonXmlProperty(localName = "loan", isAttribute = true)
    private String loan = null;
    @JacksonXmlProperty(localName = "Name")
    private final String name = null;
    @JacksonXmlProperty(localName = "Position")
    private final String position = null;

    @JacksonXmlProperty(localName = "Stat")
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Stat> stats = newArrayList();

    @JsonSetter
    public void setStatsList(Stat stat) {
      this.stats.add(stat);
    }
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stat {
    @JacksonXmlProperty(localName = "Type", isAttribute = true)
    private final String type = null;
    @JacksonXmlText
    private final String value = null;
  }
}
