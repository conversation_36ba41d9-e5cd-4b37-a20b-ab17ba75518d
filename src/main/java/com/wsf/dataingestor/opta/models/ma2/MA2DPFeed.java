package com.wsf.dataingestor.opta.models.ma2;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.opta.models.converters.StatDeserializer;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class MA2DPFeed {

  private final Content content = null;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Content {
    private final LiveData liveData = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class LiveData {
    private final MatchDetails matchDetails = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class MatchDetails {
    private final String id = null;
    private final Stats stats = null;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stats {
    private final String seqId = null;
    @JsonProperty("team")
    private final List<Team> teams = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Team {
    private final String contestantId = null;
    @JsonProperty("player")
    private final List<Player> players = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Player {
    private final String playerId = null;

    @JsonProperty("stat")
    @JsonDeserialize(using = StatDeserializer.class)
    private final Map<String, String> stats = newHashMap();
  }
}
