package com.wsf.dataingestor.opta.models.tm3;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import static com.google.common.collect.Lists.newArrayList;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class TeamSquads {

  private Instant lastUpdated = null;

  @JsonProperty("squad")
  private List<Squad> squadList = newArrayList();

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Squad {
    private String contestantId = null;
    private String contestantName = null;
    private String contestantCode = null;
    private String tournamentCalendarId = null;
    private String competitionId = null;

    @JsonProperty("person")
    private List<Person> personList = newArrayList();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Person {
    private String id = null;
    private String firstName = null;
    private String lastName = null;
    private String matchName = null;
    private String position = null;
    private String dateOfBirth = null;
    private String type = null;
    private String status = null; // active, retired
    private String active = null; // active, retired
  }

}
