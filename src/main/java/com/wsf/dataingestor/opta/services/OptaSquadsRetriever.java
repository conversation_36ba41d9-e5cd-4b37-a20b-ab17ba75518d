package com.wsf.dataingestor.opta.services;

import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.opta.clients.TM3Client;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.services.squads.SquadsRetriever;
import com.wsf.dataingestor.services.squads.SquadsRetrieverUtils;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

@Service
@RequiredArgsConstructor
public class OptaSquadsRetriever implements SquadsRetriever {

  private final TM3Client tm3Client;
  private final OptaFeedParserUtils optaFeedParserUtils;

  @Override
  public SquadsFeed retrieveSquadsFeed(Tournament tournament) {
    Set<String> externalIds = tournament.getExternalIds().getOptaIds();
    return SquadsRetrieverUtils.retrieveSquadsFeed(tm3Client, externalIds, SquadsFeed.FeedProvider.OPTA);
  }

  @Override
  public MasterPlayer findMasterPlayer(SquadPlayerDTO squadPlayerDTO) {
    String firstName = squadPlayerDTO.getFirstName();
    String lastName = squadPlayerDTO.getLastName();
    String matchName = squadPlayerDTO.getMatchName();
    LocalDate birthDate = squadPlayerDTO.getBirthDate();
    String playerId = squadPlayerDTO.getPlayerId();
    return optaFeedParserUtils.getMasterPlayerOrCreateUnmapped(firstName, lastName, matchName, birthDate, playerId);
  }

  @Override
  public Team findTeamOrCreateUnmapped(String competitionId, SquadPlayerDTO.TeamDTO teamDTO) {
    return optaFeedParserUtils.getTeamOrCreateUnmapped(competitionId, teamDTO.getExternalTeamId(),
      teamDTO.getExternalTeamName(), teamDTO.getExternalTeamAbbreviation());
  }

  @Override
  public Player.PlayerBuilder<?, ?> enrichPlayer(Player.PlayerBuilder<?, ?> playerBuilder, String optaPlayerId) {
    return playerBuilder.optaPlayerId(optaPlayerId);
  }

  @Override
  public String getExternalPlayerFieldName() {
    return "optaPlayerId";
  }
}
