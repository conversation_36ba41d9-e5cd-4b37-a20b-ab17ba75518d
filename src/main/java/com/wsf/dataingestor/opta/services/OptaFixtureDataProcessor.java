package com.wsf.dataingestor.opta.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.clients.PullAPIClient;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.handlers.WSHandler;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.events.HalfTimeNotifier;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchFinalDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;

import static com.wsf.dataingestor.cache.OngoingMatchDataCacheService.buildInitialOngoingMatchData;
import static com.wsf.dataingestor.services.stats.MatchStatsEnricher.updateTimeData;
import static com.wsf.domain.common.Fixture.FixtureStatus.FIXTURE;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.HALF_TIME;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@RequiredArgsConstructor
public class OptaFixtureDataProcessor implements FixtureDataProcessor {

  private final OngoingMatchDataCacheService ongoingMatchDataCache;

  private final PullAPIClient<MatchDataFeed> matchDataPullClient;

  private final OptaFixturePushDataRetriever matchPushDataRetriever;

  private final WebSocketManager webSocketManager;

  private final MatchLiveDataProcessor matchLiveDataProcessor;

  private final MatchFinalDataProcessor matchFinalDataProcessor;

  private final HalfTimeNotifier halfTimeNotifier;


  @Override
  public MatchDataFeed processFixtureStatsFeed(Fixture fixture) {
    String matchExternalId = fixture.getOptaFixtureId();
    MatchDataFeed dataFeed = matchDataPullClient.retrieveParsedFeed(matchExternalId);

    if (fixture.getIsLiveEnabled()) {
      return processMA2(dataFeed);
    } else {
      return dataFeed;
    }
  }

  @Override
  public void manageWebSocket(Fixture fixture, FixtureStatus fixtureStatus) {
    if (fixture.getIsLiveEnabled()) {
      if (FIXTURE == fixtureStatus || LIVE == fixtureStatus) {
        matchPushDataRetriever.establishLiveConnectionIfNotExists(fixture);
      } else {
        matchPushDataRetriever.disconnectFixture(fixture.getId().toString());
      }
    }
  }

  public MatchDataFeed processMA2(MatchDataFeed dataFeed) {
    Fixture fixture = dataFeed.getFixture();
    String fixtureId = fixture.getIdAsString();

    OngoingMatchData cachedMatchData = ongoingMatchDataCache.getOrCompute(fixtureId,
      fId -> buildInitialOngoingMatchData(fId, dataFeed.getLatestUpdateTs()));

    boolean enteringHalfTime = cachedMatchData.getMatchPeriod() == FIRST_HALF && dataFeed.getMatchPeriod() == HALF_TIME;

    if (hasFeedNewData(dataFeed, cachedMatchData)) {
      updateTimeData(dataFeed, cachedMatchData);
    }

    if (dataFeed.getFixtureStatus().isFinished()) {
      log.info("fixtureId={} is {}", fixtureId, dataFeed.getFixtureStatus());
      matchFinalDataProcessor.processFeed(dataFeed);
      ongoingMatchDataCache.set(fixtureId, cachedMatchData);
      return dataFeed;
    }

    log.info("existing cached data for fixtureId={}: {}", fixtureId, cachedMatchData);
    matchLiveDataProcessor.processPullFeed(dataFeed, cachedMatchData);

    ongoingMatchDataCache.mergeIfExists(fixtureId, cachedMatchData, false);

    if (enteringHalfTime) {
      halfTimeNotifier.notifyHalfTime(dataFeed, cachedMatchData);
    }

    // we need this so that every minute we can send an update for all the players on the pitch
    // if we remove it, the updates would be sent only in case of an event or stats changes
    if (dataFeed.isInProgress()) {
      requestSnapshot(fixtureId);
    }

    return dataFeed;
  }

  private void requestSnapshot(String fixtureId) {
    log.info("snapshot requested for fixtureId={}", fixtureId);
    WebSocketManager.WSData matchWebSocket = webSocketManager.getMatchWebSocket(fixtureId);
    if (nonNull(matchWebSocket)) {
      WSHandler handler = matchWebSocket.getHandler();
      handler.requestStatsSnapshot(matchWebSocket.getWebSocket());
    }
  }

  private static boolean hasFeedNewData(MatchDataFeed dataFeed, OngoingMatchData cachedMatchData) {
    return isNull(dataFeed.getLatestUpdateTs()) ||
      dataFeed.getLatestUpdateTs().toEpochMilli() >= cachedMatchData.getTimestamp();
  }
}
