package com.wsf.dataingestor.opta.services;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.clients.PushAPIClient;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.metrics.TagHelper.Tag;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.models.NewAndDeletedEvents;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.RATINGS_FEED_MA18DP_FROM_RECEIVED_LATENCY;
import static com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier.VALID_EVENTS;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static java.lang.String.valueOf;
import static java.time.Duration.between;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Stream.concat;

@Slf4j
@RequiredArgsConstructor
public class OptaFixturePushDataRetriever {

  private final OngoingMatchDataCacheService ongoingMatchDataCache;
  private final PushAPIClient matchDataPushClient;
  private final MatchEventsProcessor matchEventsProcessor;
  private final MatchLiveDataProcessor matchLiveDataProcessor;
  private final LiveFixtureSummaryNotifier liveFixtureSummaryNotifier;
  private final FixtureService fixtureService;
  private final ThreadPoolService threadPoolService;
  private final WebSocketManager webSocketManager;
  private final MetricsManager metrics;

  public void establishLiveConnectionIfNotExists(Fixture fixture) {
    String fixtureId = fixture.getId().toString();

    var wsData = webSocketManager.getMatchWebSocket(fixtureId);
    if (isNull(wsData)) {
      establishLiveConnection(fixture);
    } else {
      if (nonNull(wsData.getWebSocket()) && !wsData.getWebSocket().isOpen()) {
        log.error("Found a broken websocket for fixtureId={}", fixtureId);
        disconnectFixture(fixtureId);
        establishLiveConnection(fixture);
      } else {
        log.debug("There's already a websocket established for fixtureId={}", fixtureId);
      }
    }
  }

  public void disconnectFixture(String fixtureId) {
    webSocketManager.disconnectWebSocket(fixtureId);
  }

  public void processMA18DPFeed(MatchDataFeed parsedFeed) {
    String fixtureId = parsedFeed.getFixture().getIdAsString();

    log.info("scheduling operation for feed with feedId={} fixtureId={}", parsedFeed.getFeedId(), fixtureId);

    threadPoolService.processFixtureUpdateInThreadPool(fixtureId, () -> {
      var start = now();
      Fixture fixture = parsedFeed.getFixture();

      log.info("feed with feedId={} fixtureId={} received", parsedFeed.getFeedId(), fixtureId);

      if (SETTLED == fixture.getProcessStatus()) {
        log.info("fixtureId={} has already been settled, disconnecting the websocket", fixtureId);
        disconnectFixture(fixtureId);
        return;
      }

      OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixtureId);
      if (isNull(ongoingMatchData)) {
        log.warn("no cached data found for fixtureId={} ", fixtureId);
        return;
      } else if (ongoingMatchData.isFinished()) {
        log.warn("fixtureId={} is already finished", fixtureId);
        return;
      }

      checkIfMatchStarting(fixture, parsedFeed);
      Boolean isLiveEnabled = fixtureService.getFixture(fixtureId).getIsLiveEnabled();
      if (isLiveEnabled) {
        matchEventsProcessor.processMatchEvents(parsedFeed, ongoingMatchData);

        // We added preProcessEventsFn because there was a race conditions between the usual Rating/OddsUpdateEvent/UpdateEvent and the live FixtureSummary
        matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData,
          (newAndDeletedEvents) -> sendLiveFixtureSummary(newAndDeletedEvents, ongoingMatchData));

        updateSeqId(parsedFeed, ongoingMatchData);
        ongoingMatchDataCache.mergeIfExists(fixtureId, ongoingMatchData, true);
        metrics.recordTimer(RATINGS_FEED_MA18DP_FROM_RECEIVED_LATENCY.value(), between(start, now()),
          List.of(Tag.of("isSnapshot", valueOf(parsedFeed.isSnapshot()))));
      }
    });
  }

  private void sendLiveFixtureSummary(NewAndDeletedEvents newAndDeletedEvents, OngoingMatchData ongoingMatchData) {
    boolean shouldPublish = concat(newAndDeletedEvents.newEvents()
      .stream(), newAndDeletedEvents.deletedEvents()
      .stream()).anyMatch(event -> VALID_EVENTS.contains(event.getEvent()));
    if (shouldPublish) {
      liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);
    }
  }

  private void updateSeqId(MatchDataFeed parsedFeed, OngoingMatchData ongoingMatchData) {
    Integer seqId = parsedFeed.getSeqId();
    if (nonNull(seqId)) {
      String fixtureId = parsedFeed.getFixture().getIdAsString();
      log.info("updating Opta ma18dp seqId to {} in cache for fixtureId={}", seqId, fixtureId);
      ongoingMatchData.setSeqId(seqId);
      ofNullable(webSocketManager.getMatchWebSocket(fixtureId)).ifPresent(
        matchWS -> matchWS.getHandler().updateSeqId(seqId));
    }
  }

  private void checkIfMatchStarting(Fixture fixture, MatchDataFeed parsedFeed) {
    Optional<MatchEventDTO> matchStartingEvent = parsedFeed.getMatchEvents()
      .stream()
      .filter(matchEventDTO -> matchEventDTO.getEvent().equals(SoccerMatchEvent.MATCH_STARTED))
      .findFirst();
    boolean isMatchStarting = matchStartingEvent.isPresent();

    // only do it once at the beginning of the fixture
    if (isMatchStarting) {
      log.info("fixtureId={} match is starting", fixture.getIdAsString());
      fixtureService.storeFixtureStarted(fixture);
    }
  }

  private void establishLiveConnection(Fixture fixture) {
    String fixtureId = fixture.getIdAsString();
    threadPoolService.processFixtureUpdateInThreadPool(fixtureId, () -> {
      log.info("launching websocket connection for fixtureId={} fixture: {}", fixtureId, fixture);

      // this can be improved with AtomicBooleans or Semaphores as there may be a race condition here (although very rare).
      OngoingMatchData ongoingMatchData = ongoingMatchDataCache.get(fixtureId);
      int seqId = ongoingMatchData.getSeqId();
      connectWebSocketAndProcessMatchData(fixture, seqId, data -> onSubscribeHandler(fixtureId, data),
        () -> onUnsubscribeHandler(fixtureId));
    });
  }

  private void connectWebSocketAndProcessMatchData(Fixture fixture, int seqId,
                                                   Consumer<WebSocketManager.WSData> onSubscribeHandler,
                                                   Runnable onUnsubscribeHandler) {
    matchDataPushClient.startConnectionAndProcessFeed(fixture.getOptaFixtureId(), seqId, this::processMA18DPFeed,
      (message, ex) -> handleFeedException(message, ex, fixture), onSubscribeHandler, onUnsubscribeHandler);
  }

  private void onSubscribeHandler(String fixtureId, WebSocketManager.WSData data) {
    webSocketManager.setMatchWebSocket(fixtureId, data);
  }

  private void onUnsubscribeHandler(String fixtureId) {
    webSocketManager.removeMatchWebSocket(fixtureId);
  }

  private void handleFeedException(String message, Exception e, Fixture fixture) {
    Counter metric;
    if (message.contains("Flushing frames to the server failed: Connection or outbound has closed")) {
      metric = metrics.RATINGS_FEED_CONNECTION_CLOSED_ERROR;
    } else {
      metric = metrics.RATINGS_FEED_PROCESSING_ERROR;
    }
    log.error("Error for fixtureId={} error={}: {}", fixture.getId().toString(), metric.getId().getName(), message, e);
    metric.increment();
  }
}
