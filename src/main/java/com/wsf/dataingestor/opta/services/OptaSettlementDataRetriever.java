package com.wsf.dataingestor.opta.services;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.clients.MA3Client;
import com.wsf.dataingestor.services.ratings.SettlementDataRetriever;
import com.wsf.domain.common.Fixture;

@Slf4j
@Service
public class OptaSettlementDataRetriever implements SettlementDataRetriever {

  private final MA2Client ma2ClientFinal;
  private final MA3Client ma3ClientFinal;

  @Autowired
  public OptaSettlementDataRetriever(@Qualifier("ma2ClientFinal") MA2Client ma2ClientFinal, MA3Client ma3ClientFinal) {
    this.ma2ClientFinal = ma2ClientFinal;
    this.ma3ClientFinal = ma3ClientFinal;
  }

  @Override
  public MatchDataFeed retrieveSettlementData(Fixture fixture) {
    String optaFixtureId = fixture.getOptaFixtureId();
    MatchDataFeed matchDataFeed = ma2ClientFinal.retrieveParsedFeed(optaFixtureId);

    String fixtureId = fixture.getId().toString();
    log.info("Retrieving MA3 for settling fixtureId={}", fixtureId);

    try {
      var ma3Feed = ma3ClientFinal.retrieveParsedFeed(optaFixtureId);
      matchDataFeed.mergeEvents(ma3Feed);
    } catch (Exception e) {
      if (matchDataFeed.isCancelled() || matchDataFeed.isPostponed()) {
        log.warn(
          "error while ingesting MA3 feed for settlement for fixtureId={}. Settling anyway since the fixture is {}",
          fixtureId, matchDataFeed.getFixtureStatus(), e);
      } else {
        throw e;
      }
    }

    return matchDataFeed;
  }
}
