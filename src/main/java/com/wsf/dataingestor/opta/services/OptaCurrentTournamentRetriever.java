package com.wsf.dataingestor.opta.services;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.opta.clients.OT2CurrentTournamentClient;
import com.wsf.dataingestor.services.tournaments.CurrentTournamentRetriever;

@Service
@RequiredArgsConstructor
public class OptaCurrentTournamentRetriever implements CurrentTournamentRetriever {

  private final OT2CurrentTournamentClient currentTournamentClient;

  @Override
  public CurrentTournamentFeed retrieveTournamentFeed(String optaCompetitionId) {
    return currentTournamentClient.retrieveParsedFeed(optaCompetitionId);
  }
}
