package com.wsf.dataingestor.opta.services;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.services.ratings.FixtureStartDataRetriever;
import com.wsf.domain.common.Fixture;

@Service
@RequiredArgsConstructor
public class OptaFixtureStartDataRetriever implements FixtureStartDataRetriever {

  private final MA2Client ma2Client;

  @Override
  public MatchDataFeed retrieveFixtureStartData(Fixture fixture) {
    return ma2Client.retrieveParsedFeed(fixture.getOptaFixtureId());
  }
}
