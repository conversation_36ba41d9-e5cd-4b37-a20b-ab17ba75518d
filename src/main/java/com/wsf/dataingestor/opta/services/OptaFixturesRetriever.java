package com.wsf.dataingestor.opta.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.opta.clients.MA1Client;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.services.fixtures.FixtureRetrieverUtils;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class OptaFixturesRetriever implements FixturesRetriever {

  @Qualifier("ma1ClientOnlyBettingAllowed")
  private final MA1Client ma1ClientOnlyBettingAllowed;
  @Qualifier("ma1ClientNoFilters")
  private final MA1Client ma1ClientNoFilters;
  private final OptaFeedParserUtils optaFeedParserUtils;

  @Override
  public FixturesFeed retrieveFixturesFeed(Tournament tournament) {
    Set<String> externalIds = tournament.getExternalIds().getOptaIds();
    FixturesFeed allFixtures = FixtureRetrieverUtils.retrieveFixturesFeed(tournament, ma1ClientNoFilters, externalIds);
    FixturesFeed bettingAllowedFixtures = FixtureRetrieverUtils.retrieveFixturesFeed(tournament,
      ma1ClientOnlyBettingAllowed, externalIds);

    return mergeFeeds(allFixtures, bettingAllowedFixtures);
  }

  private FixturesFeed mergeFeeds(FixturesFeed allFixtures, FixturesFeed bettingAllowedFixtures) {
    Map<String, FixtureDTO> optaIdToOptaBettingAllowedFixtureDto = bettingAllowedFixtures.getFixtures()
      .stream()
      .collect(toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    Map<String, FixtureDTO> optaIdToOptaFixtureDto = allFixtures.getFixtures()
      .stream()
      .map(fixtureDTO -> fixtureDTO.withActiveStatus(false))
      .collect(toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    optaIdToOptaFixtureDto.putAll(optaIdToOptaBettingAllowedFixtureDto);

    List<FixtureDTO> fixturesDtos = optaIdToOptaFixtureDto.values()
      .stream()
      .toList();
    return new FixturesFeed(allFixtures.getFeedId(), allFixtures.getTournament(), fixturesDtos);
  }

  @Override
  public Fixture findFixture(FixtureDTO fixtureDTO, Tournament tournament) throws FixtureNotFoundException {

    Team homeTeam = optaFeedParserUtils.getTeamOrCreateUnmapped(tournament.getCompetitionId(),
      fixtureDTO.getExternalHomeTeamId(), fixtureDTO.getExternalHomeTeamName(),
      fixtureDTO.getExternalHomeTeamAbbreviation());
    Team awayTeam = optaFeedParserUtils.getTeamOrCreateUnmapped(tournament.getCompetitionId(),
      fixtureDTO.getExternalAwayTeamId(), fixtureDTO.getExternalAwayTeamName(),
      fixtureDTO.getExternalAwayTeamAbbreviation());

    if (isNull(homeTeam) || isNull(awayTeam)) {
      throw new IllegalStateException(
        format("either one of the two teams is not mapped: homeTeam %s awayTeam %s", homeTeam, awayTeam));
    }

    return ofNullable(
      optaFeedParserUtils.getFixtureByOptaIdOrTournamentAndTeamsAndDate(fixtureDTO.getExternalFixtureId(),
        tournament.getIdAsString(), homeTeam.getId().toString(), awayTeam.getId().toString(),
        fixtureDTO.getTime())).orElseThrow(() -> new FixtureNotFoundException(
      format("Could not find fixture for tournamentId=%s homeTeamId=%s awayTeamId=%s", tournament.getId().toString(),
        homeTeam.getId().toString(), awayTeam.getId().toString())));
  }

  @Override
  public Fixture findRelatedFixture(FixtureDTO fixture, Tournament tournament) throws FixtureNotFoundException {
    String externalId = fixture.getExternalRelatedFixtureId();
    if (isNull(externalId)) {
      return null;
    }
    return ofNullable(optaFeedParserUtils.getFixtureByOptaFixtureId(externalId)).orElseThrow(
      () -> new FixtureNotFoundException(format("Could not find fixture with optaMatchId=%s", externalId)));
  }

  @Override
  public Team findTeam(String externalTeamId, Tournament tournament) {
    return optaFeedParserUtils.getTeam(tournament.getCompetitionId(), externalTeamId);
  }

  @Override
  public boolean shouldProcessStages() {
    return false;
  }

  @Override
  public Stage findStage(FixtureDTO fixtureDTO, String competitionId) {
    throw new NotImplementedException("There is no need to find Opta stages as they are all supported.");
  }

  @Override
  public String getExternalFixtureFieldName() {
    return "optaFixtureId";
  }

  @Override
  public ExternalProvider getExternalFixtureProvider() {
    return ExternalProvider.OPTA;
  }
}
