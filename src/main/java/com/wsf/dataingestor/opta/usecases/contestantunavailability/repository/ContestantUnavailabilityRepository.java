package com.wsf.dataingestor.opta.usecases.contestantunavailability.repository;

import lombok.RequiredArgsConstructor;

import java.util.List;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.wsf.domain.common.ContestantUnavailability;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@RequiredArgsConstructor
public class ContestantUnavailabilityRepository {

  private final MongoTemplate template;

  public List<ContestantUnavailability> findByFixture(String fixtureId) {
    Criteria criteria = where("fixtureId").is(fixtureId);
    return template.find(new Query(criteria), ContestantUnavailability.class);
  }

  public void remove(ContestantUnavailability contestantUnavailability) {
    template.remove(contestantUnavailability);
  }

  public void upsert(ContestantUnavailability contestantUnavailability) {
    Query query = new Query(Criteria.where("fixtureId")
      .is(contestantUnavailability.getFixtureId())
      .and("contestantId")
      .is(contestantUnavailability.getContestantId()));

    template.findAndReplace(query, contestantUnavailability, FindAndReplaceOptions.options().upsert(),
      ContestantUnavailability.class, ContestantUnavailability.class);
  }
}
