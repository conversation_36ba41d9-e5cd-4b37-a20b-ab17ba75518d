package com.wsf.dataingestor.opta.usecases.contestantunavailability.models;

import lombok.Builder;

import java.time.Instant;
import java.util.Set;
import com.wsf.domain.common.Player;

@Builder
public record ContestantUnavailabilitiesDataFeed(String feedId,
                                                 Set<FeedContestantUnavailability> feedContestantUnavailabilities) {

  @Builder
  public record FeedContestantUnavailability(Player player, Set<FeedUnavailability> unavailabilities) {

    public String getContestantId() {
      return player.getIdAsString();
    }

    public boolean isContestantInjured() {
      return unavailabilities
        .stream()
        .anyMatch(unavailability -> unavailability.reason() == UnavailabilityReason.INJURY);
    }

    public boolean isContestantSuspended() {
      return unavailabilities
        .stream()
        .anyMatch(unavailability -> unavailability.reason() == UnavailabilityReason.SUSPENSION);
    }
  }

  @Builder
  public record FeedUnavailability(UnavailabilityReason reason,
                                   String description,
                                   Instant startDate,
                                   Instant endDate) {}

  public enum UnavailabilityReason {
    INJURY,
    SUSPENSION
  }
}