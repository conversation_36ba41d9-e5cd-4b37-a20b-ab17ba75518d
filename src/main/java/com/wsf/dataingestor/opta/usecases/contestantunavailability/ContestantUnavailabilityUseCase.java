package com.wsf.dataingestor.opta.usecases.contestantunavailability;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.config.PlayerAvailabilityUpdaterConfig;
import com.wsf.dataingestor.exceptions.FeedNotFoundException;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.MA46Client;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilityDto;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.service.ContestantUnavailabilityLogicService;
import com.wsf.domain.common.Fixture;
import com.wsf.kafka.domain.ContestantUnavailabilityKafka;
import com.wsf.kafka.domain.EventInfo;

import static java.time.Instant.now;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContestantUnavailabilityUseCase {

  private final PlayerAvailabilityUpdaterConfig playerAvailabilityUpdaterConfig;
  private final MA46Client ma46Client;
  private final ContestantUnavailabilityLogicService contestantUnavailabilityLogicService;
  private final MetricsManager metricsRegistry;
  private final KafkaService kafkaService;

  public Optional<String> process(Fixture fixture) {
    if (!isFixtureHandledByOpta(fixture)) {
      return Optional.empty();
    }

    try {
      ContestantUnavailabilitiesDataFeed unavailabilitiesDataFeed = ma46Client.retrieveParsedFeed(
        fixture.getOptaFixtureId());

      log.info("Retrieved and parsed correctly {} unavailabilities (injuries/suspensions) for fixture={} in date={}",
        unavailabilitiesDataFeed.feedContestantUnavailabilities()
          .size(), fixture.getIdAsString(), fixture.getDate());

      contestantUnavailabilityLogicService
        .processUnavailabilitiesAndGetVariations(fixture, unavailabilitiesDataFeed.feedContestantUnavailabilities())
        .forEach(dto -> sendToKafka(unavailabilitiesDataFeed.feedId(), dto));
    } catch (Exception e) {
      if (e instanceof FeedNotFoundException) {
        if (isMissingFeedToNotify(fixture.getDate())) {
          return Optional.of(fixture.getOptaFixtureId());
        } else {
          log.warn(
            "Still missing MA46 feed for fixture id {} - Opta id {}. Email to support should have been already sent",
            fixture.getIdAsString(), fixture.getOptaFixtureId());
        }
      } else if (isExceptionToAlert(fixture.getDate())) {
        Counter counter = metricsRegistry.PLAYER_AVAILABILITY_FEED_ERROR;
        counter.increment();
        log.error("error={} while processing the MA46 feed for fixture in the next {} hours fixture={} in date={}",
          counter.getId().getName(), playerAvailabilityUpdaterConfig.getAlertFixturesWithinHours(),
          fixture.getIdAsString(), fixture.getDate(), e);
      }
    }

    return Optional.empty();
  }

  private boolean isMissingFeedToNotify(Instant fixtureDate) {
    Instant now = Instant.now();
    Instant notNotifyUpperLimit = now.plus(
      Duration.ofHours(playerAvailabilityUpdaterConfig.getNotAlertFixturesWithinHours()));
    Instant notifyUpperLimit = now.plus(
      Duration.ofHours(playerAvailabilityUpdaterConfig.getAlertFixturesWithinHours()));

    if (fixtureDate.isAfter(now) && fixtureDate.isBefore(notNotifyUpperLimit)) {
      return false;
    }
    return fixtureDate.isAfter(now) && fixtureDate.isBefore(notifyUpperLimit);
  }

  private boolean isExceptionToAlert(Instant fixtureDate) {
    Instant now = Instant.now();
    Instant nextHours = now.plus(Duration.ofHours(playerAvailabilityUpdaterConfig.getAlertFixturesWithinHours()));
    return fixtureDate.isAfter(now) && fixtureDate.isBefore(nextHours);
  }

  private boolean isFixtureHandledByOpta(Fixture fixture) {
    return fixture.getOptaFixtureId() != null;
  }

  private void sendToKafka(String feedId, ContestantUnavailabilityDto dto) {
    EventInfo eventInfo = new EventInfo(now(), now(), feedId);
    var contestantUnavailabilityKafka = new ContestantUnavailabilityKafka(dto.fixtureId(), dto.competitionId(),
      dto.contestantId(), dto.isSuspended(), dto.isInjured(), dto.action(), eventInfo);
    kafkaService.sendContestantUnavailabilities(contestantUnavailabilityKafka);
  }
}