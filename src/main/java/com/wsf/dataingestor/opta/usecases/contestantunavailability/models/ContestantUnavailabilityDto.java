package com.wsf.dataingestor.opta.usecases.contestantunavailability.models;

import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action;

public record ContestantUnavailabilityDto(String fixtureId,
                                          String competitionId,
                                          String contestantId,
                                          Boolean isSuspended,
                                          Boolean isInjured,
                                          Action action) {}
