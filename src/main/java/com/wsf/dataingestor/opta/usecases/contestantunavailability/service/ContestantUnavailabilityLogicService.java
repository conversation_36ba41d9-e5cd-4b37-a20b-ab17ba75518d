package com.wsf.dataingestor.opta.usecases.contestantunavailability.service;

import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedContestantUnavailability;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedUnavailability;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilityDto;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.repository.ContestantUnavailabilityRepository;
import com.wsf.domain.common.ContestantUnavailability;
import com.wsf.domain.common.ContestantUnavailability.Unavailability;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.AVAILABLE;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.UNAVAILABLE;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.of;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Service
@RequiredArgsConstructor
public class ContestantUnavailabilityLogicService {

  private static final int NR_DAYS_UNCERTAIN_INJURY = 4;

  private final ContestantUnavailabilityRepository repository;

  public Set<ContestantUnavailabilityDto> processUnavailabilitiesAndGetVariations(Fixture fixture,
                                                                                  Set<FeedContestantUnavailability> feedContestantUnavailabilities) {

    var fixtureId = fixture.getIdAsString();
    var competitionId = fixture.getTournament().getCompetitionId();
    var injuryCertaintyDate = fixture.getDate().plus(NR_DAYS_UNCERTAIN_INJURY, ChronoUnit.DAYS);

    Map<String, FeedContestantUnavailability> feedUnavailabilitiesByContestantId = feedContestantUnavailabilities
      .stream()
      .map(unavailabilities -> filterOutUncertainInjuries(unavailabilities, injuryCertaintyDate))
      .<FeedContestantUnavailability>mapMulti(Optional::ifPresent)
      .collect(toMap(FeedContestantUnavailability::getContestantId, Function.identity()));

    Map<String, ContestantUnavailability> databaseUnavailabilititesByContestantId = repository.findByFixture(fixtureId)
      .stream()
      .collect(toMap(ContestantUnavailability::getContestantId, Function.identity()));

    return Stream
      .of(feedUnavailabilitiesByContestantId.keySet(), databaseUnavailabilititesByContestantId.keySet())
      .flatMap(Collection::stream)
      .distinct()
      .map(contestantId -> processContestant(fixtureId, contestantId, competitionId, feedUnavailabilitiesByContestantId,
        databaseUnavailabilititesByContestantId))
      .filter(Objects::nonNull)
      .collect(toSet());
  }

  private ContestantUnavailabilityDto processContestant(String fixtureId, String contestantId, String competitionId,
                                                        Map<String, FeedContestantUnavailability> feedUnavailabilitiesByContestantId,
                                                        Map<String, ContestantUnavailability> databaseUnavailabilititesByContestantId) {

    FeedContestantUnavailability feedContestantUnavailability = feedUnavailabilitiesByContestantId.get(contestantId);
    ContestantUnavailability databaseContestantUnavailability = databaseUnavailabilititesByContestantId.get(
      contestantId);

    if (isContestantPresentInFeed(feedContestantUnavailability) &&
      isContestantPresentInDatabase(databaseContestantUnavailability) &&
      areUnavailabilitiesTheSame(feedContestantUnavailability, databaseContestantUnavailability)) {
      return null;
    }

    if (isContestantNotPresentInFeed(feedContestantUnavailability)) {
      return clearUnavailabilityStatus(fixtureId, competitionId, contestantId, databaseContestantUnavailability);
    }

    return upsertUnavailabilityStatus(fixtureId, competitionId, contestantId, feedContestantUnavailability);
  }

  private boolean isContestantPresentInFeed(FeedContestantUnavailability feedContestantUnavailability) {
    return nonNull(feedContestantUnavailability);
  }

  private boolean isContestantPresentInDatabase(ContestantUnavailability contestantUnavailability) {
    return nonNull(contestantUnavailability);
  }

  private boolean isContestantNotPresentInFeed(FeedContestantUnavailability feedContestantUnavailability) {
    return isNull(feedContestantUnavailability);
  }

  private boolean areUnavailabilitiesTheSame(FeedContestantUnavailability feedContestantUnavailability,
                                             ContestantUnavailability databaseContestantUnavailability) {

    Set<Unavailability> databaseUnavailabilitySet = databaseContestantUnavailability.getUnavailabilities();
    Set<Unavailability> convertedUnavailabilitySet = mapFeedUnavailabilityListToDatabaseUnavailabilityList(
      feedContestantUnavailability.unavailabilities());

    return convertedUnavailabilitySet.equals(databaseUnavailabilitySet);
  }

  private ContestantUnavailabilityDto clearUnavailabilityStatus(String fixtureId, String competitionId,
                                                                String contestantId,
                                                                ContestantUnavailability databaseContestantUnavailability) {
    repository.remove(databaseContestantUnavailability);
    return new ContestantUnavailabilityDto(fixtureId, competitionId, contestantId, false, false, AVAILABLE);
  }

  private ContestantUnavailabilityDto upsertUnavailabilityStatus(String fixtureId, String competitionId,
                                                                 String contestantId,
                                                                 FeedContestantUnavailability feedContestantUnavailability) {

    Set<Unavailability> unavailabilities = mapFeedUnavailabilityListToDatabaseUnavailabilityList(
      feedContestantUnavailability.unavailabilities());
    ContestantUnavailability contestantUnavailability = new ContestantUnavailability(fixtureId, contestantId,
      unavailabilities, now());

    repository.upsert(contestantUnavailability);

    boolean isSuspended = feedContestantUnavailability.isContestantSuspended();
    boolean isInjured = feedContestantUnavailability.isContestantInjured();
    return new ContestantUnavailabilityDto(fixtureId, competitionId, contestantId, isSuspended, isInjured, UNAVAILABLE);
  }

  private static Optional<FeedContestantUnavailability> filterOutUncertainInjuries(
    FeedContestantUnavailability unavailabilities, Instant injuryCertaintyDate) {
    Set<FeedUnavailability> injuriesOrSuspensions = unavailabilities.unavailabilities()
      .stream()
      .filter(unavailability -> {
        boolean isSuspension =
          unavailability.reason() == ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION;
        boolean isInjury = unavailability.reason() == INJURY;
        boolean isInjuryCertain =
          isInjury && (isNull(unavailability.endDate()) || unavailability.endDate().isAfter(injuryCertaintyDate));
        return isSuspension || isInjuryCertain;
      })
      .collect(toSet());
    if (injuriesOrSuspensions.isEmpty()) {
      return Optional.empty();
    }
    return of(new FeedContestantUnavailability(unavailabilities.player(), injuriesOrSuspensions));
  }

  private static Set<Unavailability> mapFeedUnavailabilityListToDatabaseUnavailabilityList(
    Set<FeedUnavailability> feedUnavailabilities) {

    return feedUnavailabilities
      .stream()
      .map(ContestantUnavailabilityLogicService::mapFeedToDatabaseUnavailability)
      .collect(toSet());
  }

  private static Unavailability mapFeedToDatabaseUnavailability(FeedUnavailability feedUnavailability) {
    return new Unavailability(mapToDatabaseReason(feedUnavailability.reason()), feedUnavailability.description(),
      feedUnavailability.startDate(), feedUnavailability.endDate());
  }

  private static ContestantUnavailability.UnavailabilityReason mapToDatabaseReason(
    ContestantUnavailabilitiesDataFeed.UnavailabilityReason feedReason) {
    return switch (feedReason) {
      case INJURY -> ContestantUnavailability.UnavailabilityReason.INJURY;
      case SUSPENSION -> ContestantUnavailability.UnavailabilityReason.SUSPENSION;
    };
  }
}
