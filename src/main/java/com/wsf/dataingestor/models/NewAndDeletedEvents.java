package com.wsf.dataingestor.models;

import java.util.List;
import com.wsf.dataingestor.cache.models.OngoingMatchData;

public record NewAndDeletedEvents(List<OngoingMatchData.PlayerMatchEventDTO> newEvents,
                                  List<OngoingMatchData.PlayerMatchEventDTO> deletedEvents) {
  public boolean doesContainEvents() {
    return !newEvents.isEmpty() || !deletedEvents.isEmpty();
  }
}
