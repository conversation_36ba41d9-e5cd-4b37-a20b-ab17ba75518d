package com.wsf.dataingestor.models;

import java.time.Instant;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

public final class EntityEventDTOFactory {

  public static EntityEventDTO standaloneEvent(String eventId, String entityId, String externalEntityId, String teamId,
                                               String externalTeamId, boolean isUnknownEntity,
                                               SoccerMatchEvent eventType, MatchPeriod period, int timeMin,
                                               boolean ignore, boolean isUnknown, boolean isOnBench,
                                               Instant timestamp) {
    return new EntityEventDTO(eventId, null, entityId, externalEntityId, teamId, externalTeamId, isUnknownEntity,
      eventType, period, timeMin, ignore, isUnknown, isOnBench, timestamp);
  }

  public static EntityEventDTO withRelatedEvent(String eventId, String relatedEventId, String entityId,
                                                String externalEntityId, String teamId, String externalTeamId,
                                                boolean isUnknownEntity, SoccerMatchEvent eventType, MatchPeriod period,
                                                int timeMin, boolean ignore, boolean isUnknown, boolean isOnBench,
                                                Instant timestamp) {
    return new EntityEventDTO(eventId, relatedEventId, entityId, externalEntityId, teamId, externalTeamId,
      isUnknownEntity, eventType, period, timeMin, ignore, isUnknown, isOnBench, timestamp);
  }
}
