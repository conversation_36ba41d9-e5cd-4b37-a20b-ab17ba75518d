package com.wsf.dataingestor.models;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;

@Data
@Builder(toBuilder = true)
public class FixtureDTO {

  private final String externalFixtureId;
  private final String externalRelatedFixtureId;
  private final String externalHomeTeamId;
  private final String externalHomeTeamName;
  private final String externalHomeTeamAbbreviation;
  private final String externalAwayTeamId;
  private final String externalAwayTeamName;
  private final String externalAwayTeamAbbreviation;
  private final String externalStageId;
  private final String externalStageName;
  private final Boolean isNeutralVenue;
  private final Relation leg;
  private final Instant time;
  private final FeedFixtureStatus fixtureStatus;
  private final Instant lastUpdated;
  private final Boolean canGoExtraTime;
  private final Provider provider;
  private final Boolean isLiveSupported;
  private final Boolean isActive;

  private Integer week;

  public enum Relation {
    FIRST_LEG,
    SECOND_LEG,
    SINGLE

  }

  public enum Provider {
    OPTA,
    SPORTMONKS
  }

  public FixtureDTO withActiveStatus(boolean isActive) {
    return toBuilder().isActive(isActive).build();
  }
}
