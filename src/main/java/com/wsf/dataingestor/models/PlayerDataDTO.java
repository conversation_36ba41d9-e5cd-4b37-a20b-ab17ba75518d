package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Map;
import com.wsf.dataingestor.opta.parsers.ma18dp.ProviderPlayerKey;
import com.wsf.domain.common.Player;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.Objects.isNull;
import static java.util.UUID.randomUUID;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
public class PlayerDataDTO implements EntityDataDTO {

  private final Player player;
  private final UnknownPlayer unknownPlayer;

  private final Player.DetailedPosition matchPosition;

  private final boolean isPlaying;
  private final boolean hasPlayed;
  private final boolean isUnknown;

  @NotNull
  @Builder.Default
  private Map<String, Number> stats = newHashMap();

  private final Instant timestamp;

  @Builder.Default
  private final String eventId = randomUUID().toString();

  public String getPlayerId() {
    return player.getId().toString();
  }

  public String getTeamId() {
    return player.getTeam().getIdAsString();
  }

  public Player getPlayer() {
    if (isUnknown) {
      throw new IllegalStateException("Cannot get the player since it is unknown");
    }
    return player;
  }

  @Override
  public String getEntityId() {
    return isUnknown ? getUnknownPlayer().providerPlayerId() : getPlayerId();
  }

  public String getParentEntityId() {
    return isUnknown ? getUnknownPlayer().providerTeamId() : getTeamId();
  }

  @Override
  public ContestantType getContestantType() {
    return ContestantType.SOCCER_PLAYER;
  }

  public static PlayerDataDTO buildPlayerData(Instant timestamp, Map<String, Number> translatedStats, Player player,
                                              ProviderPlayerKey providerPlayerKey, Player.DetailedPosition position,
                                              boolean isPlaying, boolean hasPlayed, String eventId) {

    if (isNull(player)) {
      return new PlayerDataDTO(null, new UnknownPlayer(providerPlayerKey.id(), providerPlayerKey.teamId()), position,
        isPlaying, hasPlayed, true, translatedStats, timestamp, eventId);
    }

    return new PlayerDataDTO(player, null, position, isPlaying, hasPlayed, false, translatedStats, timestamp, eventId);
  }
}
