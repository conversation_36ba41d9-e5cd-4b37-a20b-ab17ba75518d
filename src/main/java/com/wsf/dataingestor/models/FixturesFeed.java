package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.wsf.domain.common.Tournament;

@Data
@Builder
@RequiredArgsConstructor
public class FixturesFeed {
  private final String feedId;
  private final Tournament tournament;
  @NotNull
  private final List<FixtureDTO> fixtures;
}
