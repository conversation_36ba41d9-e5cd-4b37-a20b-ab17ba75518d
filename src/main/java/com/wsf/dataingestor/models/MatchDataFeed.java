package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.CANCELLED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.FIXTURE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.POSTPONED;
import static java.util.Optional.ofNullable;

@Data
public class MatchDataFeed {

  public static final String MANUAL_SETTLEMENT_FEED_ID = "manual_settlement";

  @NotNull
  private final Instant receivedTs;

  private final Instant latestUpdateTs;

  @NotNull
  private final String feedId;

  @NotNull
  private final Instant date;

  @NotNull
  private final Fixture fixture;

  @NotNull
  private final List<TeamDataDTO> teamsData;

  @NotNull
  private final List<PlayerDataDTO> playersData;

  @NotNull
  private final List<MatchEventDTO> matchEvents;

  // this is the list of events happened since we received the last feed, it's NOT the full list of events for the player over the match
  // mostly updated by the ma18dp feed
  private final List<EntityEventDTO> feedPlayerMatchEvents;

  // a list of all player events happened so far in the match (eg. goals from Opta's ma2).
  // May be null if the feed doesn't include all the events but only the ones happened since the last feed received (eg. Opta's ma18dp).
  private List<EntityEventDTO> aggregatedPlayerMatchEvents;

  private Set<SoccerMatchEvent> supportedEventTypes;

  private final boolean isSnapshot;

  private final boolean isSingleEventFeed;

  private final boolean isFinalData;

  private final FeedFixtureStatus fixtureStatus;

  private final Integer seqId;

  @NotNull
  private final FeedProvider provider;

  private final Integer matchTimeMin;

  private final MatchPeriod matchPeriod;

  private final boolean extraTimeHappened;

  public enum FeedFixtureStatus {
    FIXTURE,
    LIVE,
    BET_END,
    PLAYED,
    SUSPENDED,
    CANCELLED,
    POSTPONED;

    public boolean isFinished() {
      return this == PLAYED || this == CANCELLED || this == POSTPONED;
    }

    public boolean isCancelledOrPostponed() {
      return this == CANCELLED || this == POSTPONED;
    }
  }

  @Builder
  public MatchDataFeed(Instant receivedTs, Instant latestUpdateTs, String feedId, Instant date, Fixture fixture,
                       List<TeamDataDTO> teamsData, List<PlayerDataDTO> playersData, List<MatchEventDTO> matchEvents,
                       List<EntityEventDTO> feedPlayerMatchEvents, List<EntityEventDTO> aggregatedPlayerMatchEvents,
                       Set<SoccerMatchEvent> supportedEventTypes, boolean isSnapshot, boolean isSingleEventFeed,
                       boolean isFinalData, FeedFixtureStatus fixtureStatus, Integer seqId, FeedProvider provider,
                       Integer matchTimeMin, MatchPeriod matchPeriod, boolean extraTimeHappened) {
    this.receivedTs = receivedTs;
    this.latestUpdateTs = latestUpdateTs;
    this.feedId = feedId;
    this.date = date;
    this.fixture = fixture;
    this.playersData = ofNullable(playersData).orElseGet(Collections::emptyList);
    this.matchEvents = ofNullable(matchEvents).orElseGet(Collections::emptyList);
    this.teamsData = ofNullable(teamsData).orElseGet(Collections::emptyList);
    this.feedPlayerMatchEvents = ofNullable(feedPlayerMatchEvents).orElseGet(ArrayList::new);
    this.aggregatedPlayerMatchEvents = aggregatedPlayerMatchEvents;
    this.supportedEventTypes = ofNullable(supportedEventTypes).orElseGet(Collections::emptySet);
    this.isSnapshot = isSnapshot;
    this.isSingleEventFeed = isSingleEventFeed;
    this.isFinalData = isFinalData;
    this.fixtureStatus = fixtureStatus;
    this.seqId = seqId;
    this.provider = provider;
    this.matchTimeMin = matchTimeMin;
    this.matchPeriod = matchPeriod;
    this.extraTimeHappened = extraTimeHappened;
  }

  public boolean isCancelled() {
    return CANCELLED == getFixtureStatus();
  }

  public boolean isPostponed() {
    return POSTPONED == getFixtureStatus();
  }

  public boolean isPlayed() {
    return PLAYED == getFixtureStatus();
  }

  public boolean isInProgress() {
    return LIVE == getFixtureStatus();
  }

  public boolean isPreMatch() {
    return FIXTURE == getFixtureStatus();
  }

  public boolean isManualSettlement() {
    return this.feedId.equals(MANUAL_SETTLEMENT_FEED_ID);
  }

  public boolean doesContainStats() {
    return teamsData
      .stream()
      .anyMatch(data -> !data.getStats()
        .isEmpty()) || playersData
      .stream()
      .anyMatch(data -> !data.getStats()
        .isEmpty());
  }

  @Data
  @Builder
  public static class MatchEventDTO {
    private final String eventId;
    @NotNull
    private final SoccerMatchEvent event;
    private final String teamId;
    private final Instant timestamp;

    public MatchEventDTO(String eventId, SoccerMatchEvent matchEvent, Instant timestamp) {
      this(eventId, matchEvent, null, timestamp);
    }

    public MatchEventDTO(String eventId, SoccerMatchEvent matchEvent, String teamId, Instant timestamp) {
      this.eventId = eventId;
      this.event = matchEvent;
      this.teamId = teamId;
      this.timestamp = timestamp;
    }
  }

  public enum FeedProvider {
    OPTA,
    RUNNINGBALL,
    SPORTMONKS,
    WSF
  }

  public void mergeEvents(MatchEventsFeed ma3Feed) {
    if (aggregatedPlayerMatchEvents == null) {
      aggregatedPlayerMatchEvents = new ArrayList<>(ma3Feed.getEvents());
      supportedEventTypes = new HashSet<>(ma3Feed.getSupportedEventTypes());
    } else {
      aggregatedPlayerMatchEvents = ListUtils.union(aggregatedPlayerMatchEvents, ma3Feed.getEvents());
      supportedEventTypes = SetUtils.union(supportedEventTypes, ma3Feed.getSupportedEventTypes());
    }
  }

  public boolean isHomeTeam(String teamId) {
    return fixture.getHomeTeam().getIdAsString().equals(teamId) || getProviderHomeTeamId().equals(teamId);
  }

  private String getProviderHomeTeamId() {
    return switch (provider) {
      case OPTA, RUNNINGBALL -> fixture.getHomeTeam().getOptaId();
      case SPORTMONKS -> fixture.getHomeTeam().getSportmonksId();
      case WSF -> fixture.getHomeTeam().getIdAsString();
    };
  }
}
