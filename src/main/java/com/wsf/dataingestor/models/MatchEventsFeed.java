package com.wsf.dataingestor.models;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

@Data
public class MatchEventsFeed {

  @NonNull
  private final Instant receivedTs;

  private final Instant latestUpdateTs;

  @NonNull
  private final String feedId;

  @NonNull
  private final Fixture fixture;

  private final List<EntityEventDTO> events;

  private final Set<SoccerMatchEvent> supportedEventTypes;

  @Builder
  public MatchEventsFeed(Instant receivedTs, Instant latestUpdateTs, String feedId, Fixture fixture,
                         List<EntityEventDTO> events, Set<SoccerMatchEvent> supportedEventTypes) {
    this.receivedTs = receivedTs;
    this.latestUpdateTs = latestUpdateTs;
    this.feedId = feedId;
    this.fixture = fixture;
    this.events = events;
    this.supportedEventTypes = supportedEventTypes;
  }

  @Data
  @RequiredArgsConstructor
  public static class MatchEventDTO {
    private final int timeSec;
  }
}
