package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Map;
import com.wsf.domain.common.Team;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.UUID.randomUUID;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class TeamDataDTO implements EntityDataDTO {
  @NotNull
  private final Team team;
  private final Instant timestamp;
  @Builder.Default
  private final String eventId = randomUUID().toString();

  @NotNull
  @Builder.Default
  private Map<String, Number> stats = newHashMap();

  @Override
  public String getEntityId() {
    return getTeamId();
  }

  @Override
  public ContestantType getContestantType() {
    return ContestantType.SOCCER_TEAM;
  }

  @Override
  public boolean isUnknown() {
    return false;
  }

  public String getTeamId() {
    return team.getId().toString();
  }
}
