package com.wsf.dataingestor.models;

import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Value;

import java.time.Instant;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Tournament;

@Value
@Builder
public class CurrentTournamentFeed {
  String externalSeasonId;
  Competition competition;
  Tournament existingTournament;
  String year; // format yyyy-yyyy or yyyy
  Instant startDate;
  Provider provider;
  String feedId;

  @Getter
  @RequiredArgsConstructor
  public enum Provider {
    OPTA("optaCalendarId", "optaIds"),
    SPORTMONKS("sportmonksSeasonId", "sportmonksIds");

    private final String tournamentFieldName;
    private final String externalIdFieldName;
  }
}
