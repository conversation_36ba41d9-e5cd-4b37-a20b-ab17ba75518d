package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import com.wsf.dataingestor.modules.event.ContestantEvent;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

@Data
@Builder
@RequiredArgsConstructor
public class EntityEventDTO implements ContestantEvent {
  private final String eventId;
  private final String relatedEventId;
  @NotNull
  private final String entityId;
  private final String externalEntityId;
  private final String teamId;
  private final String externalTeamId;
  private final boolean isUnknownEntity;
  private final SoccerMatchEvent eventType;
  private final MatchPeriod period;
  private final int timeMin;
  private final boolean ignore;
  private final boolean isUnknown;
  private final boolean isOnBench;
  private final Instant timestamp;

  @Override
  public String getContestantId() {
    return entityId;
  }

  public boolean wasMadeByPlayer(String playerId) {
    return playerId.equals(entityId) || playerId.equals(externalEntityId);
  }
}
