package com.wsf.dataingestor.models;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.Value;

import java.time.LocalDate;
import com.wsf.domain.common.Player;

@Data
@Builder
public class SquadPlayerDTO {
  @NotNull
  private final String feedId;

  private String playerId;
  private String firstName;
  private String lastName;
  private String matchName;
  private final LocalDate birthDate;
  private final TeamDTO team;
  private final Player.Position position;
  private final Player.DetailedPosition detailedPosition;
  private final Provider provider;

  public enum Provider {
    OPTA,
    SPORTMONKS
  }

  @Value
  @RequiredArgsConstructor(staticName = "of")
  public static class TeamDTO {
    String externalTeamId;
    String externalTeamName;
    String externalTeamAbbreviation;
  }
}
