package com.wsf.dataingestor.features.cacheinspect;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import static java.util.Optional.ofNullable;

@Slf4j
@RestController
@RequestMapping("/v1/cache")
@RequiredArgsConstructor
public class CacheInspectController {
  private final CacheManager cacheManager;
  private final ObjectMapper objectMapper;

  @RequestMapping(value = "/{cacheName}/inspect", method = RequestMethod.GET)
  public ResponseEntity<String> forward(@PathVariable String cacheName, @RequestParam(required = true) String key) {
    return ofNullable(cacheManager.getCache(cacheName))
      .map(cache -> cache.get(key))
      .map(Cache.ValueWrapper::get)
      .map(this::serialize)
      .map(ResponseEntity::ok)
      .orElse(ResponseEntity.notFound().build());
  }

  private String serialize(Object value) {
    try {
      return objectMapper.writeValueAsString(value);
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
