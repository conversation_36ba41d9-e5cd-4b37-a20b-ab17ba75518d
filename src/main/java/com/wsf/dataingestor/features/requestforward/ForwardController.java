package com.wsf.dataingestor.features.requestforward;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.opta.clients.MA1Client;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.clients.MA3Client;
import com.wsf.dataingestor.opta.clients.MA46Client;
import com.wsf.dataingestor.opta.clients.OT2CurrentTournamentClient;
import com.wsf.dataingestor.opta.clients.TM3Client;
import com.wsf.dataingestor.opta.services.OptaSettlementDataRetriever;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.sportmonks.clients.FixtureStatsClient;
import com.wsf.dataingestor.sportmonks.services.SportmonksSettlementDataRetriever;
import com.wsf.domain.common.ExternalIds;

@Slf4j
@RestController
@RequestMapping("/v1/forward")
@RequiredArgsConstructor
public class ForwardController {

  private final MA1Client ma1Client;
  private final MA2Client ma2Client;
  private final MA3Client ma3Client;
  private final MA46Client ma46Client;
  private final OT2CurrentTournamentClient ot2Client;
  private final TM3Client tm3Client;
  private final OptaSettlementDataRetriever optaSettlementDataRetriever;

  private final SportmonksSettlementDataRetriever sportmonksSettlementDataRetriever;
  private final FixtureStatsClient fixtureStatsClient;

  private final FixtureService fixtureService;
  private final TournamentService tournamentService;
  private final CompetitionService competitionService;

  @RequestMapping(value = "/{feedType}", method = RequestMethod.GET)
  public ResponseEntity<Object> forward(@PathVariable FeedType feedType,
                                        @RequestParam(required = false) String fixtureId,
                                        @RequestParam(required = false) String competitionId,
                                        @RequestParam(required = false) String tournamentId) {
    Object feed = switch (feedType) {
      case OPTA_MA1 -> getMa1(tournamentId);
      case OPTA_MA2 -> getMa2(fixtureId);
      case OPTA_MA3 -> getMa3(fixtureId);
      case OPTA_MA46 -> getMa46(fixtureId);
      case OPTA_OT2 -> getOt2(competitionId);
      case OPTA_TM3 -> getTm3(tournamentId);
      case OPTA_SETTLEMENT -> getOptaSettlement(fixtureId);
      case SM_SETTLEMENT -> getSmSettlement(fixtureId);
      case SM_FIXTURE_STATS -> getFixtureStats(fixtureId);
    };

    return ResponseEntity.ok(feed);
  }

  private FixturesFeed getMa1(String tournamentId) {
    var tournament = tournamentService.findByTournamentId(tournamentId);
    return ma1Client.retrieveParsedFeed(getOptaId(tournament.getExternalIds()));
  }

  private MatchDataFeed getMa2(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return ma2Client.retrieveParsedFeed(fixture.getOptaFixtureId());
  }

  private MatchEventsFeed getMa3(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return ma3Client.retrieveParsedFeed(fixture.getOptaFixtureId());
  }

  private ContestantUnavailabilitiesDataFeed getMa46(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId());
  }

  private CurrentTournamentFeed getOt2(String competitionId) {
    var competition = competitionService.findByCompetitionId(competitionId);
    return ot2Client.retrieveParsedFeed(getOptaId(competition.getExternalIds()));
  }

  private SquadsFeed getTm3(String tournamentId) {
    var tournament = tournamentService.findByTournamentId(tournamentId);
    return tm3Client.retrieveParsedFeed(getOptaId(tournament.getExternalIds()));
  }

  private MatchDataFeed getOptaSettlement(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return optaSettlementDataRetriever.retrieveSettlementData(fixture);
  }

  private MatchDataFeed getSmSettlement(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return sportmonksSettlementDataRetriever.retrieveSettlementData(fixture);
  }

  private MatchDataFeed getFixtureStats(String fixtureId) {
    var fixture = fixtureService.getFixture(fixtureId);
    return fixtureStatsClient.retrieveParsedFeed(fixture.getSportmonksFixtureId());
  }

  private static String getOptaId(ExternalIds externalIds) {
    return externalIds.getOptaIds()
      .stream()
      .findFirst().get();
  }

}
