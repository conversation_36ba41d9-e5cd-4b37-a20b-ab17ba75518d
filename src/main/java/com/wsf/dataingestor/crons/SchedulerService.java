package com.wsf.dataingestor.crons;

import io.micrometer.core.instrument.Counter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.quartz.JobDetail;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.metrics.MetricsManager;

@Slf4j
@Service
@RequiredArgsConstructor
public class SchedulerService {

  private final Scheduler scheduler;
  private final MetricsManager metricsRegistry;

  public boolean scheduleJob(<PERSON><PERSON><PERSON> jobKey, JobDetail jobDetail, Trigger trigger) {
    try {
      if (!scheduler.checkExists(jobKey)) {
        log.info("launching a scheduled job with jobKey={} - {}", jobKey.getName(), jobKey.getGroup());
        scheduler.scheduleJob(jobDetail, trigger);
        return true;
      } else {
        log.info("scheduled jobKey={} already exists", jobKey.getName());
        return false;
      }
    } catch (SchedulerException e) {
      Counter counter = metricsRegistry.CRON_SCHEDULE_ERROR;
      log.error("error={} scheduling job for jobKey {}", counter.getId().getName(), jobKey.getName(), e);
      counter.increment();
      return false;
    }
  }

  public JobDetail getJobDetail(JobKey jobKey) {
    try {
      return scheduler.getJobDetail(jobKey);
    } catch (SchedulerException e) {
      throw new IllegalArgumentException(String.format("could not find jobDetail for jobKey %s", jobKey), e);
    }
  }

  public void rescheduleTrigger(TriggerKey triggerKey, Trigger trigger) {
    try {
      scheduler.rescheduleJob(triggerKey, trigger);
    } catch (SchedulerException e) {
      Counter counter = metricsRegistry.CRON_SCHEDULE_ERROR;
      log.error("error={} changing trigger time for job with jobKey {}", counter.getId().getName(),
        triggerKey.getName(), e);
      counter.increment();
    }
  }

  public void pauseJob(JobKey jobKey) throws SchedulerException {
    scheduler.pauseJob(jobKey);
  }

  public void deleteJob(JobKey jobKey) throws SchedulerException {
    scheduler.deleteJob(jobKey);
  }

}
