package com.wsf.dataingestor.crons;

import io.micrometer.core.instrument.Counter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.email.OptaSupportEmailClient;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.ContestantUnavailabilityUseCase;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;

@Slf4j
@Component
@NoArgsConstructor
@AllArgsConstructor
public class PlayerAvailabilityUpdater implements Job {

  public static final String PLAYER_AVAILABILITY_JOB_POLLING_GROUP = "playerAvailabilityJobGroup";
  public static final String PLAYER_AVAILABILITY_JOB_POLLING_JOB_KEY = "playerAvailabilityPollingJob";
  public static final String DAYS_WINDOW_PARAM = "daysWindowParam";

  @Autowired
  private FixtureService fixtureService;
  @Autowired
  private MetricsManager metricsRegistry;
  @Autowired
  private ContestantUnavailabilityUseCase contestantUnavailabilityUseCase;
  @Autowired
  private FeedsConfigService feedsConfigService;
  @Autowired
  private OptaSupportEmailClient optaSupportEmailClient;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    try {
      int daysWindow = jobExecutionContext.getTrigger().getJobDataMap().getInt(DAYS_WINDOW_PARAM);

      Set<String> optaFixtureIdsWithMissingFeeds = fixtureService.findByDayWindow(Instant.now(), daysWindow)
        .stream()
        .filter(
          fixture -> feedsConfigService.isProvisionalLineupFeedAvailable(fixture.getTournament().getCompetitionId()))
        .map(contestantUnavailabilityUseCase::process)
        .filter(Optional::isPresent)
        .map(Optional::get)
        .collect(Collectors.toSet());

      notifyOptaSupportForMissingData(optaFixtureIdsWithMissingFeeds);
    } catch (Exception e) {
      Counter counter = metricsRegistry.PLAYER_AVAILABILITY_FEED_ERROR;
      log.error("error={} while parsing injuries and suspensions", counter.getId().getName(), e);
      counter.increment();
    }
  }

  private void notifyOptaSupportForMissingData(Set<String> optaFixtureIdsWithMissingFeeds) {
    try {
      if (!optaFixtureIdsWithMissingFeeds.isEmpty()) {
        optaSupportEmailClient.sendMissingMA46FeedMail(optaFixtureIdsWithMissingFeeds);
        log.info(
          "Successfully notified Opta's support for the following missing MA46 feeds for missing provisional lineups: {}",
          optaFixtureIdsWithMissingFeeds);
      }
    } catch (Exception e) {
      Counter counter = metricsRegistry.PLAYER_AVAILABILITY_FEED_ERROR;
      counter.increment();
      log.error(
        "error={} while processing the MA46 feed - automatic email to Opta's support failed. List of Opta's fixtures where MA46 feed was not found: {}",
        counter.getId().getName(), optaFixtureIdsWithMissingFeeds, e);
    }
  }
}