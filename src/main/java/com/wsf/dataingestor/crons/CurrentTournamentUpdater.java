package com.wsf.dataingestor.crons;

import io.micrometer.core.instrument.Counter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.exceptions.CurrentSeasonNotFoundException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.tournaments.CurrentTournamentRetriever;
import com.wsf.dataingestor.services.tournaments.TournamentsIngestionService;
import com.wsf.domain.common.Competition;

import static java.lang.String.format;
import static java.util.Objects.isNull;

@Slf4j
@Component
@NoArgsConstructor
@AllArgsConstructor
public class CurrentTournamentUpdater implements Job {

  public static final String CURRENT_TOURNAMENT_JOB_POLLING_GROUP = "currentTournamentJobGroup";
  public static final String CURRENT_TOURNAMENT_JOB_POLLING_JOB_KEY = "currentTournamentPollingJob";

  @Autowired
  private CompetitionService competitionService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Autowired
  private TournamentsIngestionService tournamentsIngestionService;

  @Autowired
  private MetricsManager metricsRegistry;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    List<Competition> activeCompetitions = competitionService.findActiveCompetitions();
    activeCompetitions.forEach(this::ingestCurrentTournamentForCompetition);
  }

  public void ingestCurrentTournamentForCompetition(Competition competition) {
    String competitionId = competition.getId().toString();
    log.info("launching update current tournament process for competition={}", competition);

    try {
      CurrentTournamentRetriever tournamentRetriever = feedsConfigService.getCurrentTournamentRetriever(competitionId);
      if (isNull(tournamentRetriever)) {
        return;
      }

      List<CurrentTournamentFeed> currentTournamentFeeds = findCurrentTournaments(competition, tournamentRetriever);

      if (currentTournamentFeeds.isEmpty()) {
        throw new CurrentSeasonNotFoundException(
          format("no current tournament found for competitionId=%s", competition));
      }

      tournamentsIngestionService.processTournamentFeed(currentTournamentFeeds);
    } catch (Exception e) {
      Counter counter = metricsRegistry.TOURNAMENTS_FEED_PARSING_ERROR;
      log.error("error={} while trying to ingest the current tournament for competition={}", counter.getId().getName(),
        competition, e);
      counter.increment();
    }
  }

  private List<CurrentTournamentFeed> findCurrentTournaments(Competition competition,
                                                             CurrentTournamentRetriever tournamentRetriever) {
    Set<String> externalProviderIds = feedsConfigService.getCompetitionExternalIds(competition);

    return externalProviderIds
      .stream()
      .map(providerId -> {
        log.info("looking for current tournament with externalProviderId={}", providerId);
        return tournamentRetriever.retrieveTournamentFeed(providerId);
      })
      .toList();
  }
}
