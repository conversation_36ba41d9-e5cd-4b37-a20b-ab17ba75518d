package com.wsf.dataingestor.crons;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.exceptions.UnsettlableFixtureException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ratings.FinalFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.MatchSettlementProcessor;
import com.wsf.dataingestor.services.ratings.SettlementDataRetriever;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater.toFixtureStatus;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static java.lang.String.format;
import static org.quartz.SimpleScheduleBuilder.simpleSchedule;

@Slf4j
@Component
@NoArgsConstructor
@AllArgsConstructor
public class SettlementUpdater implements Job {

  public static final String SETTLEMENT_JOB_GROUP = "settlementJobGroup";
  public static final String SETTLEMENT_JOB_TRIGGER_GROUP = "settlementTriggerGroup";
  private static final int FIXTURE_MINIMUM_SETTLEMENT_TIME_MIN = 45;

  @Value("${crons.settlement-updater.interval-secs}")
  private int settlementUpdaterIntervalSec;

  @Value("${crons.settlement-updater.max-repetitions}")
  private int settlementUpdaterMaxReps;

  @Autowired
  private SchedulerService schedulerService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Autowired
  private MatchSettlementProcessor matchSettlementProcessor;

  @Autowired
  private FixtureService fixtureService;

  @Autowired
  private OngoingMatchDataCacheService ongoingMatchDataCache;

  @Autowired
  private FinalFixtureSummaryNotifier finalFixtureSummaryNotifier;

  @Autowired
  private MetricsManager metricsRegistry;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    String fixtureId = jobExecutionContext.getMergedJobDataMap().getString("fixtureId");

    try {
      Fixture fixture = fixtureService.getFixture(fixtureId);

      if (SETTLED.equals(fixture.getProcessStatus())) {
        log.warn("fixtureId={} is already settled, stopping settlement job", fixtureId);
        cleanUpAndStopJob(jobExecutionContext, fixtureId);
        return;
      }

      boolean settlementDone = tryMatchSettlement(fixture, false).isSettlementDone();
      if (settlementDone) {
        cleanUpAndStopJob(jobExecutionContext, fixtureId);
      } else if (!jobExecutionContext.getTrigger().mayFireAgain()) {
        log.error(
          "settlement error: fixtureId={} has not been settled yet and we reached the max nr of retries, triggering an alert",
          fixtureId);
        alertAndCleanUpJob(jobExecutionContext, fixtureId);
      }
    } catch (UnsettlableFixtureException e) {
      log.error("settlement error: error while executing Settlement job for match {}", fixtureId, e);
      alertAndCleanUpJob(jobExecutionContext, fixtureId);
    } catch (Exception e) {
      log.error("settlement error: error while executing Settlement job for match {}", fixtureId, e);
      if (!jobExecutionContext.getTrigger().mayFireAgain()) {
        alertAndCleanUpJob(jobExecutionContext, fixtureId);
      } else {
        metricsRegistry.SETTLEMENT_UPDATE_ERROR.increment();
      }
    }
  }

  public void scheduleSettlementJob(Fixture fixture) {
    JobDetail jobDetail = fixtureSettlementUpdaterJobDetail(fixture);
    Trigger trigger = fixtureSettlementUpdaterTrigger(jobDetail, fixture);
    JobKey settlementJobKey = new JobKey(fixture.getId().toString(), SETTLEMENT_JOB_GROUP);
    schedulerService.scheduleJob(settlementJobKey, jobDetail, trigger);
  }

  public SettlementResultAndMatchStatus tryMatchSettlement(Fixture fixture, boolean forceSettlement) {
    if (!forceSettlement &&
      fixture.getDate().isAfter(Instant.now().minus(FIXTURE_MINIMUM_SETTLEMENT_TIME_MIN, ChronoUnit.MINUTES))) {
      throw new UnsettlableFixtureException(fixture.getIdAsString(),
        format("Settlement came before minute %s", FIXTURE_MINIMUM_SETTLEMENT_TIME_MIN));
    }

    String fixtureId = fixture.getId().toString();
    String competitionId = fixture.getTournament().getCompetitionId();
    log.info("trying settlement for fixtureId={} forceSettlement={}", fixtureId, forceSettlement);

    SettlementDataRetriever settlementDataRetriever = feedsConfigService.getSettlementDataRetriever(competitionId);
    MatchDataFeed matchDataFeed = settlementDataRetriever.retrieveSettlementData(fixture);

    boolean isSettlementDone = matchSettlementProcessor.processFeed(matchDataFeed, forceSettlement);

    if (isSettlementDone) {
      finalFixtureSummaryNotifier.sendSettlementSummary(matchDataFeed);
    }

    return SettlementResultAndMatchStatus.of(isSettlementDone,
      toFixtureStatus(matchDataFeed.getFixtureStatus(), matchDataFeed.getDate()));
  }

  private void cleanUpAndStopJob(JobExecutionContext jobExecutionContext, String fixtureId) {
    ongoingMatchDataCache.remove(fixtureId);
    stopJob(jobExecutionContext);
  }

  private void stopJob(JobExecutionContext jobExecutionContext) {
    JobKey key = jobExecutionContext.getJobDetail().getKey();
    try {
      schedulerService.deleteJob(key);
    } catch (SchedulerException e) {
      log.error("Error deleting job {}", key.toString(), e);
    }
  }

  private JobDetail fixtureSettlementUpdaterJobDetail(Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    return JobBuilder
      .newJob()
      .ofType(SettlementUpdater.class)
      .storeDurably()
      .usingJobData("fixtureId", fixtureId)
      .withIdentity(fixtureId, SETTLEMENT_JOB_GROUP)
      .withDescription(format("Settlement of fixture %s", fixtureId))
      .build();
  }

  private Trigger fixtureSettlementUpdaterTrigger(JobDetail jobDetail, Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    String competitionId = fixture.getTournament().getCompetitionId();
    int settlementDelaySeconds = feedsConfigService.getSettlementDelaySeconds(competitionId);
    Date startDate = new Date(Instant.now().plus(settlementDelaySeconds, ChronoUnit.SECONDS).toEpochMilli());

    log.info("settlement for fixtureId={} will trigger at {}", fixtureId, startDate);

    return TriggerBuilder
      .newTrigger()
      .forJob(jobDetail)
      .withIdentity(fixtureId, SETTLEMENT_JOB_TRIGGER_GROUP)
      .withDescription(format("Trigger for Settlement Updater: fixtureId - %s", fixtureId))
      .withSchedule(
        simpleSchedule().withRepeatCount(settlementUpdaterMaxReps).withIntervalInSeconds(settlementUpdaterIntervalSec))
      .startAt(startDate)
      .build();
  }

  private void alertAndCleanUpJob(JobExecutionContext jobExecutionContext, String fixtureId) {
    metricsRegistry.NO_SETTLEMENT_ERROR.increment();
    cleanUpAndStopJob(jobExecutionContext, fixtureId);
  }

  @Data
  @RequiredArgsConstructor(staticName = "of")
  public static class SettlementResultAndMatchStatus {
    private final boolean isSettlementDone;
    private final Fixture.FixtureStatus fixtureStatus;
  }
}
