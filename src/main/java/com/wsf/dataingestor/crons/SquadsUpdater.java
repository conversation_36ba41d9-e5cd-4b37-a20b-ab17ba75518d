package com.wsf.dataingestor.crons;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.exceptions.FeedException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.services.squads.SquadsIngestionService;
import com.wsf.dataingestor.services.squads.SquadsRetriever;
import com.wsf.domain.common.Tournament;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

@Slf4j
@Component
public class SquadsUpdater implements Job {

  public static final String SQUADS_JOB_POLLING_GROUP = "squadsJobGroup";
  public static final String SQUADS_JOB_POLLING_JOB_KEY = "squadsPollingJob";

  @Autowired
  private CompetitionService competitionService;

  @Autowired
  private TournamentService tournamentService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Autowired
  private SquadsIngestionService squadsIngestionService;

  @Autowired
  private MetricsManager metrics;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    competitionService.findActiveCompetitions()
      .stream()
      .map(c -> c.getId().toString())
      .forEach(this::ingestPlayersForCompetition);
  }

  public void ingestPlayersForCompetition(String competitionId) {
    ingestPlayersForCompetition(competitionId, false);
  }

  public void ingestPlayersForCompetition(String competitionId, boolean forceDeletePlayers) {
    Tournament tournament = tournamentService.findByIsActiveTrue(competitionId);

    if (isNull(tournament)) {
      log.warn("no tournament active for competitionId={}", competitionId);
      return;
    }

    if (tournament.getEndDate().isBefore(Instant.now())) {
      log.warn("tournament has already ended, ingestion won't be executed tournament={}", tournament);
      return;
    }

    String tournamentId = tournament.getId().toString();
    log.info("launching update squads process for tournamentId={}", tournamentId);

    try {
      SquadsRetriever squadsRetriever = feedsConfigService.getSquadsRetriever(competitionId);
      if (nonNull(squadsRetriever)) {
        squadsIngestionService.processSquadsFeed(squadsRetriever, tournament, forceDeletePlayers);
      }
    } catch (FeedException e) {
      log.error("Error while processing squads feed for tournamentId={} for competition={}", tournamentId,
        tournament.getCompetition().getName(), e);
      metrics.SQUADS_FEED_PARSING_ERROR.increment();
    } catch (Exception e) {
      log.error("Error while retrieving and processing the squad feed for tournamentId={}", tournament.getId(), e);
    }
  }
}
