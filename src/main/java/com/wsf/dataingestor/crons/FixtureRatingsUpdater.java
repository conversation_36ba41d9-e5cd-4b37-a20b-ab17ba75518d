package com.wsf.dataingestor.crons;

import io.micrometer.core.instrument.Counter;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Date;
import java.util.NoSuchElementException;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchSettlementLogic;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;

import static com.wsf.dataingestor.crons.FixtureStartUpdater.FIXTURE_START_JOB_GROUP;
import static com.wsf.dataingestor.crons.FixtureStartUpdater.FIXTURE_START_JOB_TRIGGER_GROUP;
import static com.wsf.dataingestor.crons.FixtureStartUpdater.buildFixtureStartTrigger;
import static com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater.toFixtureStatus;
import static com.wsf.domain.common.Fixture.FixtureStatus.CANCELLED;
import static com.wsf.domain.common.Fixture.FixtureStatus.SUSPENDED;
import static java.time.Duration.between;
import static java.time.Duration.ofHours;
import static java.time.Instant.now;

@Slf4j
@Component
@NoArgsConstructor
@AllArgsConstructor
public class FixtureRatingsUpdater implements Job {

  public static final String FIXTURE_RATINGS_JOB_POLLING_GROUP = "fixtureRatingsJobGroup";
  public static final String FIXTURE_RATINGS_TRIGGER_GROUP = "fixtureRatingsTriggerGroup";

  @Autowired
  private SchedulerService schedulerService;

  @Autowired
  private FixtureService fixtureService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Autowired
  private MatchSettlementLogic matchSettlementLogic;

  @Autowired
  private FixtureInfoUpdater fixtureInfoUpdater;

  @Autowired
  private ThreadPoolService threadPoolService;

  @Autowired
  private MetricsManager metricsRegistry;

  @Autowired
  private KafkaService kafkaService;


  @Override
  public void execute(JobExecutionContext jobExecutionContext) {
    JobKey jobKey = jobExecutionContext.getJobDetail().getKey();
    String fixtureId = jobExecutionContext.getMergedJobDataMap().getString("fixtureId");

    log.info("Job: retrieving ratings feed for fixtureId={}", fixtureId);

    threadPoolService.processFixtureUpdateInThreadPool(fixtureId, () -> fetchStatsFeedAndProcessIt(fixtureId, jobKey));
  }

  private void fetchStatsFeedAndProcessIt(String fixtureId, JobKey jobKey) {
    try {
      log.info("Start processing fixtureId={}", fixtureId);
      Fixture fixture = fixtureService.getFixture(fixtureId);
      String competitionId = fixture.getTournament().getCompetitionId();

      FixtureDataProcessor fixtureDataProcessor = feedsConfigService.getFixtureDataProcessor(competitionId);

      MatchDataFeed matchDataFeed = fixtureDataProcessor.processFixtureStatsFeed(fixture);

      String feedId = matchDataFeed.getFeedId();
      FixtureStatus fixtureStatus = toFixtureStatus(matchDataFeed.getFixtureStatus(), fixture.getDate());
      log.info("feed with feedId={} processed for fixtureId={} with status={}", feedId, fixtureId, fixtureStatus);

      if (fixtureStatus == FixtureStatus.PLAYED) {
        stopScheduledJobs(fixtureId, jobKey);
        var updatedFixture = fixtureService.storeFixtureFinished(fixture);
        matchSettlementLogic.processPlayedFixtureSettlement(updatedFixture);
        kafkaService.sendFixtureChange(fixture, feedId);
      } else {
        Instant newFixtureDate = matchDataFeed.getDate();
        fixtureInfoUpdater.processFixtureInfoDuringLive(fixture, fixtureStatus, newFixtureDate, feedId);
        if (fixtureStatus == SUSPENDED || fixtureStatus == CANCELLED) {
          log.info("stopping job for fixtureId={} since it's {}", fixtureId, fixtureStatus);
          stopScheduledJobs(fixtureId, jobKey);
          fixtureService.removeFixtureProcessStatus(fixture);
        } else {
          handlePossibleDatesMismatch(newFixtureDate, fixture, jobKey);
        }
      }
      fixtureDataProcessor.manageWebSocket(fixture, fixtureStatus);
    } catch (NoSuchElementException e) {
      log.error("Error while executing the MatchRatings job for fixture {}", fixtureId, e);
      stopScheduledJobs(fixtureId, jobKey);
    } catch (Exception e) {
      Counter metric = metricsRegistry.RATINGS_FEED_PROCESSING_ERROR;
      log.error("Error while executing the MatchRatings job for fixture {} error={}", fixtureId,
        metric.getId().getName(), e);
      metric.increment();
    }
  }

  private void handlePossibleDatesMismatch(Instant newFixtureDate, Fixture fixture, JobKey jobKey) {
    Instant currentFixtureDate = fixture.getDate();
    if (!currentFixtureDate.equals(newFixtureDate) && newFixtureDate.isAfter(now())) {
      String fixtureId = fixture.getIdAsString();
      long secondsDifference = between(now(), newFixtureDate).getSeconds();
      if (secondsDifference > 0 && secondsDifference < ofHours(3).getSeconds()) {
        log.error(
          "mismatch on fixtureId={} date: dbFixture {}, feedFixture {} of less than 3h, rescheduling preMatch job",
          fixtureId, currentFixtureDate, newFixtureDate);
        reschedulePreMatchJob(fixtureId, newFixtureDate);
      } else {
        Counter metric = metricsRegistry.CHANGED_FIXTURE_SCHEDULE;
        log.error("mismatch on fixtureId={} date: dbFixture {}, feedFixture {} of more than 3h, stopping job error={}",
          fixtureId, fixture.getDate(), newFixtureDate, metric.getId().getName());
        metric.increment();
        stopScheduledJobs(fixtureId, jobKey);

        fixtureService.removeFixtureProcessStatus(fixture);
      }
    }
  }

  private void reschedulePreMatchJob(String fixtureId, Instant feedDate) {
    TriggerKey triggerKey = new TriggerKey(fixtureId, FIXTURE_START_JOB_TRIGGER_GROUP);
    JobKey preMatchJobKey = new JobKey(fixtureId, FIXTURE_START_JOB_GROUP);
    JobDetail jobDetail = schedulerService.getJobDetail(preMatchJobKey);
    Date triggerDate = new Date(feedDate.toEpochMilli());
    Trigger trigger = buildFixtureStartTrigger(jobDetail, fixtureId, triggerDate);
    schedulerService.rescheduleTrigger(triggerKey, trigger);
  }

  private void stopScheduledJobs(String fixtureId, JobKey jobKey) {
    try {
      log.info("Stopping jobKey={} for fixtureId={}", jobKey, fixtureId);
      schedulerService.deleteJob(jobKey);
      JobKey preMatchJobKey = new JobKey(fixtureId, FIXTURE_START_JOB_GROUP);
      log.info("Stopping prematch jobKey={} for fixtureId={}", preMatchJobKey, fixtureId);
      schedulerService.deleteJob(preMatchJobKey);
    } catch (SchedulerException ex) {
      log.error("Error deleting job {}", jobKey.toString(), ex);
    }
  }
}
