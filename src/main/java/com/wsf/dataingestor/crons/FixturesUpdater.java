package com.wsf.dataingestor.crons;

import io.micrometer.core.instrument.Counter;
import lombok.extern.slf4j.Slf4j;

import java.time.temporal.ChronoUnit;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.services.fixtures.FixturesIngestionService;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Tournament;

import static java.time.Instant.now;
import static java.util.Objects.isNull;

@Slf4j
@Component
public class FixturesUpdater implements Job {

  public static final String FIXTURES_JOB_POLLING_GROUP = "fixturesJobGroup";
  public static final String FIXTURES_JOB_POLLING_JOB_KEY = "fixturesPollingJob";

  @Autowired
  private TournamentService tournamentService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Autowired
  private FixturesIngestionService fixturesIngestionService;

  @Autowired
  private CompetitionService competitionService;

  @Autowired
  private MetricsManager metricsRegistry;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    competitionService.findActiveCompetitions()
      .forEach(this::ingestFixturesForCompetition);
  }

  public void ingestFixturesForCompetition(Competition competition) {
    String competitionId = competition.getIdAsString();
    Tournament tournament = tournamentService.findByIsActiveTrue(competitionId);

    if (isNull(tournament)) {
      log.warn("no tournament active for competitionId={}", competitionId);
      return;
    }

    String tournamentId = tournament.getId().toString();
    log.info("launching update fixtures process for tournamentId={}", tournamentId);

    try {
      FixturesRetriever fixturesRetriever = feedsConfigService.getFixturesRetriever(competitionId);
      FixturesFeed fixturesFeed = fixturesRetriever.retrieveFixturesFeed(tournament);
      fixturesIngestionService.processFixturesFeed(fixturesRetriever, fixturesFeed);
    } catch (Exception e) {
      handleError(tournament, e);
    }
  }

  private void handleError(Tournament tournament, Exception e) {
    Competition competition = tournament.getCompetition();
    if (isNull(tournament.getStartDate()) || isTournamentStartDateInMoreThanNrDays(tournament, 15)) {
      log.error(
        "error while ingesting fixtures for competitionId={} competitionName={}. The tournament will start on {} which is null or in more than 15 days, not sending an alert",
        competition.getIdAsString(), competition.getName(), tournament.getStartDate(), e);
      return;
    }

    Counter counter = metricsRegistry.MATCHES_FEED_PARSING_ERROR;
    log.error("error={} while ingesting fixtures for competitionId={} competitionName={} tournamentId={}",
      counter.getId().getName(), competition.getIdAsString(), competition.getName(), tournament.getIdAsString(), e);
    counter.increment();
  }

  private static boolean isTournamentStartDateInMoreThanNrDays(Tournament tournament, int nrDays) {
    return tournament.getStartDate().isAfter(now().plus(nrDays, ChronoUnit.DAYS));
  }
}
