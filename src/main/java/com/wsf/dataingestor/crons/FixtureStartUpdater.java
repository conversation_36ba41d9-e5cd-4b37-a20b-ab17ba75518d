package com.wsf.dataingestor.crons;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Random;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static java.lang.String.format;
import static java.time.Instant.now;

@Slf4j
@Service
@AllArgsConstructor
@NoArgsConstructor
public class FixtureStartUpdater implements Job {

  public static final String FIXTURE_START_JOB_GROUP = "fixtureStartJobGroup";
  public static final String FIXTURE_START_JOB_TRIGGER_GROUP = "fixtureStartTriggerGroup";

  private static final Random randomGenerator = new Random();

  @Autowired
  private MatchEventsProcessor matchEventsProcessor;

  @Autowired
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;

  @Autowired
  private FixtureService fixtureService;

  @Autowired
  private MetricsManager metricsRegistry;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
    String fixtureId = jobExecutionContext.getMergedJobDataMap().getString("fixtureId");

    try {
      sendPreFixtureTrigger(fixtureId);
    } catch (Exception e) {
      log.error("Error while executing Match Start updater job for fixtureId {}", fixtureId, e);
      metricsRegistry.MATCH_START_UPDATE_ERROR.increment();
    }
  }

  public void sendPreFixtureTrigger(String fixtureId) {
    Fixture fixture = fixtureService.getFixture(fixtureId);

    log.info("fixtureId={} for competitionId={} is about to start", fixtureId,
      fixture.getTournament().getCompetitionId());

    Instant now = now();
    MatchDataFeed matchDataFeed = MatchDataFeed
      .builder()
      .receivedTs(now)
      .feedId("feedStartManual")
      .fixture(fixture)
      .matchEvents(List.of(fixtureAboutToStartEvent(now)))
      .provider(MatchDataFeed.FeedProvider.WSF)
      .build();

    OngoingMatchData cachedMatchData = ongoingMatchDataCacheService.get(fixture.getIdAsString());
    matchEventsProcessor.processMatchEvents(matchDataFeed, cachedMatchData);
  }

  private static MatchDataFeed.MatchEventDTO fixtureAboutToStartEvent(Instant now) {
    return new MatchDataFeed.MatchEventDTO("0", SoccerMatchEvent.MATCH_ABOUT_TO_START, now);
  }

  public static JobDetail fixtureStartUpdaterJobDetail(String fixtureId) {
    return JobBuilder
      .newJob()
      .ofType(FixtureStartUpdater.class)
      .storeDurably()
      .usingJobData("fixtureId", fixtureId)
      .withIdentity(fixtureId, FIXTURE_START_JOB_GROUP)
      .withDescription(format("Fixture Start for fixture %s", fixtureId))
      .build();
  }

  public static Trigger fixtureStartUpdaterTrigger(JobDetail jobDetail, Fixture fixture) {
    int upperBound = 90;
    int lowerBound = 15;
    int rndSeconds = randomGenerator.nextInt((upperBound - lowerBound) + 1) + lowerBound;
    Date startDate = fixture.getDate().isAfter(now()) ?
                     Date.from(fixture.getDate().minus(rndSeconds, ChronoUnit.SECONDS)) :
                     Date.from(now());

    String fixtureId = fixture.getIdAsString();
    return buildFixtureStartTrigger(jobDetail, fixtureId, startDate);
  }

  public static Trigger buildFixtureStartTrigger(JobDetail jobDetail, String fixtureId, Date startDate) {
    log.info("Fixture Start for fixtureId={} will trigger at {}", fixtureId, startDate);

    return TriggerBuilder
      .newTrigger()
      .forJob(jobDetail)
      .withIdentity(fixtureId, FIXTURE_START_JOB_TRIGGER_GROUP)
      .withDescription(format("Trigger for Fixture Start Updater: fixtureId - %s", fixtureId))
      .startAt(startDate)
      .build();
  }
}
