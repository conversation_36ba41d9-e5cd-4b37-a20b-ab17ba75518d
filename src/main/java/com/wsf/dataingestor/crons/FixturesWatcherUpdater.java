package com.wsf.dataingestor.crons;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Random;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.services.ratings.PreFixtureDataProcessor;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.crons.FixtureRatingsUpdater.FIXTURE_RATINGS_JOB_POLLING_GROUP;
import static com.wsf.dataingestor.crons.FixtureRatingsUpdater.FIXTURE_RATINGS_TRIGGER_GROUP;
import static com.wsf.dataingestor.crons.FixtureStartUpdater.FIXTURE_START_JOB_GROUP;
import static com.wsf.dataingestor.crons.SettlementUpdater.SETTLEMENT_JOB_GROUP;
import static java.util.Objects.nonNull;
import static java.util.function.Predicate.not;
import static org.quartz.SimpleScheduleBuilder.simpleSchedule;

@Slf4j
@Component
public class FixturesWatcherUpdater implements Job {

  public static final String FIXTURE_WATCHER_JOB_POLLING_GROUP = "fixturesWatcherJobGroup";
  public static final String FIXTURE_WATCHER_JOB_POLLING_JOB_KEY = "fixturesWatcherPollingJob";

  @Value("${crons.matches-stats-updater.enabled}")
  private boolean statsCronEnabled;

  @Value("${crons.matches-stats-updater.interval-secs}")
  private int statsCronIntervalSec;

  @Value("${crons.matches-stats-updater.max-repetitions}")
  private int statsCronMaxReps;

  @Autowired
  private CompetitionService competitionService;

  @Autowired
  private TournamentService tournamentService;

  @Autowired
  private FixtureService fixtureService;

  @Autowired
  private PreFixtureDataProcessor preFixtureDataProcessor;

  @Autowired
  private SchedulerService schedulerService;

  @Autowired
  private FeedsConfigService feedsConfigService;

  @Override
  public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

    List<Competition> activeCompetitions = competitionService.findActiveCompetitions();

    activeCompetitions.forEach(competition -> {
      String competitionId = competition.getId().toString();

      log.debug("Looking for an active tournament for competition {}", competitionId);

      try {

        Tournament tournament = tournamentService.findByIsActiveTrue(competitionId);

        if (nonNull(tournament)) {

          log.info("launching matches on watch process for tournamentId={}", tournament.getId());

          if (statsCronEnabled) {

            Date nextFireTime = jobExecutionContext.getNextFireTime();

            // adding 10 minutes just to make sure we're covering all the matches
            long minutes = ChronoUnit.MINUTES.between(Instant.now(), nextFireTime.toInstant()) + 10;

            List<Fixture> matchesWithinNextMinutes = fixtureService.findAllFixturesWithinNextMinutes(
              tournament.getId().toString(), minutes);

            ExternalProvider externalProvider = feedsConfigService.getCompetitionProvider(competitionId);

            long nrMatchesLaunched = matchesWithinNextMinutes
              .stream()
              .filter(not(Fixture::getWasSuspended))
              .peek(preFixtureDataProcessor::sendPreFixture)
              .map(fixture -> scheduleMatchProcess(fixture, externalProvider))
              .filter(i -> i)
              .count();

            log.info("launched {} fixture watchers for competitionId={}", nrMatchesLaunched, competitionId);
          } else {
            log.warn("Cron not enabled");
          }
        } else {
          log.warn("no tournament active for competition {}", competitionId);
        }
      } catch (Exception e) {
        log.error("Error while processing competition {}", competitionId, e);
      }
    });
  }

  public Boolean scheduleMatchProcess(Fixture fixture, ExternalProvider externalProvider) {
    log.info("fixture={} will start within the next 3h", fixture);
    deleteExistingJobs(fixture);

    if (!fixture.getActive()) {
      log.warn("fixtureId={} is not active, skipping", fixture.getIdAsString());
      return false;
    }

    scheduleMatchStartUpdater(fixture);
    boolean wasJobScheduled = scheduleFixtureRatingsUpdater(fixture);
    if (wasJobScheduled) {
      fixtureService.storeFixtureProcessStatusConnected(fixture, externalProvider);
    }
    return wasJobScheduled;
  }

  private void deleteExistingJobs(Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    try {
      JobKey fixtureStartUpdaterJobKey = new JobKey(fixtureId, FIXTURE_START_JOB_GROUP);
      schedulerService.deleteJob(fixtureStartUpdaterJobKey);

      JobKey fixtureUpdaterJobKey = new JobKey(fixtureId, FIXTURE_RATINGS_JOB_POLLING_GROUP);
      schedulerService.deleteJob(fixtureUpdaterJobKey);

      JobKey settlementJobKey = new JobKey(fixtureId, SETTLEMENT_JOB_GROUP);
      schedulerService.deleteJob(settlementJobKey);
    } catch (SchedulerException e) {
      log.error("Error while deleting jobs for fixture {}", fixtureId);
    }
  }

  private void scheduleMatchStartUpdater(Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    log.info("Scheduling Fixture Start Updater for fixtureId={}", fixtureId);

    JobDetail jobDetail = FixtureStartUpdater.fixtureStartUpdaterJobDetail(fixtureId);
    Trigger trigger = FixtureStartUpdater.fixtureStartUpdaterTrigger(jobDetail, fixture);
    JobKey matchStartUpdaterJobKey = new JobKey(fixtureId, FIXTURE_START_JOB_GROUP);
    schedulerService.scheduleJob(matchStartUpdaterJobKey, jobDetail, trigger);
  }

  private boolean scheduleFixtureRatingsUpdater(Fixture fixture) {
    JobDetail fixtureRatingsUpdaterJobDetail = fixtureRatingsUpdaterJobDetail(fixture);
    Trigger trigger = fixtureRatingsUpdaterTrigger(fixtureRatingsUpdaterJobDetail);
    JobKey jobKey = new JobKey(fixture.getId().toString(), FIXTURE_RATINGS_JOB_POLLING_GROUP);

    return schedulerService.scheduleJob(jobKey, fixtureRatingsUpdaterJobDetail, trigger);
  }

  private JobDetail fixtureRatingsUpdaterJobDetail(Fixture fixture) {
    String fixtureId = fixture.getId().toString();
    return JobBuilder
      .newJob()
      .ofType(FixtureRatingsUpdater.class)
      .requestRecovery()
      .storeDurably(false)
      .usingJobData("fixtureId", fixtureId)
      .withIdentity(fixtureId, FIXTURE_RATINGS_JOB_POLLING_GROUP)
      .withDescription("Invoking Opta MA2 feed in order to update fixture ratings")
      .build();
  }

  private Trigger fixtureRatingsUpdaterTrigger(JobDetail jobDetail) {
    Date startDate = new Date(Instant.now().plus(new Random().nextInt(15), ChronoUnit.SECONDS).toEpochMilli());

    String fixtureId = jobDetail.getJobDataMap().getString("fixtureId");
    return TriggerBuilder
      .newTrigger()
      .forJob(jobDetail)
      .withIdentity(fixtureId, FIXTURE_RATINGS_TRIGGER_GROUP)
      .withDescription("Trigger for Fixture Ratings Updater")
      .withSchedule(simpleSchedule().withRepeatCount(statsCronMaxReps).withIntervalInSeconds(statsCronIntervalSec))
      .startAt(startDate)
      .build();
  }
}
