package com.wsf.dataingestor.modules.fixturesummary;

import java.util.List;
import java.util.stream.Collectors;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.kafka.domain.FixturePeriod;
import com.wsf.kafka.domain.FixtureStatus;
import com.wsf.kafka.domain.FixtureSummaryData;

public class FixtureSummaryMapper {

  public static FixtureSummaryDto fromKafkaToDto(FixtureSummaryData data) {
    if (data == null) {
      return null;
    }

    return new FixtureSummaryDto(data.getFixtureId(), data.getSupportedEventTypes(), mapEvents(data.getEvents()),
      mapContestantStats(data.getContestantStats()), mapFixtureStatus(data.getFixtureStatus()), data.getIsFinal());
  }

  private static List<FixtureSummaryDto.Event> mapEvents(List<FixtureSummaryData.Event> events) {
    return events
      .stream()
      .map(FixtureSummaryMapper::mapEvent)
      .collect(Collectors.toList());
  }

  private static FixtureSummaryDto.Event mapEvent(FixtureSummaryData.Event event) {
    if (event == null) {
      return null;
    }

    return new FixtureSummaryDto.Event(event.getEventId(), event.getContestantId(), event.getParentContestantId(),
      event.getEventType(), event.getMatchTime(), mapFixturePeriod(event.getFixturePeriod()));
  }

  private static List<FixtureSummaryDto.ContestantStats> mapContestantStats(
    List<FixtureSummaryData.ContestantStats> stats) {
    if (stats == null) {
      return null;
    }

    return stats
      .stream()
      .map(FixtureSummaryMapper::mapContestantStat)
      .collect(Collectors.toList());
  }

  private static FixtureSummaryDto.ContestantStats mapContestantStat(FixtureSummaryData.ContestantStats stat) {
    if (stat == null) {
      return null;
    }

    return new FixtureSummaryDto.ContestantStats(stat.getContestantId(), mapContestantType(stat.getContestantType()),
      stat.getHasPlayed(), stat.getStats());
  }

  private static MatchDataFeed.FeedFixtureStatus mapFixtureStatus(FixtureStatus status) {
    if (status == null) {
      return null;
    }

    return switch (status) {
      case FIXTURE -> MatchDataFeed.FeedFixtureStatus.FIXTURE;
      case LIVE -> MatchDataFeed.FeedFixtureStatus.LIVE;
      case BET_END -> MatchDataFeed.FeedFixtureStatus.BET_END;
      case PLAYED -> MatchDataFeed.FeedFixtureStatus.PLAYED;
      case CANCELLED -> MatchDataFeed.FeedFixtureStatus.CANCELLED;
      case SUSPENDED -> MatchDataFeed.FeedFixtureStatus.SUSPENDED;
      case POSTPONED -> MatchDataFeed.FeedFixtureStatus.POSTPONED;
    };
  }

  public static MatchPeriod mapFixturePeriod(FixturePeriod fixturePeriod) {
    if (fixturePeriod == null) {
      return null;
    }

    return switch (fixturePeriod) {
      case FIRST_HALF -> MatchPeriod.FIRST_HALF;
      case HALF_TIME -> MatchPeriod.HALF_TIME;
      case SECOND_HALF -> MatchPeriod.SECOND_HALF;
      case END_REGULAR_TIMES -> MatchPeriod.END_REGULAR_TIMES;
      case EXTRA_FIRST_HALF -> MatchPeriod.EXTRA_FIRST_HALF;
      case EXTRA_HALF_TIME -> MatchPeriod.EXTRA_HALF_TIME;
      case EXTRA_SECOND_HALF -> MatchPeriod.EXTRA_SECOND_HALF;
      case END_MATCH -> MatchPeriod.END_MATCH;
    };
  }

  public static ContestantType mapContestantType(com.wsf.kafka.domain.ContestantType contestantType) {
    if (contestantType == null) {
      return null;
    }

    return switch (contestantType) {
      case SOCCER_PLAYER -> ContestantType.SOCCER_PLAYER;
      case SOCCER_TEAM -> ContestantType.SOCCER_TEAM;
      case MATCH -> null;
    };
  }

}
