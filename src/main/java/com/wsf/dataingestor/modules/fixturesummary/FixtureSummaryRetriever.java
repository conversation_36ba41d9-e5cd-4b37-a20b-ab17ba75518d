package com.wsf.dataingestor.modules.fixturesummary;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import static com.wsf.dataingestor.config.cache.CacheConstants.FIXTURE_SUMMARIES_CACHE_NAME;

@Slf4j
@Service
@RequiredArgsConstructor
public class FixtureSummaryRetriever {

  @CachePut(value = FIXTURE_SUMMARIES_CACHE_NAME, key = "#fixtureId")
  public FixtureSummaryDto put(String fixtureId, FixtureSummaryDto fixtureSummaryData) {
    log.debug("Caching fixture summary for fixtureId={}", fixtureId);
    return fixtureSummaryData;
  }

  @Cacheable(value = FIXTURE_SUMMARIES_CACHE_NAME, key = "#fixtureId")
  public FixtureSummaryDto get(String fixtureId) {
    log.warn("No fixture summary found for fixtureId={}", fixtureId);
    throw new FixtureSummaryNotFoundException("No fixture summary found for fixtureId: " + fixtureId);
  }

}
