package com.wsf.dataingestor.modules.fixturesummary;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import com.wsf.dataingestor.mappers.FeedFixtureStatusToFixtureStatus;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.soccer.SoccerMatchEvent;
import com.wsf.kafka.domain.EventInfo;
import com.wsf.kafka.domain.FixtureStatus;
import com.wsf.kafka.domain.FixtureSummaryData;

import static com.wsf.dataingestor.kafka.KafkaService.buildEventInfo;
import static java.time.Instant.now;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.toSet;

public class FixtureSummaryEventCreator {

  public static FixtureSummaryData createFixtureSummary(String fixtureId,
                                                        MatchDataFeed.FeedFixtureStatus feedFixtureStatus,
                                                        boolean isFinal, List<FixtureSummaryData.Event> events,
                                                        List<FixtureSummaryData.ContestantStats> contestantStats,
                                                        Set<SoccerMatchEvent> supportedEventTypes, String traceId) {
    Instant now = now();
    EventInfo eventInfo = buildEventInfo(now, randomUUID().toString(), traceId.formatted(now.toEpochMilli()));
    FixtureStatus fixtureStatus = FeedFixtureStatusToFixtureStatus.mapToFixtureStatus(feedFixtureStatus);

    Set<String> supportedEventTypesAsString = supportedEventTypes
      .stream()
      .map(SoccerMatchEvent::getStatisticName)
      .collect(toSet());
    return new FixtureSummaryData(fixtureId, supportedEventTypesAsString, events, contestantStats, fixtureStatus,
      isFinal, eventInfo);
  }

}
