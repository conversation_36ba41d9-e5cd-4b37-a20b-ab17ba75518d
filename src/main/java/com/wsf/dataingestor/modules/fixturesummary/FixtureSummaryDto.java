package com.wsf.dataingestor.modules.fixturesummary;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.domain.common.MatchPeriod;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FixtureSummaryDto {

  private String fixtureId;
  private Set<String> supportedEventTypes;
  private List<Event> events;
  private List<ContestantStats> contestantStats;
  private FeedFixtureStatus fixtureStatus;
  private Boolean isFinal;

  @Builder
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Event {
    private String eventId;
    private String contestantId;
    private String parentContestantId;
    private String eventType;
    private Integer matchTime;
    private MatchPeriod fixturePeriod;
  }

  @Builder
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ContestantStats {
    private String contestantId;
    private ContestantType contestantType;
    private Boolean hasPlayed;
    private Map<String, Number> stats;
  }
}
