package com.wsf.dataingestor.modules.metadata;

import java.util.List;
import com.wsf.dataingestor.modules.event.ContestantEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.UNINITIALIZED;

public final class EmptyMetadataRetriever implements MetadataRetriever {

  @Override
  public <T extends ContestantEvent> KafkaMetadata getMetadata(String playerId, List<T> events,
                                                               MetadataRetrievalFlags retrievalFlags) {
    if (retrievalFlags.isManualSettlement()) {
      return UNINITIALIZED;
    }

    return EMPTY_METADATA;
  }
}
