package com.wsf.dataingestor.modules.metadata;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import com.wsf.dataingestor.modules.event.ContestantEvent;
import com.wsf.domain.soccer.SoccerMatchEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.utils.SoccerConstants.SUBBED_IN_PLAYER_ID;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.UNINITIALIZED;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;

@Slf4j
public final class SoccerPlayerMetadataRetriever implements MetadataRetriever {

  public <T extends ContestantEvent> KafkaMetadata getMetadata(String playerId, List<T> events,
                                                               MetadataRetrievalFlags retrievalFlags) {
    if (!retrievalFlags.isFinal()) {
      return EMPTY_METADATA;
    }

    if (retrievalFlags.isManualSettlement()) {
      return UNINITIALIZED;
    }

    var metadataMap = new HashMap<String, Object>();

    ofNullable(getSubbedInPlayerId(playerId, events)).ifPresent(
      subbedInPlayerId -> metadataMap.put(SUBBED_IN_PLAYER_ID, subbedInPlayerId));

    return new KafkaMetadata(true, metadataMap);
  }

  private <T extends ContestantEvent> String getSubbedInPlayerId(String playerId, List<T> events) {
    Optional<T> optionalSubOffEvent = findEventByType(playerId, SUB_OFF, events);
    if (optionalSubOffEvent.isEmpty()) {
      log.warn("No event of type=%s found for playerId=%s".formatted(SUB_OFF, playerId));
      return null;
    }

    ContestantEvent subOffEvent = optionalSubOffEvent.get();
    String relatedEventId = subOffEvent.getRelatedEventId();
    if (isNull(relatedEventId)) {
      throw new IllegalArgumentException(
        "Found subOff event without related event id for playerId=%s".formatted(playerId));
    }

    List<T> relatedEvents = events
      .stream()
      .filter(event -> event.getEventId().equals(relatedEventId))
      .toList();
    if (relatedEvents.isEmpty()) {
      throw new IllegalArgumentException("Related subIn event not found for playerId=%s".formatted(playerId));
    }
    if (relatedEvents.size() > 1) {
      throw new IllegalArgumentException(
        "Found %s related sub events for playerId=%s".formatted(relatedEvents.size(), playerId));
    }

    var contestantId = relatedEvents.getFirst().getContestantId();
    if (contestantId.startsWith("UNKNOWN") != relatedEvents.getFirst().isUnknownEntity()) {
      log.error("ERROR: ILLEGAL UNKNOWN STATE relatedEvents: {}", relatedEvents);
    }
    if (relatedEvents.getFirst().isUnknownEntity()) {
      return null;
    }
    return contestantId;
  }

  private static <T extends ContestantEvent> Optional<T> findEventByType(String playerId, SoccerMatchEvent eventType,
                                                                         List<T> events) {
    return events
      .stream()
      .filter(event -> eventType.equals(event.getEventType()) && event.getContestantId().equals(playerId))
      .findFirst();
  }
}
