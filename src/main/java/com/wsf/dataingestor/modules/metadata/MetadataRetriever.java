package com.wsf.dataingestor.modules.metadata;

import java.util.List;
import com.wsf.dataingestor.modules.event.ContestantEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

public sealed interface MetadataRetriever permits SoccerPlayerMetadataRetriever,
                                                  EmptyMetadataRetriever {

  <T extends ContestantEvent> KafkaMetadata getMetadata(String playerId, List<T> events,
                                                        MetadataRetrievalFlags retrievalFlags);

}
