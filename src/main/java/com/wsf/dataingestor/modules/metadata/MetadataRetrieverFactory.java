package com.wsf.dataingestor.modules.metadata;

import java.util.Map;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Sport.SportType;

public class MetadataRetrieverFactory {

  private static final Map<String, MetadataRetriever> sportContestantTypeToMetadataRetrieverMap = Map.of(
    getKey(SportType.SOCCER, ContestantType.SOCCER_PLAYER), new SoccerPlayerMetadataRetriever(),
    getKey(SportType.SOCCER, ContestantType.SOCCER_TEAM), new EmptyMetadataRetriever());

  public static MetadataRetriever getMetadataRetriever(SportType sportType, ContestantType contestantType) {
    var mapKey = getKey(sportType, contestantType);
    return sportContestantTypeToMetadataRetrieverMap.get(mapKey);
  }

  public static MetadataRetriever getMetadataRetriever(Fixture fixture, ContestantType contestantType) {
    SportType sportType = fixture.getTournament().getCompetition().getSport().getType();
    return getMetadataRetriever(sportType, contestantType);
  }

  private static String getKey(SportType sportType, ContestantType contestantType) {
    return "%s-%s".formatted(sportType, contestantType);
  }
}
