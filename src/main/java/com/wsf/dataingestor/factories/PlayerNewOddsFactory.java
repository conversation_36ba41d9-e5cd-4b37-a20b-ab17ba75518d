package com.wsf.dataingestor.factories;

import java.time.Instant;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.PlayerNewOdds;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.kafka.KafkaService.buildEventInfo;
import static com.wsf.dataingestor.kafka.KafkaService.buildPlayer;
import static java.time.Instant.now;

public class PlayerNewOddsFactory {
  public static PlayerNewOdds playerNewOdds(Player player, String marketId, KafkaMetadata metadata, String eventId,
                                            String feedId) {
    Instant now = now();
    return new PlayerNewOdds(buildPlayer(player), metadata, marketId, now, buildEventInfo(now, eventId, feedId));
  }

  public static PlayerNewOdds withoutMarketId(Player player, KafkaMetadata metadata, String eventId, String feedId) {
    Instant now = now();
    return new PlayerNewOdds(buildPlayer(player), metadata, null, now, buildEventInfo(now, eventId, feedId));
  }
}
