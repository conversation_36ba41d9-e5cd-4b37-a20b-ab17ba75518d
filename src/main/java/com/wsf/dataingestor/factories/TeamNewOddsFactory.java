package com.wsf.dataingestor.factories;

import java.time.Instant;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.TeamNewOdds;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.kafka.KafkaService.buildEventInfo;
import static com.wsf.dataingestor.kafka.KafkaService.buildTeam;
import static java.time.Instant.now;

public class TeamNewOddsFactory {
  public static TeamNewOdds teamNewOdds(Team team, String tournamentId, String marketId, KafkaMetadata metadata, String eventId,
                                        String feedId) {
    Instant now = now();
    return new TeamNewOdds(buildTeam(team, tournamentId), metadata, marketId, now, buildEventInfo(now, eventId, feedId));
  }

  public static TeamNewOdds withoutMarketId(Team team, String tournamentId, KafkaMetadata metadata, String eventId,
                                        String feedId) {
    Instant now = now();
    return new TeamNewOdds(buildTeam(team, tournamentId), metadata, null, now, buildEventInfo(now, eventId, feedId));
  }
}
