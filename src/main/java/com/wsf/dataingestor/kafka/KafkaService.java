package com.wsf.dataingestor.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Map;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.TeamRating;
import com.wsf.kafka.domain.ContestantType;
import com.wsf.kafka.domain.ContestantUnavailabilityKafka;
import com.wsf.kafka.domain.Entity;
import com.wsf.kafka.domain.EventInfo;
import com.wsf.kafka.domain.FixtureChange;
import com.wsf.kafka.domain.FixtureEntityEvent;
import com.wsf.kafka.domain.FixtureSummaryData;
import com.wsf.kafka.domain.Match;
import com.wsf.kafka.domain.MatchStatus;
import com.wsf.kafka.domain.Player;
import com.wsf.kafka.domain.PlayerEvent;
import com.wsf.kafka.domain.PlayerFinalRating;
import com.wsf.kafka.domain.PlayerLiveRating;
import com.wsf.kafka.domain.PlayerNewOdds;
import com.wsf.kafka.domain.PlayerRating;
import com.wsf.kafka.domain.PlayerTransferEvent;
import com.wsf.kafka.domain.PlayerTransferEvent.ActionType;
import com.wsf.kafka.domain.RatingEvent;
import com.wsf.kafka.domain.Team;
import com.wsf.kafka.domain.TeamEvent;
import com.wsf.kafka.domain.TeamFinalRating;
import com.wsf.kafka.domain.TeamLiveRating;
import com.wsf.kafka.domain.TeamNewOdds;
import com.wsf.kafka.domain.Tournament;
import com.wsf.kafka.domain.metadata.KafkaMetadata;
import com.wsf.kafka.producers.client.KafkaClient;

import static com.wsf.kafka.domain.FixtureEntityEvent.EventType.PRE_MATCH;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Collections.emptyMap;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.UUID.randomUUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaService {

  private static final PlayerRating.IndexPerformance EMPTY_INDEX_PERFORMANCE = PlayerRating.IndexPerformance
    .builder().build();

  private final KafkaClient kafkaClient;
  private final ObjectMapper objectMapper;

  public void sendPlayerPrematchEvent(com.wsf.domain.common.Player player, com.wsf.domain.common.Fixture fixture,
                                      String eventId) {
    String playerId = player.getIdAsString();
    Player kafkaPlayer = buildPlayer(player);

    try {
      String serializedKafkaPlayer = objectMapper.writeValueAsString(kafkaPlayer);
      var entity = new Entity(ContestantType.SOCCER_PLAYER, playerId, serializedKafkaPlayer);

      String traceId = format("pre-match-%s-%s", fixture.getIdAsString(), playerId);
      EventInfo eventInfo = buildEventInfo(now(), eventId, traceId);

      var fixtureEntityEvent = new FixtureEntityEvent(buildLiveMatch(fixture), PRE_MATCH, entity, eventInfo);
      sendFixtureEntityEvent(fixtureEntityEvent);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException(String.format("could not serialize entity: %s", kafkaPlayer), e);
    }
  }

  public void sendTeamPrematchEvent(com.wsf.domain.common.Team team, com.wsf.domain.common.Fixture fixture,
                                    String eventId) {
    String teamId = team.getIdAsString();
    Team kafkaTeam = new Team(teamId, fixture.getTournament().getIdAsString());

    try {
      String serializedKafkaTeam = objectMapper.writeValueAsString(kafkaTeam);
      var entity = new Entity(ContestantType.SOCCER_TEAM, teamId, serializedKafkaTeam);

      String traceId = format("pre-match-%s-%s", fixture.getIdAsString(), teamId);
      EventInfo eventInfo = buildEventInfo(now(), eventId, traceId);

      var fixtureEntityEvent = new FixtureEntityEvent(buildLiveMatch(fixture), PRE_MATCH, entity, eventInfo);
      sendFixtureEntityEvent(fixtureEntityEvent);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException(String.format("could not serialize entity: %s", kafkaTeam), e);
    }
  }

  public void sendMatchPrematchEvent(com.wsf.domain.common.Fixture fixture, String eventId) {
    String fixtureId = fixture.getIdAsString();

    var entity = new Entity(ContestantType.MATCH, null, "");

    String traceId = format("pre-match-%s", fixtureId);
    EventInfo eventInfo = buildEventInfo(now(), eventId, traceId);

    var fixtureEntityEvent = new FixtureEntityEvent(buildLiveMatch(fixture), PRE_MATCH, entity, eventInfo);
    sendFixtureEntityEvent(fixtureEntityEvent);
  }

  public void sendFinalPlayerRating(com.wsf.domain.common.PlayerRating rating, FixtureStatus fixtureStatus,
                                    boolean settleOdds, KafkaMetadata metadata) {
    PlayerFinalRating kafkaRating = KafkaService.buildPlayerFinalRating(rating, fixtureStatus, settleOdds, metadata);

    PlayerEvent playerEvent = PlayerEvent
      .builder()
      .playerFinalRating(kafkaRating)
      .eventInfo(buildEventInfo(rating.getTimestamp(), rating.getEventId(), rating.getFeedId()))
      .build();

    sendPlayerEventRating(playerEvent);
    sendLiveRating(kafkaRating);
  }

  public void sendFinalTeamRating(TeamRating rating, FixtureStatus fixtureStatus, Boolean settleOdds,
                                  KafkaMetadata metadata) {
    TeamFinalRating kafkaRating = KafkaService.buildTeamFinalRating(rating.getTeam(), rating.getFixture(),
      fixtureStatus, rating.getStats(), settleOdds, rating.getTimestamp(), metadata);

    TeamEvent teamEvent = TeamEvent
      .builder()
      .teamFinalRating(kafkaRating)
      .eventInfo(buildEventInfo(rating.getTimestamp(), rating.getEventId(), rating.getFeedId()))
      .build();

    sendTeamEventRating(teamEvent);
    sendLiveRating(kafkaRating);
  }

  public void sendLiveTeamRating(com.wsf.domain.common.TeamRating rating, KafkaMetadata metadata) {
    TeamLiveRating kafkaRating = KafkaService.buildTeamLiveRating(rating, metadata);

    TeamEvent teamEvent = TeamEvent
      .builder()
      .teamLiveRating(kafkaRating)
      .eventInfo(buildEventInfo(rating.getTimestamp(), rating.getEventId(), rating.getFeedId()))
      .build();

    sendTeamEventRating(teamEvent);
    sendLiveRating(kafkaRating);
  }

  public void sendLivePlayerRating(com.wsf.domain.common.PlayerRating rating, KafkaMetadata metadata) {

    log.debug("sending new live rating event for player {}", rating.getPlayer().getId());

    PlayerLiveRating kafkaRating = KafkaService.buildLivePlayerRating(rating, metadata);

    PlayerEvent playerEvent = PlayerEvent
      .builder()
      .playerLiveRating(kafkaRating)
      .eventInfo(buildEventInfo(rating.getTimestamp(), rating.getEventId(), rating.getFeedId()))
      .build();

    sendPlayerEventRating(playerEvent);
    sendLiveRating(kafkaRating);
  }

  public void sendGenerateOdds(PlayerNewOdds newOddsEvent) {
    log.debug("sending new odds event for player {}", newOddsEvent.getPlayer().getId());
    sendPlayerOddsTrigger(newOddsEvent);
  }

  public void sendGenerateOdds(TeamNewOdds newOddsEvent) {
    log.debug("sending new odds event for team {}", newOddsEvent.getTeam().getId());
    sendTeamOddsTrigger(newOddsEvent);
  }

  public void sendPlayerTransferAndTeamChangeEvents(com.wsf.domain.common.Player player, String eventId, String feedId,
                                                    ActionType actionType, String competitionId,
                                                    PlayerEvent playerEvent) {
    sendPlayerTransferEvent(eventId, competitionId, player.getIdAsString(), actionType, feedId);
    sendPlayerEventRating(playerEvent);
  }

  public void sendPlayerTransferEvent(String eventId, String competitionId, String entityId, ActionType actionType,
                                      String feedId) {
    EventInfo eventInfo = new EventInfo(now(), now(), eventId, feedId);

    var event = new PlayerTransferEvent(competitionId, entityId, actionType, eventInfo);
    log.debug("sending PlayerTransferEvent to kafka: {}", event);
    kafkaClient.newPlayerTransferEvent(event);
  }

  public void sendFinalFixtureSummary(FixtureSummaryData fixtureSummaryData) {
    kafkaClient.newFinalFixtureSummary(fixtureSummaryData);
  }

  public void sendTemporaryFixtureSummary(FixtureSummaryData fixtureSummaryData) {
    kafkaClient.newTemporaryFixtureSummary(fixtureSummaryData);
  }

  public void sendContestantUnavailabilities(ContestantUnavailabilityKafka contestantUnavailabilityKafka) {
    log.debug("sending Contestant unavailabilites for fixtureId: {} and contestantId: {} to kafka: {}",
      contestantUnavailabilityKafka.getFixtureId(), contestantUnavailabilityKafka.getContestantId(),
      contestantUnavailabilityKafka);
    kafkaClient.newContestantUnavailabilities(contestantUnavailabilityKafka);
  }

  public void sendFixtureChange(Fixture fixture, String feedId) {
    FixtureChange fixtureChange = buildFixtureChangeEvent(fixture, feedId);

    log.debug("sending Fixture Change for fixtureId: {} to kafka: {}", fixtureChange.getFixture().getId(),
      fixtureChange);

    kafkaClient.newFixtureChange(fixtureChange);
  }

  private static FixtureChange buildFixtureChangeEvent(Fixture fixture, String feedId) {
    var fixtureId = fixture.getIdAsString();
    var previousDate = fixture.getDate();
    var previousStatus = mapStatus(fixture.getStatus());

    var eventInfo = buildEventInfo(feedId);

    var previousFixtureDto = new FixtureChange.FixtureDto(fixtureId, previousDate, previousStatus);
    return new FixtureChange(previousFixtureDto, FixtureChange.ChangeType.OTHER, eventInfo);
  }

  private static FixtureChange.Status mapStatus(FixtureStatus fixtureStatus) {
    if (isNull(fixtureStatus)) {
      return FixtureChange.Status.FIXTURE;
    }
    return switch (fixtureStatus) {
      case FIXTURE -> FixtureChange.Status.FIXTURE;
      case LIVE -> FixtureChange.Status.LIVE;
      case PLAYED -> FixtureChange.Status.PLAYED;
      case CANCELLED -> FixtureChange.Status.CANCELLED;
      case SUSPENDED -> FixtureChange.Status.SUSPENDED;
    };
  }

  private static EventInfo buildEventInfo(String feedId) {
    return new EventInfo(now(), now(), randomUUID().toString(), feedId);
  }

  public static EventInfo buildEventInfo(Instant timestamp, String eventId, String traceId) {
    return new EventInfo(timestamp, now(), eventId, traceId);
  }

  private static PlayerFinalRating buildPlayerFinalRating(com.wsf.domain.common.PlayerRating rating,
                                                          FixtureStatus fixtureStatus, Boolean settleOdds,
                                                          KafkaMetadata metadata) {
    IndexPerformance indexPerformance = rating.getIndexPerformance();
    boolean hasPlayed = nonNull(indexPerformance);
    com.wsf.domain.common.Player player = rating.getPlayer();
    Fixture fixture = rating.getFixture();
    Map<String, Number> stats = rating.getStats();
    Instant timestamp = rating.getTimestamp();

    Match ratingMatch = buildMatch(fixture, fixtureStatus);

    PlayerFinalRating.PlayerFinalRatingBuilder playerRatingBuilder = PlayerFinalRating
      .builder().player(buildPlayer(player)).hasPlayed(hasPlayed).match(ratingMatch)
      .timestamp(timestamp).settleOdds(settleOdds).stats(stats).metadata(metadata);

    if (nonNull(indexPerformance)) {
      playerRatingBuilder.indexPerformance(buildRating(indexPerformance));
    } else {
      playerRatingBuilder.indexPerformance(EMPTY_INDEX_PERFORMANCE);
    }
    return playerRatingBuilder.build();
  }

  private static TeamFinalRating buildTeamFinalRating(com.wsf.domain.common.Team team, Fixture fixture,
                                                      FixtureStatus fixtureStatus, Map<String, Number> stats,
                                                      Boolean isFinal, Instant timestamp, KafkaMetadata metadata) {

    Match eventMatch = buildMatch(fixture, fixtureStatus);

    boolean hasPlayed = fixtureStatus.equals(FixtureStatus.PLAYED) || fixtureStatus.equals(FixtureStatus.LIVE);
    return TeamFinalRating
      .builder()
      .team(buildTeam(team, fixture.getTournament().getId().toString()))
      .match(eventMatch)
      .timestamp(timestamp)
      .settleOdds(isFinal)
      .hasPlayed(hasPlayed)
      .stats(ofNullable(stats).orElse(emptyMap()))
      .metadata(metadata)
      .build();
  }

  private static TeamLiveRating buildTeamLiveRating(com.wsf.domain.common.TeamRating rating, KafkaMetadata metadata) {

    Match eventMatch = buildMatch(rating.getFixture(), FixtureStatus.LIVE);

    String tournamentId = rating.getFixture().getTournament().getId().toString();
    return TeamLiveRating
      .builder()
      .team(buildTeam(rating.getTeam(), tournamentId))
      .match(eventMatch)
      .matchTime(rating.getFixtureTimeMin())
      .periodId(rating.getPeriodId())
      .timestamp(rating.getTimestamp())
      .stats(ofNullable(rating.getStats()).orElse(emptyMap()))
      .metadata(metadata)
      .build();
  }

  private static PlayerLiveRating buildLivePlayerRating(com.wsf.domain.common.PlayerRating rating,
                                                        KafkaMetadata metadata) {

    com.wsf.domain.common.Player player = rating.getPlayer();
    com.wsf.domain.common.Fixture fixture = rating.getFixture();
    Match ratingMatch = buildMatch(fixture, FixtureStatus.LIVE);

    return PlayerLiveRating
      .builder()
      .player(buildPlayer(player))
      .match(ratingMatch)
      .matchTime(rating.getFixtureTimeMin())
      .periodId(rating.getPeriodId())
      .stats(rating.getStats())
      .indexPerformance(buildRating(rating.getIndexPerformance()))
      .timestamp(rating.getTimestamp())
      .metadata(metadata)
      .build();
  }

  private void sendTeamOddsTrigger(TeamNewOdds teamNewOdds) {
    log.debug("sending Team New Odds Trigger to kafka: {}", teamNewOdds);
    kafkaClient.newTeamOdds(teamNewOdds);
  }

  private void sendPlayerOddsTrigger(PlayerNewOdds playerNewOdds) {
    log.debug("sending Player New Odds Trigger to kafka: {}", playerNewOdds);
    kafkaClient.newPlayerOdds(playerNewOdds);
  }

  private void sendFixtureEntityEvent(FixtureEntityEvent fixtureEntityEvent) {
    log.debug("sending Fixture Entity Event to kafka: {}", fixtureEntityEvent);
    kafkaClient.newFixtureEntityEvent(fixtureEntityEvent);
  }

  public void sendPlayerEventRating(PlayerEvent playerEvent) {
    log.debug("sending Player Event Rating to kafka: {}", playerEvent);
    kafkaClient.newPlayerEvent(playerEvent);
  }

  public void sendTeamEventRating(TeamEvent teamEvent) {
    log.debug("sending Team Event Rating to kafka: {}", teamEvent);
    kafkaClient.newTeamEvent(teamEvent);
  }

  private void sendLiveRating(RatingEvent ratingEvent) {
    log.debug("sending Live Rating to kafka: {}", ratingEvent);
    kafkaClient.newLiveRating(ratingEvent);
  }

  private static PlayerRating.IndexPerformance buildRating(IndexPerformance index) {
    return PlayerRating.IndexPerformance
      .builder().isValid(index.isValid())
      .index(index.getIndex()).build();
  }

  public static Player buildPlayer(com.wsf.domain.common.Player player) {
    Player.Position position = convertPosition(player.getPosition());
    String detailedPosition = ofNullable(player.getDetailedPosition())
      .map(com.wsf.domain.common.Player.DetailedPosition::toString)
      .orElse(null);
    return com.wsf.kafka.domain.Player
      .builder()
      .id(player.getId().toString())
      .masterId(player.getPlayerMasterId())
      .name(player.getName())
      .position(position)
      .detailedPosition(detailedPosition)
      .teamId(player.getTeam().getId().toString())
      .team(player.getTeam().getName())
      .tournamentId(player.getTournament().getId().toString())
      .build();
  }

  public static Team buildTeam(com.wsf.domain.common.Team team, String tournamentId) {
    return Team
      .builder().id(team.getId().toString()).tournamentId(tournamentId).build();
  }

  public static Match buildFixtureMatch(com.wsf.domain.common.Fixture fixture) {
    return buildMatch(fixture, FixtureStatus.FIXTURE);
  }

  public static Match buildLiveMatch(com.wsf.domain.common.Fixture fixture) {
    return buildMatch(fixture, FixtureStatus.LIVE);
  }

  public static Match buildMatch(com.wsf.domain.common.Fixture fixture, FixtureStatus status) {
    return Match
      .builder()
      .id(fixture.getId().toString())
      .sequentialId(fixture.getSequentialId())
      .homeTeam(fixture.getHomeTeam().getId().toString())
      .awayTeam(fixture.getAwayTeam().getId().toString())
      .status(parseFixtureStatus(status))
      .tournament(Tournament
        .builder()
        .id(fixture.getTournament().getId().toString())
        .competitionId(fixture.getTournament().getCompetitionId())
        .build())
      .isLiveEnabled(fixture.getIsLiveEnabled())
      .build();
  }

  private static MatchStatus parseFixtureStatus(FixtureStatus status) {
    return switch (status) {
      case FIXTURE -> MatchStatus.FIXTURE;
      case LIVE -> MatchStatus.LIVE;
      case PLAYED -> MatchStatus.PLAYED;
      case CANCELLED -> MatchStatus.CANCELLED;
      case SUSPENDED -> MatchStatus.SUSPENDED;
    };
  }

  private static Player.Position convertPosition(com.wsf.domain.common.Player.Position position) {
    return switch (position) {
      case GOALKEEPER -> Player.Position.GOALKEEPER;
      case DEFENDER -> Player.Position.DEFENDER;
      case MIDFIELDER -> Player.Position.MIDFIELDER;
      case FORWARD -> Player.Position.FORWARD;
    };
  }
}
