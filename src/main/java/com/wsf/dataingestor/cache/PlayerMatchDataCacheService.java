package com.wsf.dataingestor.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashSet;
import java.util.Map;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.PlayerMatchData;

import static com.wsf.dataingestor.cache.CacheUtils.buildOngoingMatchInitialPlayerData;
import static com.wsf.dataingestor.cache.CacheUtils.mergeStats;
import static com.wsf.dataingestor.config.cache.CacheConstants.PLAYER_DATA_CACHE_NAME;
import static java.lang.String.format;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlayerMatchDataCacheService {

  private static final String CACHE_ID_FORMAT = "%s-%s";

  private final CacheManager cacheManager;

  @Cacheable(cacheNames = PLAYER_DATA_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.PlayerMatchDataCacheService).buildCacheKey(#fixtureId,#playerId)", unless = "#result == null")
  public PlayerMatchData get(String fixtureId, String playerId) {
    return buildOngoingMatchInitialPlayerData(Instant.now().toEpochMilli(), playerId, fixtureId);
  }

  public void set(String fixtureId, String playerId, PlayerMatchData matchData) {
    if (nonNull(matchData)) {
      cacheManager.getCache(PLAYER_DATA_CACHE_NAME).put(buildCacheKey(fixtureId, playerId), matchData);
    }
  }

  public PlayerMatchData mergeIfExists(String fixtureId, String playerId, PlayerMatchData cachedData,
                                       PlayerMatchData matchData) {
    Cache cache = cacheManager.getCache(PLAYER_DATA_CACHE_NAME);
    String cacheKey = buildCacheKey(fixtureId, playerId);
    if (nonNull(cachedData)) {
      PlayerMatchData mergedData = mergePlayerData(cachedData, matchData);
      cache.put(cacheKey, mergedData);
      return mergedData;
    } else {
      cache.put(cacheKey, matchData);
      return matchData;
    }
  }

  @CacheEvict(cacheNames = PLAYER_DATA_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.PlayerMatchDataCacheService).buildCacheKey(#fixtureId,#playerId)")
  public void remove(String fixtureId, String playerId) {
  }

  private static PlayerMatchData mergePlayerData(PlayerMatchData existingMatchData, PlayerMatchData newMatchData) {

    Map<String, Number> existingPlayersData = existingMatchData.getStats();

    Map<String, Number> newPlayersData = newMatchData.getStats();

    Map<String, Number> mergedStats = mergeStats(existingPlayersData, newPlayersData);
    return PlayerMatchData
      .builder()
      .timestamp(newMatchData.getTimestamp())
      .playerId(newMatchData.getPlayerId())
      .fixtureId(newMatchData.getFixtureId())
      .stats(mergedStats)
      .processedEvents(ofNullable(newMatchData.getProcessedEvents()).orElse(
        ofNullable(existingMatchData.getProcessedEvents()).orElse(new HashSet<>())))
      .betStopsInfo(ofNullable(newMatchData.getBetStopsInfo()).orElse(existingMatchData.getBetStopsInfo()))
      .build();
  }

  public static String buildCacheKey(String fixtureId, String playerId) {
    return format(CACHE_ID_FORMAT, fixtureId, playerId);
  }
}
