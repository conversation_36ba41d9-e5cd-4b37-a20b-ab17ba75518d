package com.wsf.dataingestor.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashMap;
import java.util.function.Function;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.dataingestor.config.cache.CacheConstants.FIXTURE_DATA_CACHE_NAME;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Slf4j
@Service
@RequiredArgsConstructor
public class OngoingMatchDataCacheService {

  private final CacheManager cacheManager;

  public OngoingMatchData get(String fixtureId) {
    return cacheManager.getCache(FIXTURE_DATA_CACHE_NAME).get(fixtureId, OngoingMatchData.class);
  }

  public void set(String fixtureId, OngoingMatchData matchData) {
    cacheManager.getCache(FIXTURE_DATA_CACHE_NAME).put(fixtureId, matchData);
  }

  public OngoingMatchData getOrCompute(String fixtureId, Function<String, OngoingMatchData> ongoingMatchDataFn) {
    OngoingMatchData ongoingMatchData = cacheManager
      .getCache(FIXTURE_DATA_CACHE_NAME)
      .get(fixtureId, OngoingMatchData.class);
    return ofNullable(ongoingMatchData).orElseGet(() -> ongoingMatchDataFn.apply(fixtureId));
  }

  public OngoingMatchData mergeIfExists(String fixtureId, OngoingMatchData matchData, boolean validate) {
    Cache cache = cacheManager.getCache(FIXTURE_DATA_CACHE_NAME);
    OngoingMatchData cachedData = cache.get(fixtureId, OngoingMatchData.class);
    if (nonNull(cachedData)) {
      OngoingMatchData mergedData = mergeMatchData(cachedData, matchData, validate);
      cache.put(fixtureId, mergedData);
    } else {
      cache.put(fixtureId, matchData);
    }
    return matchData;
  }

  @CacheEvict(cacheNames = FIXTURE_DATA_CACHE_NAME)
  public void remove(String fixtureId) {
  }

  public static OngoingMatchData buildInitialOngoingMatchData(String fixtureId, Instant feedLatestUpdate) {
    log.info("creating cached data from zero for fixtureId={}", fixtureId);
    Instant latestUpdate = ofNullable(feedLatestUpdate).orElse(now());
    return buildInitialMatchData(fixtureId, latestUpdate.minus(5, SECONDS).toEpochMilli());
  }

  private static OngoingMatchData buildInitialMatchData(String fixtureId, long timestamp) {
    return OngoingMatchData
      .builder()
      .matchId(fixtureId)
      .seqId(1)
      .hasStarted(false)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .matchTime(0)
      .matchPeriod(MatchPeriod.FIRST_HALF)
      .homeScore(0)
      .awayScore(0)
      .teamIdToLineUp(new HashMap<>())
      .timestamp(timestamp)
      .build();
  }

  private static OngoingMatchData mergeMatchData(OngoingMatchData existingMatchData, OngoingMatchData newMatchData,
                                                 boolean validate) {
    if (validate && nonNull(newMatchData.getTimestamp()) && nonNull(existingMatchData.getTimestamp()) &&
      existingMatchData.getTimestamp() > newMatchData.getTimestamp()) {
      log.warn("New data for match {} has an earlier timestamp {} than the data in the cache {}",
        newMatchData.getMatchId(), newMatchData.getTimestamp(), existingMatchData.getTimestamp());
      return existingMatchData;
    }

    log.info("Omar test - newMatchData.getTeamIdToLineUp(): {}", newMatchData.getTeamIdToLineUp());

    return OngoingMatchData
      .builder()
      .matchId(existingMatchData.getMatchId())
      .seqId(newMatchData.getSeqId())
      .timestamp(ofNullable(newMatchData.getTimestamp()).orElse(existingMatchData.getTimestamp()))
      .matchTime(ofNullable(newMatchData.getMatchTime()).orElse(existingMatchData.getMatchTime()))
      .teamIdToLineUp(!newMatchData.getTeamIdToLineUp()
        .isEmpty() ? newMatchData.getTeamIdToLineUp() : existingMatchData.getTeamIdToLineUp())
      .eventIdToPlayerMatchEvents(ofNullable(newMatchData.getEventIdToPlayerMatchEvents()).orElse(
        existingMatchData.getEventIdToPlayerMatchEvents()))
      .processedBetStopEventIds(isNotEmpty(newMatchData.getProcessedBetStopEventIds()) ?
                                newMatchData.getProcessedBetStopEventIds() :
                                existingMatchData.getProcessedBetStopEventIds())
      .matchPeriod(ofNullable(newMatchData.getMatchPeriod()).orElse(existingMatchData.getMatchPeriod()))
      .homeScore(newMatchData.getHomeScore())
      .awayScore(newMatchData.getAwayScore())
      .hasStarted(newMatchData.isHasStarted())
      .fixtureStatus(ofNullable(newMatchData.getFixtureStatus()).orElse(existingMatchData.getFixtureStatus()))
      .latestBetStartTimestamp(
        ofNullable(newMatchData.getLatestBetStartTimestamp()).orElse(existingMatchData.getLatestBetStartTimestamp()))
      .build();
  }
}
