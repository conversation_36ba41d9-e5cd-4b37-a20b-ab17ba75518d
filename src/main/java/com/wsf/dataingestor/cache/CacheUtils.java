package com.wsf.dataingestor.cache;

import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;

import static com.google.common.collect.Maps.newHashMap;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;

@Slf4j
public class CacheUtils {

  public static PlayerMatchData buildOngoingMatchInitialPlayerData(Long timestamp, String playerId, String fixtureId) {
    return buildOngoingMatchPlayerData(timestamp, playerId, fixtureId, newHashMap(), new HashSet<>());
  }

  public static TeamMatchData buildOngoingMatchInitialTeamData(Long timestamp, String teamId, String fixtureId) {
    return buildOngoingMatchTeamData(timestamp, teamId, fixtureId, newHashMap(), new HashSet<>());
  }

  private static PlayerMatchData buildOngoingMatchPlayerData(Long timestamp, String playerId, String fixtureId,
                                                             Map<String, Number> stats, Set<String> processedEvents) {
    return PlayerMatchData
      .builder().playerId(playerId).fixtureId(fixtureId)
      .timestamp(timestamp).stats(stats).processedEvents(processedEvents).build();
  }

  private static TeamMatchData buildOngoingMatchTeamData(Long timestamp, String teamId, String fixtureId,
                                                         Map<String, Number> stats, Set<String> processedEvents) {
    return TeamMatchData
      .builder().teamId(teamId).fixtureId(fixtureId)
      .timestamp(timestamp).stats(stats).processedEvents(processedEvents).build();
  }

  public static Map<String, Number> mergeStats(Map<String, Number> existingData, Map<String, Number> newData) {
    return Stream
      .concat(existingData.keySet()
        .stream(), newData.keySet()
        .stream())
      .distinct()
      .map(key -> {
        Number feedValue = newData.get(key);
        boolean newValueExists = nonNull(feedValue);
        Number cachedValue = ofNullable(existingData.get(key)).orElse(0);
        Number newValue = newValueExists ? feedValue : 0;

        boolean shouldUseCachedValue = newValue.intValue() == 0 && cachedValue.intValue() >= 3;
        boolean useCachedValue = shouldUseCachedValue || !newValueExists;

        Number finalValue = useCachedValue ? cachedValue : newValue;

        if (newValueExists && shouldUseCachedValue) {
          log.warn("Using cached value {} instead of feed value {} for stat {} ", cachedValue.intValue(),
            newValue.intValue(), key);
        }

        return Pair.of(key, finalValue);
      })
      .collect(toMap(Pair::getKey, Pair::getValue));
  }
}
