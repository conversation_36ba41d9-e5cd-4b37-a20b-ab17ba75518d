package com.wsf.dataingestor.cache;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.opta.parsers.events.FeedEvent;

import static com.wsf.dataingestor.config.cache.CacheConstants.OPTA_PLAYERS_EVENTS_CACHE_NAME;
import static java.lang.String.format;

@Service
public class OptaCachedEventService {

  private static final String CACHE_ID_FORMAT = "%s-%s";

  @Cacheable(cacheNames = OPTA_PLAYERS_EVENTS_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.OptaCachedEventService).buildCacheKey(#teamId, #eventId)", unless = "#result == null")
  public EventEntity get(String teamId, String eventId) {
    return null;
  }

  @CachePut(cacheNames = OPTA_PLAYERS_EVENTS_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.OptaCachedEventService).buildCacheKey(#teamId, #event)")
  public EventEntity put(String teamId, FeedEvent event, String playerId) {
    String optaEventId = event.getId();
    String optaPlayerId = event.getPlayerId();
    return new EventEntity(optaEventId, optaPlayerId, playerId, Instant.now());
  }

  public static String buildCacheKey(String teamId, FeedEvent event) {
    return buildCacheKey(teamId, event.getEventId());
  }

  public static String buildCacheKey(String teamId, String eventId) {
    return format(CACHE_ID_FORMAT, teamId, eventId);
  }


  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  @Builder
  public static class EventEntity implements Serializable {
    // difference between externalEventId and cache key
    // externalEventId (feedEvent.getId()) - id of every opta event that is not found in other events as qualifier relation value
    // feedEvent.getEventId() - serves as cache key because when searching for related event from original one, this is the value we get from the qualifiers
    String externalEventId;
    String externalPlayerId;
    String playerId;
    //TODO to be removed after the delay evaluation between substitution events is done
    Instant timestamp;
  }
}