package com.wsf.dataingestor.cache;

import java.util.NoSuchElementException;
import java.util.function.BiFunction;
import java.util.function.Function;

public interface Cache<T> {

  T get(String cacheKey);

  void set(String cacheKey, T cacheData);

  void remove(String cacheKey);

  default boolean contains(String cacheKey) {
    return false;
  }

  default T getOrCompute(String cacheKey, Function<String, T> cacheDataSupplier) {
    return null;
  }

  default void setIfAbsent(String cacheKey, T cacheData) {}

  default T mergeIfExistsOrFail(String cacheKey, T cacheData,
                                BiFunction<T, T, T> mergeFn) throws NoSuchElementException {
    return null;
  }

  default T mergeIfExists(String cacheKey, T cacheData, BiFunction<T, T, T> mergeFn) {
    return null;
  }
}
