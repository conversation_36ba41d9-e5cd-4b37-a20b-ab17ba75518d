package com.wsf.dataingestor.cache;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.cache.models.TeamMatchData;

import static com.wsf.dataingestor.cache.CacheUtils.buildOngoingMatchInitialTeamData;
import static com.wsf.dataingestor.config.cache.CacheConstants.TEAM_DATA_CACHE_NAME;
import static java.lang.String.format;
import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamMatchDataCacheService {

  private static final String CACHE_ID_FORMAT = "%s-%s";

  private final CacheManager cacheManager;

  @Cacheable(cacheNames = TEAM_DATA_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.TeamMatchDataCacheService).buildCacheKey(#fixtureId,#teamId)", unless = "#result == null")
  public TeamMatchData get(String fixtureId, String teamId) {
    return buildOngoingMatchInitialTeamData(Instant.now().toEpochMilli(), teamId, fixtureId);
  }

  public void set(String fixtureId, String teamId, TeamMatchData matchData) {
    if (nonNull(matchData)) {
      cacheManager.getCache(TEAM_DATA_CACHE_NAME).put(buildCacheKey(fixtureId, teamId), matchData);
    }
  }

  @CacheEvict(cacheNames = TEAM_DATA_CACHE_NAME, key = "T(com.wsf.dataingestor.cache.TeamMatchDataCacheService).buildCacheKey(#fixtureId,#teamId)")
  public void remove(String fixtureId, String teamId) {
  }

  public static String buildCacheKey(String fixtureId, String teamId) {
    return format(CACHE_ID_FORMAT, fixtureId, teamId);
  }
}
