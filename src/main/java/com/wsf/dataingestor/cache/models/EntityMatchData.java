package com.wsf.dataingestor.cache.models;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

public interface EntityMatchData extends Serializable {

  String getFixtureId();

  Long getTimestamp();

  Map<String, Number> getStats();

  Set<String> getProcessedEvents();

  boolean isBetStop();

  TreeSet<BetStopInfo> getBetStopsInfo();

  @Data
  @AllArgsConstructor(staticName = "of")
  @NoArgsConstructor
  class BetStopInfo implements Comparable<BetStopInfo> {
    String reason;
    String eventId;
    Instant timestamp;

    @Override
    public int compareTo(BetStopInfo betStopInfo) {
      return this.timestamp.compareTo(betStopInfo.getTimestamp());
    }
  }
}
