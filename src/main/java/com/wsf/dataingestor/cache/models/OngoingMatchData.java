package com.wsf.dataingestor.cache.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.modules.event.ContestantEvent;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Maps.newHashMap;
import static com.google.common.collect.Sets.newHashSet;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.CANCELLED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.POSTPONED;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Data
@NoArgsConstructor
public class OngoingMatchData implements Serializable {
  private String matchId;
  private int seqId;
  private Long timestamp;
  private Integer matchTime;
  private MatchPeriod matchPeriod;
  private int homeScore;
  private int awayScore;
  private boolean hasStarted;
  private Map<String, Set<String>> teamIdToLineUp;
  private Map<String, Set<PlayerMatchEventDTO>> eventIdToPlayerMatchEvents; // eg. goalEventId -> [goalEvent, shotEvent, sogEvent]
  private Set<String> processedBetStopEventIds;
  private MatchDataFeed.FeedFixtureStatus fixtureStatus;
  private Instant latestBetStartTimestamp;

  @Builder
  public OngoingMatchData(String matchId, int seqId, Long timestamp, Integer matchTime, MatchPeriod matchPeriod,
                          int homeScore, int awayScore, boolean hasStarted, Map<String, Set<String>> teamIdToLineUp,
                          Map<String, Set<PlayerMatchEventDTO>> eventIdToPlayerMatchEvents,
                          Set<String> processedBetStopEventIds, MatchDataFeed.FeedFixtureStatus fixtureStatus,
                          Instant latestBetStartTimestamp) {
    this.matchId = matchId;
    this.seqId = seqId;
    this.timestamp = timestamp;
    this.matchTime = matchTime;
    this.matchPeriod = matchPeriod;
    this.homeScore = homeScore;
    this.awayScore = awayScore;
    this.hasStarted = hasStarted;
    this.teamIdToLineUp = ofNullable(teamIdToLineUp).orElse(newHashMap());
    this.eventIdToPlayerMatchEvents = ofNullable(eventIdToPlayerMatchEvents).orElse(newHashMap());
    this.processedBetStopEventIds = ofNullable(processedBetStopEventIds).orElse(newHashSet());
    this.fixtureStatus = fixtureStatus;
    this.latestBetStartTimestamp = latestBetStartTimestamp;
  }

  public boolean isFinished() {
    return this.fixtureStatus == PLAYED || this.fixtureStatus == CANCELLED || this.fixtureStatus == POSTPONED;
  }

  @JsonIgnore
  public Set<String> getLineupPlayerIds() {
    return teamIdToLineUp.values()
      .stream()
      .flatMap(Collection::stream)
      .peek(playerId -> {
        if (playerId.startsWith("UNKNOWN")) {
          log.error("ERROR: playerId: {} is UNKNOWN", playerId);
        }
      })
      // TODO: TEST Restore UNKNOWN in the id and throw exception if there is any id with UNKNOWN
      //      .filter(playerId -> !playerId.startsWith("UNKNOWN"))
      .collect(toSet());
  }

  public List<PlayerMatchEventDTO> getAllPlayerEvents() {
    return this.eventIdToPlayerMatchEvents.values()
      .stream()
      .flatMap(Set::stream)
      .toList();
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  @Builder
  public static class PlayerMatchEventDTO implements ContestantEvent {
    private String eventId;
    private String relatedEventId;
    private String entityId;
    private String teamId;
    private SoccerMatchEvent event;
    private Integer periodId;
    private int timeMin;
    private boolean ignore;
    private boolean isOnBench;
    private boolean isUnknownEntity;

    public PlayerMatchEventDTO(String eventId, String relatedEventId, String entityId, String teamId,
                               SoccerMatchEvent event, Integer periodId, int timeMin, boolean isUnknownEntity) {
      this.eventId = eventId;
      this.relatedEventId = relatedEventId;
      this.entityId = entityId;
      this.teamId = teamId;
      this.event = event;
      this.periodId = periodId;
      this.timeMin = timeMin;
      this.isUnknownEntity = isUnknownEntity;
      this.ignore = false;
      this.isOnBench = false;
    }

    public static PlayerMatchEventDTO fromMatchFeedEvent(EntityEventDTO entityEventDTO) {
      Integer periodId = ofNullable(entityEventDTO.getPeriod())
        .map(MatchPeriod::getPeriodId)
        .orElse(null);
      return new PlayerMatchEventDTO(entityEventDTO.getEventId(), entityEventDTO.getRelatedEventId(),
        entityEventDTO.getEntityId(), entityEventDTO.getTeamId(), entityEventDTO.getEventType(), periodId,
        entityEventDTO.getTimeMin(), entityEventDTO.isIgnore(), entityEventDTO.isOnBench(),
        entityEventDTO.isUnknownEntity());
    }

    @Override
    public String getContestantId() {
      return this.entityId;
    }

    @Override
    public SoccerMatchEvent getEventType() {
      return event;
    }

  }
}
