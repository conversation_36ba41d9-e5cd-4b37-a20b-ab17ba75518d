package com.wsf.dataingestor.cache.models;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Comparator;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import static com.google.common.collect.Maps.newHashMap;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public abstract class BaseEntityMatchData implements EntityMatchData {
  @NotNull
  protected String fixtureId;
  @NotNull
  protected Long timestamp;
  @NotNull
  @Builder.Default
  protected Map<String, Number> stats = newHashMap();
  protected Set<String> processedEvents;
  @NotNull
  @Builder.Default
  protected TreeSet<BetStopInfo> betStopsInfo = new TreeSet<>(Comparator.comparing(BetStopInfo::getTimestamp));

  public boolean isBetStop() {
    return !betStopsInfo.isEmpty();
  }

  public void addBetStop(BetStopInfo betStopInfo) {
    betStopsInfo.add(betStopInfo);
  }

  public void clearBetStops() {
    betStopsInfo.clear();
  }
}
