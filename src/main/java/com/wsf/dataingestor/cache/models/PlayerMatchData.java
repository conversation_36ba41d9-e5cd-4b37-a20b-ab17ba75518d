package com.wsf.dataingestor.cache.models;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import com.wsf.domain.common.Player;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlayerMatchData extends BaseEntityMatchData {
  @NotNull
  private String playerId;
  private Player.DetailedPosition matchPosition;
}
