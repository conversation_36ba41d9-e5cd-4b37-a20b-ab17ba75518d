package com.wsf.dataingestor.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Map;

import static java.util.Objects.nonNull;

public class PositiveMapValuesValidator implements ConstraintValidator<PositiveMapValues, Map<String, Number>> {

  @Override
  public boolean isValid(Map<String, Number> map, ConstraintValidatorContext constraintValidatorContext) {
    return map.values()
      .stream()
      .allMatch(number -> nonNull(number) && number.doubleValue() >= 0);
  }
}
