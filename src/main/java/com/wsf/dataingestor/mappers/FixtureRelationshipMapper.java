package com.wsf.dataingestor.mappers;

import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.domain.common.Fixture.Relation;

import static com.wsf.domain.common.Fixture.Relation.FIRST_LEG;
import static com.wsf.domain.common.Fixture.Relation.SECOND_LEG;
import static com.wsf.domain.common.Fixture.Relation.SINGLE;
import static java.util.Optional.ofNullable;

public class FixtureRelationshipMapper {
  public static Relation mapDtoRelationToDbRelation(FixtureDTO.Relation fixtureRelation) {
    return ofNullable(fixtureRelation)
      .map(leg -> {
        return switch (leg) {
          case FIRST_LEG -> FIRST_LEG;
          case SECOND_LEG -> SECOND_LEG;
          case SINGLE -> SINGLE;
        };
      })
      .orElse(SINGLE);
  }
}
