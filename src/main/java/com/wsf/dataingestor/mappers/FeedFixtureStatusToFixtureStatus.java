package com.wsf.dataingestor.mappers;

import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.kafka.domain.FixtureStatus;

public class FeedFixtureStatusToFixtureStatus {
  public static FixtureStatus mapToFixtureStatus(FeedFixtureStatus fixtureStatus) {
    switch (fixtureStatus) {
      case FIXTURE:
        return FixtureStatus.FIXTURE;
      case LIVE:
        return FixtureStatus.LIVE;
      case PLAYED:
        return FixtureStatus.PLAYED;
      case CANCELLED:
        return FixtureStatus.CANCELLED;
      case BET_END:
        return FixtureStatus.BET_END;
      case POSTPONED:
        return FixtureStatus.POSTPONED;
      case SUSPENDED:
        return FixtureStatus.SUSPENDED;
      default:
        throw new IllegalArgumentException("Unsupported FeedFixtureStatus: " + fixtureStatus);
    }
  }
}
