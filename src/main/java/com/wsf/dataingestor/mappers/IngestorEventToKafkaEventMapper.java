package com.wsf.dataingestor.mappers;

import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.kafka.domain.FixturePeriod;
import com.wsf.kafka.domain.FixtureSummaryData.Event;

import static com.wsf.kafka.domain.FixturePeriod.END_MATCH;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;

public class IngestorEventToKafkaEventMapper {

  public static Event mapEntityEventDtoToKafkaEvent(EntityEventDTO entityEventDTO) {
    String eventId = entityEventDTO.getEventId();
    String contestantId = entityEventDTO.getEntityId();
    String parentContestantId = entityEventDTO.getTeamId();
    String eventType = entityEventDTO.getEventType().getStatisticName();
    FixturePeriod fixturePeriod = mapFixturePeriod(entityEventDTO.getPeriod());
    int matchTime = entityEventDTO.getTimeMin();

    return new Event(eventId, contestantId, parentContestantId, eventType, fixturePeriod, matchTime);
  }

  public static Event mapPlayerMatchEventDtoToKafkaEvent(PlayerMatchEventDTO playerMatchEventDTO) {
    String eventId = playerMatchEventDTO.getEventId();
    String contestantId = playerMatchEventDTO.getEntityId();
    String parentContestantId = playerMatchEventDTO.getTeamId();
    String eventType = playerMatchEventDTO.getEvent().getStatisticName();
    MatchPeriod matchPeriod = ofNullable(playerMatchEventDTO.getPeriodId())
      .map(MatchPeriod::getMatchPeriod)
      .orElse(null);
    var fixturePeriod = mapFixturePeriod(matchPeriod);
    int matchTime = playerMatchEventDTO.getTimeMin();

    return new Event(eventId, contestantId, parentContestantId, eventType, fixturePeriod, matchTime);
  }

  private static FixturePeriod mapFixturePeriod(MatchPeriod matchPeriod) {
    if (isNull(matchPeriod)) {
      return null;
    }
    return switch (matchPeriod) {
      case FIRST_HALF -> FixturePeriod.FIRST_HALF;
      case SECOND_HALF -> FixturePeriod.SECOND_HALF;
      case END_REGULAR_TIMES -> FixturePeriod.END_REGULAR_TIMES;
      case END_MATCH -> END_MATCH;
      case EXTRA_FIRST_HALF -> FixturePeriod.EXTRA_FIRST_HALF;
      case HALF_TIME -> FixturePeriod.HALF_TIME;
      case EXTRA_HALF_TIME -> FixturePeriod.EXTRA_HALF_TIME;
      case EXTRA_SECOND_HALF -> FixturePeriod.EXTRA_SECOND_HALF;
    };
  }
}
