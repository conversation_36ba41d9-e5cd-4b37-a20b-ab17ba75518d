package com.wsf.dataingestor.mappers;

import java.util.Map;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.EntityDataDTO;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.FixtureSummaryData.ContestantStats;

import static com.wsf.kafka.domain.ContestantType.SOCCER_PLAYER;
import static com.wsf.kafka.domain.ContestantType.SOCCER_TEAM;

public class EntityDataDTOToContestantStats {
  public static ContestantStats map(EntityDataDTO entityDataDTO) {
    String contestantId = entityDataDTO.getEntityId();
    com.wsf.kafka.domain.ContestantType contestantType = convert(entityDataDTO.getContestantType());
    Map<String, Number> stats = entityDataDTO.getStats();

    return new ContestantStats(contestantId, contestantType, true, entityDataDTO.isUnknown(), stats);
  }

  public static ContestantStats map(Player player) {
    String contestantId = player.getIdAsString();
    return new ContestantStats(contestantId, SOCCER_PLAYER, false, false, Map.of());
  }

  private static com.wsf.kafka.domain.ContestantType convert(ContestantType contestantType) {
    return switch (contestantType) {
      case SOCCER_TEAM -> SOCCER_TEAM;
      case SOCCER_PLAYER -> SOCCER_PLAYER;
    };
  }
}
