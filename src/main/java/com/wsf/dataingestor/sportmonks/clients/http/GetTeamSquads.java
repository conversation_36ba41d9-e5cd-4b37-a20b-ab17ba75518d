package com.wsf.dataingestor.sportmonks.clients.http;

import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public class GetTeamSquads extends BaseSportmonksHttpClient {

  @Autowired
  public GetTeamSquads(RetryableHttpClient retryableHttpClient, @Value("${sportmonks.api.v3.host}") String apiHost,
                       @Value("${sportmonks.api.token}") String apiToken) {
    super(retryableHttpClient, apiHost, apiToken);
  }

  @Override
  public byte[] getFeed(String seasonId) throws IOException {
    String endpoint = String.format("/v3/football/teams/seasons/%s", seasonId);
    return retrieveFeed(endpoint, List.of("activeSeasons;players;players.player.metadata"));
  }
}
