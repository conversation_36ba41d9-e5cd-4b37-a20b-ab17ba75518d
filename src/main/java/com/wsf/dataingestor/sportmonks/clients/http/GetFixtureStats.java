package com.wsf.dataingestor.sportmonks.clients.http;

import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public class GetFixtureStats extends BaseSportmonksHttpClient {

  @Autowired
  public GetFixtureStats(RetryableHttpClient retryableHttpClient, @Value("${sportmonks.api.v3.host}") String apiHost,
                         @Value("${sportmonks.api.token}") String apiToken) {
    super(retryableHttpClient, apiHost, apiToken);
  }

  @Override
  public byte[] getFeed(String fixtureId) throws IOException {
    String endpoint = String.format("/v3/football/fixtures/%s", fixtureId);
    return retrieveFeed(endpoint, List.of("formations;scores;state;statistics;lineups.details;events.period"));
  }
}
