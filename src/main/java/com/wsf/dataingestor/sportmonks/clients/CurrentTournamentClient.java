package com.wsf.dataingestor.sportmonks.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.sportmonks.clients.http.GetLeagueById;
import com.wsf.dataingestor.sportmonks.parsers.LeagueFeedParser;

@Service
public class CurrentTournamentClient extends BasePullAPIClient<CurrentTournamentFeed> {

  @Autowired
  public CurrentTournamentClient(GetLeagueById httpFeedClient, LeagueFeedParser feedParser,
                                 FeedStoreService feedStoreService) {
    super(httpFeedClient, feedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(CurrentTournamentFeed parsedFeed) {
    return "sportmonks/league/" + parsedFeed.getFeedId() + ".json";
  }
}
