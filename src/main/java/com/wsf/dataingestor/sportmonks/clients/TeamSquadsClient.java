package com.wsf.dataingestor.sportmonks.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.sportmonks.clients.http.GetTeamSquads;
import com.wsf.dataingestor.sportmonks.parsers.TeamsFeedParser;

@Service
public class TeamSquadsClient extends BasePullAPIClient<SquadsFeed> {

  @Autowired
  public TeamSquadsClient(GetTeamSquads httpFeedClient, TeamsFeedParser feedParser, FeedStoreService feedStoreService) {
    super(httpFeedClient, feedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(SquadsFeed parsedFeed) {
    return "sportmonks/teamsquads/" + parsedFeed.getFeedId() + ".json";
  }
}
