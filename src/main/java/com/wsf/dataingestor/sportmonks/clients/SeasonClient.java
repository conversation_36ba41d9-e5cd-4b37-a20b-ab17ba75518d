package com.wsf.dataingestor.sportmonks.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.sportmonks.clients.http.GetSeasonById;
import com.wsf.dataingestor.sportmonks.parsers.SeasonFeedParser;

@Service
public class SeasonClient extends BasePullAPIClient<FixturesFeed> {

  @Autowired
  public SeasonClient(GetSeasonById httpFeedClient, SeasonFeedParser seasonFeedParser,
                      FeedStoreService feedStoreService) {
    super(httpFeedClient, seasonFeedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(FixturesFeed parsedFeed) {
    return "sportmonks/season/" + parsedFeed.getFeedId() + ".json";
  }
}
