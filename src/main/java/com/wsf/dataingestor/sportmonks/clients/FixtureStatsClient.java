package com.wsf.dataingestor.sportmonks.clients;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.clients.BasePullAPIClient;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.sportmonks.clients.http.GetFixtureStats;
import com.wsf.dataingestor.sportmonks.parsers.StatsFeedParser;

@Service
public class FixtureStatsClient extends BasePullAPIClient<MatchDataFeed> {

  @Autowired
  public FixtureStatsClient(GetFixtureStats httpFeedClient, StatsFeedParser feedParser,
                            FeedStoreService feedStoreService) {
    super(httpFeedClient, feedParser, feedStoreService);
  }

  @Override
  protected String getFeedStorePath(MatchDataFeed parsedFeed) {
    return "sportmonks/fixturestats/" + parsedFeed.getFeedId() + ".json";
  }
}
