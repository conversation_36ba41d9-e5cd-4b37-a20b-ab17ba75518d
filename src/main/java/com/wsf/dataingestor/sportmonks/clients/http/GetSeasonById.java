package com.wsf.dataingestor.sportmonks.clients.http;

import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public class GetSeasonById extends BaseSportmonksHttpClient {

  @Autowired
  public GetSeasonById(RetryableHttpClient retryableHttpClient, @Value("${sportmonks.api.v3.host}") String apiHost,
                       @Value("${sportmonks.api.token}") String apiToken) {
    super(retryableHttpClient, apiHost, apiToken);
  }

  @Override
  public byte[] getFeed(String seasonId) throws IOException {
    String endpoint = String.format("/v3/football/seasons/%s", seasonId);
    return retrieveFeed(endpoint, List.of("fixtures;fixtures.participants;fixtures.state;stages;stages.type"));
  }
}
