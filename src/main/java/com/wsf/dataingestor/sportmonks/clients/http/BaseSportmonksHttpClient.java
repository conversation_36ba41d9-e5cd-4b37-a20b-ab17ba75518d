package com.wsf.dataingestor.sportmonks.clients.http;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpRequest;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.util.UriComponentsBuilder;
import com.wsf.dataingestor.clients.http.HttpFeedClient;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

import static java.lang.String.format;

@Slf4j
@RequiredArgsConstructor
public abstract class BaseSportmonksHttpClient implements HttpFeedClient {

  private final RetryableHttpClient retryableHttpClient;
  private final String apiHost;
  private final String apiToken;

  public byte[] retrieveFeed(String apiEndpoint, List<String> includes) throws IOException {

    URI uri = UriComponentsBuilder
      .newInstance()
      .scheme("https")
      .host(apiHost)
      .port(443)
      .path(apiEndpoint)
      .queryParam("api_token", apiToken)
      .queryParam("include", String.join(",", includes))
      .build()
      .toUri();

    var request = HttpRequest.newBuilder().uri(uri).build();

    try {

      var response = retryableHttpClient.send(request);

      if (response.statusCode() == HttpStatus.OK.value()) {
        return response.body();
      } else {
        String body = new String(response.body());
        throw new IOException(
          format("Error while calling url: %s. HttpCode: %s Body: %s", uri, response.statusCode(), body));
      }
    } catch (InterruptedException e) {
      throw new IOException(format("Error while calling url: %s", uri.toString()), e);
    }
  }

}
