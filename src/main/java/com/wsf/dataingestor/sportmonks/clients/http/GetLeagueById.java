package com.wsf.dataingestor.sportmonks.clients.http;

import java.io.IOException;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.utils.http.RetryableHttpClient;

@Service
public class GetLeagueById extends BaseSportmonksHttpClient {

  @Autowired
  public GetLeagueById(RetryableHttpClient retryableHttpClient, @Value("${sportmonks.api.v3.host}") String apiHost,
                       @Value("${sportmonks.api.token}") String apiToken) {
    super(retryableHttpClient, apiHost, apiToken);
  }

  @Override
  public byte[] getFeed(String entityId) throws IOException {
    String endpoint = String.format("/v3/football/leagues/%s", entityId);
    return retrieveFeed(endpoint, List.of("currentSeason"));
  }
}
