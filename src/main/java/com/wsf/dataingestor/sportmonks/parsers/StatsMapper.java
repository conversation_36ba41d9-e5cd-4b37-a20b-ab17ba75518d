package com.wsf.dataingestor.sportmonks.parsers;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import com.wsf.dataingestor.parsers.ParsersUtils;

import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.dataingestor.sports.soccer.Constants.SHOTS_BLOCKED;
import static com.wsf.dataingestor.sports.soccer.Constants.SHOTS_OFF_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.COMPLETED_PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;

public class StatsMapper {

  private static final Set<String> SHOTS_STATS = Set.of(SHOT.getStatisticName(), SHOT_ON_GOAL.getStatisticName());

  private static final Set<String> TEAM_SHOTS_STATS = Set.of(TEAM_SHOTS, TEAM_SHOTS_ON_GOAL);

  public static final String SM_GOALS = "52";

  public static final String SM_SHOTS = "42";

  public static final String SM_SHOTS_ON_GOAL = "86";

  public static final String SM_SHOTS_OFF_GOAL = "41";

  public static final String SM_SHOTS_BLOCKED = "58";

  public static final String SM_FOULS = "56";

  public static final String SM_PASSES = "80";

  public static final String SM_COMPLETED_PASSES = "116";

  public static final String SM_MINS_PLAYED = "119";

  public static final String SM_CORNERS = "34";

  public static final String SM_OFFSIDES = "51";

  public static final Map<String, String> SM_TO_WSF_PLAYER_STATS = new HashMap<>();
  public static final Map<String, String> SM_TO_WSF_TEAM_STATS = new HashMap<>();

  static {
    SM_TO_WSF_PLAYER_STATS.put(SM_SHOTS, SHOT.getStatisticName());
    SM_TO_WSF_PLAYER_STATS.put(SM_SHOTS_ON_GOAL, SHOT_ON_GOAL.getStatisticName());
    SM_TO_WSF_PLAYER_STATS.put(SM_SHOTS_OFF_GOAL, SHOTS_OFF_GOAL);
    SM_TO_WSF_PLAYER_STATS.put(SM_SHOTS_BLOCKED, SHOTS_BLOCKED);
    SM_TO_WSF_PLAYER_STATS.put(SM_FOULS, FOUL.getStatisticName());
    SM_TO_WSF_PLAYER_STATS.put(SM_PASSES, PASS.getStatisticName());
    SM_TO_WSF_PLAYER_STATS.put(SM_COMPLETED_PASSES, COMPLETED_PASS.getStatisticName());
    SM_TO_WSF_PLAYER_STATS.put(SM_MINS_PLAYED, MINS_PLAYED);
    // player yellow cards are aggregated from the events

    SM_TO_WSF_TEAM_STATS.put(SM_GOALS, TEAM_GOALS);
    SM_TO_WSF_TEAM_STATS.put(SM_SHOTS, TEAM_SHOTS);
    SM_TO_WSF_TEAM_STATS.put(SM_SHOTS_ON_GOAL, TEAM_SHOTS_ON_GOAL);
    SM_TO_WSF_TEAM_STATS.put(SM_CORNERS, TEAM_CORNERS);
    SM_TO_WSF_TEAM_STATS.put(SM_FOULS, TEAM_FOULS);
    SM_TO_WSF_TEAM_STATS.put(SM_OFFSIDES, TEAM_OFFSIDES);
    // team yellow cards are aggregated from the events
  }

  public static Map<String, Number> translatePlayerStats(Map<String, String> stats, Set<String> supportedStats) {
    Map<String, Number> translatedStats = ParsersUtils.translateStatsWith0Fallback(stats, SM_TO_WSF_PLAYER_STATS);

    if (supportedStats.containsAll(SHOTS_STATS)) {
      processShotsStats(stats, translatedStats);
    }

    translatedStats.entrySet().removeIf(e -> !supportedStats.contains(e.getKey()));

    return translatedStats;
  }

  public static Map<String, Number> translateTeamStats(Map<String, String> stats, Set<String> supportedStats) {
    Map<String, Number> translatedStats = ParsersUtils.translateStatsWith0Fallback(stats, SM_TO_WSF_TEAM_STATS);

    if (supportedStats.containsAll(TEAM_SHOTS_STATS)) {
      processShotsStats(stats, translatedStats);
    }

    translatedStats.entrySet().removeIf(e -> !supportedStats.contains(e.getKey()));

    return translatedStats;
  }

  /* This complex logic to calculate the nr of total shots in the game is the result of an analysis carried out
   * on the sportmonks feed in order to get the most similar value to the one published on SofaScore. */
  private static void processShotsStats(Map<String, String> stats, Map<String, Number> translatedStats) {
    Number shots = parseIntOrNull(SM_SHOTS, stats);
    Number shotsOnGoal = parseIntOrNull(SM_SHOTS_ON_GOAL, stats);
    Number shotsOffGoal = parseIntOrNull(SM_SHOTS_OFF_GOAL, stats);
    Number shotsBlocked = parseIntOrNull(SM_SHOTS_BLOCKED, stats);
    int totalShots;
    if (nonNull(shotsOnGoal)) {
      if (nonNull(shotsOffGoal)) {
        totalShots = shotsOnGoal.intValue() + shotsOffGoal.intValue();
      } else {
        if (isNull(shots) || shots.intValue() < shotsOnGoal.intValue()) {
          totalShots = shotsOnGoal.intValue();
        } else { // shots exists and it's higher than shotsOnGoal
          totalShots = shots.intValue();
        }
      }
    } else if (nonNull(shots)) {
      totalShots = shots.intValue();
    } else if (nonNull(shotsOffGoal) && nonNull(shotsBlocked) && shotsOffGoal.equals(shotsBlocked)) {
      totalShots = 0;
    } else {
      totalShots = 0;
    }
    translatedStats.put(SHOT.getStatisticName(), totalShots);
  }

  private static Integer parseIntOrNull(String field, Map<String, String> stats) {
    return ofNullable(stats.get(field))
      .map(Integer::parseInt)
      .orElse(null);
  }
}
