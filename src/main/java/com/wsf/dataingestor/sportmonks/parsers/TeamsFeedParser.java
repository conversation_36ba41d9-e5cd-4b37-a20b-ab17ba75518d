package com.wsf.dataingestor.sportmonks.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.sportmonks.models.TeamsFeed;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.models.SquadsFeed.FeedProvider.SPORTMONKS;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
public class TeamsFeedParser implements FeedParser<SquadsFeed> {

  private static final DateTimeFormatter BIRTH_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
  private final ObjectMapper mapper;

  @Override
  public SquadsFeed parseFeed(byte[] feed) throws IOException {
    log.debug("Parsing SM TeamSquads feed: {}", feed);
    return parseFeed(mapper.readValue(feed, TeamsFeed.class));
  }

  @Override
  public SquadsFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing SM TeamSquads feed: {}", feed);
    return parseFeed(mapper.readValue(feed, TeamsFeed.class));
  }

  private SquadsFeed parseFeed(TeamsFeed feed) {
    if (feed.getData()
      .isEmpty()) {
      log.error("no data from feed {}", feed);
      return null;
    }

    if (feed.getData().get(0).getActiveSeasons()
      .isEmpty()) {
      throw new IllegalArgumentException("could not find activeSeason");
    }

    String smSeasonId = feed.getData().get(0).getActiveSeasons().get(0).getSeasonId();
    String feedId = buildFeedId("teams", smSeasonId, abs(feed.hashCode()), now().toEpochMilli());
    log.info("processing feedId={}", feedId);

    List<SquadPlayerDTO> tournamentPlayers = feed.getData()
      .stream()
      .map(squadData -> getTeamPlayers(squadData, feedId))
      .flatMap(List::stream)
      .collect(groupingBy(SquadPlayerDTO::getPlayerId)).entrySet()
      .stream()
      .peek(e -> {
        List<SquadPlayerDTO> squadDto = e.getValue();
        if (squadDto.size() > 1) {
          printDuplicatedPlayerLog(feedId, squadDto);
        }
      })
      .filter(e -> e.getValue()
        .size() == 1)
      .flatMap(e -> e.getValue()
        .stream())
      .collect(toList());

    return SquadsFeed
      .builder().feedId(feedId).squadPlayers(tournamentPlayers).provider(SPORTMONKS).build();
  }

  private static void printDuplicatedPlayerLog(String feedId, List<SquadPlayerDTO> squadDto) {
    SquadPlayerDTO player = squadDto.get(0);
    String teamLogs = squadDto
      .stream()
      .map(SquadPlayerDTO::getTeam)
      .map(team -> format("teamId=%s, teamName=%s", team.getExternalTeamId(), team.getExternalTeamName()))
      .collect(Collectors.joining(",", "(", ")"));
    log.warn("Sportmonks {} - Duplicated player: playerId={}, firstName={} lastName={}, matchName={} is in {}", feedId,
      player.getPlayerId(), player.getFirstName(), player.getLastName(), player.getMatchName(), teamLogs);
  }

  private List<SquadPlayerDTO> getTeamPlayers(TeamsFeed.SquadData squadData, String feedId) {
    String smTeamId = squadData.getTeamId();
    String smTeamName = squadData.getTeamName();
    String smTeamAbbreviation = squadData.getTeamAbbreviation();

    SquadPlayerDTO.TeamDTO teamDTO = SquadPlayerDTO.TeamDTO.of(smTeamId, smTeamName, smTeamAbbreviation);
    return parsePlayers(squadData, teamDTO, feedId);
  }

  private List<SquadPlayerDTO> parsePlayers(TeamsFeed.SquadData squadData, SquadPlayerDTO.TeamDTO team, String feedId) {
    return squadData.getPlayers()
      .stream()
      .map(player -> {
        TeamsFeed.PlayerInfo playerInfo = player.getPlayer();
        try {
          validatePlayer(squadData, player);

          String firstName = playerInfo.getFirstName();
          String lastName = playerInfo.getLastName();
          String matchName = playerInfo.getCommonName();

          LocalDate birthDate = parseBirthDate(playerInfo.getBirthDate());

          Player.Position position = parsePosition(player.getPositionId());
          Player.DetailedPosition detailedPosition = parseDetailedPosition(player.getDetailedPositionId());

          return SquadPlayerDTO
            .builder()
            .feedId(feedId)
            .playerId(player.getPlayerId())
            .firstName(firstName)
            .lastName(lastName)
            .matchName(matchName)
            .birthDate(birthDate)
            .team(team)
            .position(position)
            .detailedPosition(detailedPosition)
            .provider(SquadPlayerDTO.Provider.SPORTMONKS)
            .build();
        } catch (PlayerValidationException e) {
          log.warn("Skipping player {} due to validation exception: {}", player.getPlayerId(), e.getMessage());
          return null;
        } catch (PositionNotSupported e) {
          log.debug("Skipping not supported position {}.", player);
          return null;
        } catch (Exception e) {
          log.error("Skipping player {}. Error: ", player, e);
          return null;
        }
      })
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private static void validatePlayer(TeamsFeed.SquadData squadData,
                                     TeamsFeed.PlayerData player) throws PlayerValidationException {
    if (ofNullable(player.getTeamId())
      .map(teamId -> !teamId.equals(squadData.getTeamId()))
      .orElse(false)) {
      throw new PlayerValidationException(
        String.format("Different teamId=%s than the team they seem to belong to teamId=%s", player.getTeamId(),
          squadData.getTeamId()));
    }
  }

  private static LocalDate parseBirthDate(String birthDate) throws PlayerValidationException {
    try {
      return ofNullable(birthDate)
        .map(bd -> LocalDate.parse(bd, BIRTH_DATE_FORMAT))
        .orElse(null);
    } catch (DateTimeParseException e) {
      throw new PlayerValidationException(String.format("Error parsing birthDate: %s", e.getMessage()));
    }
  }

  private static Player.Position parsePosition(Integer positionId) throws PositionNotSupported {
    if (isNull(positionId)) {
      throw new IllegalArgumentException("PositionId is null");
    }

    if (positionId >= 221) {
      throw new PositionNotSupported("Coach position not supported");
    }

    switch (positionId) {
      case 24:
        return Player.Position.GOALKEEPER;
      case 25:
        return Player.Position.DEFENDER;
      case 26:
        return Player.Position.MIDFIELDER;
      case 27:
        return Player.Position.FORWARD;
      default:
        throw new IllegalArgumentException(format("PositionId %s is unknown", positionId));
    }
  }

  private static Player.DetailedPosition parseDetailedPosition(Integer detailedPositionId) {
    if (isNull(detailedPositionId)) {
      return null;
    }
    if (detailedPositionId >= 221) {
      log.warn("Detailed position is null or not supported: {}", detailedPositionId);
      return null;
    }

    switch (detailedPositionId) {
      case 24:
      case 25:
      case 26:
      case 27:
      case 28:
        // normal positions
        return null;
      case 148:
        return Player.DetailedPosition.CENTRE_BACK;
      case 149:
        return Player.DetailedPosition.DEFENSIVE_MIDFIELDER;
      case 150:
        return Player.DetailedPosition.ATTACKING_MIDFIELDER;
      case 151:
      case 163:
        return Player.DetailedPosition.STRIKER;
      case 152:
      case 156:
        return Player.DetailedPosition.WING_FORWARD;
      case 153:
      case 157:
      case 158:
        return Player.DetailedPosition.CENTRE_MIDFIELDER;
      case 154:
      case 155:
        return Player.DetailedPosition.WING_BACK;
      default:
        log.warn("Detailed position not supported: {}", detailedPositionId);
        return null;
    }
  }

  static class PositionNotSupported extends Exception {
    public PositionNotSupported(String message) {
      super(message);
    }
  }

  static class PlayerValidationException extends Exception {
    public PlayerValidationException(String message) {
      super(message);
    }
  }
}
