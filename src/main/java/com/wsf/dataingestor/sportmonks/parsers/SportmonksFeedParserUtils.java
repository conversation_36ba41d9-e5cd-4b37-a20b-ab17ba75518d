package com.wsf.dataingestor.sportmonks.parsers;

import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import java.util.function.Supplier;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.StageService;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static com.wsf.domain.common.ExternalProvider.SPORTMONKS;
import static java.lang.String.format;

@Service
@RequiredArgsConstructor
public class SportmonksFeedParserUtils {

  private final CompetitionService competitionService;
  private final TournamentService tournamentService;
  private final FixtureService fixtureService;
  private final TeamService teamService;
  private final StageService stageService;
  private final PlayerService playerService;

  public Fixture getFixtureBySportmonksIdOrTournamentAndTeamsAndDate(String smFixtureId, String tournamentId,
                                                                     String homeTeamId, String awayTeamId,
                                                                     Instant time) {
    return fixtureService
      .getFixtureBySportmonksIdOrTournamentAndTeamsAndDate(smFixtureId, tournamentId, homeTeamId, awayTeamId, time)
      .orElse(null);
  }

  public Fixture getFixtureBySportmonksFixtureId(String smFixtureId) {
    return fixtureService.findBySportmonksFixtureId(smFixtureId)
      .orElse(null);
  }

  public Team getTeam(String competitionId, String smTeamId) {
    return teamService.findTeamBySportmonksId(competitionId, smTeamId);
  }

  public Team getTeamOrCreateUnmapped(String competitionId, String smTeamId, String smTeamName,
                                      String smTeamAbbreviation) {
    return teamService.findByExternalIdOrCreateUnmapped(competitionId, smTeamId, smTeamName, smTeamAbbreviation,
      SPORTMONKS, teamService::findMasterTeamBySportmonksId);
  }

  public Competition getCompetition(String smCompetitionId) {
    return competitionService.findBySportmonksCompetitionId(smCompetitionId);
  }

  public Tournament getTournament(String smSeasonId) {
    return tournamentService.findBySportmonksExternalId(smSeasonId)
      .orElse(null);
  }

  public Tournament getTournamentByExternalSportmonksId(String sportmonksId) {
    return tournamentService.findBySportmonksExternalId(sportmonksId)
      .orElse(null);
  }

  public Fixture getFixtureOrThrow(String fixtureId) {
    return fixtureService.findBySportmonksFixtureId(fixtureId)
      .orElseThrow(
        () -> new IllegalArgumentException(format("could not find fixture with sportmonksId=%s in db", fixtureId)));
  }

  public MasterPlayer getMasterPlayerOrCreateUnmapped(String externalFirstName, String externalLastName,
                                                      String externalMatchName, LocalDate birthDate,
                                                      String externalPlayerId) {
    Supplier<MasterPlayer> masterPlayerBuilder = () -> buildMasterPlayer(externalFirstName, externalLastName,
      externalMatchName, birthDate, externalPlayerId);
    return playerService.findMasterPlayerByExternalIdOrCreateUnmapped(externalFirstName, externalLastName,
      externalMatchName, birthDate, externalPlayerId, "sportmonksPlayerId", SPORTMONKS,
      playerService::findMasterPlayerBySportmonksPlayerId, masterPlayerBuilder);
  }

  public Player getPlayerBySmPlayerId(String smPlayerId, Fixture fixture) {
    String competitionId = fixture.getTournament().getCompetitionId();
    String tournamentId = fixture.getTournament().getId().toString();
    return playerService.findBySportmonksPlayerIdAndTournamentId(competitionId, tournamentId, smPlayerId);
  }

  private static MasterPlayer buildMasterPlayer(String firstName, String lastName, String matchName,
                                                LocalDate birthDate, String smPlayerId) {
    return PlayerService
      .buildMasterPlayer(firstName, lastName, matchName, birthDate)
      .sportmonksPlayerId(smPlayerId)
      .build();
  }

  public Optional<Player> getPlayerBySportMonksPlayerNameAndTeamId(String competitionId, String smTeamId,
                                                                   String playerName) {
    Team team = teamService.findTeamBySportmonksId(competitionId, smTeamId);
    return playerService.findPlayerByFullNameOrMatchName(competitionId, playerName, team.getIdAsString());
  }

  public Stage getStageOrCreateUnmapped(String competitionId, String smStageId, String smStageName) {
    return stageService.findBySportmonksStageNameOrCreateUnmapped(smStageId, smStageName, competitionId, SPORTMONKS);
  }
}
