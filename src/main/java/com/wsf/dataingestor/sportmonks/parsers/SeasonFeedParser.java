package com.wsf.dataingestor.sportmonks.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixtureDTO.Relation;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.sportmonks.models.SeasonFeed;
import com.wsf.dataingestor.sportmonks.models.SeasonFeed.SportmonksFixture;
import com.wsf.dataingestor.sportmonks.models.SeasonFeed.Stage;

import static com.wsf.dataingestor.models.FixtureDTO.Relation.FIRST_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SECOND_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SINGLE;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static com.wsf.dataingestor.sportmonks.parsers.ParserUtils.parseFixtureStatus;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.function.Function.identity;
import static java.util.function.Predicate.isEqual;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class SeasonFeedParser implements FeedParser<FixturesFeed> {
  public static final String FEED_CODE = "season";
  public static final String SM_RELATION_SINGLE = "1/1";
  public static final String SM_RELATION_FIRST = "1/2";
  public static final String SM_RELATION_SECOND = "2/2";
  public static final String GROUP_STAGE = "Group Stage";
  public static final String FINAL = "Final";
  public static final String HOME_LOCATION = "home";
  public static final String AWAY_LOCATION = "away";
  private final ObjectMapper mapper;
  private final SportmonksFeedParserUtils smFeedParserUtils;

  @Override
  public FixturesFeed parseFeed(byte[] feed) throws IOException {
    log.debug("Parsing SM League feed: {}", feed);
    return parseFeed(mapper.readValue(feed, SeasonFeed.class));
  }

  @Override
  public FixturesFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing SM League feed: {}", feed);
    return parseFeed(mapper.readValue(feed, SeasonFeed.class));
  }

  private FixturesFeed parseFeed(SeasonFeed feed) {
    var season = feed.getData();

    var tournament = ofNullable(smFeedParserUtils.getTournamentByExternalSportmonksId(season.getId())).orElseThrow(
      () -> new IllegalArgumentException(format("Tournament %s not found", season.getId())));

    var stageIdToStage = season.getStages()
      .stream()
      .collect(toMap(Stage::getId, identity()));

    var fixtureIdToRelatedFixtures = season.getFixtures()
      .stream()
      .filter(SeasonFeedParser::isNotPlaceholder)
      .filter(sportmonksFixture -> nonNull(sportmonksFixture.getAggregateId()))
      .collect(groupingBy(SportmonksFixture::getAggregateId, toList()));

    var fixtureDTOS = season.getFixtures()
      .stream()
      .filter(SeasonFeedParser::isNotPlaceholder)
      .map(sportmonksFixture -> processFixture(stageIdToStage, sportmonksFixture, fixtureIdToRelatedFixtures))
      .filter(Objects::nonNull)
      .collect(toList());

    String feedId = buildFeedId(FEED_CODE, season.getId(), abs(feed.hashCode()), now().toEpochMilli());
    return FixturesFeed
      .builder().feedId(feedId).tournament(tournament).fixtures(fixtureDTOS).build();
  }

  private static boolean isNotPlaceholder(SportmonksFixture sportmonksFixture) {
    Boolean isPlaceholder = sportmonksFixture.getPlaceholder();
    return isPlaceholder == null || !isPlaceholder;
  }

  private FixtureDTO processFixture(Map<Integer, Stage> stageIdToStage, SportmonksFixture sportmonksFixture,
                                    Map<Integer, List<SportmonksFixture>> fixtureIdToRelatedFixtures) {
    String smFixtureId = String.valueOf(sportmonksFixture.getId());
    try {
      var relation = relationFromString(sportmonksFixture.getLeg());

      Stage stage = stageIdToStage.get(sportmonksFixture.getStageId());

      String relatedFixtureId = getRelatedFixtureId(fixtureIdToRelatedFixtures, sportmonksFixture);

      boolean canGoExtraTime = !GROUP_STAGE.equalsIgnoreCase(stage.getType().getName()) &&
        (FINAL.equalsIgnoreCase(stage.getName()) || FIRST_LEG != relation);

      MatchDataFeed.FeedFixtureStatus fixtureStatus = parseFixtureStatus(sportmonksFixture.getState().getState());
      Instant time = Instant.ofEpochSecond(sportmonksFixture.getTimestamp());

      SeasonFeed.Participant homeTeam = parseTeam(sportmonksFixture, HOME_LOCATION);
      SeasonFeed.Participant awayTeam = parseTeam(sportmonksFixture, AWAY_LOCATION);

      return FixtureDTO
        .builder()
        .externalFixtureId(smFixtureId)
        .externalRelatedFixtureId(relatedFixtureId)
        .externalHomeTeamId(String.valueOf(homeTeam.getId()))
        .externalHomeTeamName(homeTeam.getName())
        .externalHomeTeamAbbreviation(homeTeam.getAbbreviation())
        .externalAwayTeamId(String.valueOf(awayTeam.getId()))
        .externalAwayTeamName(awayTeam.getName())
        .externalAwayTeamAbbreviation(awayTeam.getAbbreviation())
        .externalStageName(stage.getName())
        .externalStageId(stage.getId().toString())
        .fixtureStatus(fixtureStatus)
        .time(time)
        .leg(relation)
        .canGoExtraTime(canGoExtraTime)
        .isLiveSupported(false)
        .isNeutralVenue(false) // Sportmonks has this field but it's unreliable, we decided to always set it to false
        .provider(FixtureDTO.Provider.SPORTMONKS)
        .build();
    } catch (IllegalArgumentException e) {
      log.warn("Error calculating stage for Sportmonks fixture {}: {}", smFixtureId, e.getMessage());
      return null;
    }
  }

  private SeasonFeed.Participant parseTeam(SportmonksFixture sportmonksFixture, String location) {
    return sportmonksFixture.getParticipants()
      .stream()
      .filter(p -> p.getMeta().getLocation().equals(location))
      .findFirst()
      .orElseThrow(() -> new IllegalStateException(format("no %s team found", location)));
  }

  private static String getRelatedFixtureId(Map<Integer, List<SportmonksFixture>> fixtureIdToRelatedFixtures,
                                            SportmonksFixture sportmonksFixture) {
    //@formatter:off
    return ofNullable(sportmonksFixture.getAggregateId())
      .flatMap(relatedFixtureId ->
        fixtureIdToRelatedFixtures.get(relatedFixtureId).stream()
          .map(SportmonksFixture::getId)
          .filter(not(isEqual(sportmonksFixture.getId())))
          .findFirst()
          .map(String::valueOf)
      ).orElse(null);
    //@formatter:on
  }

  private static Relation relationFromString(String smRelation) {
    switch (smRelation) {
      case SM_RELATION_SINGLE:
        return SINGLE;
      case SM_RELATION_FIRST:
        return FIRST_LEG;
      case SM_RELATION_SECOND:
        return SECOND_LEG;
      default:
        throw new IllegalArgumentException(format("Leg %s not found", smRelation));
    }
  }
}
