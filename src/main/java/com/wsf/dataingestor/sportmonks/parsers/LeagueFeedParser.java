package com.wsf.dataingestor.sportmonks.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.Date;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.sportmonks.models.LeagueFeed;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static java.lang.Math.abs;
import static java.time.Instant.now;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class LeagueFeedParser implements FeedParser<CurrentTournamentFeed> {

  private final ObjectMapper mapper;
  private final SportmonksFeedParserUtils smFeedParserUtils;

  @Override
  public CurrentTournamentFeed parseFeed(byte[] feed) throws IOException {
    log.debug("Parsing SM League feed: {}", feed);
    return parseFeed(mapper.readValue(feed, LeagueFeed.class));
  }

  @Override
  public CurrentTournamentFeed parseFeed(String feed) throws IOException {
    log.debug("Parsing SM League feed: {}", feed);
    return parseFeed(mapper.readValue(feed, LeagueFeed.class));
  }

  private CurrentTournamentFeed parseFeed(LeagueFeed feed) {
    String externalCompetitionId = feed.getData().getId();
    String feedId = buildFeedId("league", externalCompetitionId, abs(feed.hashCode()), now().toEpochMilli());

    Competition competition = smFeedParserUtils.getCompetition(externalCompetitionId);

    LeagueFeed.Season seasonData = feed.getData().getCurrentSeason();

    String seasonId = ofNullable(seasonData).map(LeagueFeed.Season::getId).orElse(null);
    Instant seasonStartDate = ofNullable(seasonData).map(LeagueFeed.Season::getStartingAt)
      .map(Date::toInstant)
      .orElse(null);
    Tournament existingTournament = ofNullable(seasonId).map(smFeedParserUtils::getTournament).orElse(null);
    String year = ofNullable(seasonData).map(LeagueFeedParser::getYear).orElse(null);

    return CurrentTournamentFeed
      .builder()
      .externalSeasonId(seasonId)
      .year(year)
      .competition(competition)
      .startDate(seasonStartDate)
      .existingTournament(existingTournament)
      .provider(CurrentTournamentFeed.Provider.SPORTMONKS)
      .feedId(feedId)
      .build();
  }

  private static String getYear(LeagueFeed.Season seasonData) {
    String smYear = seasonData.getName();
    if (smYear.contains("/")) {
      return smYear.replace("/", "-");
    } else {
      return smYear;
    }
  }
}
