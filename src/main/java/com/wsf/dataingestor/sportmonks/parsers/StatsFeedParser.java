package com.wsf.dataingestor.sportmonks.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.EntityEventDTOFactory;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.parsers.ma18dp.ProviderPlayerKey;
import com.wsf.dataingestor.parsers.FeedParser;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.sportmonks.models.StatsFeed;
import com.wsf.dataingestor.sportmonks.models.StatsFeed.FixtureData;
import com.wsf.dataingestor.sportmonks.models.StatsFeed.State;
import com.wsf.dataingestor.sportmonks.models.StatsFeed.TeamStatistic;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Player.DetailedPosition;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.collect.Sets.intersection;
import static com.wsf.dataingestor.parsers.ParsersUtils.buildFeedId;
import static com.wsf.dataingestor.sportmonks.parsers.ParserUtils.getExtraTimeHappenedFromScoreList;
import static com.wsf.dataingestor.sportmonks.parsers.ParserUtils.parseFixtureStatus;
import static com.wsf.dataingestor.sportmonks.parsers.ParserUtils.parseMatchPeriod;
import static com.wsf.dataingestor.sportmonks.parsers.SportmonksPositionUtils.parseDetailedPlayerPosition;
import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.SM_TO_WSF_PLAYER_STATS;
import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.translateTeamStats;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.lang.Math.abs;
import static java.lang.String.format;
import static java.time.Instant.now;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.UUID.randomUUID;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatsFeedParser implements FeedParser<MatchDataFeed> {

  private static final int GOAL_EVENT_TYPE_ID = 14;
  private static final int PENALTY_EVENT_TYPE_ID = 16;
  private static final int SUBSTITUTION_EVENT_TYPE_ID = 18;
  private static final int YELLOW_CARD_EVENT_TYPE_ID = 19;
  private static final int RED_CARD_EVENT_TYPE_ID = 20;
  private static final int SECOND_YELLOW_CARD_EVENT_TYPE_ID = 21;
  private static final List<Integer> YELLOW_CARD_EVENT_TYPES = List.of(YELLOW_CARD_EVENT_TYPE_ID);
  private static final List<Integer> RED_CARD_EVENT_TYPES = List.of(RED_CARD_EVENT_TYPE_ID,
    SECOND_YELLOW_CARD_EVENT_TYPE_ID);
  private static final List<Integer> GOAL_EVENT_TYPES = List.of(GOAL_EVENT_TYPE_ID, PENALTY_EVENT_TYPE_ID);

  private static final int LINEUP_TYPE_ID = 11;

  private final ObjectMapper mapper;
  private final SportmonksFeedParserUtils smFeedParserUtils;
  private final FeedsConfigService feedsConfigService;
  private final MetricsManager metricsManager;

  @Override
  public MatchDataFeed parseFeed(byte[] feed) throws IOException {
    log.trace("Parsing SM FixtureStats feed: {}", new String(feed));
    Instant ts = now();
    return parseFeed(mapper.readValue(feed, StatsFeed.class), ts);
  }

  @Override
  public MatchDataFeed parseFeed(String feed) throws IOException {
    log.trace("Parsing SM FixtureStats feed: {}", feed);
    Instant ts = now();
    return parseFeed(mapper.readValue(feed, StatsFeed.class), ts);
  }

  private MatchDataFeed parseFeed(StatsFeed feed, Instant receivedTs) {
    FixtureData fixtureData = feed.getData();
    String smFixtureId = fixtureData.getId();
    Fixture fixture = smFeedParserUtils.getFixtureOrThrow(smFixtureId);

    String feedId = buildFeedId("fixturestats", smFixtureId, abs(feed.hashCode()), receivedTs.toEpochMilli());

    State stateData = fixtureData.getState();
    FeedFixtureStatus fixtureStatus = parseFixtureStatus(stateData.getState());

    Integer matchTimeMin = parseMatchTimeMin(fixtureStatus);
    MatchPeriod matchPeriod = parseMatchPeriod(stateData, fixtureStatus);

    boolean extraTimeHappened = getExtraTimeHappenedFromScoreList(fixtureData.getScores());
    boolean isFinalData = fixtureStatus.isFinished();

    Map<String, String> teamIdToFormation = fixtureData.getFormations()
      .stream()
      .collect(toMap(StatsFeed.Formation::getParticipantId, StatsFeed.Formation::getFormation));

    var goalEvents = getGoalEvents(fixtureData.getEvents(), fixture, fixtureStatus);
    var assistEvents = getAssistEvents(fixtureData.getEvents(), fixture);
    var yellowCardEvents = getYellowCardEvents(fixtureData.getEvents(), fixture);
    var redCardEvents = getRedCardEvents(fixtureData.getEvents(), fixture);
    var subOnEvents = getSubOnEvents(fixtureData.getEvents(), fixture);
    var subOffEvents = getSubOffEvents(fixtureData.getEvents(), fixture);

    List<EntityEventDTO> playerMatchEvents = new ArrayList<>();
    playerMatchEvents.addAll(goalEvents);
    playerMatchEvents.addAll(assistEvents);
    playerMatchEvents.addAll(yellowCardEvents);
    playerMatchEvents.addAll(redCardEvents);
    playerMatchEvents.addAll(subOnEvents);
    playerMatchEvents.addAll(subOffEvents);

    Set<SoccerMatchEvent> supportedEventTypes = Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, RED_CARD, SUB_ON, SUB_OFF);

    List<PlayerDataDTO> playersData = parsePlayersData(feedId, fixtureData, fixtureStatus, fixture, teamIdToFormation,
      playerMatchEvents);

    List<TeamDataDTO> teamsData = parseTeamsData(fixture.getTournament().getCompetitionId(), fixtureData);

    return MatchDataFeed
      .builder()
      .date(feed.getData().getDate())
      .receivedTs(receivedTs)
      .latestUpdateTs(receivedTs)
      .fixture(fixture)
      .feedId(feedId)
      .matchTimeMin(matchTimeMin)
      .matchPeriod(matchPeriod)
      .fixtureStatus(fixtureStatus)
      .teamsData(teamsData)
      .playersData(playersData)
      .aggregatedPlayerMatchEvents(playerMatchEvents)
      .supportedEventTypes(supportedEventTypes)
      .isFinalData(isFinalData)
      .provider(MatchDataFeed.FeedProvider.SPORTMONKS)
      .extraTimeHappened(extraTimeHappened)
      .build();
  }


  private List<TeamDataDTO> parseTeamsData(String competitionId, FixtureData fixtureData) {
    Set<String> supportedStatsForCompetition = feedsConfigService.getSupportedStatsForCompetitionId(competitionId);

    Map<Integer, List<TeamStatistic>> smTeamIdToStatistics = fixtureData.getStatistics()
      .stream()
      .collect(groupingBy(TeamStatistic::getParticipantId));

    return smTeamIdToStatistics.entrySet()
      .stream()
      .map(teamIdToStats -> {
        String smTeamId = teamIdToStats.getKey().toString();
        return buildTeamDataDTO(competitionId, smTeamId, teamIdToStats.getValue(), supportedStatsForCompetition);
      })
      .toList();
  }

  private TeamDataDTO buildTeamDataDTO(String competitionId, String smTeamId, List<TeamStatistic> teamStats,
                                       Set<String> supportedStatsForCompetition) {
    var team = smFeedParserUtils.getTeam(competitionId, smTeamId);
    Map<String, String> teamStatToValue = teamStats
      .stream()
      .filter(stat -> nonNull(stat.getData().getValue()))
      .collect(toMap(TeamStatistic::getTypeId, stat -> stat.getData().getValue()));
    var translateTeamStats = translateTeamStats(teamStatToValue, supportedStatsForCompetition);
    return TeamDataDTO
      .builder().team(team).stats(translateTeamStats)
      .timestamp(now()).build();
  }

  private static int parseMatchTimeMin(FeedFixtureStatus fixtureStatus) {
    if (fixtureStatus == FeedFixtureStatus.FIXTURE) {
      return 0;
    } else if (fixtureStatus.isFinished()) {
      return 90;
    } else {
      return 0;
    }
  }

  private List<PlayerDataDTO> parsePlayersData(String feedId, FixtureData fixtureData, FeedFixtureStatus fixtureStatus,
                                               Fixture fixture, Map<String, String> teamIdToFormation,
                                               List<EntityEventDTO> playerMatchEvents) {
    String competitionId = fixture.getTournament().getCompetitionId();
    Set<String> supportedStatsForCompetition = feedsConfigService.getSupportedStatsForCompetitionId(competitionId);

    return fixtureData.getLineups()
      .stream()
      .map(playerData -> {
        String smPlayerId = playerData.getPlayerId();
        String smTeamId = playerData.getTeamId();
        Map<String, String> smStats = playerData.getDetails();

        if (isPreMatchAndPlayerIsOnTheBench(fixtureStatus, playerData)) {
          return null;
        }

        String smPlayerName = playerData.getPlayerName();

        Player player = ofNullable(smFeedParserUtils.getPlayerBySmPlayerId(smPlayerId, fixture))
          .or(() -> smFeedParserUtils.getPlayerBySportMonksPlayerNameAndTeamId(competitionId, smTeamId, smPlayerName)
            .map(p -> {
              sendErrorForNotFoundPlayer(feedId, fixture, p, smPlayerId);
              return p;
            }))
          .orElse(null);

        if (isNull(player)) {
          return null;
        }

        if (isMatchLiveOrFinishedAndPlayerNotPlaying(player.getIdAsString(), fixtureStatus, smStats,
          playerMatchEvents)) {
          return null;
        }

        Map<String, Number> stats = StatsMapper.translatePlayerStats(smStats, supportedStatsForCompetition);
        String formation = teamIdToFormation.get(playerData.getTeamId());
        DetailedPosition detailedPosition = parseDetailedPlayerPosition(formation, playerData.getFormationPosition());
        ProviderPlayerKey providerPlayerKey = new ProviderPlayerKey(smPlayerId, smTeamId);

        return PlayerDataDTO.buildPlayerData(now(), stats, player, providerPlayerKey, detailedPosition, true, true,
          randomUUID().toString());
      })
      .filter(Objects::nonNull)
      .collect(Collectors.toList());
  }

  private static boolean isMatchLiveOrFinishedAndPlayerNotPlaying(String playerId, FeedFixtureStatus fixtureStatus,
                                                                  Map<String, String> smStats,
                                                                  List<EntityEventDTO> playerMatchEvents) {
    boolean isFixtureLiveOrFinished = FeedFixtureStatus.LIVE.equals(fixtureStatus) || fixtureStatus.isFinished();
    boolean playerHasScoredOrMadeAnAssist = playerMatchEvents
      .stream()
      .anyMatch(event -> event.wasMadeByPlayer(playerId));
    return isFixtureLiveOrFinished && !playerHasScoredOrMadeAnAssist &&
      intersection(smStats.keySet(), SM_TO_WSF_PLAYER_STATS.keySet()).isEmpty();
  }

  private static boolean isPreMatchAndPlayerIsOnTheBench(FeedFixtureStatus fixtureStatus,
                                                         StatsFeed.PlayerData playerData) {
    return FeedFixtureStatus.FIXTURE.equals(fixtureStatus) && !playerData.getTypeId().equals(LINEUP_TYPE_ID);
  }

  private List<EntityEventDTO> getSubOffEvents(List<StatsFeed.Event> events, Fixture fixture) {
    return events
      .stream()
      .filter(event -> SUBSTITUTION_EVENT_TYPE_ID == event.getTypeId())
      .map(event -> getEventData(event, fixture, event.getRelatedPlayerId(), SUB_OFF, false))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getSubOnEvents(List<StatsFeed.Event> events, Fixture fixture) {
    return events
      .stream()
      .filter(event -> SUBSTITUTION_EVENT_TYPE_ID == event.getTypeId())
      .map(event -> getEventData(event, fixture, event.getPlayerId(), SUB_ON, false))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getYellowCardEvents(List<StatsFeed.Event> events, Fixture fixture) {
    return events
      .stream()
      .filter(event -> YELLOW_CARD_EVENT_TYPES.contains(event.getTypeId()))
      .map(event -> getEventData(event, fixture, event.getPlayerId(), YELLOW_CARD, event.getIsOnBench()))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getRedCardEvents(List<StatsFeed.Event> events, Fixture fixture) {
    return events
      .stream()
      .filter(event -> RED_CARD_EVENT_TYPES.contains(event.getTypeId()))
      .map(event -> getEventData(event, fixture, event.getPlayerId(), RED_CARD, event.getIsOnBench()))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getAssistEvents(List<StatsFeed.Event> events, Fixture fixture) {
    return events
      .stream()
      .filter(event -> GOAL_EVENT_TYPE_ID == event.getTypeId())
      .filter(event -> nonNull(event.getRelatedPlayerId()))
      .map(event -> getEventData(event, fixture, event.getRelatedPlayerId(), ASSIST_GOAL, false))
      .filter(Objects::nonNull)
      .collect(toList());
  }

  private List<EntityEventDTO> getGoalEvents(List<StatsFeed.Event> events, Fixture fixture,
                                             FeedFixtureStatus fixtureStatus) {
    return events
      .stream()
      .filter(event -> GOAL_EVENT_TYPES.contains(event.getTypeId()))
      .map(event -> getEventData(event, fixture, event.getPlayerId(), GOAL, false))
      .filter(Objects::nonNull)
      .peek(event -> {
        if (fixtureStatus.isFinished() && isNull(event.getPeriod())) {
          log.error(
            "error=no_event_info_error, goal event with no period id when the match is finished event={} for fixtureId={}",
            event, fixture.getId().toString());
          metricsManager.NO_EVENT_INFO_ERROR.increment();
        }
      })
      .collect(toList());
  }

  private EntityEventDTO getEventData(StatsFeed.Event event, Fixture fixture, String smPlayerId,
                                      SoccerMatchEvent eventName, boolean isOnBench) {
    try {
      String playerId;
      String teamId;
      boolean isPlayerUnknown = false;

      if (nonNull(smPlayerId)) {
        Player player = smFeedParserUtils.getPlayerBySmPlayerId(smPlayerId, fixture);
        if (isNull(player)) {
          playerId = smPlayerId;
          String smTeamId = event.getParticipantId();
          var team = smFeedParserUtils.getTeam(fixture.getTournament().getCompetitionId(), smTeamId);
          teamId = team.getIdAsString();
          log.error("could not find a match for event with sportmonksPlayerId:{}. Replacing with a fake id {}",
            smPlayerId, playerId);
        } else {
          playerId = player.getId().toString();
          teamId = player.getTeam().getId().toString();
        }
      } else {
        isPlayerUnknown = true;
        playerId = smPlayerId;
        teamId = "";
        log.warn("Goal event with no sportmonksPlayerId. Replacing with a fake id {}", playerId);
      }

      MatchPeriod matchPeriodType = parseMatchPeriod(event);

      String eventId = format("%s_%s", event.getId(), getEventNameForEventId(eventName));
      String externalTeamId = event.getParticipantId();

      return EntityEventDTOFactory.standaloneEvent(eventId, playerId, smPlayerId, teamId, externalTeamId,
        isPlayerUnknown, eventName, matchPeriodType, event.getMinute(), false, false, isOnBench, now());
    } catch (IllegalArgumentException e) {
      log.error("error=no_event_info_error, could not process event={} for fixtureId={}: {}", event,
        fixture.getId().toString(), e.getMessage());
      metricsManager.NO_EVENT_INFO_ERROR.increment();
      return null;
    }
  }

  private static String getEventNameForEventId(SoccerMatchEvent eventName) {
    if (SUB_OFF == eventName || SUB_ON == eventName) {
      return "substitution";
    }
    return eventName.getStatisticName();
  }

  public void sendErrorForNotFoundPlayer(String feedId, Fixture fixture, Player player, String smPlayerId) {
    log.error("Player not found by id but was found by name. feedId={}, fixtureId={}, sportmonksId={}, player={}",
      feedId, fixture.getIdAsString(), smPlayerId, player);
    metricsManager.RATINGS_FEED_PLAYER_NOT_FOUND_BY_ID_BUT_FOUND_BY_NAME.increment();
  }
}
