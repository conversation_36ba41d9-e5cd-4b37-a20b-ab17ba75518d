package com.wsf.dataingestor.sportmonks.parsers;

import java.util.List;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.sportmonks.models.StatsFeed;
import com.wsf.dataingestor.sportmonks.models.StatsFeed.Score;
import com.wsf.dataingestor.sportmonks.models.StatsFeed.State;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.CANCELLED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.FIXTURE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.SUSPENDED;
import static java.lang.String.format;
import static java.util.Optional.ofNullable;

public class ParserUtils {

  private static final String EXTRA_TIME_SCORE_DESCRIPTION = "ET";

  /**
   * Status decode for https://api.sportmonks.com/v3/football/state using state or short_name
   * {
   * state:
   * short_name:
   * }
   *
   * @param status
   * @return
   */
  static FeedFixtureStatus parseFixtureStatus(String status) {
    switch (status) {
      case "LIVE": //Live The game is currently inplay
      case "HT": //Half-Time The game currently is in half-time
      case "BREAK": //Regular time finished Waiting for extra time or penalties   to start
      case "ET": //Extra-Time The game currently is in extra time, can happen in knockout games
      case "PEN_LIVE": //Penalty Shootout: in ET status didn't get a winner,  penalties are taken to determine the winner
      case "INT": //Interrupted The game has been interrupted. Can be due to bad weather
      case "INTERRUPTED":
      case "INPLAY_ET":
      case "INPLAY_1ST_HALF":
      case "EXTRA_TIME_BREAK":
      case "INPLAY_2ND_HALF":
      case "INPLAY_ET_2ND_HALF":
      case "PEN_BREAK":
        return LIVE;
      case "AET":// Finished after extra time The game has finished after 120  minutes
      case "FT_PEN": //Full-Time after penalties Finished after penalty shootout
      case "INPLAY_PENALTIES":
      case "FT": //Full-Time The game has ended after 90  minutes. When a game goes into extra time, the FT status will be  presented for a few seconds and then move to the BREAK status.
      case "AWARDED": //Awarded Winner is being decided externally
      case "AWAR":
      case "WO": // Walk Over  Awarding of a victory to a  contestant because there are no  other contestants
        return PLAYED;
      case "CANCL": //Canceled The game has been canceled
      case "CANC":
      case "CANCELLED":
      case "POSTP": //PostPoned The game has been postponed
      case "POST":
      case "POSTPONED":
      case "Deleted": //The game is not avaiable anymore via normal api because it has been replaced
      case "DEL":
      case "DELETED":
        return CANCELLED;
      case "ABAN": //Abandoned  The game has abandoned and will continue at a later time or day
      case "ABANDONED":
      case "PEN": //PENDING???
      case "PENDING":
      case "AU": //Awaiting Updates  Can occur when there is a  connectivity issue or something
      case "AWAITING_UPDATES":
      case "SUSP": // Suspended The game has suspended and will continue at a later time or day
      case "SUSPENDED":
        return SUSPENDED;
      case "DELAYED": // Delayed The game is delayed so it will start later
      case "DELA":
      case "NS": // Not Started The initial status of a game
      case "TBA": //To Be Announced  The game does not have a  confirmed date and time yet. It will  be announced later on.
        return FIXTURE;
      default:
        throw new IllegalArgumentException(format("Unknown fixture state %s", status));
    }
  }

  static MatchPeriod parseMatchPeriod(State stateData, FeedFixtureStatus status) {
    switch (stateData.getId()) {
      case 1:
      case 2:
        return MatchPeriod.FIRST_HALF;
      case 3:
        return MatchPeriod.HALF_TIME;
      case 4:
        return MatchPeriod.END_REGULAR_TIMES;
      case 5:
        return status.isFinished() ? MatchPeriod.END_MATCH : MatchPeriod.END_REGULAR_TIMES;
      case 6:
        return MatchPeriod.EXTRA_FIRST_HALF;
      case 7:
      case 8:
      case 9:
      case 25:
        return MatchPeriod.END_MATCH;
      case 10:
      case 11:
      case 12:
      case 13:
      case 14:
      case 15:
      case 16:
      case 17:
      case 18:
      case 19:
      case 20:
      case 26:
        return null; // match cancelled or postponed
      case 21:
        return MatchPeriod.EXTRA_HALF_TIME;
      case 22:
        return MatchPeriod.SECOND_HALF;
      case 23:
        return MatchPeriod.EXTRA_SECOND_HALF;
      default:
        throw new IllegalArgumentException(format("PeriodId %s not supported", stateData.getId()));
    }
  }

  static MatchPeriod parseMatchPeriod(StatsFeed.Event goalEvent) {
    return ofNullable(goalEvent.getPeriod())
      .map(period -> parseMatchPeriodFromTypeId(period.getTypeId()))
      .orElse(null);
  }

  static boolean getExtraTimeHappenedFromScoreList(List<Score> scoreList) {
    return ofNullable(scoreList)
      .map(scores -> scores
        .stream()
        .anyMatch(score -> EXTRA_TIME_SCORE_DESCRIPTION.equals(score.getDescription())))
      .orElse(false);
  }

  private static MatchPeriod parseMatchPeriodFromTypeId(int typeId) {
    return switch (typeId) {
      case 1 -> MatchPeriod.FIRST_HALF;
      case 2 -> MatchPeriod.SECOND_HALF;
      case 3 -> MatchPeriod.EXTRA_FIRST_HALF;
      case 4 -> MatchPeriod.EXTRA_SECOND_HALF;
      default -> throw new IllegalArgumentException(format("Period with typeId %s is not supported", typeId));
    };
  }
}
