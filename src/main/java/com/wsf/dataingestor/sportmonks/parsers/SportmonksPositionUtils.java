package com.wsf.dataingestor.sportmonks.parsers;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import com.wsf.domain.common.Player;

import static com.google.common.collect.ImmutableList.of;
import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.opta.parsers.OptaPositionUtils.buildOptaPositionToPosition;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;

@Slf4j
public class SportmonksPositionUtils {

  public static Player.DetailedPosition parseDetailedPlayerPosition(String formation, Integer playerPosition) {
    if (isNull(playerPosition)) {
      return null;
    }
    return ofNullable(formationToSMPositionToPosition.get(formation))
      .map(f -> {
        return ofNullable(f.get(playerPosition)).orElseGet(() -> {
          log.error("Position {} for formation {} is not supported", playerPosition, formation);
          return null;
        });
      })
      .orElseGet(() -> {
        log.error("Formation {} is not supported", formation);
        return null;
      });
  }

  private final static Map<String, Map<Integer, Player.DetailedPosition>> formationToSMPositionToPosition = newHashMap();

  static {
    Map<Player.DetailedPosition, List<Integer>> form4231 = newHashMap();
    form4231.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4231.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4231.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4231.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 7));
    form4231.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form4231.put(Player.DetailedPosition.WING_FORWARD, of(8, 10));
    form4231.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4231 = buildOptaPositionToPosition(form4231);

    Map<Player.DetailedPosition, List<Integer>> form433 = newHashMap();
    form433.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form433.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form433.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form433.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form433.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 8));
    form433.put(Player.DetailedPosition.WING_FORWARD, of(9, 11));
    form433.put(Player.DetailedPosition.STRIKER, of(10));
    Map<Integer, Player.DetailedPosition> smPosToPosition433 = buildOptaPositionToPosition(form433);

    Map<Player.DetailedPosition, List<Integer>> form442 = newHashMap();
    form442.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form442.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form442.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form442.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form442.put(Player.DetailedPosition.WING_MIDFIELDER, of(6, 9));
    form442.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition442 = buildOptaPositionToPosition(form442);

    Map<Player.DetailedPosition, List<Integer>> form4141 = newHashMap();
    form4141.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4141.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4141.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4141.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6));
    form4141.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(8, 9));
    form4141.put(Player.DetailedPosition.WING_FORWARD, of(7, 10));
    form4141.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4141 = buildOptaPositionToPosition(form4141);

    Map<Player.DetailedPosition, List<Integer>> form352 = newHashMap();
    form352.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form352.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form352.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form352.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 8));
    form352.put(Player.DetailedPosition.WING_MIDFIELDER, of(5, 9));
    form352.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition352 = buildOptaPositionToPosition(form352);

    Map<Player.DetailedPosition, List<Integer>> form343 = newHashMap();
    form343.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form343.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form343.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 7));
    form343.put(Player.DetailedPosition.WING_MIDFIELDER, of(5, 8));
    form343.put(Player.DetailedPosition.WING_FORWARD, of(9, 11));
    form343.put(Player.DetailedPosition.STRIKER, of(10));
    Map<Integer, Player.DetailedPosition> smPosToPosition343 = buildOptaPositionToPosition(form343);

    Map<Player.DetailedPosition, List<Integer>> form4312 = newHashMap();
    form4312.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4312.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4312.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4312.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form4312.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 8));
    form4312.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form4312.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4312 = buildOptaPositionToPosition(form4312);

    Map<Player.DetailedPosition, List<Integer>> form4411 = newHashMap();
    form4411.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4411.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4411.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4411.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form4411.put(Player.DetailedPosition.WING_MIDFIELDER, of(6, 9));
    form4411.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10));
    form4411.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4411 = buildOptaPositionToPosition(form4411);

    Map<Player.DetailedPosition, List<Integer>> form3412 = newHashMap();
    form3412.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3412.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3412.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 7));
    form3412.put(Player.DetailedPosition.WING_MIDFIELDER, of(5, 8));
    form3412.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form3412.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3412 = buildOptaPositionToPosition(form3412);

    Map<Player.DetailedPosition, List<Integer>> form3421 = newHashMap();
    form3421.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3421.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3421.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 7));
    form3421.put(Player.DetailedPosition.WING_MIDFIELDER, of(5, 8));
    form3421.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9, 10));
    form3421.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3421 = buildOptaPositionToPosition(form3421);

    Map<Player.DetailedPosition, List<Integer>> form532 = newHashMap();
    form532.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form532.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4, 5));
    form532.put(Player.DetailedPosition.WING_BACK, of(2, 6));
    form532.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(8));
    form532.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 9));
    form532.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition532 = buildOptaPositionToPosition(form532);

    Map<Player.DetailedPosition, List<Integer>> form541 = newHashMap();
    form541.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form541.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4, 5));
    form541.put(Player.DetailedPosition.WING_BACK, of(2, 6));
    form541.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(8, 9));
    form541.put(Player.DetailedPosition.WING_MIDFIELDER, of(7, 10));
    form541.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition541 = buildOptaPositionToPosition(form541);

    Map<Player.DetailedPosition, List<Integer>> form451 = newHashMap();
    form451.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form451.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form451.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form451.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8, 9));
    form451.put(Player.DetailedPosition.WING_FORWARD, of(6, 10));
    form451.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition451 = buildOptaPositionToPosition(form451);

    Map<Player.DetailedPosition, List<Integer>> form3142 = newHashMap();
    form3142.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3142.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3142.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(5));
    form3142.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form3142.put(Player.DetailedPosition.WING_MIDFIELDER, of(6, 9));
    form3142.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3142 = buildOptaPositionToPosition(form3142);

    Map<Player.DetailedPosition, List<Integer>> form4222 = newHashMap();
    form4222.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4222.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4222.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4222.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6, 7));
    form4222.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8, 9));
    form4222.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4222 = buildOptaPositionToPosition(form4222);

    Map<Player.DetailedPosition, List<Integer>> form4321 = newHashMap();
    form4321.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4321.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4321.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4321.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form4321.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 8));
    form4321.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9, 10));
    form4321.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4321 = buildOptaPositionToPosition(form4321);

    Map<Player.DetailedPosition, List<Integer>> form3511 = newHashMap();
    form3511.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3511.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3511.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7));
    form3511.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(6, 8));
    form3511.put(Player.DetailedPosition.WING_MIDFIELDER, of(5, 9));
    form3511.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10));
    form3511.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3511 = buildOptaPositionToPosition(form3511);

    Map<Player.DetailedPosition, List<Integer>> form4132 = newHashMap();
    form4132.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4132.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4132.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4132.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6));
    form4132.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form4132.put(Player.DetailedPosition.WING_FORWARD, of(7, 9));
    form4132.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4132 = buildOptaPositionToPosition(form4132);

    Map<Player.DetailedPosition, List<Integer>> form4123 = newHashMap();
    form4123.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4123.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4123.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4123.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6));
    form4123.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form4123.put(Player.DetailedPosition.WING_FORWARD, of(7, 9));
    form4123.put(Player.DetailedPosition.STRIKER, of(10, 11));
    Map<Integer, Player.DetailedPosition> smPosToPosition4123 = buildOptaPositionToPosition(form4123);

    Map<Player.DetailedPosition, List<Integer>> form3313 = newHashMap();
    form3313.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3313.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3313.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6));
    form3313.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(5, 7));
    form3313.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8));
    form3313.put(Player.DetailedPosition.WING_FORWARD, of(9, 11));
    form3313.put(Player.DetailedPosition.STRIKER, of(10));
    Map<Integer, Player.DetailedPosition> smPosToPosition3313 = buildOptaPositionToPosition(form3313);

    Map<Player.DetailedPosition, List<Integer>> form3331 = newHashMap();
    form3331.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3331.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3331.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6));
    form3331.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(5, 7));
    form3331.put(Player.DetailedPosition.WING_MIDFIELDER, of(8, 10));
    form3331.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9));
    form3331.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3331 = buildOptaPositionToPosition(form3331);

    Map<Player.DetailedPosition, List<Integer>> form523 = newHashMap();
    form523.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form523.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4, 5));
    form523.put(Player.DetailedPosition.WING_BACK, of(2, 6));
    form523.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form523.put(Player.DetailedPosition.WING_FORWARD, of(9, 11));
    form523.put(Player.DetailedPosition.STRIKER, of(10));
    Map<Integer, Player.DetailedPosition> smPosToPosition523 = buildOptaPositionToPosition(form523);

    Map<Player.DetailedPosition, List<Integer>> form4240 = newHashMap();
    form4240.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4240.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4));
    form4240.put(Player.DetailedPosition.WING_BACK, of(2, 5));
    form4240.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(6, 7));
    form4240.put(Player.DetailedPosition.WING_FORWARD, of(8, 11));
    form4240.put(Player.DetailedPosition.STRIKER, of(9, 10));
    Map<Integer, Player.DetailedPosition> smPosToPosition4240 = buildOptaPositionToPosition(form4240);

    Map<Player.DetailedPosition, List<Integer>> form3241 = newHashMap();
    form3241.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form3241.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3, 4));
    form3241.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(5, 6));
    form3241.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8, 9));
    form3241.put(Player.DetailedPosition.WING_FORWARD, of(7, 10));
    form3241.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition3241 = buildOptaPositionToPosition(form3241);

    Map<Player.DetailedPosition, List<Integer>> form4213 = newHashMap();
    form4213.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form4213.put(Player.DetailedPosition.CENTRE_BACK, of(5, 6));
    form4213.put(Player.DetailedPosition.WING_BACK, of(2, 3));
    form4213.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(4));
    form4213.put(Player.DetailedPosition.CENTRE_MIDFIELDER, of(7, 8));
    form4213.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(10, 11));
    form4213.put(Player.DetailedPosition.STRIKER, of(9));
    Map<Integer, Player.DetailedPosition> smPosToPosition4213 = buildOptaPositionToPosition(form4213);

    Map<Player.DetailedPosition, List<Integer>> form2341 = newHashMap();
    form2341.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form2341.put(Player.DetailedPosition.CENTRE_BACK, of(2, 3));
    form2341.put(Player.DetailedPosition.WING_BACK, of(4, 6));
    form2341.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(5));
    form2341.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(8, 9));
    form2341.put(Player.DetailedPosition.WING_FORWARD, of(7, 10));
    form2341.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition2341 = buildOptaPositionToPosition(form2341);

    Map<Player.DetailedPosition, List<Integer>> form5221 = newHashMap();
    form5221.put(Player.DetailedPosition.GOALKEEPER, of(1));
    form5221.put(Player.DetailedPosition.CENTRE_BACK, of(3, 4, 5));
    form5221.put(Player.DetailedPosition.WING_BACK, of(2, 6));
    form5221.put(Player.DetailedPosition.DEFENSIVE_MIDFIELDER, of(7, 8));
    form5221.put(Player.DetailedPosition.ATTACKING_MIDFIELDER, of(9, 10));
    form5221.put(Player.DetailedPosition.STRIKER, of(11));
    Map<Integer, Player.DetailedPosition> smPosToPosition5221 = buildOptaPositionToPosition(form5221);

    formationToSMPositionToPosition.put("4-4-2", smPosToPosition442);
    formationToSMPositionToPosition.put("4-3-3", smPosToPosition433);
    formationToSMPositionToPosition.put("4-5-1", smPosToPosition451);
    formationToSMPositionToPosition.put("4-4-1-1", smPosToPosition4411);
    formationToSMPositionToPosition.put("4-1-4-1", smPosToPosition4141);
    formationToSMPositionToPosition.put("4-2-3-1", smPosToPosition4231);
    formationToSMPositionToPosition.put("4-3-2-1", smPosToPosition4321);
    formationToSMPositionToPosition.put("3-3-1-3", smPosToPosition3313);
    formationToSMPositionToPosition.put("5-3-2", smPosToPosition532);
    formationToSMPositionToPosition.put("5-2-3", smPosToPosition523);
    formationToSMPositionToPosition.put("5-4-1", smPosToPosition541);
    formationToSMPositionToPosition.put("3-5-2", smPosToPosition352);
    formationToSMPositionToPosition.put("3-4-3", smPosToPosition343);
    formationToSMPositionToPosition.put("4-2-2-2", smPosToPosition4222);
    formationToSMPositionToPosition.put("3-5-1-1", smPosToPosition3511);
    formationToSMPositionToPosition.put("3-4-1-2", smPosToPosition3412);
    formationToSMPositionToPosition.put("3-4-2-1", smPosToPosition3421);
    formationToSMPositionToPosition.put("3-1-4-2", smPosToPosition3142);
    formationToSMPositionToPosition.put("2-3-4-1", smPosToPosition2341);
    formationToSMPositionToPosition.put("4-1-3-2", smPosToPosition4132);
    formationToSMPositionToPosition.put("4-1-2-3", smPosToPosition4123);
    formationToSMPositionToPosition.put("4-2-4-0", smPosToPosition4240);
    formationToSMPositionToPosition.put("4-3-1-2", smPosToPosition4312);
    formationToSMPositionToPosition.put("4-2-1-3", smPosToPosition4213);
    formationToSMPositionToPosition.put("3-2-4-1", smPosToPosition3241);
    formationToSMPositionToPosition.put("3-3-3-1", smPosToPosition3331);
    formationToSMPositionToPosition.put("5-2-2-1", smPosToPosition5221);
  }
}
