package com.wsf.dataingestor.sportmonks.models;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class LeagueFeed {

  @JsonProperty
  private League data;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class League {
    private String id;
    @JsonProperty("currentseason")
    private Season currentSeason;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Season {
    private String id;
    private String name;
    @JsonProperty("starting_at")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    private Date startingAt;
  }
}
