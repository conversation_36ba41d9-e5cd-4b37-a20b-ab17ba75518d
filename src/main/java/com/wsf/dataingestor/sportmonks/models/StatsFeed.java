package com.wsf.dataingestor.sportmonks.models;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wsf.dataingestor.sportmonks.models.converters.StatDeserializer;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class StatsFeed {

  @JsonProperty
  private FixtureData data;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class FixtureData {
    private String id;
    @JsonProperty("starting_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    private Instant date;
    private State state;
    private List<Score> scores;
    private List<TeamStatistic> statistics;
    private List<PlayerData> lineups;
    private List<Formation> formations;
    private List<Event> events;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class State {
    private Integer id;
    private String state;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Score {
    private ScoreInfo score;
    private String description;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ScoreInfo {
    private Integer goals;
    private String participant;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Formation {
    @JsonProperty("participant_id")
    private String participantId;
    private String formation;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PlayerData {
    @JsonProperty("player_id")
    private String playerId;
    @JsonProperty("team_id")
    private String teamId;
    @JsonProperty("type_id")
    private Integer typeId;
    @JsonProperty("player_name")
    private String playerName;
    @JsonProperty("formation_position")
    private Integer formationPosition;
    @JsonDeserialize(using = StatDeserializer.class)
    private Map<String, String> details = new HashMap<>();
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Event {
    private String id;
    @JsonProperty("player_id")
    private String playerId;
    @JsonProperty("participant_id")
    private String participantId;
    @JsonProperty("related_player_id")
    private String relatedPlayerId;
    @JsonProperty("type_id")
    private Integer typeId;
    @JsonProperty("on_bench")
    private Boolean isOnBench;
    private Period period;
    private Integer minute;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TeamStatistic {
    private String id;
    @JsonProperty("type_id")
    private String typeId;
    @JsonProperty("participant_id")
    private Integer participantId;
    private TeamStatisticData data;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TeamStatisticData {
    private String value;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Period {
    @JsonProperty("type_id")
    private Integer typeId;
  }
}
