package com.wsf.dataingestor.sportmonks.models;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class TeamsFeed {

  @JsonProperty
  private List<SquadData> data;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class SquadData {
    @JsonProperty("id")
    private String teamId;
    @JsonProperty("name")
    private String teamName;
    @JsonProperty("short_code")
    private String teamAbbreviation;
    @JsonProperty("activeseasons")
    private List<ActiveSeason> activeSeasons;
    private List<PlayerData> players;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ActiveSeason {
    @JsonProperty("id")
    private String seasonId;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PlayerData {
    @JsonProperty("player_id")
    private String playerId;
    @JsonProperty("team_id")
    private String teamId;
    @JsonProperty("position_id")
    private Integer positionId;
    @JsonProperty("detailed_position_id")
    private Integer detailedPositionId;
    private PlayerInfo player;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class PlayerInfo {
    @JsonProperty("firstname")
    private String firstName;
    @JsonProperty("lastname")
    private String lastName;
    @JsonProperty("common_name")
    private String commonName;
    @JsonProperty("date_of_birth")
    private String birthDate; // yyyy-mm-dd
  }
}
