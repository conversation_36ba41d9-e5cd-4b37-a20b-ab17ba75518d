package com.wsf.dataingestor.sportmonks.models.converters;

import java.io.IOException;
import java.util.AbstractMap;
import java.util.Map;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import static java.util.stream.Collectors.toMap;
import static org.springframework.data.util.StreamUtils.createStreamFromIterator;

public class StatDeserializer extends JsonDeserializer<Map<String, String>> {
  @Override
  public Map<String, String> deserialize(JsonParser jsonParser,
                                         DeserializationContext deserializationContext) throws IOException {
    ObjectCodec oc = jsonParser.getCodec();
    JsonNode node = oc.readTree(jsonParser);

    return createStreamFromIterator(node.elements())
      .map(entry -> {
        String value = entry.get("data").get("value").asText();
        return new AbstractMap.SimpleEntry<>(entry.get("type_id").asText(), value);
      })
      .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
  }
}
