package com.wsf.dataingestor.sportmonks.models;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RequiredArgsConstructor
public class SeasonFeed {
  @JsonProperty
  private Season data;

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Season {
    private String id;
    @JsonProperty("league_id")
    private String leagueId;
    @JsonProperty("is_current")
    private Boolean isCurrentSeason;
    private List<SportmonksFixture> fixtures;
    private List<Stage> stages;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class SportmonksFixture {
    private Integer id;
    @JsonProperty("aggregate_id")
    private Integer aggregateId;
    @JsonProperty("stage_id")
    private Integer stageId;
    private List<Participant> participants;
    private State state;
    @JsonProperty("starting_at_timestamp")
    private Long timestamp;
    private String leg;
    private Boolean placeholder;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class State {
    private Integer id;
    private String state;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Stage {
    private Integer id;
    private String name;
    private TypeData type;
    @JsonProperty("sort_order")
    private Integer sortOrder;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class TypeData {
    private String name;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class Participant {
    private String id;
    private String name;
    private ParticipantMeta meta;
    @JsonProperty("short_code")
    private String abbreviation;
  }

  @Data
  @RequiredArgsConstructor
  @JsonIgnoreProperties(ignoreUnknown = true)
  public static class ParticipantMeta {
    private String location;
  }
}
