package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.tournaments.CurrentTournamentRetriever;
import com.wsf.dataingestor.sportmonks.clients.CurrentTournamentClient;

@Service
@RequiredArgsConstructor
public class SportmonksCurrentTournamentRetriever implements CurrentTournamentRetriever {

  private final CurrentTournamentClient currentTournamentClient;

  @Override
  public CurrentTournamentFeed retrieveTournamentFeed(String sportmonksCompetitionId) {
    return currentTournamentClient.retrieveParsedFeed(sportmonksCompetitionId);
  }
}
