package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.services.squads.SquadsRetriever;
import com.wsf.dataingestor.services.squads.SquadsRetrieverUtils;
import com.wsf.dataingestor.sportmonks.clients.TeamSquadsClient;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

@Service
@RequiredArgsConstructor
public class SportmonksSquadsRetriever implements SquadsRetriever {

  private final TeamSquadsClient client;
  private final SportmonksFeedParserUtils smFeedParserUtils;

  @Override
  public SquadsFeed retrieveSquadsFeed(Tournament tournament) {
    Set<String> externalIds = tournament.getExternalIds().getSportmonksIds();
    return SquadsRetrieverUtils.retrieveSquadsFeed(client, externalIds, SquadsFeed.FeedProvider.SPORTMONKS);
  }

  @Override
  public MasterPlayer findMasterPlayer(SquadPlayerDTO squadPlayerDTO) {
    String firstName = squadPlayerDTO.getFirstName();
    String lastName = squadPlayerDTO.getLastName();
    String matchName = squadPlayerDTO.getMatchName();
    LocalDate birthDate = squadPlayerDTO.getBirthDate();
    String playerId = squadPlayerDTO.getPlayerId();
    return smFeedParserUtils.getMasterPlayerOrCreateUnmapped(firstName, lastName, matchName, birthDate, playerId);
  }

  @Override
  public Team findTeamOrCreateUnmapped(String competitionId, SquadPlayerDTO.TeamDTO teamDTO) {
    return smFeedParserUtils.getTeamOrCreateUnmapped(competitionId, teamDTO.getExternalTeamId(),
      teamDTO.getExternalTeamName(), teamDTO.getExternalTeamAbbreviation());
  }

  @Override
  public Player.PlayerBuilder<?, ?> enrichPlayer(Player.PlayerBuilder<?, ?> playerBuilder, String smPlayerId) {
    return playerBuilder.sportmonksPlayerId(smPlayerId);
  }

  @Override
  public String getExternalPlayerFieldName() {
    return "sportmonksPlayerId";
  }
}
