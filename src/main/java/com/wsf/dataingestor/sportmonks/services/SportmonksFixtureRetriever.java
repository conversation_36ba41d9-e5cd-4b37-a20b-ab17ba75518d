package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import org.springframework.stereotype.Service;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.services.fixtures.FixtureRetrieverUtils;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.sportmonks.clients.SeasonClient;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static java.lang.String.format;
import static java.util.Objects.isNull;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class SportmonksFixtureRetriever implements FixturesRetriever {

  private final SeasonClient seasonClient;
  private final SportmonksFeedParserUtils smFeedParserUtils;

  @Override
  public FixturesFeed retrieveFixturesFeed(Tournament tournament) {
    Set<String> externalIds = tournament.getExternalIds().getSportmonksIds();
    return FixtureRetrieverUtils.retrieveFixturesFeed(tournament, seasonClient, externalIds);
  }

  @Override
  public Fixture findFixture(FixtureDTO fixtureDTO, Tournament tournament) throws FixtureNotFoundException {
    Team homeTeam = smFeedParserUtils.getTeamOrCreateUnmapped(tournament.getCompetitionId(),
      fixtureDTO.getExternalHomeTeamId(), fixtureDTO.getExternalHomeTeamName(),
      fixtureDTO.getExternalHomeTeamAbbreviation());

    Team awayTeam = smFeedParserUtils.getTeamOrCreateUnmapped(tournament.getCompetitionId(),
      fixtureDTO.getExternalAwayTeamId(), fixtureDTO.getExternalAwayTeamName(),
      fixtureDTO.getExternalAwayTeamAbbreviation());

    if (isNull(homeTeam) || isNull(awayTeam)) {
      throw new IllegalStateException(
        format("either one of the two teams is not mapped: homeTeam %s awayTeam %s", homeTeam, awayTeam));
    }

    return ofNullable(
      smFeedParserUtils.getFixtureBySportmonksIdOrTournamentAndTeamsAndDate(fixtureDTO.getExternalFixtureId(),
        tournament.getIdAsString(), homeTeam.getId().toString(), awayTeam.getId().toString(),
        fixtureDTO.getTime())).orElseThrow(() -> new FixtureNotFoundException(
      format("Could not find fixture for tournamentId=%s homeTeamId=%s awayTeamId=%s", tournament.getId().toString(),
        homeTeam.getId().toString(), awayTeam.getId().toString())));
  }

  @Override
  public Fixture findRelatedFixture(FixtureDTO fixture, Tournament tournament) throws FixtureNotFoundException {
    String externalId = fixture.getExternalRelatedFixtureId();
    if (isNull(externalId)) {
      return null;
    }
    return ofNullable(smFeedParserUtils.getFixtureBySportmonksFixtureId(externalId)).orElseThrow(
      () -> new FixtureNotFoundException(format("Could not find fixture with sportmonksFixtureId=%s", externalId)));
  }

  @Override
  public Team findTeam(String externalTeamId, Tournament tournament) {
    return smFeedParserUtils.getTeam(tournament.getCompetitionId(), externalTeamId);
  }

  @Override
  public boolean shouldProcessStages() {
    return true;
  }

  @Override
  public Stage findStage(FixtureDTO fixtureDTO, String competitionId) {
    Stage stage = smFeedParserUtils.getStageOrCreateUnmapped(competitionId, fixtureDTO.getExternalStageId(),
      fixtureDTO.getExternalStageName());
    return ofNullable(stage).orElseThrow(() -> new IllegalArgumentException(
      format("Could not find stage with sportmonksStageId %s and sportmonksStageName %s",
        fixtureDTO.getExternalStageId(), fixtureDTO.getExternalStageName())));
  }

  @Override
  public String getExternalFixtureFieldName() {
    return "sportmonksFixtureId";
  }

  @Override
  public ExternalProvider getExternalFixtureProvider() {
    return ExternalProvider.SPORTMONKS;
  }
}
