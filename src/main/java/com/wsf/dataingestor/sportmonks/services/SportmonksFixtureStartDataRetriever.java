package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ratings.FixtureStartDataRetriever;
import com.wsf.dataingestor.sportmonks.clients.FixtureStatsClient;
import com.wsf.domain.common.Fixture;

@Service
@RequiredArgsConstructor
public class SportmonksFixtureStartDataRetriever implements FixtureStartDataRetriever {

  private final FixtureStatsClient fixtureStatsClient;

  @Override
  public MatchDataFeed retrieveFixtureStartData(Fixture fixture) {
    return fixtureStatsClient.retrieveParsedFeed(fixture.getSportmonksFixtureId());
  }
}
