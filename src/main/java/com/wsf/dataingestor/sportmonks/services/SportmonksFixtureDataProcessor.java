package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.sportmonks.clients.FixtureStatsClient;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;

@Service
@RequiredArgsConstructor
public class SportmonksFixtureDataProcessor implements FixtureDataProcessor {

  private final FixtureStatsClient fixtureStatsClient;

  @Override
  public MatchDataFeed processFixtureStatsFeed(Fixture fixture) {
    return fixtureStatsClient.retrieveParsedFeed(fixture.getSportmonksFixtureId());
  }

  @Override
  public void manageWebSocket(Fixture fixture, FixtureStatus fixtureStatus) {}
}
