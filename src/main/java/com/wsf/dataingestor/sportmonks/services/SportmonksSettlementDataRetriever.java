package com.wsf.dataingestor.sportmonks.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ratings.SettlementDataRetriever;
import com.wsf.dataingestor.sportmonks.clients.FixtureStatsClient;
import com.wsf.domain.common.Fixture;

@Slf4j
@Service
@RequiredArgsConstructor
public class SportmonksSettlementDataRetriever implements SettlementDataRetriever {

  private final FixtureStatsClient fixtureStatsClient;

  @Override
  public MatchDataFeed retrieveSettlementData(Fixture fixture) {
    String smFixtureId = fixture.getSportmonksFixtureId();
    return fixtureStatsClient.retrieveParsedFeed(smFixtureId);
  }
}
