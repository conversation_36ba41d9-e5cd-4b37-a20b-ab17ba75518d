package com.wsf.dataingestor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

import static org.springframework.boot.Banner.Mode.OFF;

@Slf4j
@ComponentScan("com.wsf")
@SpringBootApplication
@RequiredArgsConstructor
@EnableMongoRepositories(basePackages = {"com.wsf.repository.common", "com.wsf.repository.main",
                                         "com.wsf.dataingestor.repositories"})
@EnableScheduling
@EnableRetry
@EnableConfigurationProperties
public class DataIngestorApplication implements CommandLineRunner {

  public static void main(String[] args) {
    new SpringApplicationBuilder().bannerMode(OFF).sources(DataIngestorApplication.class).build().run(args);
  }

  @Override
  public void run(String... strings) throws Exception {
  }
}
