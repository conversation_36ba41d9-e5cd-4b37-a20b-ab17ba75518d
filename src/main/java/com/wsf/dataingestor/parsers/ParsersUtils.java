package com.wsf.dataingestor.parsers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class ParsersUtils {

  public static String buildFeedId(String prefix, String externalId, long hash, long ts) {
    return String.format("%s_%s_%s_%s", prefix, externalId, ts, hash);
  }

  public static String buildFeedId(List<String> feedsIds) {
    if (feedsIds.isEmpty()) {
      throw new IllegalArgumentException("Empty feedsIds list");
    }
    if (feedsIds.size() > 1) {
      return String.format("multiple[%s]", String.join("-", feedsIds));
    }
    return feedsIds.get(0);
  }

  public static Map<String, Number> translateStatsWith0Fallback(Map<String, String> stats,
                                                                Map<String, String> providerToWSFStatsNames) {
    return translateStats(stats, providerToWSFStatsNames, 0);
  }

  private static Map<String, Number> translateStats(Map<String, String> stats,
                                                    Map<String, String> providerToWSFStatsNames, Number fallback) {
    return providerToWSFStatsNames.entrySet()
      .stream()
      .map(entry -> {
        String statName = entry.getKey();
        String mappedName = entry.getValue();
        if (stats.containsKey(statName)) {
          String value = stats.get(statName);
          int valInt = Integer.parseInt(value);
          return Pair.of(mappedName, Math.max(valInt, 0));
        } else if (nonNull(fallback)) {
          return Pair.of(mappedName, fallback);
        } else {
          return null;
        }
      })
      .filter(Objects::nonNull)
      .collect(toMap(Pair::getKey, Pair::getValue));
  }
}
