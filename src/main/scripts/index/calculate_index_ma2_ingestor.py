import csv
import json
import os

import requests
from pymongo import MongoClient

in_folder = "data/matches/"
out_folder = "data/out/"
out_csv_file = "data/out/export.csv"
out = "csv"

url = "http://localhost:8080/competitions/5d3a33fe999fc4072ab589af/tournaments/5d3a33fe999fc4072ab589c4/stats/ma2/calculate-index"

if not os.path.exists(out_folder):
    os.mkdir(out_folder)

client = MongoClient("mongodb://localhost:27017")
db = client['seriea']

if out == "json":
    for filename in os.listdir(in_folder):
        with open(in_folder + filename, 'rb') as feed:
            match_id = filename.split('_')[2].split('.')[0]
            print(match_id)

            match = db.matches.find_one({'optaFixtureId': match_id})
            home_team = match['homeTeam']['name']
            away_team = match['awayTeam']['name']

            resp = requests.post(url, data=feed)

            with open(f'{out_folder}/{home_team}-{away_team}.txt', 'w+') as output:
                output.write(json.dumps(resp.json(), indent=4, ensure_ascii=False))

elif out == "csv":
    with open(out_csv_file, "w+") as csv_file:
        writer = csv.writer(csv_file, delimiter=';')
        writer.writerow(("week", "match", "player", "index", "base", "bonus"))

        for filename in os.listdir(in_folder):
            with open(in_folder + filename, 'rb') as feed:
                match_id = filename.split('_')[2].split('.')[0]
                print(match_id)

                match = db.matches.find_one({'optaFixtureId': match_id})
                home_team = match['homeTeam']['name']
                away_team = match['awayTeam']['name']
                match_str = f"{home_team}-{away_team}"
                week = match['sequentialId']

                resp = requests.post(url, data=feed)

                resp_json = resp.json()

                for key, value in resp_json.items():
                    index = round(float(value["index"]) / 10, 2)
                    base = round(float(value["base"]) / 10, 2)
                    bonus = round(float(value["bonus"]) / 10, 2)
                    writer.writerow((week, match_str, key, index, base, bonus))
