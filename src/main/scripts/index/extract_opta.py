import json
import os

import requests

competition = 'seriea'
year = '20-21'
season = '4b80uzt9gxak7d1vaa5jp17qi'

schedule = requests.get(f'http://api.performfeeds.com/soccerdata/match/t2vho64fveax13yf6b6a7s9tg?_rt=b&_fmt=json&tmcl={season}&_pgSz=400')

parsed_schedule = json.loads(schedule.text)

matches = parsed_schedule['match']

path = f"/Users/<USER>/projects/WSF/repos/data-ingestor/data/opta/{competition}/{year}"

if not os.path.exists(path):
    os.mkdir(path)

for match in matches:
    match_info = match['matchInfo']

    match_id = match_info['id']
    match_seq = match_info['week']

    print(f"Processing match {match_id}")

    match_data = requests.get(f'http://api.performfeeds.com/soccerdata/matchstats/t2vho64fveax13yf6b6a7s9tg?_rt=b&_fmt=json&fx={match_id}&detailed=yes')

    with open(f"{path}/ma2_{match_seq}_{match_id}.json", "w+") as match_data_file:
        match_data_file.write(match_data.text)
        match_data_file.close()

