import json
import os

from pymongo import MongoClient

folder = "seriea/test/"

client = MongoClient("")
db = client['seriea']

for filename in os.listdir(folder):
    match_id = filename.split('_')[2].split('.')[0]
    print(match_id)

    match = db.matches.find_one({'optaFixtureId': match_id})
    home_team = match['homeTeam']['name']
    away_team = match['awayTeam']['name']
    ratings = db.ratings.find({'match.optaFixtureId': match_id})

    with open(f'seriea/out/{home_team}-{away_team}.txt', 'w+') as output:
        arr = []
        for rating in ratings:
            name = rating['player']['name']
            index = rating['indexPerformance']
            arr.append({
                "name": name,
                "index": index
            })
        output.write(json.dumps(arr, indent=4, ensure_ascii=False))
