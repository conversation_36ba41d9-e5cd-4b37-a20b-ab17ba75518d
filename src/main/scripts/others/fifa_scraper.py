import requests
import codecs
from string import Template
import unicodedata


def parse_players(t_url, season):
    tot_players = []
    for start in range(0,1000,25):
        url = t_url.substitute(start=start)

        r = requests.get(url = url)

        data = r.json()

        players = data['aaData']

        if len(players) == 0:
            break

        inserted_players = []

        for player in players:
            first_name = player['1']
            last_name = player['2']
            known_name = player['3']
            team = player['10']
            team = team[team.index('>')+1:-4]
            team = 'Juventus' if team == 'Piemonte Calcio' else team

            name = known_name if known_name else first_name + ' ' + last_name

            if name in inserted_players:
                continue

            inserted_players.append(name)

            attrs = {
                'name': name,
                'team': team,
                'season': season,
                'rtg': player['4'],
                'pac': player['13'],
                'sho': player['14'],
                'pas': player['15'],
                'dri': player['16'],
                'def': player['17'],
                'phy': player['18'],
                'acc': player['19'],
                'agi': player['20'],
                'bal': player['21'],
                'jum': player['22'],
                'rea': player['23'],
                'spr': player['24'],
                'sta': player['25'],
                'str': player['26'],
                'agg': player['27'],
                'int': player['28'],
                'pos': player['29'],
                'vis': player['30'],
                'com': player['67'] if season == '2019-2020' else '',
                'ball_con': player['32'],
                'crossing': player['33'],
                'curve': player['34'],
                'drib': player['35'],
                'finish': player['36'],
                'free_kick': player['37'],
                'head': player['38'],
                'long_pass': player['39'],
                'long_shots': player['40'],
                'marking': player['41'],
                'pen': player['42'],
                'short_pass': player['43'],
                'shot_power': player['44'],
                'slide_tackle': player['45'],
                'stand_tackle': player['46'],
                'volleys': player['47'],
            }

            p = unicodedata.normalize('NFKD', csv_row.format(**attrs)).encode('ascii', 'ignore')
            tot_players.append(p.decode('utf-8'))

    return tot_players



players_url_seriea_20 = Template('https://wefut.com/ajax/getPlayers/20?sEcho=11&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=player&clubs=%5B39%2C189%2C190%2C1842%2C110374%2C110556%2C206%2C44%2C45%2C46%2C347%2C47%2C48%2C50%2C114153%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')
gk_url_seriea_20 = Template('https://wefut.com/ajax/getPlayers/20?sEcho=11&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=goalkeeper&clubs=%5B39%2C189%2C190%2C1842%2C110374%2C110556%2C206%2C44%2C45%2C46%2C347%2C47%2C48%2C50%2C114153%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')

players_url_seriea_19 = Template('https://wefut.com/ajax/getPlayers/19?sEcho=7&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=player&clubs=%5B39%2C189%2C1842%2C192%2C1746%2C110374%2C111657%2C110556%2C44%2C45%2C46%2C47%2C48%2C50%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')
gk_url_seriea_19 = Template('https://wefut.com/ajax/getPlayers/19?sEcho=7&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=goalkeeper&clubs=%5B39%2C189%2C1842%2C192%2C1746%2C110374%2C111657%2C110556%2C44%2C45%2C46%2C47%2C48%2C50%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')

players_url_seriea_18 = Template('https://wefut.com/ajax/getPlayers/18?sEcho=4&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=player&clubs=%5B39%2C112026%2C189%2C1842%2C192%2C110734%2C110374%2C110556%2C206%2C44%2C45%2C46%2C47%2C48%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')
gk_url_seriea_18 = Template('https://wefut.com/ajax/getPlayers/18?sEcho=4&iColumns=75&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&mDataProp_74=74&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&sSearch_74=&bRegex_74=false&bSearchable_74=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=true&bSortable_68=false&bSortable_69=false&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&bSortable_74=true&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_composure=&max_composure=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=goalkeeper&clubs=%5B39%2C112026%2C189%2C1842%2C192%2C110734%2C110374%2C110556%2C206%2C44%2C45%2C46%2C47%2C48%2C52%2C1837%2C111974%2C112791%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')

players_url_seriea_17 = Template('https://wefut.com/ajax/getPlayers/17?sEcho=4&iColumns=74&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=false&bSortable_68=false&bSortable_69=true&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=player&clubs=%5B39%2C189%2C1842%2C192%2C110734%2C1746%2C110374%2C110556%2C44%2C45%2C46%2C47%2C48%2C1843%2C200%2C52%2C1837%2C111974%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')
gk_url_seriea_17 = Template('https://wefut.com/ajax/getPlayers/17?sEcho=4&iColumns=74&sColumns=&iDisplayStart=$start&iDisplayLength=25&mDataProp_0=0&mDataProp_1=1&mDataProp_2=2&mDataProp_3=3&mDataProp_4=4&mDataProp_5=5&mDataProp_6=6&mDataProp_7=7&mDataProp_8=8&mDataProp_9=9&mDataProp_10=10&mDataProp_11=11&mDataProp_12=12&mDataProp_13=13&mDataProp_14=14&mDataProp_15=15&mDataProp_16=16&mDataProp_17=17&mDataProp_18=18&mDataProp_19=19&mDataProp_20=20&mDataProp_21=21&mDataProp_22=22&mDataProp_23=23&mDataProp_24=24&mDataProp_25=25&mDataProp_26=26&mDataProp_27=27&mDataProp_28=28&mDataProp_29=29&mDataProp_30=30&mDataProp_31=31&mDataProp_32=32&mDataProp_33=33&mDataProp_34=34&mDataProp_35=35&mDataProp_36=36&mDataProp_37=37&mDataProp_38=38&mDataProp_39=39&mDataProp_40=40&mDataProp_41=41&mDataProp_42=42&mDataProp_43=43&mDataProp_44=44&mDataProp_45=45&mDataProp_46=46&mDataProp_47=47&mDataProp_48=48&mDataProp_49=49&mDataProp_50=50&mDataProp_51=51&mDataProp_52=52&mDataProp_53=53&mDataProp_54=54&mDataProp_55=55&mDataProp_56=56&mDataProp_57=57&mDataProp_58=58&mDataProp_59=59&mDataProp_60=60&mDataProp_61=61&mDataProp_62=62&mDataProp_63=63&mDataProp_64=64&mDataProp_65=65&mDataProp_66=66&mDataProp_67=67&mDataProp_68=68&mDataProp_69=69&mDataProp_70=70&mDataProp_71=71&mDataProp_72=72&mDataProp_73=73&sSearch=&bRegex=false&sSearch_0=&bRegex_0=false&bSearchable_0=true&sSearch_1=&bRegex_1=false&bSearchable_1=true&sSearch_2=&bRegex_2=false&bSearchable_2=true&sSearch_3=&bRegex_3=false&bSearchable_3=true&sSearch_4=&bRegex_4=false&bSearchable_4=true&sSearch_5=&bRegex_5=false&bSearchable_5=true&sSearch_6=&bRegex_6=false&bSearchable_6=true&sSearch_7=&bRegex_7=false&bSearchable_7=true&sSearch_8=&bRegex_8=false&bSearchable_8=true&sSearch_9=&bRegex_9=false&bSearchable_9=true&sSearch_10=&bRegex_10=false&bSearchable_10=true&sSearch_11=&bRegex_11=false&bSearchable_11=true&sSearch_12=&bRegex_12=false&bSearchable_12=true&sSearch_13=&bRegex_13=false&bSearchable_13=true&sSearch_14=&bRegex_14=false&bSearchable_14=true&sSearch_15=&bRegex_15=false&bSearchable_15=true&sSearch_16=&bRegex_16=false&bSearchable_16=true&sSearch_17=&bRegex_17=false&bSearchable_17=true&sSearch_18=&bRegex_18=false&bSearchable_18=true&sSearch_19=&bRegex_19=false&bSearchable_19=true&sSearch_20=&bRegex_20=false&bSearchable_20=true&sSearch_21=&bRegex_21=false&bSearchable_21=true&sSearch_22=&bRegex_22=false&bSearchable_22=true&sSearch_23=&bRegex_23=false&bSearchable_23=true&sSearch_24=&bRegex_24=false&bSearchable_24=true&sSearch_25=&bRegex_25=false&bSearchable_25=true&sSearch_26=&bRegex_26=false&bSearchable_26=true&sSearch_27=&bRegex_27=false&bSearchable_27=true&sSearch_28=&bRegex_28=false&bSearchable_28=true&sSearch_29=&bRegex_29=false&bSearchable_29=true&sSearch_30=&bRegex_30=false&bSearchable_30=true&sSearch_31=&bRegex_31=false&bSearchable_31=true&sSearch_32=&bRegex_32=false&bSearchable_32=true&sSearch_33=&bRegex_33=false&bSearchable_33=true&sSearch_34=&bRegex_34=false&bSearchable_34=true&sSearch_35=&bRegex_35=false&bSearchable_35=true&sSearch_36=&bRegex_36=false&bSearchable_36=true&sSearch_37=&bRegex_37=false&bSearchable_37=true&sSearch_38=&bRegex_38=false&bSearchable_38=true&sSearch_39=&bRegex_39=false&bSearchable_39=true&sSearch_40=&bRegex_40=false&bSearchable_40=true&sSearch_41=&bRegex_41=false&bSearchable_41=true&sSearch_42=&bRegex_42=false&bSearchable_42=true&sSearch_43=&bRegex_43=false&bSearchable_43=true&sSearch_44=&bRegex_44=false&bSearchable_44=true&sSearch_45=&bRegex_45=false&bSearchable_45=true&sSearch_46=&bRegex_46=false&bSearchable_46=true&sSearch_47=&bRegex_47=false&bSearchable_47=true&sSearch_48=&bRegex_48=false&bSearchable_48=true&sSearch_49=&bRegex_49=false&bSearchable_49=true&sSearch_50=&bRegex_50=false&bSearchable_50=true&sSearch_51=&bRegex_51=false&bSearchable_51=true&sSearch_52=&bRegex_52=false&bSearchable_52=true&sSearch_53=&bRegex_53=false&bSearchable_53=true&sSearch_54=&bRegex_54=false&bSearchable_54=true&sSearch_55=&bRegex_55=false&bSearchable_55=true&sSearch_56=&bRegex_56=false&bSearchable_56=true&sSearch_57=&bRegex_57=false&bSearchable_57=true&sSearch_58=&bRegex_58=false&bSearchable_58=true&sSearch_59=&bRegex_59=false&bSearchable_59=true&sSearch_60=&bRegex_60=false&bSearchable_60=true&sSearch_61=&bRegex_61=false&bSearchable_61=true&sSearch_62=&bRegex_62=false&bSearchable_62=true&sSearch_63=&bRegex_63=false&bSearchable_63=true&sSearch_64=&bRegex_64=false&bSearchable_64=true&sSearch_65=&bRegex_65=false&bSearchable_65=true&sSearch_66=&bRegex_66=false&bSearchable_66=true&sSearch_67=&bRegex_67=false&bSearchable_67=true&sSearch_68=&bRegex_68=false&bSearchable_68=true&sSearch_69=&bRegex_69=false&bSearchable_69=true&sSearch_70=&bRegex_70=false&bSearchable_70=true&sSearch_71=&bRegex_71=false&bSearchable_71=true&sSearch_72=&bRegex_72=false&bSearchable_72=true&sSearch_73=&bRegex_73=false&bSearchable_73=true&iSortCol_0=4&sSortDir_0=desc&iSortingCols=1&bSortable_0=false&bSortable_1=true&bSortable_2=true&bSortable_3=true&bSortable_4=true&bSortable_5=true&bSortable_6=true&bSortable_7=true&bSortable_8=false&bSortable_9=true&bSortable_10=true&bSortable_11=true&bSortable_12=true&bSortable_13=true&bSortable_14=true&bSortable_15=true&bSortable_16=true&bSortable_17=true&bSortable_18=true&bSortable_19=true&bSortable_20=true&bSortable_21=true&bSortable_22=true&bSortable_23=true&bSortable_24=true&bSortable_25=true&bSortable_26=true&bSortable_27=true&bSortable_28=true&bSortable_29=true&bSortable_30=true&bSortable_31=true&bSortable_32=true&bSortable_33=true&bSortable_34=true&bSortable_35=true&bSortable_36=true&bSortable_37=true&bSortable_38=true&bSortable_39=true&bSortable_40=true&bSortable_41=true&bSortable_42=true&bSortable_43=true&bSortable_44=true&bSortable_45=true&bSortable_46=true&bSortable_47=true&bSortable_48=true&bSortable_49=true&bSortable_50=true&bSortable_51=true&bSortable_52=true&bSortable_53=true&bSortable_54=true&bSortable_55=true&bSortable_56=true&bSortable_57=true&bSortable_58=true&bSortable_59=true&bSortable_60=true&bSortable_61=true&bSortable_62=true&bSortable_63=true&bSortable_64=true&bSortable_65=true&bSortable_66=true&bSortable_67=false&bSortable_68=false&bSortable_69=true&bSortable_70=true&bSortable_71=true&bSortable_72=true&bSortable_73=false&min_rating=40&max_rating=99&min_pace_tot=&max_pace_tot=&min_shooting_tot=&max_shooting_tot=&min_passing_tot=&max_passing_tot=&min_dribbling_tot=&max_dribbling_tot=&min_defending_tot=&max_defending_tot=&min_heading_tot=&max_heading_tot=&min_acceleration=&max_acceleration=&min_agility=&max_agility=&min_balance=&max_balance=&min_jumping=&max_jumping=&min_reactions=&max_reactions=&min_sprintspeed=&max_sprintspeed=&min_stamina=&max_stamina=&min_strength=&max_strength=&min_aggression=&max_aggression=&min_tact_aware=&max_tact_aware=&min_positioning=&max_positioning=&min_vision=&max_vision=&min_potential=&max_potential=&min_ball_control=&max_ball_control=&min_crossing=&max_crossing=&min_curve=&max_curve=&min_dribbling=&max_dribbling=&min_finishing=&max_finishing=&min_fk_acc=&max_fk_acc=&min_heading_acc=&max_heading_acc=&min_longpass=&max_longpass=&min_longshot=&max_longshot=&min_marking=&max_marking=&min_penalties=&max_penalties=&min_shortpass=&max_shortpass=&min_shotpower=&max_shotpower=&min_sliding_tackle=&max_sliding_tackle=&min_standing_tackle=&max_standing_tackle=&min_volleys=&max_volleys=&min_gk_diving=&max_gk_diving=&min_gk_handling=&max_gk_handling=&min_gk_kicking=&max_gk_kicking=&min_gk_reflexes=&max_gk_reflexes=&min_gk_speed=&max_gk_speed=&min_gk_positioning=&max_gk_positioning=&min_height=&max_height=&min_weight=&max_weight=&awr=%5B%5D&dwr=%5B%5D&skillmoves=%5B%5D&weakfoot=%5B%5D&preferredfoot=%5B%5D&console=1&type=goalkeeper&clubs=%5B39%2C189%2C1842%2C192%2C110734%2C1746%2C110374%2C110556%2C44%2C45%2C46%2C47%2C48%2C1843%2C200%2C52%2C1837%2C111974%2C54%2C55%5D&nations=%5B%5D&positions=%5B%5D&traits=%5B%5D&minprice=0&maxprice=0&cardtype=%5B%22ct1%22%2C%22ct2%22%2C%22ct3%22%5D&min_dob=&max_dob=&totw=&wf_csrf=8b7574639d3aaf69b932ce808752c196')

csv_row = "{name},{team},{season},{rtg},{pac},{sho},{pas},{dri},{def},{phy},{acc},{agi},{bal},{jum},{rea},{spr},{sta},{str},{agg},{int},{pos},{vis},{com},{ball_con},{crossing},{curve},{drib},{finish},{free_kick},{head},{long_pass},{long_shots},{marking},{pen},{short_pass},{shot_power},{slide_tackle},{stand_tackle},{volleys}"

seasons = {
    '2019-2020': [players_url_seriea_20, gk_url_seriea_20],
    '2018-2019': [players_url_seriea_19, gk_url_seriea_19],
    '2017-2018': [players_url_seriea_18, gk_url_seriea_18],
    '2016-2017': [players_url_seriea_17, gk_url_seriea_17]
}

out = []
for season in seasons:
    urls = seasons[season]
    for url in urls:
        out += parse_players(url, season)

f = open("fifa_stats_17_18_19_20.csv","w+")

f.write('name,team,season,rtg,pac,sho,pas,dri,def,phy,acc,agi,bal,jump,reactions,sprint speed,stamina,strenght,aggression,interceptions,positioning,vision,composure,ball control,crossing,curve,drib,finish,free_kick,head,long_pass,long_shots,marking,penalties,short_pass,shot_power,slide_tackle,stand_tackle,volleys\n')

for p in out:
    f.write('{player}\n'.format(player=p))
f.close()
