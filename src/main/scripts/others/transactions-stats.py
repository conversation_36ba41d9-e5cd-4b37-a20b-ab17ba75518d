from pymongo import MongoClient
from bson import ObjectId


def calculate_stats():
    client = MongoClient("")
    db=client.wsf

    transactions = db.transactions.find({"status" : "PAYED_OUT"})

    amount_bet = 0
    amount_won = 0

    for transaction in transactions:
        if (transaction['user'] != ObjectId("5acfeb21102cf0216fd9b19b")):
            print("found")
            amount_bet += int(transaction['amount'])
            amount_won += float(transaction['soldAmount'])

    print("Amount Bet {}").format(amount_bet)
    print("Amount Won by users {}").format(amount_won)

if __name__ == "__main__":
    calculate_stats()
