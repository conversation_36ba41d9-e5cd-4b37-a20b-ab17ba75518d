import boto3

s3 = boto3.resource('s3')
my_bucket = s3.Bucket('wsf-feeds')

for object_summary in my_bucket.objects.filter(Prefix="player-index/"):
    obj = s3.Object('wsf-feeds', object_summary.key)
    body = str(obj.get()['Body'].read())

    if body.find('<Country>Italy</Country>') != -1:
        print(f"FOUND!: {object_summary.key}")
        s3.Bucket('wsf-feeds').download_file(object_summary.key, '/Users/<USER>/projects/WSF/data-ingestor/src/main/scripts/votes/' + object_summary.key)
    else:
        print("Not found")
