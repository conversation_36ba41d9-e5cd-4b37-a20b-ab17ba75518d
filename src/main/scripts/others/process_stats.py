import sys
import csv

with open('team_stats_raw.csv', 'rU') as csvfile:
    statsreader = csv.reader(csvfile, delimiter=',')

    res = {}

    next(statsreader, None)
    for row in statsreader:
        tournament = row[0]
        team = row[1]
        points = row[2]
        goal_scored = row[3]
        goal_conceded = row[4]

        if tournament not in res:
            res[tournament] = {}

        if team not in res[tournament]:
            res[tournament][team] = {'points': points, 'goal_scored': goal_scored, 'goal_conceded': goal_conceded}

    # it will only contain the teams per season that are playing the following serie A season
    # because that's what we need in order to calculate odds for next season
    # eg. Pescara doesn't exist in 2015-2016 although they played in serie A
    # but they didn't play in serie A during season 2016-2017
    f = open("team_stats.csv","w+")
    f.write("Season,Team,p_a,gf_a,gs_a")

    for tournament in res:
        tour_dict = res[tournament]
        for team in tour_dict:
            team_dict = res[tournament][team]
            f.write(f"{tournament},{team},{team_dict['points']},{team_dict['goal_scored']},{team_dict['goal_conceded']}\n")
    f.close()



