import requests
from bson import ObjectId
from pymongo import MongoClient
import concurrent.futures

num_matches = 2
competition_id = "5d3a33fe999fc4072ab589af"
tournament_id = "5d3a33fe999fc4072ab589c2"
ingestor_url = "http://localhost:8080"
db_url = ""
db_name = "test_seriea"

client = MongoClient(db_url)
db = client[db_name]


def process_matches():
    matches = db.matches.find({'tournament._id': ObjectId(tournament_id), 'sequentialId': { '$lte': num_matches}})

    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        futures = []
        for match in matches:
            futures.append(executor.submit(process_match, match))


def process_match(match):
    match_id = match['_id']

    print(f"Processing match {match_id}")

    url = f"{ingestor_url}/v1/competitions/{competition_id}/" \
          f"tournaments/{tournament_id}/matches/{match_id}/calculate-post-match-stats"

    resp = requests.post(url)
    if resp.status_code != 200:
        raise Exception("Response from server {}".format(resp.status_code))

    print(f"Match {match_id} processed")


if __name__ == '__main__':
    process_matches()
