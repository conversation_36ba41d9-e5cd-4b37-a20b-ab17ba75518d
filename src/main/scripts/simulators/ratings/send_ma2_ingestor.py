import collections
import datetime as datetime
import json
import os
import requests
import sys
import re
from time import sleep


#
# Watch out, launching this will result in inaccurate results when it comes for the engine to generate contracts
# Players are stored in the db with their initial teams, but the feeds may contain their updated teams
#
def send_ma2_in_folder(ingestor_url, folder, sort_matches_by_time=False):

    url = "{}/v1/competitions/5fbf66ad6edb896fdcfefd47/tournaments/5d3a33fe999fc4072ab589e4/stats/ma2".format(ingestor_url)

    filenames = os.listdir(folder)

    if sort_matches_by_time:
        ts_to_filename = dict()

        for filename in filenames:
            file_path = folder+'/'+filename
            with open(file_path, 'r', encoding="utf-8") as feed:
                print(filename)
                parsed = json.load(feed)
                match_id = parsed["matchInfo"]["id"]
                date = datetime.datetime.strptime(parsed["matchInfo"]["date"], "%Y-%m-%dZ")
                time_feed = parsed["matchInfo"]["time"]
                time = datetime.datetime.now().time() if time_feed == '' else datetime.datetime.strptime(parsed["matchInfo"]["time"], "%H:%M:%SZ").time()
                ts = datetime.datetime.combine(date, time).timestamp()
                ts_to_filename[f"{ts}_{match_id}"] = filename

            ts_to_filename_sorted = sorted(ts_to_filename.items())
            filenames = collections.OrderedDict(ts_to_filename_sorted).values()
    else:
        filenames = sorted_alphanumeric(filenames)

    for filename in filenames:
        file_path = folder+'/'+filename
        if os.path.isfile(file_path):
            print("sending ma2: {} to url {}".format(filename, url))
            with open(file_path, 'r', encoding="utf-8") as feed:
                feed_str = feed.read()

                send_ma2_to_ingestor(feed_str, url)

def sorted_alphanumeric(data):
    convert = lambda text: int(text) if text.isdigit() else text.lower()
    alphanum_key = lambda key: [ convert(c) for c in re.split('([0-9]+)', key) ]
    return sorted(data, key=alphanum_key)

def send_ma2_to_ingestor(feed_str, url):
    date = datetime.datetime.now().astimezone().replace(microsecond=0).isoformat()
    replaced = feed_str.replace("$TIMESTAMP", date)
    resp = requests.post(url, data=replaced.encode('utf-8'))
    if resp.status_code == 504:
        print("504 from LB, it's fine, let's just give the ingestor some time to recover")
        sleep(40)
    elif resp.status_code != 200:
        raise Exception("Response from server {}".format(resp.status_code))
    sleep(5)


if __name__ == "__main__":
    ingestor_url = (len(sys.argv) > 1 and sys.argv[1]) or "http://localhost:8080"
    folder = (len(sys.argv) > 1 and sys.argv[2]) or "/Users/<USER>/projects/WSF/repos/data-ingestor/data/opta/liga/20-21"
    send_ma2_in_folder(ingestor_url, folder, True)
