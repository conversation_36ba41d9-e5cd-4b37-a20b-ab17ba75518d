import sys

from send_ma2_ingestor import send_ma2_in_folder


def send_ratings_loop(ingestor_url, folder):
    while(True):
        send_ma2_in_folder(ingestor_url, folder, False)


if __name__ == "__main__":
    ingestor_url = "https://ingestor.sandbox.wallstreetfootball.io"
    folder = "seriea/20-21/live/"

    if len(sys.argv) == 2:
        ingestor_url = sys.argv[1]
    elif len(sys.argv) == 3:
        ingestor_url = sys.argv[1]
        folder = sys.argv[2]

    print("Sending live ratings to {} from {}".format(ingestor_url, folder))
    send_ratings_loop(ingestor_url, folder)

