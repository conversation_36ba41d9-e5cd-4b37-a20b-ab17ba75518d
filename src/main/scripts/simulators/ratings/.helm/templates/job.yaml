apiVersion: batch/v1
kind: Job
metadata:
  name: live-ratings-simulator
spec:
  template:
    spec:
      containers:
        - name: live-ratings-simulator
          image: "{{ .Values.deployment.image.repository }}:{{ .Chart.AppVersion }}"
          imagePullPolicy: IfNotPresent
      restartPolicy: Never
      { { - with .Values.deployment.nodeSelector } }
      nodeSelector:
      { { - toYaml . | nindent 8 } }
      { { - end } }
  backoffLimit: 4
