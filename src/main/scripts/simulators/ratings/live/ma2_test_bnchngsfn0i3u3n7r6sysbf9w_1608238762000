{"matchInfo": {"id": "bnchngsfn0i3u3n7r6sysbf9w", "coverageLevel": "15", "date": "2020-12-17Z", "time": "19:45:00Z", "week": "12", "attendanceInfoId": "1", "attendanceInfo": "Behind Closed Doors", "lastUpdated": "$TIMESTAMP", "description": "Roma vs Torino", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "4b80uzt9gxak7d1vaa5jp17qi", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "2020/2021"}, "stage": {"id": "4bkrobysg6zi7tugsqlj403my", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "7gnly6999wao1xarwct4p8fe9", "name": "Torino", "shortName": "Torino", "officialName": "Torino FC", "code": "TOR", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"matchTime": 52, "periodId": 2, "matchStatus": "Playing", "period": [{"id": 1, "start": "2020-12-17T19:47:58Z", "end": "2020-12-17T20:34:57Z", "lengthMin": 46, "lengthSec": 59}, {"id": 2, "start": "2020-12-17T20:51:55Z"}], "scores": {"ht": {"home": 2, "away": 0}, "ft": {"home": 2, "away": 0}, "total": {"home": 2, "away": 0}}}, "goal": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 27, "timeMinSec": "26:56", "lastUpdated": "2020-12-17T20:15:36Z", "timestamp": "2020-12-17T20:14:54Z", "type": "G", "scorerId": "1xscywgyfqisqexmpfvbf9g45", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246037083", "homeScore": 1, "awayScore": 0}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 43, "timeMinSec": "42:37", "lastUpdated": "2020-12-17T20:30:41Z", "timestamp": "2020-12-17T20:30:36Z", "type": "PG", "scorerId": "5fbnkkf1saix4lonwx1i9k7o5", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246044707", "homeScore": 2, "awayScore": 0}], "card": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 7, "timeMinSec": "6:32", "lastUpdated": "2020-12-17T19:54:35Z", "timestamp": "2020-12-17T19:54:31Z", "type": "YC", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246026767", "cardReason": "F<PERSON>l"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 14, "timeMinSec": "13:48", "lastUpdated": "2020-12-17T20:01:55Z", "timestamp": "2020-12-17T20:01:47Z", "type": "Y2C", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246029583", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 20, "timeMinSec": "20:00", "lastUpdated": "2020-12-17T20:08:20Z", "timestamp": "2020-12-17T20:07:58Z", "type": "YC", "playerId": "46xszpgn7zit2cakaoyws551x", "playerName": "<PERSON>", "optaEventId": "2246032737", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 23, "timeMinSec": "22:04", "lastUpdated": "2020-12-17T20:10:05Z", "timestamp": "2020-12-17T20:10:03Z", "type": "YC", "playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "playerName": "<PERSON><PERSON>", "optaEventId": "2246033929", "cardReason": "F<PERSON>l"}], "substitute": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 23, "timeMinSec": "22:20", "lastUpdated": "2020-12-17T20:10:30Z", "timestamp": "2020-12-17T20:10:18Z", "playerOnId": "140ehcmf8mbibw8jkfwncupjp", "playerOnName": "<PERSON><PERSON>", "playerOffId": "e2vhgeqrw3uy0tstydc5nldxx", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 45, "timeMinSec": "44:23", "lastUpdated": "2020-12-17T20:32:27Z", "timestamp": "2020-12-17T20:32:22Z", "playerOnId": "e2w8qlrfmunsg7oq4733cu6l1", "playerOnName": "<PERSON><PERSON>", "playerOffId": "140ehcmf8mbibw8jkfwncupjp", "playerOffName": "<PERSON><PERSON>", "subReason": "Injury"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:02Z", "timestamp": "2020-12-17T20:50:49Z", "playerOnId": "cddggachzf198p1yhq9owtofd", "playerOnName": "<PERSON><PERSON>", "playerOffId": "6fttn5mk6n1lstqt0dl5xzn4l", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:02Z", "timestamp": "2020-12-17T20:50:55Z", "playerOnId": "8lgsqmwavspz4fv7t11rb6j9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "46xszpgn7zit2cakaoyws551x", "playerOffName": "<PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:27Z", "timestamp": "2020-12-17T20:51:21Z", "playerOnId": "eqggzoepu1d4t13fw6ybebtn9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "9qrewt049r6yq88c715ymau39", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:36Z", "timestamp": "2020-12-17T20:51:28Z", "playerOnId": "55hr275k72a0ka78irp7z6uw9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "cdrzpuj49ploquvdolgh5c3bp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "3421", "player": [{"playerId": "26atxu4fr8f87oi8mt6z70mhh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "saves", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accurateKeeperThrows", "value": "2"}, {"type": "openPlayPass", "value": "4"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "4"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "possLostAll", "value": "1"}, {"type": "savedObox", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "ab4kuqa33849h6z77ksuqhdk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "17"}, {"type": "accuratePass", "value": "59"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "15"}, {"type": "rightsidePass", "value": "30"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "69"}, {"type": "totalFwdZonePass", "value": "32"}, {"type": "accurateFwdZonePass", "value": "29"}, {"type": "totalChippedPass", "value": "3"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "30"}, {"type": "passesRight", "value": "1"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "57"}, {"type": "totalBackZonePass", "value": "31"}, {"type": "totalLongBalls", "value": "5"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "61"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "63"}, {"type": "totalLaunches", "value": "2"}, {"type": "fwdPass", "value": "14"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "19"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "headPass", "value": "3"}, {"type": "possLostCtrl", "value": "4"}, {"type": "aerialLost", "value": "1"}, {"type": "finalThirdEntries", "value": "12"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "7"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "7o9bkf7no8wz167ingslfimdx", "firstName": "<PERSON>", "lastName": "Smalling", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "28"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "53"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "15"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "58"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "20"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "33"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "52"}, {"type": "totalBackZonePass", "value": "33"}, {"type": "totalLongBalls", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "53"}, {"type": "totalPass", "value": "54"}, {"type": "fwdPass", "value": "9"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "10"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "headPass", "value": "3"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "9"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "27"}, {"type": "accuratePass", "value": "41"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "48"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "20"}, {"type": "accurateFwdZonePass", "value": "19"}, {"type": "totalChippedPass", "value": "2"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "passesRight", "value": "5"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "41"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attLfTotal", "value": "2"}, {"type": "openPlayPass", "value": "44"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "44"}, {"type": "attLfTarget", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "attSvLowLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attSvLowRight", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "3"}, {"type": "backwardPass", "value": "1"}, {"type": "fouls", "value": "3"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "28"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "19"}, {"type": "rightsidePass", "value": "18"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "47"}, {"type": "totalFwdZonePass", "value": "28"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "20"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "8"}, {"type": "totalThrows", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "attOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "32"}, {"type": "totalPass", "value": "33"}, {"type": "fwdPass", "value": "6"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "attMissRight", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "15"}, {"type": "attCmissRight", "value": "1"}, {"type": "totalCrossNocorner", "value": "4"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "11"}, {"type": "possLostAll", "value": "11"}, {"type": "totalCross", "value": "4"}, {"type": "attIboxMiss", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "11"}, {"type": "crosses18yard", "value": "4"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "9"}, {"type": "penAreaEntries", "value": "6"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalClearance", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "5fbnkkf1saix4lonwx1i9k7o5", "firstName": "Jordan", "lastName": "Veretout", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "35"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "16"}, {"type": "rightsidePass", "value": "15"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "40"}, {"type": "totalFwdZonePass", "value": "33"}, {"type": "accurateFwdZonePass", "value": "32"}, {"type": "totalChippedPass", "value": "2"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attPenGoal", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "passesRight", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "34"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalLongBalls", "value": "7"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "35"}, {"type": "totalPass", "value": "36"}, {"type": "fwdPass", "value": "6"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "16"}, {"type": "attRfGoal", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "13"}, {"type": "bigChanceScored", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "7"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "fouls", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bh4a2zneilpvi1t7etuhkfl61", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "6"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "19"}, {"type": "dispossessed", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "47"}, {"type": "wasFouled", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "18"}, {"type": "rightsidePass", "value": "14"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "66"}, {"type": "totalFwdZonePass", "value": "43"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "accurateFwdZonePass", "value": "38"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "45"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "50"}, {"type": "totalPass", "value": "52"}, {"type": "fwdPass", "value": "13"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "7"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "15"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "12"}, {"type": "possLostAll", "value": "8"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "challengeLost", "value": "3"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "8"}, {"type": "finalThirdEntries", "value": "9"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "6"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "totalContest", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "7"}, {"type": "attemptsObox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "46xszpgn7zit2cakaoyws551x", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "matchName": "<PERSON>", "shirtNumber": 33, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "accuratePass", "value": "23"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "34"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "12"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "passesRight", "value": "7"}, {"type": "totalThrows", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "21"}, {"type": "totalPass", "value": "24"}, {"type": "fwdPass", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attMissRight", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "totalCross", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "14"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accurateThrows", "value": "6"}, {"type": "fouls", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "1xscywgyfqisqexmpfvbf9g45", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Attacking Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "5"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "32"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "24"}, {"type": "rightsidePass", "value": "17"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "53"}, {"type": "totalFwdZonePass", "value": "38"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "28"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "passesRight", "value": "3"}, {"type": "totalThrows", "value": "3"}, {"type": "attGoalHighLeft", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "31"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "40"}, {"type": "totalPass", "value": "41"}, {"type": "fwdPass", "value": "11"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"type": "attRfGoal", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "16"}, {"type": "possLostAll", "value": "13"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possLostCtrl", "value": "13"}, {"type": "aerialLost", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalContest", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "blockedScoringAtt", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "attRfTotal", "value": "2"}, {"type": "attBxRight", "value": "1"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "28"}, {"type": "totalFinalThirdPasses", "value": "12"}, {"type": "rightsidePass", "value": "10"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "47"}, {"type": "totalFwdZonePass", "value": "26"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "21"}, {"type": "totalChippedPass", "value": "5"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "passesRight", "value": "8"}, {"type": "successfulOpenPlayPass", "value": "26"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "attOpenplay", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "30"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "32"}, {"type": "fwdPass", "value": "7"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "possLostAll", "value": "10"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "10"}, {"type": "attIboxBlocked", "value": "2"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "wonCorners", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "7"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalLayoffs", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "totalContest", "value": "3"}, {"type": "attemptsIbox", "value": "2"}, {"type": "cornerTaken", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "a5eki9p236y4zxmp9qz202pcl", "firstName": "<PERSON>in", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "9"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "16"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "totalChippedPass", "value": "2"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalLongBalls", "value": "2"}, {"type": "assistPenaltyWon", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "11"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "5"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "penaltyWon", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "dhs8pujk55ewcis7y5alchu22", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Calafiori", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "14cbkgi3dk03rslbdb3kfr8fe", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "143brmwsfow8o0svtdq98tiw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 42, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "321h649n4oqjzr0g44k7qw1cl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "3ftcqipzb86lzdztg4j1oz2s5", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "touches", "value": "5"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "accuratePass", "value": "9"}, {"type": "rightsidePass", "value": "1"}, {"type": "touches", "value": "10"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "openPlayPass", "value": "10"}, {"type": "totalPass", "value": "10"}, {"type": "fwdPass", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "52e2ogoex674bz6pcwluapdxx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mayoral <PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "68cu39v9m8pykapkh2b97ltqt", "firstName": "Antonio", "lastName": "Mirante", "matchName": "<PERSON><PERSON>", "shirtNumber": 83, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8n0c5t5wzfkoauryyzy8be0gl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "64h0x8sur55l07oaiuog4v2z9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d02p1ldr57bkxcpmq12vtvhsl", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "23lqodnib2we4fw5l286m5x3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "22", "sh": "3", "type": "duelLost", "value": "25"}, {"fh": "1", "sh": "0", "type": "divingSave", "value": "1"}, {"fh": "1", "sh": "2", "type": "blockedScoringAtt", "value": "3"}, {"fh": "111", "sh": "30", "type": "leftside<PERSON><PERSON>", "value": "141"}, {"fh": "2", "sh": "0", "type": "possWonAtt3rd", "value": "2"}, {"fh": "4", "sh": "0", "type": "dispossessed", "value": "4"}, {"fh": "4", "sh": "2", "type": "attRfTotal", "value": "6"}, {"fh": "1", "sh": "0", "type": "attBxRight", "value": "1"}, {"fh": "1", "sh": "0", "type": "sixYardBlock", "value": "1"}, {"fh": "287", "sh": "85", "type": "accuratePass", "value": "372"}, {"fh": "4", "sh": "0", "type": "won<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "91", "sh": "33", "type": "totalFinalThirdPasses", "value": "124"}, {"fh": "101", "sh": "33", "type": "rightsidePass", "value": "134"}, {"fh": "2", "sh": "0", "type": "attemptsConcededIbox", "value": "2"}, {"fh": "396", "sh": "106", "type": "touches", "value": "502"}, {"fh": "200", "sh": "75", "type": "totalFwdZonePass", "value": "275"}, {"fh": "2", "sh": "1", "type": "attAssistOpenplay", "value": "3"}, {"fh": "5", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "1", "sh": "0", "type": "goalsOpenplay", "value": "1"}, {"fh": "170", "sh": "68", "type": "accurateFwdZonePass", "value": "238"}, {"fh": "15", "sh": "5", "type": "totalChippedPass", "value": "20"}, {"fh": "1", "sh": "0", "type": "effectiveHeadClearance", "value": "1"}, {"fh": "1", "sh": "0", "type": "lostCorners", "value": "1"}, {"fh": "1", "sh": "0", "type": "fouledFinalThird", "value": "1"}, {"fh": "2", "sh": "0", "type": "saves", "value": "2"}, {"fh": "4", "sh": "0", "type": "ontargetScoringAtt", "value": "4"}, {"fh": "7", "sh": "2", "type": "totalScoringAtt", "value": "9"}, {"fh": "5", "sh": "1", "type": "blocked<PERSON><PERSON>", "value": "6"}, {"fh": "1", "sh": "0", "type": "attPenGoal", "value": "1"}, {"fh": "1", "sh": "0", "type": "attemptsConcededObox", "value": "1"}, {"fh": "23", "sh": "4", "type": "ballRecovery", "value": "27"}, {"fh": "0", "sh": "2", "type": "subsMade", "value": "2"}, {"fh": "9", "sh": "0", "type": "possWonDef3rd", "value": "9"}, {"fh": "117", "sh": "17", "type": "accurateBackZonePass", "value": "134"}, {"fh": "28", "sh": "13", "type": "passesRight", "value": "41"}, {"fh": "10", "sh": "3", "type": "totalThrows", "value": "13"}, {"fh": "1", "sh": "0", "type": "attOboxTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "attGoalHighLeft", "value": "1"}, {"fh": "278", "sh": "81", "type": "successfulOpenPlayPass", "value": "359"}, {"fh": "123", "sh": "18", "type": "totalBackZonePass", "value": "141"}, {"fh": "2", "sh": "0", "type": "accurateLayoffs", "value": "2"}, {"fh": "25", "sh": "5", "type": "totalLongBalls", "value": "30"}, {"fh": "2", "sh": "0", "type": "totalYellowCard", "value": "2"}, {"fh": "2", "sh": "0", "type": "accurateKeeperThrows", "value": "2"}, {"fh": "1", "sh": "1", "type": "attObxCentre", "value": "2"}, {"fh": "5", "sh": "2", "type": "attOpenplay", "value": "7"}, {"fh": "10", "sh": "4", "type": "possWonMid3rd", "value": "14"}, {"fh": "5", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "2"}, {"fh": "1", "sh": "0", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "headClea<PERSON>", "value": "1"}, {"fh": "3", "sh": "0", "type": "attLfTotal", "value": "3"}, {"fh": "308", "sh": "88", "type": "openPlayPass", "value": "396"}, {"fh": "4", "sh": "1", "type": "aerialWon", "value": "5"}, {"fh": "317", "sh": "92", "type": "totalPass", "value": "409"}, {"fh": "2", "sh": "0", "type": "attLfTarget", "value": "2"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "4", "sh": "0", "type": "totalLaunches", "value": "4"}, {"fh": "62", "sh": "16", "type": "fwdPass", "value": "78"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "2", "sh": "0", "type": "goals", "value": "2"}, {"fh": "17", "sh": "3", "type": "touchesInOppBox", "value": "20"}, {"fh": "1", "sh": "0", "type": "totalCornersIntobox", "value": "1"}, {"fh": "2", "sh": "0", "type": "attMissRight", "value": "2"}, {"fh": "2", "sh": "1", "type": "attBxCentre", "value": "3"}, {"fh": "2", "sh": "1", "type": "ontargetAttAssist", "value": "3"}, {"fh": "26", "sh": "8", "type": "longPassOwnToOpp", "value": "34"}, {"fh": "2", "sh": "0", "type": "attIboxGoal", "value": "2"}, {"fh": "8", "sh": "2", "type": "accurateChippedPass", "value": "10"}, {"fh": "21", "sh": "5", "type": "duel<PERSON>on", "value": "26"}, {"fh": "74", "sh": "27", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "101"}, {"fh": "2", "sh": "0", "type": "attRfGoal", "value": "2"}, {"fh": "1", "sh": "0", "type": "attCmissRight", "value": "1"}, {"fh": "8", "sh": "4", "type": "fkFoulWon", "value": "12"}, {"fh": "5", "sh": "1", "type": "totalCrossNocorner", "value": "6"}, {"fh": "2", "sh": "0", "type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "2", "sh": "0", "type": "attBxLeft", "value": "2"}, {"fh": "1", "sh": "0", "type": "successfulPutThrough", "value": "1"}, {"fh": "5", "sh": "0", "type": "totalTackle", "value": "5"}, {"fh": "1", "sh": "0", "type": "attSvLowLeft", "value": "1"}, {"fh": "62", "sh": "21", "type": "passesLeft", "value": "83"}, {"fh": "1", "sh": "0", "type": "bigChanceScored", "value": "1"}, {"fh": "2", "sh": "0", "type": "accurateLaunches", "value": "2"}, {"fh": "52", "sh": "9", "type": "possLostAll", "value": "61"}, {"fh": "21", "sh": "5", "type": "accurateLongBalls", "value": "26"}, {"fh": "4", "sh": "0", "type": "challengeLost", "value": "4"}, {"fh": "6", "sh": "1", "type": "totalCross", "value": "7"}, {"fh": "1", "sh": "0", "type": "attGoalLowRight", "value": "1"}, {"fh": "0", "sh": "1", "type": "attOboxBlocked", "value": "1"}, {"fh": "2", "sh": "0", "type": "attIboxMiss", "value": "2"}, {"fh": "1", "sh": "0", "type": "savedObox", "value": "1"}, {"fh": "7", "sh": "1", "type": "unsuccessfulTouch", "value": "8"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "sh": "0", "type": "forwardGoals", "value": "1"}, {"fh": "52", "sh": "9", "type": "possLostCtrl", "value": "61"}, {"fh": "1", "sh": "1", "type": "attIboxBlocked", "value": "2"}, {"fh": "2", "sh": "0", "type": "aerialLost", "value": "2"}, {"fh": "1", "sh": "0", "type": "attSvLowRight", "value": "1"}, {"fh": "5", "sh": "1", "type": "crosses18yard", "value": "6"}, {"fh": "30", "sh": "14", "type": "finalThirdEntries", "value": "44"}, {"fh": "5", "sh": "0", "type": "effectiveClearance", "value": "5"}, {"fh": "7", "sh": "3", "type": "fkFoulLost", "value": "10"}, {"fh": "2", "sh": "0", "type": "wonCorners", "value": "2"}, {"fh": "62", "sh": "78.2", "type": "possessionPercentage", "value": "65"}, {"fh": "2", "sh": "1", "type": "interception", "value": "3"}, {"fh": "6", "sh": "2", "type": "attemptedTackleFoul", "value": "8"}, {"fh": "43", "sh": "13", "type": "backwardPass", "value": "56"}, {"type": "firstHalfGoals", "value": "2"}, {"fh": "2", "sh": "1", "type": "interceptionWon", "value": "3"}, {"fh": "13", "sh": "3", "type": "penAreaEntries", "value": "16"}, {"fh": "10", "sh": "3", "type": "accurateThrows", "value": "13"}, {"fh": "2", "sh": "0", "type": "totalLayoffs", "value": "2"}, {"fh": "1", "sh": "1", "type": "bigChanceMissed", "value": "2"}, {"fh": "1", "sh": "0", "type": "penaltyWon", "value": "1"}, {"fh": "10", "sh": "0", "type": "totalContest", "value": "10"}, {"fh": "5", "sh": "0", "type": "totalClearance", "value": "5"}, {"fh": "20", "sh": "8", "type": "longPassOwnToOppSuccess", "value": "28"}, {"fh": "1", "sh": "0", "type": "savedIbox", "value": "1"}, {"fh": "1", "sh": "1", "type": "attemptsObox", "value": "2"}, {"fh": "2", "sh": "1", "type": "totalAttAssist", "value": "3"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "6", "sh": "1", "type": "attemptsIbox", "value": "7"}, {"fh": "2", "sh": "0", "type": "cornerTaken", "value": "2"}, {"type": "formationUsed", "value": "3421"}], "kit": {"id": "8380", "colour1": "#990000", "type": "home"}}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "formationUsed": "3511", "player": [{"playerId": "5syage212151fcixahbq3k8ut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "accuratePass", "value": "11"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "22"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalChippedPass", "value": "4"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "saves", "value": "2"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "diveSave", "value": "2"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "penaltyFaced", "value": "1"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "totalLongBalls", "value": "7"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "goalKicks", "value": "2"}, {"type": "openPlayPass", "value": "15"}, {"type": "totalPass", "value": "18"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "10"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "6"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possLostAll", "value": "7"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "savedObox", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "22x1ucwwak58wi6r5x1j6ohk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "21"}, {"type": "rightsidePass", "value": "12"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "32"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "5"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "17"}, {"type": "successfulOpenPlayPass", "value": "21"}, {"type": "totalBackZonePass", "value": "21"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "28"}, {"type": "totalPass", "value": "28"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "7"}, {"type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "6"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "7"}, {"type": "possLostCtrl", "value": "7"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "4042kk7ylrs378rh26b7mh42x", "firstName": "<PERSON>yan<PERSON>", "lastName": "Silveira Neves Vojnović", "matchName": "Lyanco", "shirtNumber": 4, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "accuratePass", "value": "40"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "rightsidePass", "value": "16"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "52"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "3"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "38"}, {"type": "successfulOpenPlayPass", "value": "39"}, {"type": "totalBackZonePass", "value": "40"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "42"}, {"type": "totalPass", "value": "43"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "10"}, {"type": "interceptionsInBox", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "effectiveClearance", "value": "4"}, {"type": "interception", "value": "2"}, {"type": "backwardPass", "value": "5"}, {"type": "interceptionWon", "value": "2"}, {"type": "totalClearance", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "d196vatyvq6ycn1q7d477d2i1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> Silva <PERSON>ci<PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "19"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "29"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "penaltyConceded", "value": "1"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "44"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "26"}, {"type": "passesRight", "value": "1"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "totalBackZonePass", "value": "27"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "28"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "30"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "effectiveClearance", "value": "3"}, {"type": "interception", "value": "2"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "totalClearance", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "16kv5p8we3pwqpt5ktwrb7mj9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Vojvoda", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "dispossessed", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "30"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "passesRight", "value": "1"}, {"type": "totalThrows", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "12"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "16"}, {"type": "totalPass", "value": "17"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "possLostAll", "value": "7"}, {"type": "totalCross", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "crosses18yard", "value": "2"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "interceptionWon", "value": "2"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalContest", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "6ol411t0mogw6m83heiqd0m39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "wasFouled", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "22"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "8"}, {"type": "successfulOpenPlayPass", "value": "12"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "12"}, {"type": "totalPass", "value": "13"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "totalContest", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9qrewt049r6yq88c715ymau39", "firstName": "Soualiho", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "23"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "touches", "value": "30"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "totalChippedPass", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "20"}, {"type": "totalPass", "value": "23"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "8"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "e2vhgeqrw3uy0tstydc5nldxx", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "8"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "fwdPass", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "challengeLost", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "f065kc6qn2cpxjxcm6i08rz3e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "3"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "redCard", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "cdrzpuj49ploquvdolgh5c3bp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "11"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "goalsConceded", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "11"}, {"type": "fwdPass", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "97mmzzi3dwdos6jtj2m05w4ph", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "5"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "attRfTotal", "value": "2"}, {"type": "accuratePass", "value": "14"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "touches", "value": "33"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "attSvHighCentre", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "8"}, {"type": "passesRight", "value": "1"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "12"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attFreekickTotal", "value": "1"}, {"type": "openPlayPass", "value": "20"}, {"type": "totalPass", "value": "22"}, {"type": "fwdPass", "value": "6"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attBxLeft", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "10"}, {"type": "challengeLost", "value": "1"}, {"type": "attFreekickTarget", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "2"}, {"type": "possLostCtrl", "value": "10"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "attSvLowRight", "value": "1"}, {"type": "attHdTotal", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "9"}, {"type": "penAreaEntries", "value": "2"}, {"type": "totalLayoffs", "value": "3"}, {"type": "fouls", "value": "3"}, {"type": "attHdTarget", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "140ehcmf8mbibw8jkfwncupjp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "duelLost", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "5"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "shieldBallOop", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "19gglji6ai70jiu7iqpkaxbyt", "firstName": "<PERSON>", "lastName": "Bonaz<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e2w8qlrfmunsg7oq4733cu6l1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalPass", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "thnje2i2s96fcbuscj57053p", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d73maq8id4xflzaj6jfxlduj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eqggzoepu1d4t13fw6ybebtn9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 88, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "4"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "5"}, {"type": "possLostAll", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "9ak3zczupeken1itsmkherbh1", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "23avr8oet7764jflt8y1o1gb9", "firstName": "Antonio", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "55hr275k72a0ka78irp7z6uw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Segre", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "8ud1kq9mdgukhlbda5vcn0s0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 39, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "f38rlbvo9k6kommwf1n3frfje", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 73, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "6gpsygusukoecmq6bnq65rfmd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "21", "sh": "5", "type": "duelLost", "value": "26"}, {"fh": "2", "sh": "0", "type": "divingSave", "value": "2"}, {"fh": "1", "sh": "0", "type": "blockedScoringAtt", "value": "1"}, {"fh": "64", "sh": "7", "type": "leftside<PERSON><PERSON>", "value": "71"}, {"fh": "1", "sh": "0", "type": "possWonAtt3rd", "value": "1"}, {"fh": "3", "sh": "0", "type": "dispossessed", "value": "3"}, {"fh": "1", "sh": "0", "type": "accurateCross", "value": "1"}, {"fh": "2", "sh": "0", "type": "attRfTotal", "value": "2"}, {"fh": "171", "sh": "19", "type": "accuratePass", "value": "190"}, {"fh": "6", "sh": "0", "type": "won<PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "20", "sh": "0", "type": "totalFinalThirdPasses", "value": "20"}, {"fh": "49", "sh": "9", "type": "rightsidePass", "value": "58"}, {"fh": "1", "sh": "0", "type": "penaltyConceded", "value": "1"}, {"fh": "6", "sh": "1", "type": "attemptsConcededIbox", "value": "7"}, {"fh": "273", "sh": "36", "type": "touches", "value": "309"}, {"fh": "64", "sh": "4", "type": "totalFwdZonePass", "value": "68"}, {"fh": "2", "sh": "0", "type": "attAssistOpenplay", "value": "2"}, {"fh": "4", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "1", "sh": "0", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "48", "sh": "1", "type": "accurateFwdZonePass", "value": "49"}, {"fh": "1", "sh": "0", "type": "attSvHighCentre", "value": "1"}, {"fh": "11", "sh": "4", "type": "totalChippedPass", "value": "15"}, {"fh": "6", "sh": "0", "type": "effectiveHeadClearance", "value": "6"}, {"fh": "2", "sh": "0", "type": "lostCorners", "value": "2"}, {"fh": "1", "sh": "0", "type": "fouledFinalThird", "value": "1"}, {"fh": "2", "sh": "0", "type": "goalsConceded", "value": "2"}, {"fh": "2", "sh": "0", "type": "saves", "value": "2"}, {"fh": "2", "sh": "0", "type": "ontargetScoringAtt", "value": "2"}, {"fh": "3", "sh": "0", "type": "totalScoringAtt", "value": "3"}, {"fh": "5", "sh": "1", "type": "blocked<PERSON><PERSON>", "value": "6"}, {"fh": "1", "sh": "1", "type": "attemptsConcededObox", "value": "2"}, {"fh": "22", "sh": "6", "type": "ballRecovery", "value": "28"}, {"fh": "2", "sh": "2", "type": "subsMade", "value": "4"}, {"fh": "9", "sh": "6", "type": "possWonDef3rd", "value": "15"}, {"fh": "124", "sh": "18", "type": "accurateBackZonePass", "value": "142"}, {"fh": "8", "sh": "0", "type": "passesRight", "value": "8"}, {"fh": "8", "sh": "0", "type": "totalThrows", "value": "8"}, {"fh": "1", "sh": "0", "type": "attOboxTarget", "value": "1"}, {"fh": "162", "sh": "15", "type": "successfulOpenPlayPass", "value": "177"}, {"fh": "1", "sh": "0", "type": "penaltyFaced", "value": "1"}, {"fh": "135", "sh": "22", "type": "totalBackZonePass", "value": "157"}, {"fh": "1", "sh": "0", "type": "accurateLayoffs", "value": "1"}, {"fh": "15", "sh": "4", "type": "totalLongBalls", "value": "19"}, {"fh": "2", "sh": "0", "type": "goalsConcededIbox", "value": "2"}, {"fh": "1", "sh": "0", "type": "attObxCentre", "value": "1"}, {"fh": "2", "sh": "0", "type": "attOpenplay", "value": "2"}, {"fh": "11", "sh": "0", "type": "possWonMid3rd", "value": "11"}, {"fh": "5", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "1", "sh": "0", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "attFreekickTotal", "value": "1"}, {"fh": "6", "sh": "0", "type": "headClea<PERSON>", "value": "6"}, {"fh": "2", "sh": "0", "type": "goalKicks", "value": "2"}, {"fh": "1", "sh": "0", "type": "totalRedCard", "value": "1"}, {"fh": "186", "sh": "22", "type": "openPlayPass", "value": "208"}, {"fh": "2", "sh": "0", "type": "aerialWon", "value": "2"}, {"fh": "196", "sh": "26", "type": "totalPass", "value": "222"}, {"fh": "3", "sh": "2", "type": "totalLaunches", "value": "5"}, {"fh": "46", "sh": "5", "type": "fwdPass", "value": "51"}, {"fh": "1", "sh": "2", "type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "4", "sh": "0", "type": "touchesInOppBox", "value": "4"}, {"fh": "3", "sh": "0", "type": "interceptionsInBox", "value": "3"}, {"fh": "1", "sh": "0", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "sh": "0", "type": "attBxCentre", "value": "1"}, {"fh": "2", "sh": "0", "type": "ontargetAttAssist", "value": "2"}, {"fh": "20", "sh": "4", "type": "longPassOwnToOpp", "value": "24"}, {"fh": "4", "sh": "0", "type": "accurateChippedPass", "value": "4"}, {"fh": "22", "sh": "3", "type": "duel<PERSON>on", "value": "25"}, {"fh": "14", "sh": "0", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "14"}, {"fh": "1", "sh": "0", "type": "shieldBallOop", "value": "1"}, {"fh": "7", "sh": "3", "type": "fkFoulWon", "value": "10"}, {"fh": "2", "sh": "0", "type": "totalCrossNocorner", "value": "2"}, {"fh": "1", "sh": "0", "type": "attBxLeft", "value": "1"}, {"fh": "4", "sh": "1", "type": "successfulPutThrough", "value": "5"}, {"fh": "9", "sh": "0", "type": "totalTackle", "value": "9"}, {"fh": "21", "sh": "1", "type": "passesLeft", "value": "22"}, {"fh": "1", "sh": "0", "type": "attRfTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "accurateLaunches", "value": "1"}, {"fh": "42", "sh": "7", "type": "possLostAll", "value": "49"}, {"fh": "7", "sh": "0", "type": "accurateLongBalls", "value": "7"}, {"fh": "5", "sh": "0", "type": "challengeLost", "value": "5"}, {"fh": "3", "sh": "0", "type": "totalCross", "value": "3"}, {"fh": "1", "sh": "0", "type": "attFreekickTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "accurateGoalKicks", "value": "1"}, {"fh": "1", "sh": "0", "type": "savedObox", "value": "1"}, {"fh": "9", "sh": "0", "type": "unsuccessfulTouch", "value": "9"}, {"fh": "42", "sh": "7", "type": "possLostCtrl", "value": "49"}, {"fh": "1", "sh": "0", "type": "attIboxBlocked", "value": "1"}, {"fh": "4", "sh": "1", "type": "aerialLost", "value": "5"}, {"fh": "1", "sh": "0", "type": "penGoalsConceded", "value": "1"}, {"fh": "1", "sh": "0", "type": "attSvLowRight", "value": "1"}, {"fh": "2", "sh": "0", "type": "crosses18yard", "value": "2"}, {"fh": "9", "sh": "0", "type": "finalThirdEntries", "value": "9"}, {"fh": "1", "sh": "0", "type": "attHdTotal", "value": "1"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "10", "sh": "3", "type": "effectiveClearance", "value": "13"}, {"fh": "8", "sh": "4", "type": "fkFoulLost", "value": "12"}, {"fh": "1", "sh": "0", "type": "wonCorners", "value": "1"}, {"fh": "38", "sh": "21.8", "type": "possessionPercentage", "value": "35"}, {"fh": "7", "sh": "1", "type": "interception", "value": "8"}, {"fh": "5", "sh": "1", "type": "attemptedTackleFoul", "value": "6"}, {"fh": "37", "sh": "5", "type": "backwardPass", "value": "42"}, {"fh": "7", "sh": "1", "type": "interceptionWon", "value": "8"}, {"fh": "6", "sh": "0", "type": "penAreaEntries", "value": "6"}, {"fh": "7", "sh": "0", "type": "accurateThrows", "value": "7"}, {"fh": "4", "sh": "0", "type": "totalLayoffs", "value": "4"}, {"fh": "1", "sh": "0", "type": "attHdTarget", "value": "1"}, {"fh": "6", "sh": "0", "type": "totalContest", "value": "6"}, {"fh": "10", "sh": "3", "type": "totalClearance", "value": "13"}, {"fh": "10", "sh": "1", "type": "longPassOwnToOppSuccess", "value": "11"}, {"fh": "1", "sh": "0", "type": "savedIbox", "value": "1"}, {"fh": "1", "sh": "0", "type": "attemptsObox", "value": "1"}, {"fh": "2", "sh": "0", "type": "totalAttAssist", "value": "2"}, {"fh": "2", "sh": "0", "type": "attemptsIbox", "value": "2"}, {"fh": "1", "sh": "0", "type": "cornerTaken", "value": "1"}, {"type": "formationUsed", "value": "3511"}], "kit": {"id": "594", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Main", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "136yv5gcvir1xgseenoley95h", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "ent9gzo7zh7botgnb58k6n9at", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>"}, {"id": "a72lwc1d9oou0kp2d3f2vsp1x", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}]}}}