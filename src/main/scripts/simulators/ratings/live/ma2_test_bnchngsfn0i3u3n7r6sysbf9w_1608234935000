{"matchInfo": {"id": "bnchngsfn0i3u3n7r6sysbf9w", "coverageLevel": "15", "date": "2020-12-17Z", "time": "19:45:00Z", "week": "12", "attendanceInfoId": "1", "attendanceInfo": "Behind Closed Doors", "lastUpdated": "$TIMESTAMP", "description": "Roma vs Torino", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "4b80uzt9gxak7d1vaa5jp17qi", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "2020/2021"}, "stage": {"id": "4bkrobysg6zi7tugsqlj403my", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "7gnly6999wao1xarwct4p8fe9", "name": "Torino", "shortName": "Torino", "officialName": "Torino FC", "code": "TOR", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"matchTime": 8, "periodId": 1, "matchStatus": "Playing", "period": [{"id": 1, "start": "2020-12-17T19:47:58Z"}], "scores": {"ft": {"home": 0, "away": 0}, "total": {"home": 0, "away": 0}}}, "card": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 7, "timeMinSec": "6:32", "lastUpdated": "2020-12-17T19:54:35Z", "timestamp": "2020-12-17T19:54:31Z", "type": "YC", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246026767", "cardReason": "F<PERSON>l"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "3421", "player": [{"playerId": "26atxu4fr8f87oi8mt6z70mhh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "accuratePass", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "ab4kuqa33849h6z77ksuqhdk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "10"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "12"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "11"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "7o9bkf7no8wz167ingslfimdx", "firstName": "<PERSON>", "lastName": "Smalling", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "accuratePass", "value": "11"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "14"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "accurateBackZonePass", "value": "10"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "10"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "openPlayPass", "value": "12"}, {"type": "totalPass", "value": "12"}, {"type": "fwdPass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "7"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "9"}, {"type": "totalChippedPass", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "8"}, {"type": "totalPass", "value": "8"}, {"type": "fwdPass", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "accuratePass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalPass", "value": "3"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "5fbnkkf1saix4lonwx1i9k7o5", "firstName": "Jordan", "lastName": "Veretout", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "accuratePass", "value": "3"}, {"type": "wasFouled", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "6"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalPass", "value": "4"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bh4a2zneilpvi1t7etuhkfl61", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "5"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "3"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "46xszpgn7zit2cakaoyws551x", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "matchName": "<PERSON>", "shirtNumber": 33, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "6"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalPass", "value": "4"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "accurateThrows", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "1xscywgyfqisqexmpfvbf9g45", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Attacking Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "5"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "5"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "totalLayoffs", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "7"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "6"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "backwardPass", "value": "4"}, {"type": "totalLayoffs", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "a5eki9p236y4zxmp9qz202pcl", "firstName": "<PERSON>in", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "4"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "dhs8pujk55ewcis7y5alchu22", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Calafiori", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "14cbkgi3dk03rslbdb3kfr8fe", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "143brmwsfow8o0svtdq98tiw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 42, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "321h649n4oqjzr0g44k7qw1cl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "3ftcqipzb86lzdztg4j1oz2s5", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "52e2ogoex674bz6pcwluapdxx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mayoral <PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "68cu39v9m8pykapkh2b97ltqt", "firstName": "Antonio", "lastName": "Mirante", "matchName": "<PERSON><PERSON>", "shirtNumber": 83, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8n0c5t5wzfkoauryyzy8be0gl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "64h0x8sur55l07oaiuog4v2z9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d02p1ldr57bkxcpmq12vtvhsl", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "23lqodnib2we4fw5l286m5x3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "3", "type": "duelLost", "value": "3"}, {"fh": "20", "type": "leftside<PERSON><PERSON>", "value": "20"}, {"fh": "1", "type": "attRfTotal", "value": "1"}, {"fh": "53", "type": "accuratePass", "value": "53"}, {"fh": "5", "type": "totalFinalThirdPasses", "value": "5"}, {"fh": "18", "type": "rightsidePass", "value": "18"}, {"fh": "1", "type": "attemptsConcededIbox", "value": "1"}, {"fh": "78", "type": "touches", "value": "78"}, {"fh": "18", "type": "totalFwdZonePass", "value": "18"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "13", "type": "accurateFwdZonePass", "value": "13"}, {"fh": "4", "type": "totalChippedPass", "value": "4"}, {"fh": "1", "type": "lostCorners", "value": "1"}, {"fh": "1", "type": "ontargetScoringAtt", "value": "1"}, {"fh": "1", "type": "totalScoringAtt", "value": "1"}, {"fh": "1", "type": "blocked<PERSON><PERSON>", "value": "1"}, {"fh": "4", "type": "ballRecovery", "value": "4"}, {"fh": "2", "type": "possWonDef3rd", "value": "2"}, {"fh": "40", "type": "accurateBackZonePass", "value": "40"}, {"fh": "2", "type": "passesRight", "value": "2"}, {"fh": "2", "type": "totalThrows", "value": "2"}, {"fh": "48", "type": "successfulOpenPlayPass", "value": "48"}, {"fh": "43", "type": "totalBackZonePass", "value": "43"}, {"fh": "2", "type": "accurateLayoffs", "value": "2"}, {"fh": "7", "type": "totalLongBalls", "value": "7"}, {"fh": "1", "type": "attOpenplay", "value": "1"}, {"fh": "2", "type": "possWonMid3rd", "value": "2"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "56", "type": "openPlayPass", "value": "56"}, {"fh": "3", "type": "aerialWon", "value": "3"}, {"fh": "61", "type": "totalPass", "value": "61"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "13", "type": "fwdPass", "value": "13"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "2", "type": "touchesInOppBox", "value": "2"}, {"fh": "7", "type": "longPassOwnToOpp", "value": "7"}, {"fh": "2", "type": "accurateChippedPass", "value": "2"}, {"fh": "7", "type": "duel<PERSON>on", "value": "7"}, {"fh": "3", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "4", "type": "fkFoulWon", "value": "4"}, {"fh": "12", "type": "passesLeft", "value": "12"}, {"fh": "1", "type": "attRfTarget", "value": "1"}, {"fh": "10", "type": "possLostAll", "value": "10"}, {"fh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "5", "type": "accurateLongBalls", "value": "5"}, {"fh": "10", "type": "possLostCtrl", "value": "10"}, {"fh": "3", "type": "finalThirdEntries", "value": "3"}, {"fh": "3", "type": "effectiveClearance", "value": "3"}, {"fh": "1", "type": "fkFoulLost", "value": "1"}, {"fh": "63.5", "type": "possessionPercentage", "value": "63.5"}, {"fh": "1", "type": "attemptedTackleFoul", "value": "1"}, {"fh": "10", "type": "backwardPass", "value": "10"}, {"fh": "2", "type": "accurateThrows", "value": "2"}, {"fh": "2", "type": "totalLayoffs", "value": "2"}, {"fh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "3", "type": "totalContest", "value": "3"}, {"fh": "3", "type": "totalClearance", "value": "3"}, {"fh": "4", "type": "longPassOwnToOppSuccess", "value": "4"}, {"fh": "1", "type": "attemptsIbox", "value": "1"}, {"type": "formationUsed", "value": "3421"}], "kit": {"id": "8380", "colour1": "#990000", "type": "home"}}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "formationUsed": "3511", "player": [{"playerId": "5syage212151fcixahbq3k8ut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "3"}, {"type": "wasFouled", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "5"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalPass", "value": "3"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "22x1ucwwak58wi6r5x1j6ohk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "accuratePass", "value": "5"}, {"type": "rightsidePass", "value": "5"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "7"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "7"}, {"type": "totalPass", "value": "7"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "4042kk7ylrs378rh26b7mh42x", "firstName": "<PERSON>yan<PERSON>", "lastName": "Silveira Neves Vojnović", "matchName": "Lyanco", "shirtNumber": 4, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "7"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "7"}, {"type": "totalPass", "value": "7"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "d196vatyvq6ycn1q7d477d2i1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> Silva <PERSON>ci<PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "6"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "16kv5p8we3pwqpt5ktwrb7mj9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Vojvoda", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "accuratePass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "6ol411t0mogw6m83heiqd0m39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "possWonAtt3rd", "value": "1"}, {"type": "accuratePass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9qrewt049r6yq88c715ymau39", "firstName": "Soualiho", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "1"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "e2vhgeqrw3uy0tstydc5nldxx", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "accuratePass", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "4"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "fwdPass", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "penAreaEntries", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "f065kc6qn2cpxjxcm6i08rz3e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "2"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "cdrzpuj49ploquvdolgh5c3bp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "97mmzzi3dwdos6jtj2m05w4ph", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "7"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "4"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "attBxLeft", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "140ehcmf8mbibw8jkfwncupjp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "19gglji6ai70jiu7iqpkaxbyt", "firstName": "<PERSON>", "lastName": "Bonaz<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e2w8qlrfmunsg7oq4733cu6l1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "thnje2i2s96fcbuscj57053p", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d73maq8id4xflzaj6jfxlduj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eqggzoepu1d4t13fw6ybebtn9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 88, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9ak3zczupeken1itsmkherbh1", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "23avr8oet7764jflt8y1o1gb9", "firstName": "Antonio", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "55hr275k72a0ka78irp7z6uw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Segre", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8ud1kq9mdgukhlbda5vcn0s0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 39, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "f38rlbvo9k6kommwf1n3frfje", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 73, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "6gpsygusukoecmq6bnq65rfmd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "7", "type": "duelLost", "value": "7"}, {"fh": "1", "type": "blockedScoringAtt", "value": "1"}, {"fh": "11", "type": "leftside<PERSON><PERSON>", "value": "11"}, {"fh": "1", "type": "possWonAtt3rd", "value": "1"}, {"fh": "1", "type": "attRfTotal", "value": "1"}, {"fh": "27", "type": "accuratePass", "value": "27"}, {"fh": "1", "type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "5", "type": "totalFinalThirdPasses", "value": "5"}, {"fh": "8", "type": "rightsidePass", "value": "8"}, {"fh": "1", "type": "attemptsConcededIbox", "value": "1"}, {"fh": "48", "type": "touches", "value": "48"}, {"fh": "11", "type": "totalFwdZonePass", "value": "11"}, {"fh": "1", "type": "attAssistOpenplay", "value": "1"}, {"fh": "6", "type": "accurateFwdZonePass", "value": "6"}, {"fh": "2", "type": "totalChippedPass", "value": "2"}, {"fh": "1", "type": "totalScoringAtt", "value": "1"}, {"fh": "1", "type": "blocked<PERSON><PERSON>", "value": "1"}, {"fh": "5", "type": "ballRecovery", "value": "5"}, {"fh": "1", "type": "possWonDef3rd", "value": "1"}, {"fh": "21", "type": "accurateBackZonePass", "value": "21"}, {"fh": "27", "type": "successfulOpenPlayPass", "value": "27"}, {"fh": "24", "type": "totalBackZonePass", "value": "24"}, {"fh": "3", "type": "totalLongBalls", "value": "3"}, {"fh": "1", "type": "totalYellowCard", "value": "1"}, {"fh": "1", "type": "attOpenplay", "value": "1"}, {"fh": "3", "type": "possWonMid3rd", "value": "3"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "34", "type": "openPlayPass", "value": "34"}, {"fh": "34", "type": "totalPass", "value": "34"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "11", "type": "fwdPass", "value": "11"}, {"fh": "2", "type": "touchesInOppBox", "value": "2"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "ontargetAttAssist", "value": "1"}, {"fh": "3", "type": "longPassOwnToOpp", "value": "3"}, {"fh": "3", "type": "duel<PERSON>on", "value": "3"}, {"fh": "3", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "1", "type": "fkFoulWon", "value": "1"}, {"fh": "1", "type": "attBxLeft", "value": "1"}, {"fh": "1", "type": "successfulPutThrough", "value": "1"}, {"fh": "2", "type": "totalTackle", "value": "2"}, {"fh": "1", "type": "accurateLaunches", "value": "1"}, {"fh": "11", "type": "possLostAll", "value": "11"}, {"fh": "2", "type": "accurateLongBalls", "value": "2"}, {"fh": "1", "type": "challengeLost", "value": "1"}, {"fh": "1", "type": "totalCross", "value": "1"}, {"fh": "3", "type": "unsuccessfulTouch", "value": "3"}, {"fh": "11", "type": "possLostCtrl", "value": "11"}, {"fh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "3", "type": "aerialLost", "value": "3"}, {"fh": "1", "type": "finalThirdEntries", "value": "1"}, {"fh": "4", "type": "fkFoulLost", "value": "4"}, {"fh": "1", "type": "wonCorners", "value": "1"}, {"fh": "36.5", "type": "possessionPercentage", "value": "36.5"}, {"fh": "2", "type": "interception", "value": "2"}, {"fh": "3", "type": "attemptedTackleFoul", "value": "3"}, {"fh": "4", "type": "backwardPass", "value": "4"}, {"fh": "2", "type": "interceptionWon", "value": "2"}, {"fh": "4", "type": "penAreaEntries", "value": "4"}, {"fh": "1", "type": "longPassOwnToOppSuccess", "value": "1"}, {"fh": "1", "type": "savedIbox", "value": "1"}, {"fh": "1", "type": "totalAttAssist", "value": "1"}, {"fh": "1", "type": "attemptsIbox", "value": "1"}, {"fh": "1", "type": "cornerTaken", "value": "1"}, {"type": "formationUsed", "value": "3511"}], "kit": {"id": "594", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Main", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "136yv5gcvir1xgseenoley95h", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "ent9gzo7zh7botgnb58k6n9at", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>"}, {"id": "a72lwc1d9oou0kp2d3f2vsp1x", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}]}}}