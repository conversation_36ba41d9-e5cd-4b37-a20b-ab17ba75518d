{"matchInfo": {"id": "bnchngsfn0i3u3n7r6sysbf9w", "coverageLevel": "15", "date": "2020-12-17Z", "time": "19:45:00Z", "week": "12", "attendanceInfoId": "1", "attendanceInfo": "Behind Closed Doors", "lastUpdated": "$TIMESTAMP", "description": "Roma vs Torino", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "4b80uzt9gxak7d1vaa5jp17qi", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "2020/2021"}, "stage": {"id": "4bkrobysg6zi7tugsqlj403my", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "7gnly6999wao1xarwct4p8fe9", "name": "Torino", "shortName": "Torino", "officialName": "Torino FC", "code": "TOR", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"matchTime": 73, "periodId": 2, "matchStatus": "Playing", "period": [{"id": 1, "start": "2020-12-17T19:47:58Z", "end": "2020-12-17T20:34:57Z", "lengthMin": 46, "lengthSec": 59}, {"id": 2, "start": "2020-12-17T20:51:55Z"}], "scores": {"ht": {"home": 2, "away": 0}, "ft": {"home": 3, "away": 1}, "total": {"home": 3, "away": 1}}}, "goal": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 27, "timeMinSec": "26:56", "lastUpdated": "2020-12-17T20:15:36Z", "timestamp": "2020-12-17T20:14:54Z", "type": "G", "scorerId": "1xscywgyfqisqexmpfvbf9g45", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246037083", "homeScore": 1, "awayScore": 0}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 43, "timeMinSec": "42:37", "lastUpdated": "2020-12-17T20:30:41Z", "timestamp": "2020-12-17T20:30:36Z", "type": "PG", "scorerId": "5fbnkkf1saix4lonwx1i9k7o5", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246044707", "homeScore": 2, "awayScore": 0}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 68, "timeMinSec": "67:40", "lastUpdated": "2020-12-17T21:15:33Z", "timestamp": "2020-12-17T21:14:36Z", "type": "G", "scorerId": "57bin6he6kbdo2bs4i78z2k0l", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "52e2ogoex674bz6pcwluapdxx", "assistPlayerName": "<PERSON><PERSON><PERSON>", "optaEventId": "2246062771", "homeScore": 3, "awayScore": 0}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 2, "timeMin": 73, "timeMinSec": "72:02", "lastUpdated": "2020-12-17T21:19:47Z", "timestamp": "2020-12-17T21:18:58Z", "type": "G", "scorerId": "97mmzzi3dwdos6jtj2m05w4ph", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246064421", "homeScore": 3, "awayScore": 1}], "card": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 7, "timeMinSec": "6:32", "lastUpdated": "2020-12-17T19:54:35Z", "timestamp": "2020-12-17T19:54:31Z", "type": "YC", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246026767", "cardReason": "F<PERSON>l"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 14, "timeMinSec": "13:48", "lastUpdated": "2020-12-17T20:01:55Z", "timestamp": "2020-12-17T20:01:47Z", "type": "Y2C", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246029583", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 20, "timeMinSec": "20:00", "lastUpdated": "2020-12-17T20:08:20Z", "timestamp": "2020-12-17T20:07:58Z", "type": "YC", "playerId": "46xszpgn7zit2cakaoyws551x", "playerName": "<PERSON>", "optaEventId": "2246032737", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 23, "timeMinSec": "22:04", "lastUpdated": "2020-12-17T20:10:05Z", "timestamp": "2020-12-17T20:10:03Z", "type": "YC", "playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "playerName": "<PERSON><PERSON>", "optaEventId": "2246033929", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 56, "timeMinSec": "55:32", "lastUpdated": "2020-12-17T21:02:37Z", "timestamp": "2020-12-17T21:02:28Z", "type": "YC", "playerId": "bh4a2zneilpvi1t7etuhkfl61", "playerName": "<PERSON><PERSON><PERSON>", "optaEventId": "2246057477", "cardReason": "F<PERSON>l"}], "substitute": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 23, "timeMinSec": "22:20", "lastUpdated": "2020-12-17T20:10:30Z", "timestamp": "2020-12-17T20:10:18Z", "playerOnId": "140ehcmf8mbibw8jkfwncupjp", "playerOnName": "<PERSON><PERSON>", "playerOffId": "e2vhgeqrw3uy0tstydc5nldxx", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 45, "timeMinSec": "44:23", "lastUpdated": "2020-12-17T20:32:27Z", "timestamp": "2020-12-17T20:32:22Z", "playerOnId": "e2w8qlrfmunsg7oq4733cu6l1", "playerOnName": "<PERSON><PERSON>", "playerOffId": "140ehcmf8mbibw8jkfwncupjp", "playerOffName": "<PERSON><PERSON>", "subReason": "Injury"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:02Z", "timestamp": "2020-12-17T20:50:49Z", "playerOnId": "cddggachzf198p1yhq9owtofd", "playerOnName": "<PERSON><PERSON>", "playerOffId": "6fttn5mk6n1lstqt0dl5xzn4l", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:02Z", "timestamp": "2020-12-17T20:50:55Z", "playerOnId": "8lgsqmwavspz4fv7t11rb6j9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "46xszpgn7zit2cakaoyws551x", "playerOffName": "<PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:27Z", "timestamp": "2020-12-17T20:51:21Z", "playerOnId": "eqggzoepu1d4t13fw6ybebtn9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "9qrewt049r6yq88c715ymau39", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2020-12-17T20:51:36Z", "timestamp": "2020-12-17T20:51:28Z", "playerOnId": "55hr275k72a0ka78irp7z6uw9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "cdrzpuj49ploquvdolgh5c3bp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 59, "timeMinSec": "58:55", "lastUpdated": "2020-12-17T21:05:56Z", "timestamp": "2020-12-17T21:05:51Z", "playerOnId": "52e2ogoex674bz6pcwluapdxx", "playerOnName": "<PERSON><PERSON><PERSON>", "playerOffId": "a5eki9p236y4zxmp9qz202pcl", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 60, "timeMinSec": "59:03", "lastUpdated": "2020-12-17T21:06:07Z", "timestamp": "2020-12-17T21:05:59Z", "playerOnId": "d02p1ldr57bkxcpmq12vtvhsl", "playerOnName": "<PERSON>", "playerOffId": "bh4a2zneilpvi1t7etuhkfl61", "playerOffName": "<PERSON><PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 71, "timeMinSec": "70:09", "lastUpdated": "2020-12-17T21:17:12Z", "timestamp": "2020-12-17T21:17:04Z", "playerOnId": "dhs8pujk55ewcis7y5alchu22", "playerOnName": "<PERSON><PERSON>", "playerOffId": "9ka7y81b5kst146mhq7mom5ed", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "3421", "player": [{"playerId": "26atxu4fr8f87oi8mt6z70mhh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "9"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "20"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "saves", "value": "5"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accurateKeeperThrows", "value": "3"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "goalKicks", "value": "2"}, {"type": "openPlayPass", "value": "8"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "10"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateGoalKicks", "value": "2"}, {"type": "savedObox", "value": "2"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "ab4kuqa33849h6z77ksuqhdk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "23"}, {"type": "accuratePass", "value": "81"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "24"}, {"type": "rightsidePass", "value": "41"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "102"}, {"type": "totalFwdZonePass", "value": "47"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "42"}, {"type": "totalChippedPass", "value": "6"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "6"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "39"}, {"type": "passesRight", "value": "3"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "79"}, {"type": "totalBackZonePass", "value": "41"}, {"type": "totalLongBalls", "value": "7"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "86"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "88"}, {"type": "totalLaunches", "value": "2"}, {"type": "fwdPass", "value": "22"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "11"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "20"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "27"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "8"}, {"type": "accurateLongBalls", "value": "6"}, {"type": "headPass", "value": "3"}, {"type": "possLostCtrl", "value": "8"}, {"type": "aerialLost", "value": "1"}, {"type": "finalThirdEntries", "value": "20"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "9"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "7o9bkf7no8wz167ingslfimdx", "firstName": "<PERSON>", "lastName": "Smalling", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "duelLost", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "32"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "58"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "15"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "65"}, {"type": "totalFwdZonePass", "value": "22"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "21"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "5"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "37"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "57"}, {"type": "totalBackZonePass", "value": "37"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "58"}, {"type": "totalPass", "value": "59"}, {"type": "fwdPass", "value": "10"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "11"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "headPass", "value": "3"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "2"}, {"type": "backwardPass", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "10"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "27"}, {"type": "accuratePass", "value": "41"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "48"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "20"}, {"type": "accurateFwdZonePass", "value": "19"}, {"type": "totalChippedPass", "value": "2"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "passesRight", "value": "5"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "41"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attLfTotal", "value": "2"}, {"type": "openPlayPass", "value": "44"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "44"}, {"type": "attLfTarget", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "attSvLowLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attSvLowRight", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "3"}, {"type": "backwardPass", "value": "1"}, {"type": "fouls", "value": "3"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "43"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "26"}, {"type": "rightsidePass", "value": "23"}, {"type": "offsideProvoked", "value": "1"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "64"}, {"type": "totalFwdZonePass", "value": "43"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "35"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "8"}, {"type": "totalThrows", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "42"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "attOpenplay", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "47"}, {"type": "totalPass", "value": "48"}, {"type": "fwdPass", "value": "7"}, {"type": "touchesInOppBox", "value": "5"}, {"type": "attMissRight", "value": "1"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "22"}, {"type": "attCmissRight", "value": "1"}, {"type": "totalCrossNocorner", "value": "4"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "21"}, {"type": "possLostAll", "value": "12"}, {"type": "totalCross", "value": "4"}, {"type": "attIboxMiss", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "12"}, {"type": "crosses18yard", "value": "4"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "effectiveClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "backwardPass", "value": "16"}, {"type": "penAreaEntries", "value": "6"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalContest", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "totalAttAssist", "value": "2"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "5fbnkkf1saix4lonwx1i9k7o5", "firstName": "Jordan", "lastName": "Veretout", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "duelLost", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "16"}, {"type": "attRfTotal", "value": "2"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "48"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "20"}, {"type": "rightsidePass", "value": "20"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "59"}, {"type": "totalFwdZonePass", "value": "45"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "43"}, {"type": "totalChippedPass", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attPenGoal", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "passesRight", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "47"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "totalLongBalls", "value": "7"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "49"}, {"type": "totalPass", "value": "50"}, {"type": "fwdPass", "value": "9"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attBxCentre", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "19"}, {"type": "attRfGoal", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "19"}, {"type": "bigChanceScored", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "7"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attemptsIbox", "value": "2"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bh4a2zneilpvi1t7etuhkfl61", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "7"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "21"}, {"type": "dispossessed", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "55"}, {"type": "wasFouled", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "19"}, {"type": "rightsidePass", "value": "18"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "75"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "49"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "accurateFwdZonePass", "value": "44"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "passesRight", "value": "5"}, {"type": "successfulOpenPlayPass", "value": "53"}, {"type": "totalBackZonePass", "value": "11"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "58"}, {"type": "totalPass", "value": "60"}, {"type": "fwdPass", "value": "14"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "8"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "16"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "12"}, {"type": "possLostAll", "value": "8"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "challengeLost", "value": "3"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "8"}, {"type": "finalThirdEntries", "value": "10"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "3"}, {"type": "backwardPass", "value": "7"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "totalContest", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "8"}, {"type": "attemptsObox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "46xszpgn7zit2cakaoyws551x", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "matchName": "<PERSON>", "shirtNumber": 33, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "accuratePass", "value": "23"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "34"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "12"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "passesRight", "value": "7"}, {"type": "totalThrows", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "21"}, {"type": "totalPass", "value": "24"}, {"type": "fwdPass", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attMissRight", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "totalCross", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "14"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accurateThrows", "value": "6"}, {"type": "fouls", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "1xscywgyfqisqexmpfvbf9g45", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Attacking Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "6"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "43"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "31"}, {"type": "rightsidePass", "value": "22"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "71"}, {"type": "totalFwdZonePass", "value": "50"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "37"}, {"type": "totalChippedPass", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "4"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "passesRight", "value": "3"}, {"type": "totalThrows", "value": "3"}, {"type": "attGoalHighLeft", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "42"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "54"}, {"type": "totalPass", "value": "55"}, {"type": "fwdPass", "value": "14"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "22"}, {"type": "attRfGoal", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "19"}, {"type": "possLostAll", "value": "19"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "totalCross", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "19"}, {"type": "aerialLost", "value": "1"}, {"type": "crosses18yard", "value": "2"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "7"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "5"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalContest", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "4"}, {"type": "blockedScoringAtt", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "22"}, {"type": "possWonAtt3rd", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "attRfTotal", "value": "2"}, {"type": "attBxRight", "value": "1"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "60"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "26"}, {"type": "rightsidePass", "value": "20"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "86"}, {"type": "totalFwdZonePass", "value": "53"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "47"}, {"type": "totalChippedPass", "value": "7"}, {"type": "goalsConceded", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "ballRecovery", "value": "6"}, {"type": "turnover", "value": "3"}, {"type": "accurateBackZonePass", "value": "13"}, {"type": "passesRight", "value": "12"}, {"type": "attGoalHighLeft", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "57"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attOpenplay", "value": "3"}, {"type": "possWonMid3rd", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "63"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "66"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "13"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "attBxCentre", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "accurateChippedPass", "value": "5"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "21"}, {"type": "totalTackle", "value": "3"}, {"type": "passesLeft", "value": "10"}, {"type": "possLostAll", "value": "13"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "possLostCtrl", "value": "13"}, {"type": "attIboxBlocked", "value": "2"}, {"type": "finalThirdEntries", "value": "11"}, {"type": "wonCorners", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "11"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "4"}, {"type": "attLfGoal", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "totalContest", "value": "3"}, {"type": "attemptsIbox", "value": "3"}, {"type": "cornerTaken", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "a5eki9p236y4zxmp9qz202pcl", "firstName": "<PERSON>in", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "10"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "18"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "totalChippedPass", "value": "2"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalLongBalls", "value": "2"}, {"type": "assistPenaltyWon", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "13"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "13"}, {"type": "fwdPass", "value": "1"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "6"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "penaltyWon", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "dhs8pujk55ewcis7y5alchu22", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Calafiori", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "accuratePass", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "14cbkgi3dk03rslbdb3kfr8fe", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "143brmwsfow8o0svtdq98tiw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 42, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "321h649n4oqjzr0g44k7qw1cl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "3ftcqipzb86lzdztg4j1oz2s5", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "11"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "20"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "passesRight", "value": "8"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "14"}, {"type": "totalPass", "value": "14"}, {"type": "fwdPass", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "totalCross", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "6"}, {"type": "crosses18yard", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "15"}, {"type": "accuratePass", "value": "25"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "29"}, {"type": "attSvHighRight", "value": "1"}, {"type": "totalFwdZonePass", "value": "16"}, {"type": "accurateFwdZonePass", "value": "16"}, {"type": "totalChippedPass", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "passesRight", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "24"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "openPlayPass", "value": "27"}, {"type": "totalPass", "value": "28"}, {"type": "totalLaunches", "value": "2"}, {"type": "fwdPass", "value": "8"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attHdTotal", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "attHdTarget", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "52e2ogoex674bz6pcwluapdxx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mayoral <PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "accuratePass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "goalAssist", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "68cu39v9m8pykapkh2b97ltqt", "firstName": "Antonio", "lastName": "Mirante", "matchName": "<PERSON><PERSON>", "shirtNumber": 83, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8n0c5t5wzfkoauryyzy8be0gl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "64h0x8sur55l07oaiuog4v2z9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d02p1ldr57bkxcpmq12vtvhsl", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "9"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "11"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "totalChippedPass", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "openPlayPass", "value": "10"}, {"type": "totalPass", "value": "10"}, {"type": "fwdPass", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "4"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "23lqodnib2we4fw5l286m5x3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "22", "sh": "10", "type": "duelLost", "value": "32"}, {"fh": "1", "sh": "2", "type": "divingSave", "value": "3"}, {"fh": "1", "sh": "3", "type": "blockedScoringAtt", "value": "4"}, {"fh": "111", "sh": "83", "type": "leftside<PERSON><PERSON>", "value": "194"}, {"fh": "2", "sh": "1", "type": "possWonAtt3rd", "value": "3"}, {"fh": "4", "sh": "0", "type": "dispossessed", "value": "4"}, {"fh": "4", "sh": "3", "type": "attRfTotal", "value": "7"}, {"fh": "1", "sh": "0", "type": "attBxRight", "value": "1"}, {"fh": "1", "sh": "1", "type": "sixYardBlock", "value": "2"}, {"fh": "287", "sh": "234", "type": "accuratePass", "value": "521"}, {"fh": "4", "sh": "2", "type": "won<PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "91", "sh": "88", "type": "totalFinalThirdPasses", "value": "179"}, {"fh": "101", "sh": "80", "type": "rightsidePass", "value": "181"}, {"fh": "2", "sh": "2", "type": "attemptsConcededIbox", "value": "4"}, {"fh": "396", "sh": "311", "type": "touches", "value": "707"}, {"fh": "0", "sh": "1", "type": "attSvHighRight", "value": "1"}, {"fh": "200", "sh": "193", "type": "totalFwdZonePass", "value": "393"}, {"fh": "2", "sh": "2", "type": "attAssistOpenplay", "value": "4"}, {"fh": "5", "sh": "3", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"fh": "1", "sh": "1", "type": "goalsOpenplay", "value": "2"}, {"fh": "170", "sh": "174", "type": "accurateFwdZonePass", "value": "344"}, {"fh": "15", "sh": "16", "type": "totalChippedPass", "value": "31"}, {"fh": "1", "sh": "0", "type": "effectiveHeadClearance", "value": "1"}, {"fh": "1", "sh": "0", "type": "lostCorners", "value": "1"}, {"fh": "1", "sh": "0", "type": "fouledFinalThird", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalsConceded", "value": "1"}, {"fh": "2", "sh": "3", "type": "saves", "value": "5"}, {"fh": "4", "sh": "2", "type": "ontargetScoringAtt", "value": "6"}, {"fh": "7", "sh": "5", "type": "totalScoringAtt", "value": "12"}, {"fh": "5", "sh": "7", "type": "blocked<PERSON><PERSON>", "value": "12"}, {"fh": "1", "sh": "0", "type": "attPenGoal", "value": "1"}, {"fh": "1", "sh": "3", "type": "attemptsConcededObox", "value": "4"}, {"fh": "23", "sh": "18", "type": "ballRecovery", "value": "41"}, {"fh": "0", "sh": "5", "type": "subsMade", "value": "5"}, {"fh": "9", "sh": "3", "type": "possWonDef3rd", "value": "12"}, {"fh": "117", "sh": "60", "type": "accurateBackZonePass", "value": "177"}, {"fh": "0", "sh": "1", "type": "goalAssistOpenplay", "value": "1"}, {"fh": "28", "sh": "31", "type": "passesRight", "value": "59"}, {"fh": "10", "sh": "5", "type": "totalThrows", "value": "15"}, {"fh": "1", "sh": "0", "type": "attOboxTarget", "value": "1"}, {"fh": "1", "sh": "1", "type": "attGoalHighLeft", "value": "2"}, {"fh": "278", "sh": "225", "type": "successfulOpenPlayPass", "value": "503"}, {"fh": "123", "sh": "67", "type": "totalBackZonePass", "value": "190"}, {"fh": "2", "sh": "0", "type": "accurateLayoffs", "value": "2"}, {"fh": "25", "sh": "12", "type": "totalLongBalls", "value": "37"}, {"fh": "2", "sh": "1", "type": "totalYellowCard", "value": "3"}, {"fh": "2", "sh": "1", "type": "accurateKeeperThrows", "value": "3"}, {"fh": "0", "sh": "1", "type": "goalsConcededIbox", "value": "1"}, {"fh": "1", "sh": "1", "type": "attObxCentre", "value": "2"}, {"fh": "5", "sh": "4", "type": "attOpenplay", "value": "9"}, {"fh": "10", "sh": "13", "type": "possWonMid3rd", "value": "23"}, {"fh": "5", "sh": "7", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "2"}, {"fh": "1", "sh": "1", "type": "attIboxTarget", "value": "2"}, {"fh": "1", "sh": "0", "type": "headClea<PERSON>", "value": "1"}, {"fh": "0", "sh": "2", "type": "goalKicks", "value": "2"}, {"fh": "3", "sh": "1", "type": "attLfTotal", "value": "4"}, {"fh": "308", "sh": "248", "type": "openPlayPass", "value": "556"}, {"fh": "0", "sh": "1", "type": "goalAssistIntentional", "value": "1"}, {"fh": "4", "sh": "1", "type": "aerialWon", "value": "5"}, {"fh": "0", "sh": "1", "type": "goalAssist", "value": "1"}, {"fh": "317", "sh": "257", "type": "totalPass", "value": "574"}, {"fh": "2", "sh": "0", "type": "attLfTarget", "value": "2"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "4", "sh": "3", "type": "totalLaunches", "value": "7"}, {"fh": "62", "sh": "56", "type": "fwdPass", "value": "118"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "2", "sh": "1", "type": "goals", "value": "3"}, {"fh": "17", "sh": "11", "type": "touchesInOppBox", "value": "28"}, {"fh": "1", "sh": "0", "type": "totalCornersIntobox", "value": "1"}, {"fh": "2", "sh": "0", "type": "attMissRight", "value": "2"}, {"fh": "2", "sh": "4", "type": "attBxCentre", "value": "6"}, {"fh": "2", "sh": "3", "type": "ontargetAttAssist", "value": "5"}, {"fh": "26", "sh": "16", "type": "longPassOwnToOpp", "value": "42"}, {"fh": "2", "sh": "1", "type": "attIboxGoal", "value": "3"}, {"fh": "8", "sh": "10", "type": "accurateChippedPass", "value": "18"}, {"fh": "21", "sh": "12", "type": "duel<PERSON>on", "value": "33"}, {"fh": "74", "sh": "74", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "148"}, {"fh": "2", "sh": "0", "type": "attRfGoal", "value": "2"}, {"fh": "1", "sh": "0", "type": "attCmissRight", "value": "1"}, {"fh": "8", "sh": "4", "type": "fkFoulWon", "value": "12"}, {"fh": "5", "sh": "3", "type": "totalCrossNocorner", "value": "8"}, {"fh": "2", "sh": "1", "type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "2", "sh": "0", "type": "attBxLeft", "value": "2"}, {"fh": "1", "sh": "3", "type": "successfulPutThrough", "value": "4"}, {"fh": "5", "sh": "4", "type": "totalTackle", "value": "9"}, {"fh": "1", "sh": "0", "type": "attSvLowLeft", "value": "1"}, {"fh": "62", "sh": "55", "type": "passesLeft", "value": "117"}, {"fh": "1", "sh": "0", "type": "bigChanceScored", "value": "1"}, {"fh": "2", "sh": "0", "type": "accurateLaunches", "value": "2"}, {"fh": "52", "sh": "35", "type": "possLostAll", "value": "87"}, {"fh": "21", "sh": "9", "type": "accurateLongBalls", "value": "30"}, {"fh": "4", "sh": "1", "type": "challengeLost", "value": "5"}, {"fh": "6", "sh": "3", "type": "totalCross", "value": "9"}, {"fh": "1", "sh": "0", "type": "attGoalLowRight", "value": "1"}, {"fh": "0", "sh": "1", "type": "attOboxBlocked", "value": "1"}, {"fh": "2", "sh": "0", "type": "attIboxMiss", "value": "2"}, {"fh": "0", "sh": "2", "type": "accurateGoalKicks", "value": "2"}, {"fh": "1", "sh": "1", "type": "savedObox", "value": "2"}, {"fh": "7", "sh": "7", "type": "unsuccessfulTouch", "value": "14"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "sh": "1", "type": "forwardGoals", "value": "2"}, {"fh": "52", "sh": "35", "type": "possLostCtrl", "value": "87"}, {"fh": "1", "sh": "2", "type": "attIboxBlocked", "value": "3"}, {"fh": "2", "sh": "0", "type": "aerialLost", "value": "2"}, {"fh": "1", "sh": "0", "type": "attSvLowRight", "value": "1"}, {"fh": "5", "sh": "3", "type": "crosses18yard", "value": "8"}, {"fh": "30", "sh": "37", "type": "finalThirdEntries", "value": "67"}, {"fh": "0", "sh": "1", "type": "attHdTotal", "value": "1"}, {"fh": "5", "sh": "1", "type": "effectiveClearance", "value": "6"}, {"fh": "7", "sh": "7", "type": "fkFoulLost", "value": "14"}, {"fh": "2", "sh": "1", "type": "wonCorners", "value": "3"}, {"fh": "62", "sh": "72.5", "type": "possessionPercentage", "value": "66.3"}, {"fh": "2", "sh": "2", "type": "interception", "value": "4"}, {"fh": "6", "sh": "4", "type": "attemptedTackleFoul", "value": "10"}, {"fh": "43", "sh": "38", "type": "backwardPass", "value": "81"}, {"type": "firstHalfGoals", "value": "2"}, {"fh": "2", "sh": "2", "type": "interceptionWon", "value": "4"}, {"fh": "13", "sh": "10", "type": "penAreaEntries", "value": "23"}, {"fh": "0", "sh": "1", "type": "attLfGoal", "value": "1"}, {"fh": "10", "sh": "5", "type": "accurateThrows", "value": "15"}, {"fh": "2", "sh": "0", "type": "totalLayoffs", "value": "2"}, {"fh": "1", "sh": "2", "type": "bigChanceMissed", "value": "3"}, {"fh": "1", "sh": "0", "type": "penaltyWon", "value": "1"}, {"fh": "0", "sh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "10", "sh": "5", "type": "totalContest", "value": "15"}, {"fh": "5", "sh": "1", "type": "totalClearance", "value": "6"}, {"fh": "20", "sh": "15", "type": "longPassOwnToOppSuccess", "value": "35"}, {"fh": "1", "sh": "0", "type": "savedIbox", "value": "1"}, {"fh": "1", "sh": "1", "type": "attemptsObox", "value": "2"}, {"fh": "2", "sh": "3", "type": "totalAttAssist", "value": "5"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "6", "sh": "4", "type": "attemptsIbox", "value": "10"}, {"fh": "2", "sh": "1", "type": "cornerTaken", "value": "3"}, {"type": "formationUsed", "value": "3421"}], "kit": {"id": "8380", "colour1": "#990000", "type": "home"}}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "formationUsed": "3511", "player": [{"playerId": "5syage212151fcixahbq3k8ut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "accuratePass", "value": "16"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "32"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalChippedPass", "value": "5"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "saves", "value": "3"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "16"}, {"type": "diveSave", "value": "2"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "14"}, {"type": "penaltyFaced", "value": "1"}, {"type": "totalBackZonePass", "value": "17"}, {"type": "totalLongBalls", "value": "8"}, {"type": "accurateKeeperThrows", "value": "2"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "errorLeadToShot", "value": "1"}, {"type": "goalKicks", "value": "2"}, {"type": "openPlayPass", "value": "21"}, {"type": "totalPass", "value": "24"}, {"type": "totalLaunches", "value": "2"}, {"type": "fwdPass", "value": "13"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "possLostAll", "value": "9"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "savedObox", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "9"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "22x1ucwwak58wi6r5x1j6ohk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "24"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "37"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "totalChippedPass", "value": "5"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "successfulOpenPlayPass", "value": "24"}, {"type": "totalBackZonePass", "value": "22"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "31"}, {"type": "totalPass", "value": "31"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "6"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "8"}, {"type": "possLostAll", "value": "7"}, {"type": "possLostCtrl", "value": "7"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "7"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "4042kk7ylrs378rh26b7mh42x", "firstName": "<PERSON>yan<PERSON>", "lastName": "Silveira Neves Vojnović", "matchName": "Lyanco", "shirtNumber": 4, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "46"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "17"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "65"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "4"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "7"}, {"type": "possWonDef3rd", "value": "5"}, {"type": "accurateBackZonePass", "value": "44"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "44"}, {"type": "totalBackZonePass", "value": "47"}, {"type": "totalLongBalls", "value": "5"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "50"}, {"type": "totalPass", "value": "52"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "17"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "interceptionsInBox", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "4"}, {"type": "interception", "value": "3"}, {"type": "backwardPass", "value": "6"}, {"type": "interceptionWon", "value": "3"}, {"type": "totalClearance", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attemptsObox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "d196vatyvq6ycn1q7d477d2i1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> Silva <PERSON>ci<PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "22"}, {"type": "dispossessed", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "40"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "3"}, {"type": "penaltyConceded", "value": "1"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "60"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "totalChippedPass", "value": "2"}, {"type": "effectiveHeadClearance", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "7"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "4"}, {"type": "accurateBackZonePass", "value": "35"}, {"type": "passesRight", "value": "2"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "38"}, {"type": "totalBackZonePass", "value": "37"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "headClea<PERSON>", "value": "3"}, {"type": "openPlayPass", "value": "40"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "42"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "12"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "4"}, {"type": "interception", "value": "3"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "interceptionWon", "value": "3"}, {"type": "fouls", "value": "1"}, {"type": "totalClearance", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "attemptsObox", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attMissHigh", "value": "1"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "attOboxMiss", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "16kv5p8we3pwqpt5ktwrb7mj9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Vojvoda", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "accuratePass", "value": "18"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "10"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "44"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "totalChippedPass", "value": "2"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "goalsConceded", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "3"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "10"}, {"type": "passesRight", "value": "1"}, {"type": "totalThrows", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "16"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "22"}, {"type": "totalPass", "value": "24"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "5"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "successfulPutThrough", "value": "3"}, {"type": "passesLeft", "value": "7"}, {"type": "possLostAll", "value": "11"}, {"type": "totalCross", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "11"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "crosses18yard", "value": "2"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "3"}, {"type": "backwardPass", "value": "4"}, {"type": "interceptionWon", "value": "3"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateThrows", "value": "4"}, {"type": "totalContest", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "6ol411t0mogw6m83heiqd0m39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "wasFouled", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "30"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "goalsConceded", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "turnover", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "14"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "15"}, {"type": "totalPass", "value": "16"}, {"type": "fwdPass", "value": "4"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "4"}, {"type": "possLostAll", "value": "4"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9qrewt049r6yq88c715ymau39", "firstName": "Soualiho", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "23"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "touches", "value": "30"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "totalChippedPass", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "20"}, {"type": "totalPass", "value": "23"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "8"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "e2vhgeqrw3uy0tstydc5nldxx", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "8"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "fwdPass", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "challengeLost", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "f065kc6qn2cpxjxcm6i08rz3e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "3"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "redCard", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "cdrzpuj49ploquvdolgh5c3bp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "11"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "goalsConceded", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "11"}, {"type": "fwdPass", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "97mmzzi3dwdos6jtj2m05w4ph", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "7"}, {"type": "blockedScoringAtt", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "attRfTotal", "value": "4"}, {"type": "attGoalLowLeft", "value": "1"}, {"type": "accuratePass", "value": "19"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "10"}, {"type": "touches", "value": "45"}, {"type": "totalFwdZonePass", "value": "12"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "attSvHighCentre", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "ontargetScoringAtt", "value": "3"}, {"type": "totalScoringAtt", "value": "5"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "ballRecovery", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "passesRight", "value": "1"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "16"}, {"type": "totalBackZonePass", "value": "16"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "4"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attFreekickTotal", "value": "1"}, {"type": "openPlayPass", "value": "25"}, {"type": "totalPass", "value": "28"}, {"type": "fwdPass", "value": "7"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "5"}, {"type": "attBxCentre", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "attRfGoal", "value": "1"}, {"type": "attBxLeft", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "13"}, {"type": "challengeLost", "value": "2"}, {"type": "attFreekickTarget", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "2"}, {"type": "possLostCtrl", "value": "13"}, {"type": "attIboxBlocked", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "attSvLowRight", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "attHdTotal", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "11"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalLayoffs", "value": "4"}, {"type": "fouls", "value": "3"}, {"type": "attHdTarget", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "attemptsIbox", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "140ehcmf8mbibw8jkfwncupjp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "duelLost", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "5"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "shieldBallOop", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "penGoalsConceded", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "19gglji6ai70jiu7iqpkaxbyt", "firstName": "<PERSON>", "lastName": "Bonaz<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e2w8qlrfmunsg7oq4733cu6l1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "6"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "14"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "attOboxPost", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "6"}, {"type": "postScoringAtt", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalTackle", "value": "2"}, {"type": "attPostHigh", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "hitWoodwork", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "thnje2i2s96fcbuscj57053p", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d73maq8id4xflzaj6jfxlduj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eqggzoepu1d4t13fw6ybebtn9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 88, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "17"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "22"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "3"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "accurateBackZonePass", "value": "13"}, {"type": "passesRight", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "16"}, {"type": "totalPass", "value": "19"}, {"type": "fwdPass", "value": "6"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "9ak3zczupeken1itsmkherbh1", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "23avr8oet7764jflt8y1o1gb9", "firstName": "Antonio", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "55hr275k72a0ka78irp7z6uw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Segre", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalFlickOn", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "accuratePass", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "11"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "9"}, {"type": "totalPass", "value": "9"}, {"type": "fwdPass", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalOffside", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "totalSubOn", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "assistBlockedShot", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "8ud1kq9mdgukhlbda5vcn0s0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 39, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "f38rlbvo9k6kommwf1n3frfje", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 73, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "6gpsygusukoecmq6bnq65rfmd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "0", "sh": "1", "type": "totalFlickOn", "value": "1"}, {"fh": "21", "sh": "12", "type": "duelLost", "value": "33"}, {"fh": "2", "sh": "0", "type": "divingSave", "value": "2"}, {"fh": "1", "sh": "1", "type": "blockedScoringAtt", "value": "2"}, {"fh": "64", "sh": "24", "type": "leftside<PERSON><PERSON>", "value": "88"}, {"fh": "1", "sh": "1", "type": "possWonAtt3rd", "value": "2"}, {"fh": "3", "sh": "1", "type": "dispossessed", "value": "4"}, {"fh": "1", "sh": "0", "type": "accurateCross", "value": "1"}, {"fh": "2", "sh": "4", "type": "attRfTotal", "value": "6"}, {"fh": "0", "sh": "1", "type": "attGoalLowLeft", "value": "1"}, {"fh": "171", "sh": "77", "type": "accuratePass", "value": "248"}, {"fh": "6", "sh": "1", "type": "won<PERSON><PERSON><PERSON>", "value": "7"}, {"fh": "20", "sh": "13", "type": "totalFinalThirdPasses", "value": "33"}, {"fh": "49", "sh": "24", "type": "rightsidePass", "value": "73"}, {"fh": "1", "sh": "0", "type": "penaltyConceded", "value": "1"}, {"fh": "6", "sh": "4", "type": "attemptsConcededIbox", "value": "10"}, {"fh": "273", "sh": "150", "type": "touches", "value": "423"}, {"fh": "64", "sh": "25", "type": "totalFwdZonePass", "value": "89"}, {"fh": "2", "sh": "3", "type": "attAssistOpenplay", "value": "5"}, {"fh": "4", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "1", "sh": "0", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalsOpenplay", "value": "1"}, {"fh": "48", "sh": "16", "type": "accurateFwdZonePass", "value": "64"}, {"fh": "1", "sh": "0", "type": "attSvHighCentre", "value": "1"}, {"fh": "11", "sh": "11", "type": "totalChippedPass", "value": "22"}, {"fh": "6", "sh": "1", "type": "effectiveHeadClearance", "value": "7"}, {"fh": "2", "sh": "1", "type": "lostCorners", "value": "3"}, {"fh": "1", "sh": "0", "type": "fouledFinalThird", "value": "1"}, {"fh": "2", "sh": "1", "type": "goalsConceded", "value": "3"}, {"fh": "2", "sh": "1", "type": "saves", "value": "3"}, {"fh": "2", "sh": "2", "type": "ontargetScoringAtt", "value": "4"}, {"fh": "3", "sh": "5", "type": "totalScoringAtt", "value": "8"}, {"fh": "5", "sh": "7", "type": "blocked<PERSON><PERSON>", "value": "12"}, {"fh": "1", "sh": "1", "type": "attemptsConcededObox", "value": "2"}, {"fh": "22", "sh": "21", "type": "ballRecovery", "value": "43"}, {"fh": "2", "sh": "2", "type": "subsMade", "value": "4"}, {"fh": "9", "sh": "14", "type": "possWonDef3rd", "value": "23"}, {"fh": "124", "sh": "61", "type": "accurateBackZonePass", "value": "185"}, {"fh": "8", "sh": "5", "type": "passesRight", "value": "13"}, {"fh": "8", "sh": "1", "type": "totalThrows", "value": "9"}, {"fh": "1", "sh": "1", "type": "attOboxTarget", "value": "2"}, {"fh": "162", "sh": "69", "type": "successfulOpenPlayPass", "value": "231"}, {"fh": "1", "sh": "0", "type": "penaltyFaced", "value": "1"}, {"fh": "135", "sh": "72", "type": "totalBackZonePass", "value": "207"}, {"fh": "0", "sh": "1", "type": "attOboxPost", "value": "1"}, {"fh": "1", "sh": "1", "type": "accurateLayoffs", "value": "2"}, {"fh": "15", "sh": "9", "type": "totalLongBalls", "value": "24"}, {"fh": "0", "sh": "2", "type": "accurateKeeperThrows", "value": "2"}, {"fh": "2", "sh": "1", "type": "goalsConcededIbox", "value": "3"}, {"fh": "1", "sh": "3", "type": "attObxCentre", "value": "4"}, {"fh": "2", "sh": "5", "type": "attOpenplay", "value": "7"}, {"fh": "11", "sh": "4", "type": "possWonMid3rd", "value": "15"}, {"fh": "5", "sh": "7", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"fh": "0", "sh": "1", "type": "errorLeadToShot", "value": "1"}, {"fh": "1", "sh": "0", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "attFreekickTotal", "value": "1"}, {"fh": "6", "sh": "1", "type": "headClea<PERSON>", "value": "7"}, {"fh": "2", "sh": "0", "type": "goalKicks", "value": "2"}, {"fh": "0", "sh": "1", "type": "attLfTotal", "value": "1"}, {"fh": "1", "sh": "0", "type": "totalRedCard", "value": "1"}, {"fh": "186", "sh": "88", "type": "openPlayPass", "value": "274"}, {"fh": "2", "sh": "0", "type": "aerialWon", "value": "2"}, {"fh": "196", "sh": "97", "type": "totalPass", "value": "293"}, {"fh": "3", "sh": "3", "type": "totalLaunches", "value": "6"}, {"fh": "46", "sh": "32", "type": "fwdPass", "value": "78"}, {"fh": "1", "sh": "3", "type": "<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "0", "sh": "1", "type": "goals", "value": "1"}, {"fh": "4", "sh": "4", "type": "touchesInOppBox", "value": "8"}, {"fh": "3", "sh": "0", "type": "interceptionsInBox", "value": "3"}, {"fh": "1", "sh": "0", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "sh": "1", "type": "attBxCentre", "value": "2"}, {"fh": "0", "sh": "1", "type": "postScoringAtt", "value": "1"}, {"fh": "2", "sh": "2", "type": "ontargetAttAssist", "value": "4"}, {"fh": "20", "sh": "12", "type": "longPassOwnToOpp", "value": "32"}, {"fh": "0", "sh": "1", "type": "attIboxGoal", "value": "1"}, {"fh": "4", "sh": "5", "type": "accurateChippedPass", "value": "9"}, {"fh": "22", "sh": "9", "type": "duel<PERSON>on", "value": "31"}, {"fh": "14", "sh": "9", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "23"}, {"fh": "0", "sh": "1", "type": "attRfGoal", "value": "1"}, {"fh": "1", "sh": "0", "type": "shieldBallOop", "value": "1"}, {"fh": "7", "sh": "7", "type": "fkFoulWon", "value": "14"}, {"fh": "2", "sh": "0", "type": "totalCrossNocorner", "value": "2"}, {"fh": "0", "sh": "2", "type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "sh": "1", "type": "attBxLeft", "value": "2"}, {"fh": "4", "sh": "4", "type": "successfulPutThrough", "value": "8"}, {"fh": "9", "sh": "2", "type": "totalTackle", "value": "11"}, {"fh": "0", "sh": "1", "type": "totalOffside", "value": "1"}, {"fh": "0", "sh": "1", "type": "attPostHigh", "value": "1"}, {"fh": "21", "sh": "7", "type": "passesLeft", "value": "28"}, {"fh": "1", "sh": "1", "type": "attRfTarget", "value": "2"}, {"fh": "1", "sh": "0", "type": "accurateLaunches", "value": "1"}, {"fh": "42", "sh": "30", "type": "possLostAll", "value": "72"}, {"fh": "0", "sh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "7", "sh": "2", "type": "accurateLongBalls", "value": "9"}, {"fh": "5", "sh": "3", "type": "challengeLost", "value": "8"}, {"fh": "3", "sh": "0", "type": "totalCross", "value": "3"}, {"fh": "1", "sh": "0", "type": "attFreekickTarget", "value": "1"}, {"fh": "1", "sh": "0", "type": "accurateGoalKicks", "value": "1"}, {"fh": "1", "sh": "0", "type": "savedObox", "value": "1"}, {"fh": "9", "sh": "5", "type": "unsuccessfulTouch", "value": "14"}, {"fh": "0", "sh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "0", "sh": "1", "type": "forwardGoals", "value": "1"}, {"fh": "42", "sh": "30", "type": "possLostCtrl", "value": "72"}, {"fh": "1", "sh": "1", "type": "attIboxBlocked", "value": "2"}, {"fh": "4", "sh": "1", "type": "aerialLost", "value": "5"}, {"fh": "1", "sh": "0", "type": "penGoalsConceded", "value": "1"}, {"fh": "1", "sh": "0", "type": "attSvLowRight", "value": "1"}, {"fh": "2", "sh": "0", "type": "crosses18yard", "value": "2"}, {"fh": "9", "sh": "5", "type": "finalThirdEntries", "value": "14"}, {"fh": "1", "sh": "0", "type": "attHdTotal", "value": "1"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "10", "sh": "5", "type": "effectiveClearance", "value": "15"}, {"fh": "8", "sh": "4", "type": "fkFoulLost", "value": "12"}, {"fh": "1", "sh": "0", "type": "wonCorners", "value": "1"}, {"fh": "38", "sh": "27.5", "type": "possessionPercentage", "value": "33.7"}, {"fh": "7", "sh": "5", "type": "interception", "value": "12"}, {"fh": "5", "sh": "1", "type": "attemptedTackleFoul", "value": "6"}, {"fh": "37", "sh": "17", "type": "backwardPass", "value": "54"}, {"fh": "7", "sh": "5", "type": "interceptionWon", "value": "12"}, {"fh": "6", "sh": "2", "type": "penAreaEntries", "value": "8"}, {"fh": "7", "sh": "1", "type": "accurateThrows", "value": "8"}, {"fh": "4", "sh": "1", "type": "totalLayoffs", "value": "5"}, {"fh": "1", "sh": "0", "type": "attHdTarget", "value": "1"}, {"fh": "6", "sh": "3", "type": "totalContest", "value": "9"}, {"fh": "0", "sh": "1", "type": "hitWoodwork", "value": "1"}, {"fh": "10", "sh": "5", "type": "totalClearance", "value": "15"}, {"fh": "10", "sh": "5", "type": "longPassOwnToOppSuccess", "value": "15"}, {"fh": "1", "sh": "1", "type": "savedIbox", "value": "2"}, {"fh": "1", "sh": "3", "type": "attemptsObox", "value": "4"}, {"fh": "2", "sh": "3", "type": "totalAttAssist", "value": "5"}, {"fh": "0", "sh": "1", "type": "attMissHigh", "value": "1"}, {"fh": "0", "sh": "1", "type": "offtargetAttAssist", "value": "1"}, {"fh": "2", "sh": "2", "type": "attemptsIbox", "value": "4"}, {"fh": "0", "sh": "1", "type": "attOboxMiss", "value": "1"}, {"fh": "1", "sh": "0", "type": "cornerTaken", "value": "1"}, {"type": "formationUsed", "value": "3511"}], "kit": {"id": "594", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Main", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "136yv5gcvir1xgseenoley95h", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "ent9gzo7zh7botgnb58k6n9at", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>"}, {"id": "a72lwc1d9oou0kp2d3f2vsp1x", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}]}}}