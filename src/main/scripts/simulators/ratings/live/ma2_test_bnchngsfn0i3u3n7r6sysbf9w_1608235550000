{"matchInfo": {"id": "bnchngsfn0i3u3n7r6sysbf9w", "coverageLevel": "15", "date": "2020-12-17Z", "time": "19:45:00Z", "week": "12", "attendanceInfoId": "1", "attendanceInfo": "Behind Closed Doors", "lastUpdated": "$TIMESTAMP", "description": "Roma vs Torino", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "4b80uzt9gxak7d1vaa5jp17qi", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "2020/2021"}, "stage": {"id": "4bkrobysg6zi7tugsqlj403my", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "7gnly6999wao1xarwct4p8fe9", "name": "Torino", "shortName": "Torino", "officialName": "Torino FC", "code": "TOR", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"matchTime": 18, "periodId": 1, "matchStatus": "Playing", "period": [{"id": 1, "start": "2020-12-17T19:47:58Z"}], "scores": {"ft": {"home": 0, "away": 0}, "total": {"home": 0, "away": 0}}}, "card": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 7, "timeMinSec": "6:32", "lastUpdated": "2020-12-17T19:54:35Z", "timestamp": "2020-12-17T19:54:31Z", "type": "YC", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246026767", "cardReason": "F<PERSON>l"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 14, "timeMinSec": "13:48", "lastUpdated": "2020-12-17T20:01:55Z", "timestamp": "2020-12-17T20:01:47Z", "type": "Y2C", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246029583", "cardReason": "F<PERSON>l"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "3421", "player": [{"playerId": "26atxu4fr8f87oi8mt6z70mhh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "6"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "saves", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accurateKeeperThrows", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "ab4kuqa33849h6z77ksuqhdk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "accuratePass", "value": "15"}, {"type": "rightsidePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "18"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "14"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "totalLongBalls", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "15"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "16"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "7o9bkf7no8wz167ingslfimdx", "firstName": "<PERSON>", "lastName": "Smalling", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "accuratePass", "value": "18"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "22"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "accurateBackZonePass", "value": "15"}, {"type": "successfulOpenPlayPass", "value": "18"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "19"}, {"type": "totalPass", "value": "19"}, {"type": "fwdPass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "16"}, {"type": "totalPass", "value": "16"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "accurateCross", "value": "1"}, {"type": "accuratePass", "value": "7"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "12"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "openPlayPass", "value": "7"}, {"type": "totalPass", "value": "8"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "5fbnkkf1saix4lonwx1i9k7o5", "firstName": "Jordan", "lastName": "Veretout", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "totalChippedPass", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "7"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "5"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bh4a2zneilpvi1t7etuhkfl61", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "9"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "15"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "9"}, {"type": "totalPass", "value": "10"}, {"type": "fwdPass", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "46xszpgn7zit2cakaoyws551x", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "matchName": "<PERSON>", "shirtNumber": 33, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "14"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "passesRight", "value": "2"}, {"type": "totalThrows", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "8"}, {"type": "totalPass", "value": "9"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attMissRight", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "1xscywgyfqisqexmpfvbf9g45", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Attacking Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "9"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "rightsidePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "20"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "passesRight", "value": "2"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "totalPass", "value": "12"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "crosses18yard", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateThrows", "value": "2"}, {"type": "totalLayoffs", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "attBxRight", "value": "1"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "15"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "attOpenplay", "value": "1"}, {"type": "openPlayPass", "value": "9"}, {"type": "totalPass", "value": "11"}, {"type": "fwdPass", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "cornerTaken", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "a5eki9p236y4zxmp9qz202pcl", "firstName": "<PERSON>in", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "3"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "6"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "4"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "dhs8pujk55ewcis7y5alchu22", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Calafiori", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "14cbkgi3dk03rslbdb3kfr8fe", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "143brmwsfow8o0svtdq98tiw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 42, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "321h649n4oqjzr0g44k7qw1cl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "3ftcqipzb86lzdztg4j1oz2s5", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "52e2ogoex674bz6pcwluapdxx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mayoral <PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "68cu39v9m8pykapkh2b97ltqt", "firstName": "Antonio", "lastName": "Mirante", "matchName": "<PERSON><PERSON>", "shirtNumber": 83, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8n0c5t5wzfkoauryyzy8be0gl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "64h0x8sur55l07oaiuog4v2z9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d02p1ldr57bkxcpmq12vtvhsl", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "23lqodnib2we4fw5l286m5x3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "7", "type": "duelLost", "value": "7"}, {"fh": "2", "type": "blockedScoringAtt", "value": "2"}, {"fh": "41", "type": "leftside<PERSON><PERSON>", "value": "41"}, {"fh": "1", "type": "dispossessed", "value": "1"}, {"fh": "1", "type": "accurateCross", "value": "1"}, {"fh": "2", "type": "attRfTotal", "value": "2"}, {"fh": "1", "type": "attBxRight", "value": "1"}, {"fh": "2", "type": "sixYardBlock", "value": "2"}, {"fh": "102", "type": "accuratePass", "value": "102"}, {"fh": "1", "type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "24", "type": "totalFinalThirdPasses", "value": "24"}, {"fh": "37", "type": "rightsidePass", "value": "37"}, {"fh": "2", "type": "attemptsConcededIbox", "value": "2"}, {"fh": "154", "type": "touches", "value": "154"}, {"fh": "54", "type": "totalFwdZonePass", "value": "54"}, {"fh": "2", "type": "attAssistOpenplay", "value": "2"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "42", "type": "accurateFwdZonePass", "value": "42"}, {"fh": "6", "type": "totalChippedPass", "value": "6"}, {"fh": "1", "type": "effectiveHeadClearance", "value": "1"}, {"fh": "1", "type": "lostCorners", "value": "1"}, {"fh": "1", "type": "saves", "value": "1"}, {"fh": "1", "type": "ontargetScoringAtt", "value": "1"}, {"fh": "4", "type": "totalScoringAtt", "value": "4"}, {"fh": "2", "type": "blocked<PERSON><PERSON>", "value": "2"}, {"fh": "7", "type": "ballRecovery", "value": "7"}, {"fh": "3", "type": "possWonDef3rd", "value": "3"}, {"fh": "61", "type": "accurateBackZonePass", "value": "61"}, {"fh": "7", "type": "passesRight", "value": "7"}, {"fh": "5", "type": "totalThrows", "value": "5"}, {"fh": "94", "type": "successfulOpenPlayPass", "value": "94"}, {"fh": "64", "type": "totalBackZonePass", "value": "64"}, {"fh": "2", "type": "accurateLayoffs", "value": "2"}, {"fh": "10", "type": "totalLongBalls", "value": "10"}, {"fh": "1", "type": "accurateKeeperThrows", "value": "1"}, {"fh": "3", "type": "attOpenplay", "value": "3"}, {"fh": "3", "type": "possWonMid3rd", "value": "3"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"fh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "type": "headClea<PERSON>", "value": "1"}, {"fh": "2", "type": "attLfTotal", "value": "2"}, {"fh": "107", "type": "openPlayPass", "value": "107"}, {"fh": "3", "type": "aerialWon", "value": "3"}, {"fh": "115", "type": "totalPass", "value": "115"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "20", "type": "fwdPass", "value": "20"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "10", "type": "touchesInOppBox", "value": "10"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "attMissRight", "value": "1"}, {"fh": "1", "type": "attBxCentre", "value": "1"}, {"fh": "2", "type": "ontargetAttAssist", "value": "2"}, {"fh": "11", "type": "longPassOwnToOpp", "value": "11"}, {"fh": "3", "type": "accurateChippedPass", "value": "3"}, {"fh": "11", "type": "duel<PERSON>on", "value": "11"}, {"fh": "18", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "18"}, {"fh": "6", "type": "fkFoulWon", "value": "6"}, {"fh": "2", "type": "totalCrossNocorner", "value": "2"}, {"fh": "1", "type": "keeper<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "1", "type": "totalTackle", "value": "1"}, {"fh": "22", "type": "passesLeft", "value": "22"}, {"fh": "1", "type": "attRfTarget", "value": "1"}, {"fh": "20", "type": "possLostAll", "value": "20"}, {"fh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "8", "type": "accurateLongBalls", "value": "8"}, {"fh": "2", "type": "challengeLost", "value": "2"}, {"fh": "3", "type": "totalCross", "value": "3"}, {"fh": "1", "type": "attIboxMiss", "value": "1"}, {"fh": "2", "type": "unsuccessfulTouch", "value": "2"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "20", "type": "possLostCtrl", "value": "20"}, {"fh": "2", "type": "attIboxBlocked", "value": "2"}, {"fh": "2", "type": "crosses18yard", "value": "2"}, {"fh": "9", "type": "finalThirdEntries", "value": "9"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "4", "type": "effectiveClearance", "value": "4"}, {"fh": "2", "type": "fkFoulLost", "value": "2"}, {"fh": "2", "type": "wonCorners", "value": "2"}, {"fh": "58.9", "type": "possessionPercentage", "value": "58.9"}, {"fh": "1", "type": "interception", "value": "1"}, {"fh": "2", "type": "attemptedTackleFoul", "value": "2"}, {"fh": "17", "type": "backwardPass", "value": "17"}, {"fh": "1", "type": "interceptionWon", "value": "1"}, {"fh": "6", "type": "penAreaEntries", "value": "6"}, {"fh": "5", "type": "accurateThrows", "value": "5"}, {"fh": "2", "type": "totalLayoffs", "value": "2"}, {"fh": "2", "type": "bigChanceMissed", "value": "2"}, {"fh": "4", "type": "totalContest", "value": "4"}, {"fh": "4", "type": "totalClearance", "value": "4"}, {"fh": "7", "type": "longPassOwnToOppSuccess", "value": "7"}, {"fh": "1", "type": "savedIbox", "value": "1"}, {"fh": "2", "type": "totalAttAssist", "value": "2"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "4", "type": "attemptsIbox", "value": "4"}, {"fh": "2", "type": "cornerTaken", "value": "2"}, {"type": "formationUsed", "value": "3421"}], "kit": {"id": "8380", "colour1": "#990000", "type": "home"}}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "formationUsed": "3511", "player": [{"playerId": "5syage212151fcixahbq3k8ut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "5"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "8"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "saves", "value": "1"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "diveSave", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalKicks", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "6"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "22x1ucwwak58wi6r5x1j6ohk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "9"}, {"type": "rightsidePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "14"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "12"}, {"type": "totalPass", "value": "12"}, {"type": "fwdPass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "3"}, {"type": "possLostCtrl", "value": "3"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "4042kk7ylrs378rh26b7mh42x", "firstName": "<PERSON>yan<PERSON>", "lastName": "Silveira Neves Vojnović", "matchName": "Lyanco", "shirtNumber": 4, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "accuratePass", "value": "18"}, {"type": "rightsidePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "23"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "17"}, {"type": "successfulOpenPlayPass", "value": "18"}, {"type": "totalBackZonePass", "value": "17"}, {"type": "totalLongBalls", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "18"}, {"type": "totalPass", "value": "18"}, {"type": "fwdPass", "value": "2"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "d196vatyvq6ycn1q7d477d2i1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> Silva <PERSON>ci<PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "totalLongBalls", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "14"}, {"type": "totalPass", "value": "14"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "16kv5p8we3pwqpt5ktwrb7mj9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Vojvoda", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "accurateCross", "value": "1"}, {"type": "accuratePass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "8"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "4"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "6ol411t0mogw6m83heiqd0m39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "5"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "ballRecovery", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "openPlayPass", "value": "4"}, {"type": "totalPass", "value": "4"}, {"type": "fwdPass", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9qrewt049r6yq88c715ymau39", "firstName": "Soualiho", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "7"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "10"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "7"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "e2vhgeqrw3uy0tstydc5nldxx", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "fwdPass", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "f065kc6qn2cpxjxcm6i08rz3e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "3"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "3"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "redCard", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "cdrzpuj49ploquvdolgh5c3bp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "5"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "7"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "97mmzzi3dwdos6jtj2m05w4ph", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "11"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "attSvHighCentre", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "fwdPass", "value": "3"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "attBxLeft", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "attHdTotal", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "attHdTarget", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "140ehcmf8mbibw8jkfwncupjp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "19gglji6ai70jiu7iqpkaxbyt", "firstName": "<PERSON>", "lastName": "Bonaz<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e2w8qlrfmunsg7oq4733cu6l1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "thnje2i2s96fcbuscj57053p", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d73maq8id4xflzaj6jfxlduj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eqggzoepu1d4t13fw6ybebtn9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 88, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9ak3zczupeken1itsmkherbh1", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "23avr8oet7764jflt8y1o1gb9", "firstName": "Antonio", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "55hr275k72a0ka78irp7z6uw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Segre", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8ud1kq9mdgukhlbda5vcn0s0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 39, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "f38rlbvo9k6kommwf1n3frfje", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 73, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "6gpsygusukoecmq6bnq65rfmd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "11", "type": "duelLost", "value": "11"}, {"fh": "1", "type": "divingSave", "value": "1"}, {"fh": "1", "type": "blockedScoringAtt", "value": "1"}, {"fh": "27", "type": "leftside<PERSON><PERSON>", "value": "27"}, {"fh": "1", "type": "possWonAtt3rd", "value": "1"}, {"fh": "1", "type": "dispossessed", "value": "1"}, {"fh": "1", "type": "accurateCross", "value": "1"}, {"fh": "1", "type": "attRfTotal", "value": "1"}, {"fh": "71", "type": "accuratePass", "value": "71"}, {"fh": "2", "type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "9", "type": "totalFinalThirdPasses", "value": "9"}, {"fh": "23", "type": "rightsidePass", "value": "23"}, {"fh": "4", "type": "attemptsConcededIbox", "value": "4"}, {"fh": "115", "type": "touches", "value": "115"}, {"fh": "29", "type": "totalFwdZonePass", "value": "29"}, {"fh": "2", "type": "attAssistOpenplay", "value": "2"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "22", "type": "accurateFwdZonePass", "value": "22"}, {"fh": "1", "type": "attSvHighCentre", "value": "1"}, {"fh": "5", "type": "totalChippedPass", "value": "5"}, {"fh": "4", "type": "effectiveHeadClearance", "value": "4"}, {"fh": "2", "type": "lostCorners", "value": "2"}, {"fh": "1", "type": "saves", "value": "1"}, {"fh": "1", "type": "ontargetScoringAtt", "value": "1"}, {"fh": "2", "type": "totalScoringAtt", "value": "2"}, {"fh": "2", "type": "blocked<PERSON><PERSON>", "value": "2"}, {"fh": "8", "type": "ballRecovery", "value": "8"}, {"fh": "1", "type": "possWonDef3rd", "value": "1"}, {"fh": "50", "type": "accurateBackZonePass", "value": "50"}, {"fh": "1", "type": "passesRight", "value": "1"}, {"fh": "2", "type": "totalThrows", "value": "2"}, {"fh": "68", "type": "successfulOpenPlayPass", "value": "68"}, {"fh": "54", "type": "totalBackZonePass", "value": "54"}, {"fh": "7", "type": "totalLongBalls", "value": "7"}, {"fh": "2", "type": "attOpenplay", "value": "2"}, {"fh": "6", "type": "possWonMid3rd", "value": "6"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "4", "type": "headClea<PERSON>", "value": "4"}, {"fh": "1", "type": "goalKicks", "value": "1"}, {"fh": "1", "type": "totalRedCard", "value": "1"}, {"fh": "78", "type": "openPlayPass", "value": "78"}, {"fh": "81", "type": "totalPass", "value": "81"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "19", "type": "fwdPass", "value": "19"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "type": "touchesInOppBox", "value": "3"}, {"fh": "1", "type": "interceptionsInBox", "value": "1"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "attBxCentre", "value": "1"}, {"fh": "2", "type": "ontargetAttAssist", "value": "2"}, {"fh": "7", "type": "longPassOwnToOpp", "value": "7"}, {"fh": "2", "type": "accurateChippedPass", "value": "2"}, {"fh": "7", "type": "duel<PERSON>on", "value": "7"}, {"fh": "6", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "2", "type": "fkFoulWon", "value": "2"}, {"fh": "1", "type": "totalCrossNocorner", "value": "1"}, {"fh": "1", "type": "attBxLeft", "value": "1"}, {"fh": "2", "type": "successfulPutThrough", "value": "2"}, {"fh": "3", "type": "totalTackle", "value": "3"}, {"fh": "10", "type": "passesLeft", "value": "10"}, {"fh": "1", "type": "accurateLaunches", "value": "1"}, {"fh": "15", "type": "possLostAll", "value": "15"}, {"fh": "5", "type": "accurateLongBalls", "value": "5"}, {"fh": "2", "type": "challengeLost", "value": "2"}, {"fh": "2", "type": "totalCross", "value": "2"}, {"fh": "1", "type": "accurateGoalKicks", "value": "1"}, {"fh": "3", "type": "unsuccessfulTouch", "value": "3"}, {"fh": "15", "type": "possLostCtrl", "value": "15"}, {"fh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "3", "type": "aerialLost", "value": "3"}, {"fh": "1", "type": "crosses18yard", "value": "1"}, {"fh": "3", "type": "finalThirdEntries", "value": "3"}, {"fh": "1", "type": "attHdTotal", "value": "1"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "6", "type": "effectiveClearance", "value": "6"}, {"fh": "6", "type": "fkFoulLost", "value": "6"}, {"fh": "1", "type": "wonCorners", "value": "1"}, {"fh": "41.1", "type": "possessionPercentage", "value": "41.1"}, {"fh": "3", "type": "interception", "value": "3"}, {"fh": "4", "type": "attemptedTackleFoul", "value": "4"}, {"fh": "12", "type": "backwardPass", "value": "12"}, {"fh": "3", "type": "interceptionWon", "value": "3"}, {"fh": "4", "type": "penAreaEntries", "value": "4"}, {"fh": "2", "type": "accurateThrows", "value": "2"}, {"fh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "2", "type": "totalContest", "value": "2"}, {"fh": "6", "type": "totalClearance", "value": "6"}, {"fh": "4", "type": "longPassOwnToOppSuccess", "value": "4"}, {"fh": "1", "type": "savedIbox", "value": "1"}, {"fh": "2", "type": "totalAttAssist", "value": "2"}, {"fh": "2", "type": "attemptsIbox", "value": "2"}, {"fh": "1", "type": "cornerTaken", "value": "1"}, {"type": "formationUsed", "value": "3511"}], "kit": {"id": "594", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Main", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "136yv5gcvir1xgseenoley95h", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "ent9gzo7zh7botgnb58k6n9at", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>"}, {"id": "a72lwc1d9oou0kp2d3f2vsp1x", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}]}}}