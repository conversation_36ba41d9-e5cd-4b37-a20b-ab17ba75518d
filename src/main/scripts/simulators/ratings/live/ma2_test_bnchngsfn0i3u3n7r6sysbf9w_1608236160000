{"matchInfo": {"id": "bnchngsfn0i3u3n7r6sysbf9w", "coverageLevel": "15", "date": "2020-12-17Z", "time": "19:45:00Z", "week": "12", "attendanceInfoId": "1", "attendanceInfo": "Behind Closed Doors", "lastUpdated": "$TIMESTAMP", "description": "Roma vs Torino", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "4b80uzt9gxak7d1vaa5jp17qi", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "2020/2021"}, "stage": {"id": "4bkrobysg6zi7tugsqlj403my", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2020-09-19Z", "endDate": "2021-05-23Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "7gnly6999wao1xarwct4p8fe9", "name": "Torino", "shortName": "Torino", "officialName": "Torino FC", "code": "TOR", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"matchTime": 28, "periodId": 1, "matchStatus": "Playing", "period": [{"id": 1, "start": "2020-12-17T19:47:58Z"}], "scores": {"ft": {"home": 1, "away": 0}, "total": {"home": 1, "away": 0}}}, "goal": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 27, "timeMinSec": "26:56", "lastUpdated": "2020-12-17T20:15:36Z", "timestamp": "2020-12-17T20:14:54Z", "type": "G", "scorerId": "1xscywgyfqisqexmpfvbf9g45", "scorerName": "<PERSON><PERSON>", "optaEventId": "2246037083", "homeScore": 1, "awayScore": 0}], "card": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 7, "timeMinSec": "6:32", "lastUpdated": "2020-12-17T19:54:35Z", "timestamp": "2020-12-17T19:54:31Z", "type": "YC", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246026767", "cardReason": "F<PERSON>l"}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 14, "timeMinSec": "13:48", "lastUpdated": "2020-12-17T20:01:55Z", "timestamp": "2020-12-17T20:01:47Z", "type": "Y2C", "playerId": "f065kc6qn2cpxjxcm6i08rz3e", "playerName": "<PERSON><PERSON>", "optaEventId": "2246029583", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 20, "timeMinSec": "20:00", "lastUpdated": "2020-12-17T20:08:20Z", "timestamp": "2020-12-17T20:07:58Z", "type": "YC", "playerId": "46xszpgn7zit2cakaoyws551x", "playerName": "<PERSON>", "optaEventId": "2246032737", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 23, "timeMinSec": "22:04", "lastUpdated": "2020-12-17T20:10:05Z", "timestamp": "2020-12-17T20:10:03Z", "type": "YC", "playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "playerName": "<PERSON><PERSON>", "optaEventId": "2246033929", "cardReason": "F<PERSON>l"}], "substitute": [{"contestantId": "7gnly6999wao1xarwct4p8fe9", "periodId": 1, "timeMin": 23, "timeMinSec": "22:20", "lastUpdated": "2020-12-17T20:10:30Z", "timestamp": "2020-12-17T20:10:18Z", "playerOnId": "140ehcmf8mbibw8jkfwncupjp", "playerOnName": "<PERSON><PERSON>", "playerOffId": "e2vhgeqrw3uy0tstydc5nldxx", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "3421", "player": [{"playerId": "26atxu4fr8f87oi8mt6z70mhh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "8"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "saves", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accurateKeeperThrows", "value": "2"}, {"type": "openPlayPass", "value": "3"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "possLostAll", "value": "1"}, {"type": "savedObox", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "ab4kuqa33849h6z77ksuqhdk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "accuratePass", "value": "26"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "30"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "16"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "25"}, {"type": "totalBackZonePass", "value": "17"}, {"type": "totalLongBalls", "value": "3"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "26"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "27"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "7o9bkf7no8wz167ingslfimdx", "firstName": "<PERSON>", "lastName": "Smalling", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "13"}, {"type": "accuratePass", "value": "22"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "5"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "26"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "successfulOpenPlayPass", "value": "22"}, {"type": "totalBackZonePass", "value": "18"}, {"type": "totalLongBalls", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "openPlayPass", "value": "23"}, {"type": "totalPass", "value": "23"}, {"type": "fwdPass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "2"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "16"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "24"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "5"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "27"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "totalChippedPass", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "passesRight", "value": "2"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "24"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attLfTotal", "value": "2"}, {"type": "openPlayPass", "value": "25"}, {"type": "totalPass", "value": "25"}, {"type": "attLfTarget", "value": "1"}, {"type": "fwdPass", "value": "4"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attSvLowLeft", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "accuratePass", "value": "11"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "10"}, {"type": "rightsidePass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "23"}, {"type": "totalFwdZonePass", "value": "14"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "totalChippedPass", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "13"}, {"type": "totalPass", "value": "14"}, {"type": "fwdPass", "value": "6"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "8"}, {"type": "totalCross", "value": "3"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "8"}, {"type": "crosses18yard", "value": "3"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "5"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "5fbnkkf1saix4lonwx1i9k7o5", "firstName": "Jordan", "lastName": "Veretout", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "9"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "12"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "totalChippedPass", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "openPlayPass", "value": "9"}, {"type": "totalPass", "value": "10"}, {"type": "fwdPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bh4a2zneilpvi1t7etuhkfl61", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "21"}, {"type": "totalFwdZonePass", "value": "12"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "12"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "14"}, {"type": "totalPass", "value": "15"}, {"type": "fwdPass", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "3"}, {"type": "possLostCtrl", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "46xszpgn7zit2cakaoyws551x", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "matchName": "<PERSON>", "shirtNumber": 33, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accuratePass", "value": "10"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "18"}, {"type": "yellowCard", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "passesRight", "value": "2"}, {"type": "totalThrows", "value": "5"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "10"}, {"type": "totalPass", "value": "11"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attMissRight", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "8"}, {"type": "accurateThrows", "value": "5"}, {"type": "fouls", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "1xscywgyfqisqexmpfvbf9g45", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Attacking Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "accuratePass", "value": "14"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "9"}, {"type": "rightsidePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "29"}, {"type": "totalFwdZonePass", "value": "15"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateFwdZonePass", "value": "11"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "passesRight", "value": "2"}, {"type": "totalThrows", "value": "2"}, {"type": "attGoalHighLeft", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "openPlayPass", "value": "16"}, {"type": "totalPass", "value": "17"}, {"type": "attLfTarget", "value": "1"}, {"type": "fwdPass", "value": "4"}, {"type": "goals", "value": "1"}, {"type": "touchesInOppBox", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"type": "attRfGoal", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "possLostAll", "value": "7"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "crosses18yard", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "backwardPass", "value": "3"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateThrows", "value": "2"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalContest", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "attBxRight", "value": "1"}, {"type": "sixYardBlock", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "19"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attOpenplay", "value": "1"}, {"type": "openPlayPass", "value": "12"}, {"type": "totalPass", "value": "14"}, {"type": "fwdPass", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "3"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "cornerTaken", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "a5eki9p236y4zxmp9qz202pcl", "firstName": "<PERSON>in", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalChippedPass", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "aerialWon", "value": "1"}, {"type": "totalPass", "value": "6"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "dhs8pujk55ewcis7y5alchu22", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Calafiori", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "14cbkgi3dk03rslbdb3kfr8fe", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "143brmwsfow8o0svtdq98tiw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 42, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "321h649n4oqjzr0g44k7qw1cl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "3ftcqipzb86lzdztg4j1oz2s5", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "52e2ogoex674bz6pcwluapdxx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Mayoral <PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "68cu39v9m8pykapkh2b97ltqt", "firstName": "Antonio", "lastName": "Mirante", "matchName": "<PERSON><PERSON>", "shirtNumber": 83, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8n0c5t5wzfkoauryyzy8be0gl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "64h0x8sur55l07oaiuog4v2z9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d02p1ldr57bkxcpmq12vtvhsl", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "23lqodnib2we4fw5l286m5x3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "13", "type": "duelLost", "value": "13"}, {"fh": "1", "type": "divingSave", "value": "1"}, {"fh": "2", "type": "blockedScoringAtt", "value": "2"}, {"fh": "60", "type": "leftside<PERSON><PERSON>", "value": "60"}, {"fh": "1", "type": "possWonAtt3rd", "value": "1"}, {"fh": "2", "type": "dispossessed", "value": "2"}, {"fh": "2", "type": "attRfTotal", "value": "2"}, {"fh": "1", "type": "attBxRight", "value": "1"}, {"fh": "2", "type": "sixYardBlock", "value": "2"}, {"fh": "148", "type": "accuratePass", "value": "148"}, {"fh": "2", "type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "40", "type": "totalFinalThirdPasses", "value": "40"}, {"fh": "50", "type": "rightsidePass", "value": "50"}, {"fh": "2", "type": "attemptsConcededIbox", "value": "2"}, {"fh": "222", "type": "touches", "value": "222"}, {"fh": "93", "type": "totalFwdZonePass", "value": "93"}, {"fh": "3", "type": "attAssistOpenplay", "value": "3"}, {"fh": "3", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "1", "type": "goalsOpenplay", "value": "1"}, {"fh": "75", "type": "accurateFwdZonePass", "value": "75"}, {"fh": "9", "type": "totalChippedPass", "value": "9"}, {"fh": "1", "type": "effectiveHeadClearance", "value": "1"}, {"fh": "1", "type": "lostCorners", "value": "1"}, {"fh": "2", "type": "saves", "value": "2"}, {"fh": "3", "type": "ontargetScoringAtt", "value": "3"}, {"fh": "6", "type": "totalScoringAtt", "value": "6"}, {"fh": "4", "type": "blocked<PERSON><PERSON>", "value": "4"}, {"fh": "1", "type": "attemptsConcededObox", "value": "1"}, {"fh": "11", "type": "ballRecovery", "value": "11"}, {"fh": "5", "type": "possWonDef3rd", "value": "5"}, {"fh": "73", "type": "accurateBackZonePass", "value": "73"}, {"fh": "11", "type": "passesRight", "value": "11"}, {"fh": "8", "type": "totalThrows", "value": "8"}, {"fh": "1", "type": "attOboxTarget", "value": "1"}, {"fh": "1", "type": "attGoalHighLeft", "value": "1"}, {"fh": "140", "type": "successfulOpenPlayPass", "value": "140"}, {"fh": "77", "type": "totalBackZonePass", "value": "77"}, {"fh": "2", "type": "accurateLayoffs", "value": "2"}, {"fh": "16", "type": "totalLongBalls", "value": "16"}, {"fh": "2", "type": "totalYellowCard", "value": "2"}, {"fh": "2", "type": "accurateKeeperThrows", "value": "2"}, {"fh": "1", "type": "attObxCentre", "value": "1"}, {"fh": "5", "type": "attOpenplay", "value": "5"}, {"fh": "3", "type": "possWonMid3rd", "value": "3"}, {"fh": "4", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"fh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "type": "headClea<PERSON>", "value": "1"}, {"fh": "4", "type": "attLfTotal", "value": "4"}, {"fh": "157", "type": "openPlayPass", "value": "157"}, {"fh": "3", "type": "aerialWon", "value": "3"}, {"fh": "165", "type": "totalPass", "value": "165"}, {"fh": "2", "type": "attLfTarget", "value": "2"}, {"fh": "2", "type": "totalLaunches", "value": "2"}, {"fh": "32", "type": "fwdPass", "value": "32"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "1", "type": "goals", "value": "1"}, {"fh": "14", "type": "touchesInOppBox", "value": "14"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "attMissRight", "value": "1"}, {"fh": "1", "type": "attBxCentre", "value": "1"}, {"fh": "3", "type": "ontargetAttAssist", "value": "3"}, {"fh": "13", "type": "longPassOwnToOpp", "value": "13"}, {"fh": "1", "type": "attIboxGoal", "value": "1"}, {"fh": "6", "type": "accurateChippedPass", "value": "6"}, {"fh": "13", "type": "duel<PERSON>on", "value": "13"}, {"fh": "32", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "32"}, {"fh": "1", "type": "attRfGoal", "value": "1"}, {"fh": "6", "type": "fkFoulWon", "value": "6"}, {"fh": "4", "type": "totalCrossNocorner", "value": "4"}, {"fh": "2", "type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "attBxLeft", "value": "1"}, {"fh": "1", "type": "successfulPutThrough", "value": "1"}, {"fh": "2", "type": "totalTackle", "value": "2"}, {"fh": "1", "type": "attSvLowLeft", "value": "1"}, {"fh": "35", "type": "passesLeft", "value": "35"}, {"fh": "1", "type": "accurateLaunches", "value": "1"}, {"fh": "31", "type": "possLostAll", "value": "31"}, {"fh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "14", "type": "accurateLongBalls", "value": "14"}, {"fh": "4", "type": "challengeLost", "value": "4"}, {"fh": "5", "type": "totalCross", "value": "5"}, {"fh": "1", "type": "attIboxMiss", "value": "1"}, {"fh": "1", "type": "savedObox", "value": "1"}, {"fh": "4", "type": "unsuccessfulTouch", "value": "4"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "1", "type": "forwardGoals", "value": "1"}, {"fh": "31", "type": "possLostCtrl", "value": "31"}, {"fh": "2", "type": "attIboxBlocked", "value": "2"}, {"fh": "4", "type": "crosses18yard", "value": "4"}, {"fh": "15", "type": "finalThirdEntries", "value": "15"}, {"fh": "4", "type": "effectiveClearance", "value": "4"}, {"fh": "4", "type": "fkFoulLost", "value": "4"}, {"fh": "2", "type": "wonCorners", "value": "2"}, {"fh": "60.8", "type": "possessionPercentage", "value": "60.8"}, {"fh": "1", "type": "interception", "value": "1"}, {"fh": "4", "type": "attemptedTackleFoul", "value": "4"}, {"fh": "23", "type": "backwardPass", "value": "23"}, {"type": "firstHalfGoals", "value": "1"}, {"fh": "1", "type": "interceptionWon", "value": "1"}, {"fh": "9", "type": "penAreaEntries", "value": "9"}, {"fh": "8", "type": "accurateThrows", "value": "8"}, {"fh": "2", "type": "totalLayoffs", "value": "2"}, {"fh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "6", "type": "totalContest", "value": "6"}, {"fh": "4", "type": "totalClearance", "value": "4"}, {"fh": "9", "type": "longPassOwnToOppSuccess", "value": "9"}, {"fh": "1", "type": "savedIbox", "value": "1"}, {"fh": "1", "type": "attemptsObox", "value": "1"}, {"fh": "3", "type": "totalAttAssist", "value": "3"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "5", "type": "attemptsIbox", "value": "5"}, {"fh": "2", "type": "cornerTaken", "value": "2"}, {"type": "formationUsed", "value": "3421"}], "kit": {"id": "8380", "colour1": "#990000", "type": "home"}}, {"contestantId": "7gnly6999wao1xarwct4p8fe9", "formationUsed": "3511", "player": [{"playerId": "5syage212151fcixahbq3k8ut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "divingSave", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "accuratePass", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "12"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "saves", "value": "3"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "diveSave", "value": "2"}, {"type": "passesRight", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "goalKicks", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "8"}, {"type": "fwdPass", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "savedObox", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "savedIbox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "22x1ucwwak58wi6r5x1j6ohk9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "11"}, {"type": "rightsidePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "15"}, {"type": "totalPass", "value": "15"}, {"type": "fwdPass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "4"}, {"type": "possLostCtrl", "value": "4"}, {"type": "effectiveClearance", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "4042kk7ylrs378rh26b7mh42x", "firstName": "<PERSON>yan<PERSON>", "lastName": "Silveira Neves Vojnović", "matchName": "Lyanco", "shirtNumber": 4, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "accuratePass", "value": "22"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "rightsidePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "29"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "21"}, {"type": "successfulOpenPlayPass", "value": "21"}, {"type": "totalBackZonePass", "value": "21"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "21"}, {"type": "totalPass", "value": "22"}, {"type": "fwdPass", "value": "3"}, {"type": "interceptionsInBox", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "2"}, {"type": "backwardPass", "value": "4"}, {"type": "interceptionWon", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "d196vatyvq6ycn1q7d477d2i1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> Silva <PERSON>ci<PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "dispossessed", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "21"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "16"}, {"type": "totalPass", "value": "16"}, {"type": "totalLaunches", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "16kv5p8we3pwqpt5ktwrb7mj9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Vojvoda", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Midfielder", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "duelLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "accuratePass", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "16"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "6"}, {"type": "fwdPass", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "possLostAll", "value": "3"}, {"type": "totalCross", "value": "2"}, {"type": "possLostCtrl", "value": "3"}, {"type": "crosses18yard", "value": "2"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateThrows", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "6ol411t0mogw6m83heiqd0m39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "accuratePass", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "openPlayPass", "value": "6"}, {"type": "totalPass", "value": "6"}, {"type": "fwdPass", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesLeft", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9qrewt049r6yq88c715ymau39", "firstName": "Soualiho", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "11", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "accuratePass", "value": "12"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "16"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "openPlayPass", "value": "11"}, {"type": "totalPass", "value": "12"}, {"type": "fwdPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "passesLeft", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "backwardPass", "value": "4"}, {"type": "totalContest", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "e2vhgeqrw3uy0tstydc5nldxx", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "touches", "value": "9"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "totalPass", "value": "5"}, {"type": "fwdPass", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "challengeLost", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "backwardPass", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "f065kc6qn2cpxjxcm6i08rz3e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "duelLost", "value": "3"}, {"type": "accuratePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalThrows", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "redCard", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "accurateThrows", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "cdrzpuj49ploquvdolgh5c3bp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accuratePass", "value": "7"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "12"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "passesRight", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "openPlayPass", "value": "7"}, {"type": "totalPass", "value": "7"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "97mmzzi3dwdos6jtj2m05w4ph", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "captain": "yes", "stat": [{"type": "blockedScoringAtt", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "attRfTotal", "value": "2"}, {"type": "accuratePass", "value": "5"}, {"type": "wasFouled", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "17"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "attSvHighCentre", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "attOboxTarget", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attFreekickTotal", "value": "1"}, {"type": "openPlayPass", "value": "9"}, {"type": "totalPass", "value": "9"}, {"type": "fwdPass", "value": "4"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "attBxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attBxLeft", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "attRfTarget", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "attFreekickTarget", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "possLostCtrl", "value": "5"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "attSvLowRight", "value": "1"}, {"type": "attHdTotal", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "attHdTarget", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "140ehcmf8mbibw8jkfwncupjp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "accuratePass", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "2"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalThrows", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "accurateThrows", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "19gglji6ai70jiu7iqpkaxbyt", "firstName": "<PERSON>", "lastName": "Bonaz<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e2w8qlrfmunsg7oq4733cu6l1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "thnje2i2s96fcbuscj57053p", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d73maq8id4xflzaj6jfxlduj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eqggzoepu1d4t13fw6ybebtn9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 88, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9ak3zczupeken1itsmkherbh1", "firstName": "<PERSON>", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "23avr8oet7764jflt8y1o1gb9", "firstName": "Antonio", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "55hr275k72a0ka78irp7z6uw9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Segre", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8ud1kq9mdgukhlbda5vcn0s0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 39, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "f38rlbvo9k6kommwf1n3frfje", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 73, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "6gpsygusukoecmq6bnq65rfmd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "13", "type": "duelLost", "value": "13"}, {"fh": "3", "type": "divingSave", "value": "3"}, {"fh": "1", "type": "blockedScoringAtt", "value": "1"}, {"fh": "37", "type": "leftside<PERSON><PERSON>", "value": "37"}, {"fh": "1", "type": "possWonAtt3rd", "value": "1"}, {"fh": "2", "type": "dispossessed", "value": "2"}, {"fh": "1", "type": "accurateCross", "value": "1"}, {"fh": "2", "type": "attRfTotal", "value": "2"}, {"fh": "95", "type": "accuratePass", "value": "95"}, {"fh": "4", "type": "won<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "15", "type": "totalFinalThirdPasses", "value": "15"}, {"fh": "28", "type": "rightsidePass", "value": "28"}, {"fh": "5", "type": "attemptsConcededIbox", "value": "5"}, {"fh": "163", "type": "touches", "value": "163"}, {"fh": "42", "type": "totalFwdZonePass", "value": "42"}, {"fh": "2", "type": "attAssistOpenplay", "value": "2"}, {"fh": "4", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "1", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "33", "type": "accurateFwdZonePass", "value": "33"}, {"fh": "1", "type": "attSvHighCentre", "value": "1"}, {"fh": "6", "type": "totalChippedPass", "value": "6"}, {"fh": "5", "type": "effectiveHeadClearance", "value": "5"}, {"fh": "2", "type": "lostCorners", "value": "2"}, {"fh": "1", "type": "fouledFinalThird", "value": "1"}, {"fh": "1", "type": "goalsConceded", "value": "1"}, {"fh": "3", "type": "saves", "value": "3"}, {"fh": "2", "type": "ontargetScoringAtt", "value": "2"}, {"fh": "3", "type": "totalScoringAtt", "value": "3"}, {"fh": "4", "type": "blocked<PERSON><PERSON>", "value": "4"}, {"fh": "1", "type": "attemptsConcededObox", "value": "1"}, {"fh": "12", "type": "ballRecovery", "value": "12"}, {"fh": "1", "type": "subsMade", "value": "1"}, {"fh": "4", "type": "possWonDef3rd", "value": "4"}, {"fh": "63", "type": "accurateBackZonePass", "value": "63"}, {"fh": "4", "type": "passesRight", "value": "4"}, {"fh": "5", "type": "totalThrows", "value": "5"}, {"fh": "1", "type": "attOboxTarget", "value": "1"}, {"fh": "91", "type": "successfulOpenPlayPass", "value": "91"}, {"fh": "69", "type": "totalBackZonePass", "value": "69"}, {"fh": "8", "type": "totalLongBalls", "value": "8"}, {"fh": "1", "type": "goalsConcededIbox", "value": "1"}, {"fh": "1", "type": "attObxCentre", "value": "1"}, {"fh": "2", "type": "attOpenplay", "value": "2"}, {"fh": "7", "type": "possWonMid3rd", "value": "7"}, {"fh": "4", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "1", "type": "attFreekickTotal", "value": "1"}, {"fh": "5", "type": "headClea<PERSON>", "value": "5"}, {"fh": "1", "type": "goalKicks", "value": "1"}, {"fh": "1", "type": "totalRedCard", "value": "1"}, {"fh": "104", "type": "openPlayPass", "value": "104"}, {"fh": "108", "type": "totalPass", "value": "108"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "25", "type": "fwdPass", "value": "25"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "type": "touchesInOppBox", "value": "3"}, {"fh": "2", "type": "interceptionsInBox", "value": "2"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "attBxCentre", "value": "1"}, {"fh": "2", "type": "ontargetAttAssist", "value": "2"}, {"fh": "9", "type": "longPassOwnToOpp", "value": "9"}, {"fh": "2", "type": "accurateChippedPass", "value": "2"}, {"fh": "13", "type": "duel<PERSON>on", "value": "13"}, {"fh": "11", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "11"}, {"fh": "4", "type": "fkFoulWon", "value": "4"}, {"fh": "2", "type": "totalCrossNocorner", "value": "2"}, {"fh": "1", "type": "attBxLeft", "value": "1"}, {"fh": "3", "type": "successfulPutThrough", "value": "3"}, {"fh": "5", "type": "totalTackle", "value": "5"}, {"fh": "13", "type": "passesLeft", "value": "13"}, {"fh": "1", "type": "attRfTarget", "value": "1"}, {"fh": "1", "type": "accurateLaunches", "value": "1"}, {"fh": "22", "type": "possLostAll", "value": "22"}, {"fh": "5", "type": "accurateLongBalls", "value": "5"}, {"fh": "3", "type": "challengeLost", "value": "3"}, {"fh": "3", "type": "totalCross", "value": "3"}, {"fh": "1", "type": "attFreekickTarget", "value": "1"}, {"fh": "1", "type": "accurateGoalKicks", "value": "1"}, {"fh": "1", "type": "savedObox", "value": "1"}, {"fh": "5", "type": "unsuccessfulTouch", "value": "5"}, {"fh": "22", "type": "possLostCtrl", "value": "22"}, {"fh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "3", "type": "aerialLost", "value": "3"}, {"fh": "1", "type": "attSvLowRight", "value": "1"}, {"fh": "2", "type": "crosses18yard", "value": "2"}, {"fh": "7", "type": "finalThirdEntries", "value": "7"}, {"fh": "1", "type": "attHdTotal", "value": "1"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "8", "type": "effectiveClearance", "value": "8"}, {"fh": "6", "type": "fkFoulLost", "value": "6"}, {"fh": "1", "type": "wonCorners", "value": "1"}, {"fh": "39.2", "type": "possessionPercentage", "value": "39.2"}, {"fh": "5", "type": "interception", "value": "5"}, {"fh": "4", "type": "attemptedTackleFoul", "value": "4"}, {"fh": "18", "type": "backwardPass", "value": "18"}, {"fh": "5", "type": "interceptionWon", "value": "5"}, {"fh": "5", "type": "penAreaEntries", "value": "5"}, {"fh": "5", "type": "accurateThrows", "value": "5"}, {"fh": "1", "type": "totalLayoffs", "value": "1"}, {"fh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "4", "type": "totalContest", "value": "4"}, {"fh": "8", "type": "totalClearance", "value": "8"}, {"fh": "5", "type": "longPassOwnToOppSuccess", "value": "5"}, {"fh": "1", "type": "savedIbox", "value": "1"}, {"fh": "1", "type": "attemptsObox", "value": "1"}, {"fh": "2", "type": "totalAttAssist", "value": "2"}, {"fh": "2", "type": "attemptsIbox", "value": "2"}, {"fh": "1", "type": "cornerTaken", "value": "1"}, {"type": "formationUsed", "value": "3511"}], "kit": {"id": "594", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Main", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "136yv5gcvir1xgseenoley95h", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "ent9gzo7zh7botgnb58k6n9at", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>"}, {"id": "a72lwc1d9oou0kp2d3f2vsp1x", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}]}}}