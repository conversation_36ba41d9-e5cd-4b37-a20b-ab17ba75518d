package com.wsf.dataingestor.unit.sportmonks.services;

import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.SquadsFeedCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.dataingestor.sportmonks.clients.TeamSquadsClient;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.dataingestor.sportmonks.services.SportmonksSquadsRetriever;
import com.wsf.domain.common.ExternalIds;
import com.wsf.domain.common.Tournament;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class SportmonksSquadsRetrieverTest extends TestWithMocks {
  @Mock
  private TeamSquadsClient teamSquadsClient;
  @Mock
  private TournamentService tournamentService;
  @Mock
  private SportmonksFeedParserUtils smFeedParserUtils;

  private SportmonksSquadsRetriever sportmonksSquadsRetriever;

  @Before
  public void setUp() throws Exception {
    sportmonksSquadsRetriever = new SportmonksSquadsRetriever(teamSquadsClient, smFeedParserUtils);
  }

  @Test
  public void whenThereAreTwoSportmonksTournamentIds_thenSquadIngestionIsProcessedForEachOfThoseTournaments() {
    Set<String> sportmonksIds = Set.of("20873", "22839");
    Set<String> optaIds = Set.of("1");

    Tournament superCopaDeArgentina = TournamentCanned
      .tournamentCanned()
      .externalIds(new ExternalIds(sportmonksIds, optaIds, null, null))
      .build();
    SquadsFeed squadsFeed1 = SquadsFeedCanned.defaultSquadsFeed().build();
    SquadsFeed squadsFeed2 = SquadsFeedCanned.defaultSquadsFeed().feedId("555").build();

    when(teamSquadsClient.retrieveParsedFeed(eq("20873"))).thenReturn(squadsFeed1);
    when(teamSquadsClient.retrieveParsedFeed(eq("22839"))).thenReturn(squadsFeed2);
    when(tournamentService.findByIsActiveTrue(anyString())).thenReturn(superCopaDeArgentina);

    var setOfSquads = sportmonksSquadsRetriever.retrieveSquadsFeed(superCopaDeArgentina);
    assertEquals(2, setOfSquads.getSquadPlayers()
      .size());
  }

}
