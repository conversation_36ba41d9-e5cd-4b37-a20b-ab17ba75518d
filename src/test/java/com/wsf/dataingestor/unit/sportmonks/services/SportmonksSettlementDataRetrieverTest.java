package com.wsf.dataingestor.unit.sportmonks.services;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.sportmonks.clients.FixtureStatsClient;
import com.wsf.dataingestor.sportmonks.services.SportmonksSettlementDataRetriever;
import com.wsf.domain.common.Fixture;

import static org.mockito.Mockito.verify;

public class SportmonksSettlementDataRetrieverTest extends TestWithMocks {

  @Mock
  FixtureStatsClient fixtureStatsClient;

  SportmonksSettlementDataRetriever sportmonksSettlementDataRetriever;

  @Before
  public void init() {
    sportmonksSettlementDataRetriever = new SportmonksSettlementDataRetriever(fixtureStatsClient);
  }

  @Test
  public void whenASettlementIsProcessed_butTheMatchIsStartedMoreThan45MinutesAgo_thenItsProcessed() {
    Instant fixtureDate = Instant.now().minus(46, ChronoUnit.MINUTES);
    String expectedSportmonksId = "roma vs feyenord";
    Fixture fixture = FixtureCanned
      .defaultFixtureBuilder()
      .date(fixtureDate)
      .sportmonksFixtureId(expectedSportmonksId)
      .build();
    MatchDataFeedCanned.matchDataFeedCanned().fixture(fixture).build();

    sportmonksSettlementDataRetriever.retrieveSettlementData(fixture);

    verify(fixtureStatsClient).retrieveParsedFeed(expectedSportmonksId);
  }
}
