package com.wsf.dataingestor.unit.services.ratings.team;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.services.LiveTeamRatingService;
import com.wsf.dataingestor.services.TeamRatingService;
import com.wsf.dataingestor.services.ratings.team.TeamFinalRatingsProcessor;
import com.wsf.dataingestor.services.stats.AllStatistic0Validator;
import com.wsf.dataingestor.services.stats.StatsDuplicatorFactory;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamDataDTOCanned.teamDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamMatchDataCanned.teamMatchDataCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS_REGULAR_TIMES;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class TeamFinalRatingsProcessorTest extends TestWithMocks {

  @Mock
  TeamRatingService teamRatingServiceMock;

  @Mock
  LiveTeamRatingService teamLiveRatingServiceMock;

  @Mock
  OngoingMatchDataCacheService ongoingMatchDataCacheServiceMock;

  @Mock
  TeamMatchDataCacheService teamMatchDataCacheServiceMock;

  @Mock
  KafkaService kafkaServiceMock;

  @Mock
  AllStatistic0Validator allStatistic0ValidatorMock;

  @Mock
  CompetitionConfigRepository competitionConfigRepositoryMock;

  TeamFinalRatingsProcessor teamFinalRatingsProcessor;

  @Before
  public void setUp() throws Exception {
    TeamStatsEnricher teamStatsEnricher = new TeamStatsEnricher(new StatsDuplicatorFactory());
    this.teamFinalRatingsProcessor = new TeamFinalRatingsProcessor(teamStatsEnricher, teamRatingServiceMock,
      teamLiveRatingServiceMock, teamMatchDataCacheServiceMock, ongoingMatchDataCacheServiceMock, kafkaServiceMock,
      competitionConfigRepositoryMock, allStatistic0ValidatorMock);
  }

  @Test
  public void ifForceSettlement_ThenStatValidatorIsNotCalled() {
    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of())
      .fixtureStatus(PLAYED)
      .supportedEventTypes(Set.of(FOUL))
      .build();

    teamFinalRatingsProcessor.sendFinalRatingsTeams(matchDataFeed, true, true);

    verifyNoInteractions(allStatistic0ValidatorMock);
  }

  @Test
  public void ifAnEventTypeIsSupported_then90MinutesStatIsCreated() {
    when(ongoingMatchDataCacheServiceMock.get(anyString())).thenReturn(ongoingMatchDataCanned().build());
    when(teamMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(teamMatchDataCanned().build());

    Team firstTeam = TeamCanned.defaultTeam(new ObjectId(TEAM1_ID.toString()));
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).build();

    Team secondTeam = TeamCanned.defaultTeam(new ObjectId(TEAM2_ID.toString()));
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).build();

    Fixture fixture = fixtureCanned().homeTeam(firstTeam).awayTeam(secondTeam).build();
    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixture(fixture)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .aggregatedPlayerMatchEvents(List.of())
      .fixtureStatus(PLAYED)
      .supportedEventTypes(Set.of(FOUL))
      .build();

    teamFinalRatingsProcessor.sendFinalRatingsTeams(matchDataFeed, true, false);

    ArgumentCaptor<TeamRating> capt = ArgumentCaptor.forClass(TeamRating.class);
    verify(teamRatingServiceMock, times(2)).storeRating(capt.capture());

    Map<String, Number> expectedFirstTeamStats = newHashMap();
    expectedFirstTeamStats.put(TEAM_YELLOW_CARDS, 0);
    expectedFirstTeamStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_FOULS, 0);
    expectedFirstTeamStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_OFFSIDES, 0);
    expectedFirstTeamStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_RED_CARDS, 0);
    expectedFirstTeamStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_TACKLES_WON, 0);
    expectedFirstTeamStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    Map<String, Number> expectedSecondTeamStats = newHashMap();
    expectedSecondTeamStats.put(TEAM_YELLOW_CARDS, 0);
    expectedSecondTeamStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_FOULS, 0);
    expectedSecondTeamStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_OFFSIDES, 0);
    expectedSecondTeamStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_RED_CARDS, 0);
    expectedSecondTeamStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_TACKLES_WON, 0);
    expectedSecondTeamStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    List<TeamRating> storedRatings = capt.getAllValues();
    verifyRatings(firstTeam, secondTeam, expectedFirstTeamStats, expectedSecondTeamStats, matchDataFeed, fixture,
      storedRatings);

    verify(kafkaServiceMock, times(2)).sendFinalTeamRating(capt.capture(), eq(Fixture.FixtureStatus.PLAYED), eq(true),
      eq(EMPTY_METADATA));
    List<TeamRating> sentRatings = capt.getAllValues();
    verifyRatings(firstTeam, secondTeam, expectedFirstTeamStats, expectedSecondTeamStats, matchDataFeed, fixture,
      sentRatings);
  }

  @Test
  public void whenFinalTeamRatingsAreProcessed_thenTheExpectedTeamStatsAreSent() {
    when(ongoingMatchDataCacheServiceMock.get(anyString())).thenReturn(ongoingMatchDataCanned().build());
    when(teamMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(teamMatchDataCanned().build());

    Map<String, Number> firstTeamStats = newHashMap();
    firstTeamStats.put(TEAM_CORNERS, 4);
    Team firstTeam = TeamCanned.defaultTeam(new ObjectId(TEAM1_ID.toString()));
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).stats(firstTeamStats).build();

    Map<String, Number> secondTeamStats = newHashMap();
    secondTeamStats.put(TEAM_CORNERS, 6);
    Team secondTeam = TeamCanned.defaultTeam(new ObjectId(TEAM2_ID.toString()));
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).stats(secondTeamStats).build();

    Fixture fixture = fixtureCanned().homeTeam(firstTeam).awayTeam(secondTeam).build();
    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixture(fixture)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .aggregatedPlayerMatchEvents(List.of())
      .fixtureStatus(PLAYED)
      .build();

    teamFinalRatingsProcessor.sendFinalRatingsTeams(matchDataFeed, true, false);

    ArgumentCaptor<TeamRating> capt = ArgumentCaptor.forClass(TeamRating.class);
    verify(teamRatingServiceMock, times(2)).storeRating(capt.capture());

    Map<String, Number> expectedFirstTeamStats = newHashMap();
    expectedFirstTeamStats.put(TEAM_CORNERS, 4);
    expectedFirstTeamStats.put(TEAM_YELLOW_CARDS, 0);
    expectedFirstTeamStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_FOULS, 0);
    expectedFirstTeamStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedFirstTeamStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_OFFSIDES, 0);
    expectedFirstTeamStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_RED_CARDS, 0);
    expectedFirstTeamStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedFirstTeamStats.put(TEAM_TACKLES_WON, 0);
    expectedFirstTeamStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    Map<String, Number> expectedSecondTeamStats = newHashMap();
    expectedSecondTeamStats.put(TEAM_CORNERS, 6);
    expectedSecondTeamStats.put(TEAM_YELLOW_CARDS, 0);
    expectedSecondTeamStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_FOULS, 0);
    expectedSecondTeamStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedSecondTeamStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_OFFSIDES, 0);
    expectedSecondTeamStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_RED_CARDS, 0);
    expectedSecondTeamStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedSecondTeamStats.put(TEAM_TACKLES_WON, 0);
    expectedSecondTeamStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    List<TeamRating> storedRatings = capt.getAllValues();
    verifyRatings(firstTeam, secondTeam, expectedFirstTeamStats, expectedSecondTeamStats, matchDataFeed, fixture,
      storedRatings);

    verify(kafkaServiceMock, times(2)).sendFinalTeamRating(capt.capture(), eq(Fixture.FixtureStatus.PLAYED), eq(true),
      eq(EMPTY_METADATA));
    List<TeamRating> sentRatings = capt.getAllValues();
    verifyRatings(firstTeam, secondTeam, expectedFirstTeamStats, expectedSecondTeamStats, matchDataFeed, fixture,
      sentRatings);
  }

  private void verifyRatings(Team firstTeam, Team secondTeam, Map<String, Number> firstTeamStats,
                             Map<String, Number> secondTeamStats, MatchDataFeed matchDataFeed, Fixture fixture,
                             List<TeamRating> ratingsToVerify) {
    TeamRating firstTeamStoredRating = ratingsToVerify.get(0);
    verifyRating(firstTeamStoredRating, firstTeam, fixture, matchDataFeed, firstTeamStats);

    TeamRating secondTeamStoredRating = ratingsToVerify.get(1);
    verifyRating(secondTeamStoredRating, secondTeam, fixture, matchDataFeed, secondTeamStats);
  }

  private void verifyRating(TeamRating rating, Team team, Fixture fixture, MatchDataFeed matchDataFeed,
                            Map<String, Number> stats) {
    assertThat(rating.getTeam(), is(team));
    assertThat(rating.getStats(), is(stats));
    assertThat(rating.getFixture(), is(fixture));
    assertThat(rating.getFeedId(), is(matchDataFeed.getFeedId()));
    assertThat(rating.getFixtureTimeMin(), is(matchDataFeed.getMatchTimeMin()));
    assertThat(rating.getPeriodId(), is(matchDataFeed.getMatchPeriod().getPeriodId()));
    assertThat(rating.getIsFinal(), is(true));
    assertThat(rating.getTimestamp(), is(matchDataFeed.getReceivedTs()));
  }
}
