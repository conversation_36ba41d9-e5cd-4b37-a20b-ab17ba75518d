package com.wsf.dataingestor.unit.services.fixtures;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.hamcrest.MatcherAssert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.fixtures.FixtureRelationshipService;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.time.Instant.now;
import static java.util.Objects.nonNull;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class FixtureRelationshipServiceTest extends TestWithMocks {

  @Mock
  private FixturesRetriever fixturesRetrieverMock;
  @Mock
  private FixtureService fixtureServiceMock;

  FixtureRelationshipService fixtureRelationshipService;
  MetricsManager metrics = new MetricsManager(new SimpleMeterRegistry());

  @Before
  public void setUp() throws Exception {
    when(fixturesRetrieverMock.getExternalFixtureFieldName()).thenReturn("providerField");
    fixtureRelationshipService = new FixtureRelationshipService(fixtureServiceMock, metrics);
  }

  @Test
  public void whenTwoRelatedMatchesAreRetrieved_theirRelationIsSavedInTheDb() throws FixtureNotFoundException {
    String firstLegId = "firstLegId";
    String secondLegId = "ritorno";

    Instant dateFirstLeg = Instant.now().plus(1, ChronoUnit.DAYS);
    Instant dateSecondLeg = Instant.now().plus(7, ChronoUnit.DAYS);
    var fixturesFeed = MatchDataFeedCanned.buildMatchesFeed(firstLegId, secondLegId, dateFirstLeg, dateSecondLeg);
    Fixture firstLeg = FixtureCanned.buildFixture(1).build();
    Fixture secondLeg = FixtureCanned.buildFixture(2).build();
    FixtureDTO firstLegDto = fixturesFeed.getFixtures().get(0);
    FixtureDTO secondLegDto = fixturesFeed.getFixtures().get(1);

    when(fixturesRetrieverMock.findFixture(eq(firstLegDto), any())).thenReturn(firstLeg);
    when(fixturesRetrieverMock.findRelatedFixture(eq(firstLegDto), any())).thenReturn(secondLeg);
    when(fixturesRetrieverMock.findFixture(eq(secondLegDto), any())).thenReturn(secondLeg);
    when(fixturesRetrieverMock.findRelatedFixture(eq(secondLegDto), any())).thenReturn(firstLeg);

    List<String> processedFixtureIds = fixturesFeed.getFixtures()
      .stream()
      .map(FixtureDTO::getExternalFixtureId)
      .collect(Collectors.toList());
    fixtureRelationshipService.storeRelations(fixturesRetrieverMock, fixturesFeed.getTournament(),
      fixturesFeed.getFixtures(), processedFixtureIds);

    ArgumentCaptor<Fixture> fixtureArgumentCaptor = ArgumentCaptor.forClass(Fixture.class);
    ArgumentCaptor<Map<String, Object>> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
    verify(fixtureServiceMock, times(2)).updateFixture(fixtureArgumentCaptor.capture(), mapArgumentCaptor.capture());

    var fixtures = fixtureArgumentCaptor.getAllValues();
    assertThat(fixtures.get(0), is(firstLeg));
    assertThat(fixtures.get(1), is(secondLeg));

    var updates = mapArgumentCaptor.getAllValues();
    assertEquals(buildExpectedRelationUpdate(Fixture.Relation.FIRST_LEG, secondLeg.getId()), updates.get(0));
    assertEquals(buildExpectedRelationUpdate(Fixture.Relation.SECOND_LEG, firstLeg.getId()), updates.get(1));
  }

  @Test
  public void whenTwoUnrelatedMatchesAreRetrieved_andTheExternalIdIsNotPresent_theyAreSavedIntoTheDbAsSingleMatches() throws FixtureNotFoundException {
    String firstLegId = "firstLegId";
    String secondLegId = "ritorno";

    Instant dateFirstLeg = Instant.now().plus(1, ChronoUnit.DAYS);
    Instant dateSecondLeg = Instant.now().plus(7, ChronoUnit.DAYS);

    FixtureDTO firstLegDto = FixtureDTO
      .builder()
      .externalFixtureId(firstLegId)
      .externalRelatedFixtureId(null)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .leg(FixtureDTO.Relation.SINGLE)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateFirstLeg)
      .isLiveSupported(true)
      .build();

    FixtureDTO secondLegDto = FixtureDTO
      .builder()
      .externalFixtureId(secondLegId)
      .externalRelatedFixtureId(null)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .leg(FixtureDTO.Relation.SINGLE)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateSecondLeg)
      .isLiveSupported(true)
      .build();

    FixturesFeed fixturesFeed = FixturesFeed
      .builder()
      .feedId("feedId")
      .tournament(tournamentCanned().build())
      .fixtures(List.of(firstLegDto, secondLegDto))
      .build();

    Fixture firstLeg = FixtureCanned.buildFixture(1).build();
    Fixture secondLeg = FixtureCanned.buildFixture(2).build();

    when(fixturesRetrieverMock.findFixture(eq(firstLegDto), any())).thenReturn(firstLeg);
    when(fixturesRetrieverMock.findFixture(eq(secondLegDto), any())).thenReturn(secondLeg);

    List<String> processedFixtureIds = fixturesFeed.getFixtures()
      .stream()
      .map(FixtureDTO::getExternalFixtureId)
      .collect(Collectors.toList());
    fixtureRelationshipService.storeRelations(fixturesRetrieverMock, fixturesFeed.getTournament(),
      fixturesFeed.getFixtures(), processedFixtureIds);

    ArgumentCaptor<Fixture> fixtureArgumentCaptor = ArgumentCaptor.forClass(Fixture.class);
    ArgumentCaptor<Map<String, Object>> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
    verify(fixtureServiceMock, times(2)).updateFixture(fixtureArgumentCaptor.capture(), mapArgumentCaptor.capture());

    var fixtures = fixtureArgumentCaptor.getAllValues();
    assertThat(fixtures.get(0), is(firstLeg));
    assertThat(fixtures.get(1), is(secondLeg));

    var updates = mapArgumentCaptor.getAllValues();
    assertEquals(buildExpectedRelationUpdate(Fixture.Relation.SINGLE, null), updates.get(0));
    assertEquals(buildExpectedRelationUpdate(Fixture.Relation.SINGLE, null), updates.get(1));
  }

  @Test
  public void whenTwoRelatedMatchesAreRetrieved_butTheExternalIdIsNotPresent_AnErrorIsRaised() throws FixtureNotFoundException {
    String firstLegId = "firstLegId";
    String secondLegId = "ritorno";

    Instant dateFirstLeg = Instant.now().plus(1, ChronoUnit.DAYS);
    Instant dateSecondLeg = Instant.now().plus(7, ChronoUnit.DAYS);

    FixtureDTO firstLegDto = FixtureDTO
      .builder()
      .externalFixtureId(firstLegId)
      .externalRelatedFixtureId(null)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .leg(FixtureDTO.Relation.FIRST_LEG)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateFirstLeg)
      .isLiveSupported(true)
      .build();

    FixtureDTO secondLegDto = FixtureDTO
      .builder()
      .externalFixtureId(secondLegId)
      .externalRelatedFixtureId(null)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .leg(FixtureDTO.Relation.SECOND_LEG)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateSecondLeg)
      .isLiveSupported(true)
      .build();

    FixturesFeed fixturesFeed = FixturesFeed
      .builder()
      .feedId("feedId")
      .tournament(tournamentCanned().build())
      .fixtures(List.of(firstLegDto, secondLegDto))
      .build();

    Fixture firstLeg = FixtureCanned.buildFixture(1).build();
    Fixture secondLeg = FixtureCanned.buildFixture(2).build();

    when(fixturesRetrieverMock.findFixture(eq(firstLegDto), any())).thenReturn(firstLeg);
    when(fixturesRetrieverMock.findFixture(eq(secondLegDto), any())).thenReturn(secondLeg);

    List<String> processedFixtureIds = fixturesFeed.getFixtures()
      .stream()
      .map(FixtureDTO::getExternalFixtureId)
      .collect(Collectors.toList());
    fixtureRelationshipService.storeRelations(fixturesRetrieverMock, fixturesFeed.getTournament(),
      fixturesFeed.getFixtures(), processedFixtureIds);

    verifyNoInteractions(fixtureServiceMock);
    assertEquals((int) metrics.MATCHES_FEED_PARSING_ERROR.count(), 2);
  }

  @Test
  public void whenTwoRelatedMatchesAreRetrieved_butOnlyOneIsPresentOnTheDb_anErrorIsCounted() throws FixtureNotFoundException {
    // Arrange
    String firstLegId = "firstLegId";
    String secondLegId = "ritorno";

    Instant dateFirstLeg = Instant.now().plus(1, ChronoUnit.DAYS);
    Instant dateSecondLeg = Instant.now().plus(7, ChronoUnit.DAYS);

    var fixturesFeed = MatchDataFeedCanned.buildMatchesFeed(firstLegId, secondLegId, dateFirstLeg, dateSecondLeg);

    Fixture firstLeg = FixtureCanned.buildFixture(1).build();
    Fixture secondLeg = FixtureCanned.buildFixture(2).build();

    FixtureDTO firstLegDto = fixturesFeed.getFixtures().get(0);
    FixtureDTO secondLegDto = fixturesFeed.getFixtures().get(1);
    when(fixturesRetrieverMock.findFixture(eq(firstLegDto), any())).thenReturn(firstLeg);
    when(fixturesRetrieverMock.findRelatedFixture(eq(firstLegDto), any())).thenReturn(secondLeg);
    when(fixturesRetrieverMock.findFixture(eq(secondLegDto), any())).thenReturn(secondLeg);
    when(fixturesRetrieverMock.findRelatedFixture(eq(secondLegDto), any())).thenThrow(
      new FixtureNotFoundException("not found"));

    List<String> processedFixtureIds = fixturesFeed.getFixtures()
      .stream()
      .map(FixtureDTO::getExternalFixtureId)
      .collect(Collectors.toList());

    // Act
    fixtureRelationshipService.storeRelations(fixturesRetrieverMock, fixturesFeed.getTournament(),
      fixturesFeed.getFixtures(), processedFixtureIds);

    // Assert
    ArgumentCaptor<Fixture> fixtureArgumentCaptor = ArgumentCaptor.forClass(Fixture.class);
    ArgumentCaptor<Map<String, Object>> mapArgumentCaptor = ArgumentCaptor.forClass(Map.class);
    verify(fixtureServiceMock, times(1)).updateFixture(fixtureArgumentCaptor.capture(), mapArgumentCaptor.capture());

    var fixture = fixtureArgumentCaptor.getValue();
    assertThat(fixture, is(firstLeg));

    var updates = mapArgumentCaptor.getAllValues();
    assertEquals(buildExpectedRelationUpdate(Fixture.Relation.FIRST_LEG, secondLeg.getId()), updates.get(0));

    assertEquals(1, (int) metrics.MATCHES_FEED_PARSING_ERROR.count());
  }


  private static Map<String, Object> buildExpectedRelationUpdate(Fixture.Relation relation,
                                                                 ObjectId relatedExternalId) {
    var map = new HashMap<String, Object>();
    map.put("leg", relation);
    if (nonNull(relatedExternalId)) {
      map.put("relatedFixtureId", relatedExternalId);
    }
    return map;
  }

}
