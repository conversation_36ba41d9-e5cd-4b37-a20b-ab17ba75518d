package com.wsf.dataingestor.unit.services.ratings;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.crons.SettlementUpdater;
import com.wsf.dataingestor.services.ratings.MatchSettlementLogic;
import com.wsf.dataingestor.shared.TestWithMocks;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static org.mockito.Mockito.verify;

public class MatchSettlementLogicTest extends TestWithMocks {

  @Mock
  SettlementUpdater settlementUpdater;

  MatchSettlementLogic matchSettlementLogic;

  @Before
  public void setup() {
    matchSettlementLogic = new MatchSettlementLogic(settlementUpdater);
  }

  @Test
  public void whenAFixtureHasBeenPlayed_thenTheSettlementIsScheduled() {
    // Arrange
    var fixture = fixtureCanned().build();

    // Act
    matchSettlementLogic.processPlayedFixtureSettlement(fixture);

    // Assert
    verify(settlementUpdater).scheduleSettlementJob(fixture);
  }
}