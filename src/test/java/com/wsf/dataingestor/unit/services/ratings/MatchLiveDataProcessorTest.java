package com.wsf.dataingestor.unit.services.ratings;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.google.common.collect.Sets;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingsProcessor;
import com.wsf.dataingestor.services.ratings.team.TeamLiveRatingsProcessor;
import com.wsf.dataingestor.shared.TestUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static com.google.common.collect.Sets.newHashSet;
import static com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO.fromMatchFeedEvent;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.shared.TestUtils.buildCachedData;
import static com.wsf.dataingestor.shared.TestUtils.buildFeed;
import static com.wsf.dataingestor.shared.TestUtils.buildPlayerFeedData;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.entityEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.subOffPlayerEvent;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.subOnPlayerEvent;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_AWAY_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_HOME_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.createPlayerMatchEvent;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned.playerMatchEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.DELETED_EVENT;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.UNKNOWN_EVENT;
import static java.util.Collections.emptyMap;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

public class MatchLiveDataProcessorTest extends TestWithMocks {

  private static final Fixture FIXTURE = fixtureCanned().build();
  private static final Player PLAYER_1 = playerCanned().id(PlayerCanned.PLAYER_1_ID).build();

  @Mock
  PlayerLiveRatingsProcessor playerLiveRatingsProcessorMock;
  @Mock
  TeamLiveRatingsProcessor teamLiveRatingsProcessorMock;

  MatchLiveDataProcessor matchLiveDataProcessor;

  @Before
  public void setup() {
    matchLiveDataProcessor = new MatchLiveDataProcessor(playerLiveRatingsProcessorMock, teamLiveRatingsProcessorMock);
  }

  @Test
  public void whenMatchDataContainsTheScore_thenCacheIsUpdated() {
    AtomicBoolean processed = new AtomicBoolean(false);
    OngoingMatchData cachedMatchData = buildCachedData();
    EntityEventDTO playerGoalEventHome = entityEventDTOCanned().teamId(DEFAULT_HOME_TEAM.getIdAsString()).build();
    EntityEventDTO playerGoalEventAway = entityEventDTOCanned().teamId(DEFAULT_AWAY_TEAM.getIdAsString()).build();
    MatchDataFeed feed = buildFeed().feedPlayerMatchEvents(List.of(playerGoalEventAway, playerGoalEventHome)).build();

    matchLiveDataProcessor.processFeed(feed, cachedMatchData, (events) -> processed.set(true));

    assertThat(cachedMatchData.getHomeScore(), is(1));
    assertThat(cachedMatchData.getAwayScore(), is(1));
  }

  @Test
  public void whenAPlayerJustCameOnThePitch_thenCacheIsUpdatedWithTheNewLineup() {
    AtomicBoolean processed = new AtomicBoolean(false);
    EntityEventDTO subOnEvent = subOnPlayerEvent().entityId(PLAYER_2_ID_STR).build();
    Player player = playerCanned().id(PLAYER_2_ID).team(teamCanned().id(TEAM1_ID).build()).build();
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(subOnEvent))
      .build();
    Map<String, Set<String>> lineup = new HashMap<>();
    Set<String> teamLineup = new HashSet<>();
    teamLineup.add(PLAYER_1_ID_STR);
    lineup.put(TEAM1_ID.toString(), teamLineup);
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().teamIdToLineUp(lineup).build();

    matchLiveDataProcessor.processFeed(feed, cachedMatchData, (events) -> processed.set(true));

    Set<String> team1LineUp = cachedMatchData.getTeamIdToLineUp().get(TEAM1_ID.toString());
    assertEquals(2, team1LineUp.size());
    assertThat(team1LineUp, is(Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)));
  }

  @Test
  public void whenAPlayerJustCameOffThePitch_thenCacheIsUpdatedWithTheNewLineup() {
    AtomicBoolean processed = new AtomicBoolean(false);
    EntityEventDTO subOffEvent = subOffPlayerEvent().entityId(PLAYER_2_ID_STR).build();
    Player player = playerCanned().id(PLAYER_2_ID).team(teamCanned().id(TEAM1_ID).build()).build();
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(subOffEvent))
      .build();
    Map<String, Set<String>> lineup = new HashMap<>();
    Set<String> teamLineup = new HashSet<>();
    teamLineup.add(PLAYER_1_ID_STR);
    teamLineup.add(PLAYER_2_ID_STR);
    lineup.put(TEAM1_ID.toString(), teamLineup);
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().teamIdToLineUp(lineup).build();

    matchLiveDataProcessor.processFeed(feed, cachedMatchData, (events) -> processed.set(true));

    Set<String> team1LineUp = cachedMatchData.getTeamIdToLineUp().get(TEAM1_ID.toString());
    assertEquals(1, team1LineUp.size());
    assertThat(team1LineUp, is(Set.of(PLAYER_1_ID_STR)));
  }

  @Test
  public void whenAPlayerPreviouslyGotARedCard_andTheEventIsDeleted_thenCacheIsUpdatedWithTheNewLineup() {
    // Arrange
    AtomicBoolean processed = new AtomicBoolean(false);
    String team1Str = TEAM1_ID.toString();
    PlayerMatchEventDTO existingRedCardEvent = playerMatchEventDTOCanned()
      .eventId("existingRedCard")
      .event(RED_CARD)
      .entityId(PLAYER_2_ID_STR)
      .teamId(team1Str)
      .build();
    EntityEventDTO deletedRedCardEvent = entityEventDTOCanned()
      .eventId(existingRedCardEvent.getEventId())
      .entityId(PLAYER_2_ID_STR)
      .eventType(DELETED_EVENT)
      .build();
    Player player = playerCanned().id(PLAYER_2_ID).team(teamCanned().id(TEAM1_ID).build()).build();
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(deletedRedCardEvent))
      .build();
    Map<String, Set<String>> lineup = new HashMap<>();
    Set<String> teamLineup = new HashSet<>();
    teamLineup.add(PLAYER_1_ID_STR);
    lineup.put(team1Str, teamLineup);
    Map<String, Set<PlayerMatchEventDTO>> eventIdToEvents = new HashMap<>();
    eventIdToEvents.put(existingRedCardEvent.getEventId(), Set.of(existingRedCardEvent));
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(eventIdToEvents)
      .teamIdToLineUp(lineup)
      .build();

    // Act
    matchLiveDataProcessor.processFeed(feed, cachedMatchData, (events) -> processed.set(true));

    // Assert
    Set<String> team1LineUp = cachedMatchData.getTeamIdToLineUp().get(team1Str);
    Assertions
      .assertThat(team1LineUp)
      .hasSize(2)
      .containsExactly(PLAYER_1_ID_STR, PLAYER_2_ID_STR);
    assertTrue(cachedMatchData.getEventIdToPlayerMatchEvents()
      .isEmpty());
  }

  @Test
  public void whenAFeedIsReceived_andItContainsAGoalEventForAPlayer_andTheMatchCacheAlreadyContainsIt_thenTheMatchCacheIsNotUpdated() {
    String playerId = PLAYER_1.getId().toString();
    List<PlayerDataDTO> playerDataDTOS = List.of(buildPlayingPlayerDTO(emptyMap()));
    EntityEventDTO goalEvent = buildGoalPlayerEvent(playerId);
    List<EntityEventDTO> matchEvents = List.of(goalEvent);
    MatchDataFeed parsedFeed = TestUtils.buildMA2Feed(FIXTURE, 10, LIVE, playerDataDTOS, matchEvents);

    Map<String, Set<PlayerMatchEventDTO>> cachedPlayerMatchEvents = new HashMap<>();
    cachedPlayerMatchEvents.put(goalEvent.getEventId(), Sets.newHashSet(fromMatchFeedEvent(goalEvent)));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(cachedPlayerMatchEvents)
      .build();

    matchLiveDataProcessor.processPullFeed(parsedFeed, ongoingMatchData);

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(1));
    Set<PlayerMatchEventDTO> playerMatchEvents = ongoingMatchData
      .getEventIdToPlayerMatchEvents()
      .get(goalEvent.getEventId());
    assertThat(playerMatchEvents.size(), is(1));
    var playerMatchEventDTO = playerMatchEvents
      .stream()
      .findFirst().get();
    assertThat(playerMatchEventDTO.getEventId(), is(goalEvent.getEventId()));
    assertThat(playerMatchEventDTO.getEvent(), is(goalEvent.getEventType()));
    assertThat(playerMatchEventDTO.getEntityId(), is(goalEvent.getEntityId()));
    assertThat(playerMatchEventDTO.getPeriodId(), is(goalEvent.getPeriod().getPeriodId()));
    assertThat(playerMatchEventDTO.getTimeMin(), is(goalEvent.getTimeMin()));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(true));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(true));
  }

  @Test
  public void whenAFeedIsReceived_andAPlayerJustScored_andTheMatchCacheDoesNotContainTheEvent_thenTheMatchCacheIsUpdated() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String player1Id = PLAYER_1.getId().toString();
    EntityEventDTO goalEvent = buildGoalPlayerEvent(player1Id);
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(goalEvent))
      .build();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(FIXTURE.getHomeTeam().getIdAsString(), newHashSet(player1Id, "randomId")))
      .build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(1));
    Set<PlayerMatchEventDTO> playerMatchEvents = ongoingMatchData
      .getEventIdToPlayerMatchEvents()
      .get(goalEvent.getEventId());
    assertThat(playerMatchEvents.size(), is(1));
    var playerMatchEventDTO = playerMatchEvents
      .stream()
      .findFirst().get();
    assertThat(playerMatchEventDTO.getEventId(), is(goalEvent.getEventId()));
    assertThat(playerMatchEventDTO.getEvent(), is(goalEvent.getEventType()));
    assertThat(playerMatchEventDTO.getEntityId(), is(goalEvent.getEntityId()));
    assertThat(playerMatchEventDTO.getPeriodId(), is(goalEvent.getPeriod().getPeriodId()));
    assertThat(playerMatchEventDTO.getTimeMin(), is(goalEvent.getTimeMin()));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
  }

  @Test
  public void whenAFeedIsReceived_andAPlayerJustScored_andTheMatchCacheContainsTheEvent_thenTheMatchCacheIsNotUpdated() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String player1Id = PLAYER_1.getId().toString();
    EntityEventDTO goalEvent = buildGoalPlayerEvent(player1Id);
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(goalEvent))
      .build();

    Map<String, Set<PlayerMatchEventDTO>> cachedPlayerMatchEvents = new HashMap<>();
    cachedPlayerMatchEvents.put(goalEvent.getEventId(), newHashSet(fromMatchFeedEvent(goalEvent)));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(cachedPlayerMatchEvents)
      .build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(1));
    Set<PlayerMatchEventDTO> playerMatchEvents = ongoingMatchData
      .getEventIdToPlayerMatchEvents()
      .get(goalEvent.getEventId());
    assertThat(playerMatchEvents.size(), is(1));
    var playerMatchEventDTO = playerMatchEvents
      .stream()
      .findFirst().get();
    assertThat(playerMatchEventDTO.getEventId(), is(goalEvent.getEventId()));
    assertThat(playerMatchEventDTO.getEvent(), is(goalEvent.getEventType()));
    assertThat(playerMatchEventDTO.getEntityId(), is(goalEvent.getEntityId()));
    assertThat(playerMatchEventDTO.getPeriodId(), is(goalEvent.getPeriod().getPeriodId()));
    assertThat(playerMatchEventDTO.getTimeMin(), is(goalEvent.getTimeMin()));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
  }

  @Test
  public void whenAGoalEventIsReceived_andItWasPresentAsAShotInTheCache_thenTheEventIsUpdated() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String eventId = "event1";
    EntityEventDTO goalEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(GOAL)
      .teamId(TEAM1_ID.toString())
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(goalEvent))
      .build();

    PlayerMatchEventDTO existingShotEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT)
      .build();

    PlayerMatchEventDTO existingSOGEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT_ON_GOAL)
      .build();
    Map<String, Set<PlayerMatchEventDTO>> cachedPlayerMatchEvents = new HashMap<>();
    cachedPlayerMatchEvents.put(eventId, newHashSet(existingShotEvent, existingSOGEvent));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(cachedPlayerMatchEvents)
      .build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(1));
    Set<PlayerMatchEventDTO> playerMatchEvents = ongoingMatchData.getEventIdToPlayerMatchEvents().get(eventId);
    assertThat(playerMatchEvents.size(), is(1));
    var playerMatchEventDTO = playerMatchEvents
      .stream()
      .findFirst().get();
    assertThat(playerMatchEventDTO.getEventId(), is(goalEvent.getEventId()));
    assertThat(playerMatchEventDTO.getEvent(), is(goalEvent.getEventType()));
    assertThat(playerMatchEventDTO.getEntityId(), is(goalEvent.getEntityId()));
    assertThat(playerMatchEventDTO.getPeriodId(), is(goalEvent.getPeriod().getPeriodId()));
    assertThat(playerMatchEventDTO.getTimeMin(), is(goalEvent.getTimeMin()));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
  }

  @Test
  public void whenAnUnknownEventIsReceived_andItWasPresentAsAShotInTheCache_thenTheEventIsDeleted() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String eventId = "event1";
    EntityEventDTO unknownEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(UNKNOWN_EVENT)
      .isUnknown(true)
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(unknownEvent))
      .build();

    PlayerMatchEventDTO existingShotEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT)
      .build();

    PlayerMatchEventDTO existingSOGEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT_ON_GOAL)
      .build();
    Map<String, Set<PlayerMatchEventDTO>> cachedPlayerMatchEvents = new HashMap<>();
    cachedPlayerMatchEvents.put(eventId, newHashSet(existingShotEvent, existingSOGEvent));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(cachedPlayerMatchEvents)
      .build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(0));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
  }

  @Test
  public void whenADeletedEventIsReceived_andItWasPresentAsAShotInTheCache_thenTheEventIsDeleted() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String eventId = "event1";
    EntityEventDTO deletedEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(DELETED_EVENT)
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(deletedEvent))
      .build();

    PlayerMatchEventDTO existingShotEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT)
      .build();

    PlayerMatchEventDTO existingSOGEvent = createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(PLAYER_2_ID_STR)
      .event(SHOT_ON_GOAL)
      .build();
    Map<String, Set<PlayerMatchEventDTO>> cachedPlayerMatchEvents = new HashMap<>();
    cachedPlayerMatchEvents.put(eventId, newHashSet(existingShotEvent, existingSOGEvent));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(cachedPlayerMatchEvents)
      .build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(0));

    verify(playerLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
    verify(teamLiveRatingsProcessorMock).processFeed(any(), any(), eq(false));
  }

  @Test
  public void whenADeletedGoalEventIsReceived_andGoalAndAssistEventsAreInTheCache_theBothTheEventsAreDeleted() {
    // Arrange
    String team1Str = TEAM1_ID.toString();

    PlayerMatchEventDTO existingGoalEvent = playerMatchEventDTOCanned()
      .eventId("existingGoal")
      .event(GOAL)
      .entityId(PLAYER_1_ID_STR)
      .teamId(team1Str)
      .build();
    PlayerMatchEventDTO existingAssistEvent = playerMatchEventDTOCanned()
      .eventId("existingAssist")
      .relatedEventId(existingGoalEvent.getEventId())
      .event(ASSIST_GOAL)
      .entityId(PLAYER_2_ID_STR)
      .teamId(team1Str)
      .build();
    Map<String, Set<PlayerMatchEventDTO>> eventIdToEvents = new HashMap<>();
    eventIdToEvents.put(existingGoalEvent.getEventId(), Set.of(existingGoalEvent));
    eventIdToEvents.put(existingAssistEvent.getEventId(), Set.of(existingAssistEvent));
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().eventIdToPlayerMatchEvents(eventIdToEvents).build();

    EntityEventDTO deletedGoalEvent = entityEventDTOCanned()
      .eventId(existingGoalEvent.getEventId())
      .entityId(PLAYER_1_ID_STR)
      .eventType(DELETED_EVENT)
      .build();
    Player player = playerCanned().id(PlayerCanned.PLAYER_1_ID).team(teamCanned().id(TEAM1_ID).build()).build();
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(deletedGoalEvent))
      .build();

    // Act
    var newAndDeletedEvents = matchLiveDataProcessor.processFeed(feed, cachedMatchData, (events) -> {});

    // Assert
    Assertions
      .assertThat(cachedMatchData.getEventIdToPlayerMatchEvents())
      .isEmpty();
    Assertions
      .assertThat(newAndDeletedEvents.deletedEvents())
      .hasSize(2)
      .anySatisfy(playerMatchEventDTO -> {
        Assertions
          .assertThat(playerMatchEventDTO.getEventId())
          .isEqualTo(existingGoalEvent.getEventId());
      })
      .anySatisfy(playerMatchEventDTO -> {
        Assertions
          .assertThat(playerMatchEventDTO.getEventId())
          .isEqualTo(existingAssistEvent.getEventId());
      });
  }

  @Test
  public void whenADeletedEventIsReceived_andTheEventIsNotInTheCache_thenTheFeedIsNotProcessed() {
    AtomicBoolean processed = new AtomicBoolean(false);
    String eventId = "event1";
    EntityEventDTO deletedEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(DELETED_EVENT)
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(deletedEvent))
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();

    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(0));

    verify(playerLiveRatingsProcessorMock, never()).processFeed(any(), any(), anyBoolean());
    verify(teamLiveRatingsProcessorMock, never()).processFeed(any(), any(), anyBoolean());
  }

  @Test
  public void whenMultipleEventsForOneEventIdAreReceived_andTheFirstOneIsUnknown_thenTheEventIsAddedToTheCache() {
    // Arrange
    AtomicBoolean processed = new AtomicBoolean(false);
    String eventId = "event1";
    EntityEventDTO unknownEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(UNKNOWN_EVENT)
      .isUnknown(true)
      .build();
    EntityEventDTO shotEvent = entityEventDTOCanned()
      .eventId(eventId)
      .entityId(PLAYER_1_ID_STR)
      .eventType(SHOT)
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerFeedData(emptyMap())))
      .feedPlayerMatchEvents(List.of(unknownEvent, shotEvent))
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();

    // Act
    matchLiveDataProcessor.processFeed(parsedFeed, ongoingMatchData, (events) -> processed.set(true));

    // Assert
    assertThat(ongoingMatchData.getEventIdToPlayerMatchEvents()
      .size(), is(1));
    Set<PlayerMatchEventDTO> events = ongoingMatchData.getEventIdToPlayerMatchEvents().get(eventId);
    assertThat(events.size(), is(1));

    PlayerMatchEventDTO cachedShotEvent = events
      .stream()
      .findFirst().get();
    assertThat(cachedShotEvent.getEvent(), is(shotEvent.getEventType()));
    assertThat(cachedShotEvent.getEntityId(), is(shotEvent.getEntityId()));
    assertThat(cachedShotEvent.getTeamId(), is(shotEvent.getTeamId()));
    assertThat(cachedShotEvent.getTimeMin(), is(shotEvent.getTimeMin()));
    assertThat(cachedShotEvent.getPeriodId(), is(shotEvent.getPeriod().getPeriodId()));
  }

  private static EntityEventDTO buildGoalPlayerEvent(String playerId) {
    return entityEventDTOCanned().entityId(playerId).teamId(TEAM1_ID.toString()).eventType(GOAL).build();
  }

  private static PlayerDataDTO buildPlayingPlayerDTO(Map<String, Number> stats) {
    return buildPlayerFeedData(PLAYER_1, stats);
  }
}
