package com.wsf.dataingestor.unit.services.stats;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.dataingestor.services.stats.StatsDuplicatorFactory;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.EntityEventDTOCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedProvider.SPORTMONKS;
import static com.wsf.dataingestor.shared.TestUtils.buildCachedData;
import static com.wsf.dataingestor.shared.TestUtils.buildFeedWithGoalEvents;
import static com.wsf.dataingestor.shared.TestUtils.buildHomePlayerFeedData;
import static com.wsf.dataingestor.shared.TestUtils.buildPlayerFeedData;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.entityEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_AWAY_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_HOME_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.DEFAULT_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned.playerMatchEventDTOCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.ASSISTS_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.AWAY_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.CORNERS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.FOULS_COMMITTED_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.GOALS_MADE_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.GOAL_KICKS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.HOME_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.dataingestor.sports.soccer.Constants.OFFSIDES_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.OPPONENT_TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.RED_CARDS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.SHOTS_ON_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.SHOTS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TACKLES_WON_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.YELLOW_CARDS_REGULAR_TIMES;
import static com.wsf.domain.customer.soccer.Constants.AWAY_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.customer.soccer.Constants.HOME_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Collections.emptyMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;


public class PlayerStatsEnricherTest extends TestWithMocks {

  PlayerStatsEnricher playerStatsEnricher;

  @Before
  public void setup() {
    playerStatsEnricher = new PlayerStatsEnricher(new StatsDuplicatorFactory());
  }

  @Test
  public void whenCachedDataIsEmpty_andFeedDataHasNewStats_thenEnrichLiveStatsReturnsTrue() {
    Map<String, Number> stats = Map.of(PASS.getStatisticName(), 5);
    PlayerDataDTO playerData = buildHomePlayerFeedData(stats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(emptyMap()).build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.put(PASS.getStatisticName(), 5);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenAYellowCardEventIsReceived_andItHappenedDuringRegularTimes_thenExpectedStatsAreReturned() {
    PlayerDataDTO playerData = buildHomePlayerFeedData(emptyMap());
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(emptyMap()).build();

    PlayerMatchEventDTO yellowCardEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(YELLOW_CARD)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .timeMin(35)
      .build();
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of("eventYellowCard", Set.of(yellowCardEvent)))
      .build();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.put(YELLOW_CARD.getStatisticName(), 1);
    expectedStats.put(YELLOW_CARDS_REGULAR_TIMES, 1);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenAYellowCardEventIsReceived_andItHappenedDuringExtraTimes_thenExpectedStatsAreReturned() {
    PlayerDataDTO playerData = buildHomePlayerFeedData(emptyMap());
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(emptyMap()).build();

    PlayerMatchEventDTO yellowCardEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(YELLOW_CARD)
      .periodId(MatchPeriod.EXTRA_FIRST_HALF.getPeriodId())
      .timeMin(105)
      .build();
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of("eventYellowCard", Set.of(yellowCardEvent)))
      .build();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.put(YELLOW_CARD.getStatisticName(), 1);
    expectedStats.put(YELLOW_CARDS_REGULAR_TIMES, 0);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenATackleWonEventIsReceived_andItHappenedDuringRegularTimes_thenExpectedStatsAreReturned() {
    PlayerDataDTO playerData = buildHomePlayerFeedData(emptyMap());
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(emptyMap()).build();

    PlayerMatchEventDTO tacklesWonEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(TACKLE_WON)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .timeMin(35)
      .build();
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of("eventTacklesWon", Set.of(tacklesWonEvent)))
      .build();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.put(TACKLE_WON.getStatisticName(), 1);
    expectedStats.put(TACKLES_WON_REGULAR_TIMES, 1);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenATacklesWonEventIsReceived_andItHappenedDuringExtraTimes_thenExpectedStatsAreReturned() {
    PlayerDataDTO playerData = buildHomePlayerFeedData(emptyMap());
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(emptyMap()).build();

    PlayerMatchEventDTO tacklesWonEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(TACKLE_WON)
      .periodId(MatchPeriod.EXTRA_FIRST_HALF.getPeriodId())
      .timeMin(105)
      .build();
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of("eventTacklesWon", Set.of(tacklesWonEvent)))
      .build();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.put(TACKLE_WON.getStatisticName(), 1);
    expectedStats.put(TACKLES_WON_REGULAR_TIMES, 0);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenCachedDataIsNotEmpty_andNewStatsAreTheSameAsBefore_thenEnrichLiveStatsReturnsFalse() {
    Map<String, Number> stats = Map.of(PASS.getStatisticName(), 5);
    PlayerDataDTO playerData = buildHomePlayerFeedData(stats);
    Map<String, Number> cachedStats = buildDefaultStats();
    cachedStats.putAll(stats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(cachedStats).build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    assertEquals(cachedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataContainsMinsPlayed_andPlayerHasNotJustComeIn_thenMinsPlayedFromPlayerDataIsTaken() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0, MINS_PLAYED, 5);
    PlayerDataDTO playerData = buildHomePlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataDoesNotContainMinsPlayed_andCachedDataContainsMinsPlayed_andPlayerHasNotJustComeIn_thenMinsPlayedFromCacheIsTaken() {
    Map<String, Number> stats = Map.of(MINS_PLAYED, 8);
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0);
    PlayerDataDTO playerData = buildHomePlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    expectedStats.put(MINS_PLAYED, 8);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataAndCachedDataDoNotContainMinsPlayed_andPlayerHasNotComeIn_thenItFallsBackTo1() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0);
    PlayerDataDTO playerData = buildHomePlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataDoesNotContainMinsPlayed_andPlayerHasJustComeIn_thenItIsSetTo1() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0);
    List<EntityEventDTO> events = List.of(buildSubOnEvent());

    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    expectedStats.put(SUB_ON.getStatisticName(), 1);

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, events, cachedMatchData);

    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataDoesNotContainContainTeamScores_andCachedDataDoes_thenItIsSetToFeedValue() {
    Map<String, Number> stats = Map.of(MINS_PLAYED, 1);
    Map<String, Number> newStats = Map.of();
    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    OngoingMatchData cachedMatchData = buildCachedData(2, 1);

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(stats);
    expectedStats.put(HOME_SCORE, 2);
    expectedStats.put(AWAY_SCORE, 1);
    expectedStats.put(TEAM_SCORE, 2);
    expectedStats.put(OPPONENT_TEAM_SCORE, 1);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerDataAndFeedDoNotContainContainTeamScores_andCacheDoes_thenItIsSetToCacheValue() {
    Map<String, Number> stats = Map.of(MINS_PLAYED, 1);
    Map<String, Number> newStats = Map.of();
    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    OngoingMatchData cachedMatchData = buildCachedData(2, 1);

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(stats);
    expectedStats.put(HOME_SCORE, 2);
    expectedStats.put(AWAY_SCORE, 1);
    expectedStats.put(TEAM_SCORE, 2);
    expectedStats.put(OPPONENT_TEAM_SCORE, 1);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerMatchEventsContainEventsThatAreAlreadyCountedInTheStats_thenTheStatsAreNotUpdated() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0, MINS_PLAYED, 5,
      GOAL.getStatisticName(), 1);

    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    String teamId = playerData.getPlayer().getTeam().getId().toString();
    PlayerMatchEventDTO goal = new PlayerMatchEventDTO("1", "2", playerData.getPlayerId(), teamId, GOAL, 1, 23, false);
    OngoingMatchData cachedMatchData = buildCachedData(Map.of(goal.getEventId(), Set.of(goal)));

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    expectedStats.put(GOAL.getStatisticName(), 1);
    expectedStats.put(GOALS_MADE_REGULAR_TIMES, 1);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerMatchEventsContainEventsThatAreNotCountedInTheStats_thenTheStatsAreUpdated() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0, MINS_PLAYED, 5);

    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    PlayerMatchEventDTO goalEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(GOAL)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .timeMin(35)
      .build();
    PlayerMatchEventDTO foulEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId("foulEventId")
      .entityId(playerData.getPlayerId())
      .event(FOUL)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .timeMin(34)
      .build();
    OngoingMatchData cachedMatchData = buildCachedData(
      Map.of(goalEvent.getEventId(), Set.of(goalEvent), foulEvent.getEventId(), Set.of(foulEvent)));

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    expectedStats.put(GOAL.getStatisticName(), 1);
    expectedStats.put(GOALS_MADE_REGULAR_TIMES, 1);
    expectedStats.put(FOUL.getStatisticName(), 1);
    expectedStats.put(FOULS_COMMITTED_REGULAR_TIMES, 1);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenPlayerMatchEventsContainEventsThatAreNotCountedInTheStats_andOneEventHasToBeIgnored_thenTheStatForThatEventIsNotUpdated() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0, MINS_PLAYED, 5);

    PlayerDataDTO playerData = buildPlayerFeedData(newStats);
    PlayerMatchData playerCachedData = playerMatchDataCanned().stats(stats).build();

    PlayerMatchEventDTO goalEvent = playerMatchEventDTOCanned()
      .entityId(playerData.getPlayerId())
      .event(GOAL)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .timeMin(35)
      .ignore(true)
      .build();
    OngoingMatchData cachedMatchData = buildCachedData(Map.of(goalEvent.getEventId(), Set.of(goalEvent)));

    Map<String, Number> updatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(), playerData,
      playerCachedData, List.of(), cachedMatchData);

    Map<String, Number> expectedStats = buildDefaultStats();
    expectedStats.putAll(newStats);
    assertEquals(expectedStats, updatedStats);
  }

  @Test
  public void whenFinalStatsAreCalculated_andTeamsScoreIsNotInTheStats_thenItIsRetrievedFromTheFeed() {
    Map<String, Number> newStats = new HashMap<>(Map.of(MINS_PLAYED, 5));

    Fixture fixture = fixtureCanned().build();
    String homeTeamId = fixture.getHomeTeam().getIdAsString();
    Player homePlayer = PlayerCanned.playerCanned().team(fixture.getHomeTeam()).id(DEFAULT_ID).build();
    Player awayPlayer = PlayerCanned.playerCanned().team(fixture.getAwayTeam()).id(PLAYER_2_ID).build();
    PlayerDataDTO homeTeamPlayerData = buildPlayerFeedData(homePlayer, newStats);
    var firstGoal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(DEFAULT_ID.toString())
      .eventType(GOAL)
      .eventId("goal1")
      .teamId(homeTeamId)
      .build();
    var secondGoal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(DEFAULT_ID.toString())
      .eventType(GOAL)
      .eventId("goal2")
      .teamId(homeTeamId)
      .build();
    MatchDataFeed feed = buildFeedWithGoalEvents(List.of(homeTeamPlayerData), List.of(firstGoal, secondGoal))
      .fixture(fixture)
      .fixtureStatus(PLAYED)
      .build();

    Map<String, Number> updatedStatsHomeTeam = playerStatsEnricher.enrichFinalPlayerStats(feed, homeTeamPlayerData);

    Map<String, Number> expectedHomePlayerStats = new HashMap<>(newStats);
    expectedHomePlayerStats.put(TEAM_SCORE, 2);
    expectedHomePlayerStats.put(OPPONENT_TEAM_SCORE, 0);
    expectedHomePlayerStats.put(HOME_SCORE, 2);
    expectedHomePlayerStats.put(AWAY_SCORE, 0);
    expectedHomePlayerStats.put(GOAL.getStatisticName(), 2);
    expectedHomePlayerStats.put(GOALS_MADE_REGULAR_TIMES, 2);
    expectedHomePlayerStats.put(ASSIST_GOAL.getStatisticName(), 0);
    expectedHomePlayerStats.put(ASSISTS_GOAL_REGULAR_TIMES, 0);
    expectedHomePlayerStats.put(YELLOW_CARD.getStatisticName(), 0);
    expectedHomePlayerStats.put(YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedHomePlayerStats.put(FOUL.getStatisticName(), 0);
    expectedHomePlayerStats.put(FOULS_COMMITTED_REGULAR_TIMES, 0);
    expectedHomePlayerStats.put(SHOT.getStatisticName(), 0);
    expectedHomePlayerStats.put(SHOTS_REGULAR_TIMES, 0);
    expectedHomePlayerStats.put(SHOT_ON_GOAL.getStatisticName(), 0);
    expectedHomePlayerStats.put(SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedHomePlayerStats.put(OFFSIDE.getStatisticName(), 0);
    expectedHomePlayerStats.put(OFFSIDES_REGULAR_TIMES, 0);
    assertEquals(expectedHomePlayerStats, updatedStatsHomeTeam);

    Map<String, Number> awayPlayerStats = new HashMap<>(Map.of(MINS_PLAYED, 5));
    PlayerDataDTO awayTeamPlayerData = buildPlayerFeedData(awayPlayer, awayPlayerStats);

    Map<String, Number> updatedStatsAwayTeam = playerStatsEnricher.enrichFinalPlayerStats(feed, awayTeamPlayerData);

    Map<String, Number> expectedAwayPlayerStats = new HashMap<>(awayPlayerStats);
    expectedAwayPlayerStats.put(TEAM_SCORE, 0);
    expectedAwayPlayerStats.put(OPPONENT_TEAM_SCORE, 2);
    expectedAwayPlayerStats.put(HOME_SCORE, 2);
    expectedAwayPlayerStats.put(AWAY_SCORE, 0);
    expectedAwayPlayerStats.put(GOAL.getStatisticName(), 0);
    expectedAwayPlayerStats.put(GOALS_MADE_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(ASSIST_GOAL.getStatisticName(), 0);
    expectedAwayPlayerStats.put(ASSISTS_GOAL_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(YELLOW_CARD.getStatisticName(), 0);
    expectedAwayPlayerStats.put(YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(FOUL.getStatisticName(), 0);
    expectedAwayPlayerStats.put(FOULS_COMMITTED_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(SHOT.getStatisticName(), 0);
    expectedAwayPlayerStats.put(SHOTS_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(SHOT_ON_GOAL.getStatisticName(), 0);
    expectedAwayPlayerStats.put(SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedAwayPlayerStats.put(OFFSIDE.getStatisticName(), 0);
    expectedAwayPlayerStats.put(OFFSIDES_REGULAR_TIMES, 0);
    assertEquals(expectedAwayPlayerStats, updatedStatsAwayTeam);
  }

  @Test
  public void whenSportmonksFeed_thenSomeEventsAreNotAvailable_soTheirRegularTimeStatsAreDuplicatedFromExtraTimeStats_andArePresentInFinalPlayerStats() {
    Map<String, Number> newStats = new HashMap<>(
      Map.of(FOUL.getStatisticName(), 4, SHOT.getStatisticName(), 3, SHOT_ON_GOAL.getStatisticName(), 2));

    Fixture fixture = fixtureCanned().build();
    Player homePlayer = PlayerCanned.playerCanned().team(fixture.getHomeTeam()).id(DEFAULT_ID).build();
    Player awayPlayer = PlayerCanned.playerCanned().team(fixture.getAwayTeam()).id(PLAYER_2_ID).build();
    PlayerDataDTO homeTeamPlayerData = buildPlayerFeedData(homePlayer, newStats);

    MatchDataFeed feed = buildFeedWithGoalEvents(List.of(homeTeamPlayerData), List.of())
      .fixture(fixture)
      .fixtureStatus(PLAYED)
      .provider(SPORTMONKS)
      .build();

    Map<String, Number> updatedStatsHomeTeam = playerStatsEnricher.enrichFinalPlayerStats(feed, homeTeamPlayerData);
    assertThat(updatedStatsHomeTeam.get(FOULS_COMMITTED_REGULAR_TIMES)).isEqualTo(4);
    assertThat(updatedStatsHomeTeam.get(SHOTS_REGULAR_TIMES)).isEqualTo(3);
    assertThat(updatedStatsHomeTeam.get(SHOTS_ON_GOAL_REGULAR_TIMES)).isEqualTo(2);

    Map<String, Number> awayPlayerStats = new HashMap<>();
    PlayerDataDTO awayTeamPlayerData = buildPlayerFeedData(awayPlayer, awayPlayerStats);

    Map<String, Number> updatedStatsAwayTeam = playerStatsEnricher.enrichFinalPlayerStats(feed, awayTeamPlayerData);
    assertThat(updatedStatsAwayTeam.get(FOULS_COMMITTED_REGULAR_TIMES)).isEqualTo(0);
    assertThat(updatedStatsAwayTeam.get(SHOTS_REGULAR_TIMES)).isEqualTo(0);
    assertThat(updatedStatsAwayTeam.get(SHOTS_ON_GOAL_REGULAR_TIMES)).isEqualTo(0);
  }

  @Test
  public void whenLiveDataIsEnriched_andPlayerMatchEventContainARedCard_thenTheSentOffStatsAreUpdated() {
    Map<String, Number> stats = Map.of();
    Map<String, Number> newStats = Map.of(TEAM_SCORE, 0, OPPONENT_TEAM_SCORE, 0, MINS_PLAYED, 5);

    PlayerDataDTO homeTeamPlayerData = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).team(DEFAULT_HOME_TEAM).build())
      .isPlaying(true)
      .hasPlayed(true)
      .stats(newStats)
      .build();
    PlayerMatchData homeTeamPlayerCachedData = playerMatchDataCanned().stats(stats).build();

    PlayerMatchEventDTO sentOffEvent = playerMatchEventDTOCanned()
      .eventId("eventRedCard")
      .entityId(PLAYER_1_ID_STR)
      .teamId(DEFAULT_HOME_TEAM.getIdAsString())
      .event(RED_CARD)
      .timeMin(35)
      .periodId(MatchPeriod.FIRST_HALF.getPeriodId())
      .build();
    OngoingMatchData cachedMatchData = buildCachedData(Map.of(sentOffEvent.getEventId(), Set.of(sentOffEvent)));

    Map<String, Number> playerHomeTeamUpdatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(),
      homeTeamPlayerData, homeTeamPlayerCachedData, List.of(), cachedMatchData);

    Map<String, Number> playerHomeTeamExpectedStats = buildDefaultStats();
    playerHomeTeamExpectedStats.putAll(newStats);
    playerHomeTeamExpectedStats.put(HOME_TEAM_SENT_OFF_STAT, 1);
    playerHomeTeamExpectedStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);
    playerHomeTeamExpectedStats.put(RED_CARD.getStatisticName(), 1);
    playerHomeTeamExpectedStats.put(RED_CARDS_REGULAR_TIMES, 1);
    assertEquals(playerHomeTeamExpectedStats, playerHomeTeamUpdatedStats);

    PlayerDataDTO awayTeamPlayerData = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_2_ID).team(DEFAULT_AWAY_TEAM).build())
      .isPlaying(true)
      .hasPlayed(true)
      .stats(newStats)
      .build();
    PlayerMatchData awayTeamPlayerCachedData = playerMatchDataCanned().stats(stats).build();

    Map<String, Number> playerAwayTeamUpdatedStats = playerStatsEnricher.enrichLiveStats(fixtureCanned().build(),
      awayTeamPlayerData, awayTeamPlayerCachedData, List.of(), cachedMatchData);

    Map<String, Number> playerAwayTeamExpectedStats = buildDefaultStats();
    playerAwayTeamExpectedStats.putAll(newStats);
    playerAwayTeamExpectedStats.put(HOME_TEAM_SENT_OFF_STAT, 1);
    playerAwayTeamExpectedStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);
    assertEquals(playerAwayTeamExpectedStats, playerAwayTeamUpdatedStats);
  }

  private static Map<String, Number> buildDefaultStats() {
    Map<String, Number> expectedStats = new HashMap<>();
    expectedStats.put(HOME_SCORE, 0);
    expectedStats.put(AWAY_SCORE, 0);
    expectedStats.put(GOAL.getStatisticName(), 0);
    expectedStats.put(GOALS_MADE_REGULAR_TIMES, 0);
    expectedStats.put(ASSIST_GOAL.getStatisticName(), 0);
    expectedStats.put(ASSISTS_GOAL_REGULAR_TIMES, 0);
    expectedStats.put(YELLOW_CARD.getStatisticName(), 0);
    expectedStats.put(YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(RED_CARD.getStatisticName(), 0);
    expectedStats.put(RED_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(HOME_TEAM_SENT_OFF_STAT, 0);
    expectedStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);
    expectedStats.put(MINS_PLAYED, 0);
    expectedStats.put(TEAM_SCORE, 0);
    expectedStats.put(OPPONENT_TEAM_SCORE, 0);
    expectedStats.put(SHOT.getStatisticName(), 0);
    expectedStats.put(SHOTS_REGULAR_TIMES, 0);
    expectedStats.put(SHOT_ON_GOAL.getStatisticName(), 0);
    expectedStats.put(SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedStats.put(CORNER.getStatisticName(), 0);
    expectedStats.put(CORNERS_REGULAR_TIMES, 0);
    expectedStats.put(GOALKICK.getStatisticName(), 0);
    expectedStats.put(GOAL_KICKS_REGULAR_TIMES, 0);
    expectedStats.put(FOUL.getStatisticName(), 0);
    expectedStats.put(FOULS_COMMITTED_REGULAR_TIMES, 0);
    expectedStats.put(OFFSIDE.getStatisticName(), 0);
    expectedStats.put(OFFSIDES_REGULAR_TIMES, 0);
    expectedStats.put(TACKLE_WON.getStatisticName(), 0);
    expectedStats.put(TACKLES_WON_REGULAR_TIMES, 0);
    return expectedStats;
  }

  private static EntityEventDTO buildSubOnEvent() {
    String playerId = DEFAULT_ID.toString();
    return entityEventDTOCanned()
      .entityId(playerId)
      .teamId(TeamCanned.TEAM1_ID.toString())
      .eventType(SoccerMatchEvent.SUB_ON)
      .eventId(SUB_ON.getStatisticName() + playerId)
      .build();
  }
}
