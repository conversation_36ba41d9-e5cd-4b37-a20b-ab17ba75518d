package com.wsf.dataingestor.unit.services;

import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.crons.FixturesWatcherUpdater;
import com.wsf.dataingestor.opta.services.OptaFixturesRetriever;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureChangeProcessor;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionConfigCanned;
import com.wsf.dataingestor.shared.canned.FixtureChangeDtoCanned;
import com.wsf.dataingestor.web.model.FixtureChangeDto;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.FIXTURE;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.common.Fixture.FixtureStatus.PLAYED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.assertArg;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class FixtureChangeProcessorTest extends TestWithMocks {

  private FixtureChangeProcessor fixtureChangeProcessor;

  @Mock
  private FixtureService fixtureService;

  @Mock
  private FixturesWatcherUpdater fixturesWatcherUpdater;

  @Mock
  private FeedsConfigService feedsConfigService;

  @Mock
  private CompetitionConfigRepository competitionConfigRepository;

  @Mock
  OptaFixturesRetriever optaFixturesRetriever;

  @Before
  public void setUp() {
    fixtureChangeProcessor = new FixtureChangeProcessor(fixtureService, fixturesWatcherUpdater, feedsConfigService);
  }

  @Test
  public void whenProcessingFixtureChange_andOperationTypeIsNew_thenDoNothing() {
    // Arrange
    Fixture fixture = fixtureCanned().build();
    FixtureChangeDto fixtureChangeDto = FixtureChangeDtoCanned
      .fixtureChangeDtoCanned()
      .operationType(FixtureChangeDto.OperationType.NEW)
      .build();

    // Act
    fixtureChangeProcessor.process(fixture.getIdAsString(), fixtureChangeDto);

    // Assert
    verifyNoInteractions(fixtureService);
  }

  @Test
  public void whenProcessingFixtureChange_andOperationTypeIsUpdate_andFixtureHasStatusPlayed_thenDoNotReschedule() {
    // Arrange
    Fixture fixture = fixtureCanned().status(PLAYED).build();
    FixtureChangeDto fixtureChangeDto = FixtureChangeDtoCanned
      .fixtureChangeDtoCanned()
      .operationType(FixtureChangeDto.OperationType.UPDATE)
      .build();
    CompetitionConfig competitionConfig = CompetitionConfigCanned.competitionConfigCanned().build();

    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    when(feedsConfigService.getFixturesRetriever(anyString())).thenReturn(optaFixturesRetriever);
    when(competitionConfigRepository.findByCompetitionId(anyString())).thenReturn(competitionConfig);

    // Act
    fixtureChangeProcessor.process(fixture.getIdAsString(), fixtureChangeDto);

    // Assert
    verifyNoInteractions(fixturesWatcherUpdater);
  }

  @Test
  public void whenProcessingFixtureChange_andOperationTypeIsUpdate_andFixtureHasStatusLive_thenDoReschedule() {
    // Arrange
    Fixture fixture = fixtureCanned().status(LIVE).processStatus(null).build();
    FixtureChangeDto fixtureChangeDto = FixtureChangeDtoCanned
      .fixtureChangeDtoCanned()
      .operationType(FixtureChangeDto.OperationType.UPDATE)
      .originalFixtureStatus(Fixture.FixtureStatus.FIXTURE)
      .build();
    CompetitionConfig competitionConfig = CompetitionConfigCanned.competitionConfigCanned().build();

    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    when(feedsConfigService.getFixturesRetriever(anyString())).thenReturn(optaFixturesRetriever);
    when(optaFixturesRetriever.getExternalFixtureProvider()).thenReturn(ExternalProvider.OPTA);
    when(competitionConfigRepository.findByCompetitionId(anyString())).thenReturn(competitionConfig);

    // Act
    fixtureChangeProcessor.process(fixture.getIdAsString(), fixtureChangeDto);

    // Assert
    verify(fixturesWatcherUpdater, times(1)).scheduleMatchProcess(assertArg(f -> assertThat(f).isEqualTo(fixture)),
      assertArg(externalProvider -> assertThat(externalProvider).isEqualTo(ExternalProvider.OPTA)));
  }

  @Test
  public void whenProcessingFixtureChange_andOperationTypeIsUpdate_andFixtureHasBeenReplaced_thenTheFixtureIsSetToInactive() {
    // Arrange
    Fixture fixture = fixtureCanned().status(FIXTURE).wasReplaced(true).active(true).build();
    FixtureChangeDto fixtureChangeDto = FixtureChangeDtoCanned
      .fixtureChangeDtoCanned()
      .operationType(FixtureChangeDto.OperationType.UPDATE)
      .build();
    CompetitionConfig competitionConfig = CompetitionConfigCanned.competitionConfigCanned().build();

    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    when(feedsConfigService.getFixturesRetriever(anyString())).thenReturn(optaFixturesRetriever);
    when(competitionConfigRepository.findByCompetitionId(anyString())).thenReturn(competitionConfig);

    // Act
    fixtureChangeProcessor.process(fixture.getIdAsString(), fixtureChangeDto);

    // Assert
    verify(fixtureService, times(1)).updateFixture(fixture, Map.of("active", false));
  }
}
