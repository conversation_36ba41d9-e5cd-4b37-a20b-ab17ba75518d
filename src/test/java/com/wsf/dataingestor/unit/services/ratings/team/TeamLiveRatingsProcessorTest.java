package com.wsf.dataingestor.unit.services.ratings.team;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.EntityMatchData.BetStopInfo;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.publishers.RatingsPublisher;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.ratings.team.TeamLiveRatingsProcessor;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.shared.TestUtils.addDefaultTeamStats;
import static com.wsf.dataingestor.shared.TestUtils.buildCachedData;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamDataDTOCanned.teamDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamMatchDataCanned.teamMatchDataCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class TeamLiveRatingsProcessorTest extends TestWithMocks {

  private static final Map<String, Number> TEAM_1_STATS = newHashMap();
  private static final Map<String, Number> TEAM_2_STATS = newHashMap();
  private static final Fixture FIXTURE = fixtureCanned().build();

  @Mock
  TeamMatchDataCacheService teamMatchDataCacheMock;
  @Mock
  TeamService teamServiceMock;
  @Mock
  TeamStatsEnricher teamStatsEnricherMock;
  @Mock
  IndexCalculator soccerIndexCalculatorMock;
  @Mock
  RatingsPublisher ratingsPublisherMock;
  TeamLiveRatingsProcessor teamLiveRatingsProcessor;

  static {
    TEAM_1_STATS.put(TEAM_CORNERS, 2);
  }

  static {
    TEAM_2_STATS.put(TEAM_CORNERS, 1);
  }

  @Before
  public void setUp() throws Exception {
    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM1_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_1_STATS).build());
    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM2_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_2_STATS).build());

    Utils utils = new Utils(soccerIndexCalculatorMock, new MetricsManager(new SimpleMeterRegistry()));
    teamLiveRatingsProcessor = new TeamLiveRatingsProcessor(teamMatchDataCacheMock, teamServiceMock,
      teamStatsEnricherMock, ratingsPublisherMock, utils, new MetricsManager(new SimpleMeterRegistry()));
  }

  @Test
  public void whenAFeedIsReceived_andOnlyUpdateCacheIsTrue_thenOnlyCacheIsUpdated() {
    // Arrange
    when(teamStatsEnricherMock.enrichLiveStats(any(), any(), any(), any())).thenReturn(newHashMap());

    Team firstTeam = TeamCanned.defaultTeam(new ObjectId(TEAM1_ID.toString()));
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).stats(newHashMap()).build();

    Team secondTeam = TeamCanned.defaultTeam(new ObjectId(TEAM2_ID.toString()));
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).stats(newHashMap()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .fixtureStatus(LIVE)
      .build();

    OngoingMatchData cachedMatchData = buildCachedData();

    // Act
    teamLiveRatingsProcessor.processFeed(matchDataFeed, cachedMatchData, true);

    // Assert
    verify(teamStatsEnricherMock, times(2)).enrichLiveStats(any(), any(), any(), any());
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM1_ID.toString()), any());
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM2_ID.toString()), any());

    verifyNoMoreInteractions(ratingsPublisherMock);
  }

  @Test
  public void whenAFeedIsReceived_andOnlyUpdateCacheIsFalse_thenRatingsAreStoredAndSentToKafka() {
    // Arrange
    when(teamStatsEnricherMock.enrichLiveStats(any(), any(), any(), any())).thenReturn(newHashMap());

    Team firstTeam = TeamCanned.defaultTeam(new ObjectId(TEAM1_ID.toString()));
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).stats(newHashMap()).build();

    Team secondTeam = TeamCanned.defaultTeam(new ObjectId(TEAM2_ID.toString()));
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).stats(newHashMap()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .fixtureStatus(LIVE)
      .build();

    OngoingMatchData cachedMatchData = buildCachedData();

    // Act
    teamLiveRatingsProcessor.processFeed(matchDataFeed, cachedMatchData, false);

    // Assert
    TeamRating expectedFirstTeamRating = buildTeamRating(newHashMap(), firstTeam, matchDataFeed, cachedMatchData,
      firstTeamData.getEventId());
    TeamRating expectedSecondTeamRating = buildTeamRating(newHashMap(), secondTeam, matchDataFeed, cachedMatchData,
      secondTeamData.getEventId());

    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM1_ID.toString()), any());
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM2_ID.toString()), any());

    verify(ratingsPublisherMock).publishAndStore(expectedFirstTeamRating, false, EMPTY_METADATA);
    verify(ratingsPublisherMock).publishAndStore(expectedSecondTeamRating, false, EMPTY_METADATA);
  }

  @Test
  public void whenAFeedIsReceived_andOnlyUpdateCacheIsFalse_andEntityHasABetstop_thenRatingsAreStoredAndSentToKafka() {
    // Arrange
    var betstopsInfo = new TreeSet<BetStopInfo>();
    betstopsInfo.add(BetStopInfo.of(GOAL.getStatisticName(), "eventId", Instant.now()));

    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM1_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_1_STATS).betStopsInfo(betstopsInfo).build());
    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM2_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_2_STATS).betStopsInfo(betstopsInfo).build());

    when(teamStatsEnricherMock.enrichLiveStats(any(), any(), any(), any())).thenReturn(newHashMap());

    Team firstTeam = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).stats(newHashMap()).build();

    Team secondTeam = TeamCanned.teamCanned().id(new ObjectId(TEAM2_ID.toString())).build();
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).stats(newHashMap()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .fixtureStatus(LIVE)
      .build();

    OngoingMatchData cachedMatchData = buildCachedData();

    // Act
    teamLiveRatingsProcessor.processFeed(matchDataFeed, cachedMatchData, false);

    // Assert
    TeamRating expectedFirstTeamRating = buildTeamRating(newHashMap(), firstTeam, matchDataFeed, cachedMatchData,
      firstTeamData.getEventId());
    TeamRating expectedSecondTeamRating = buildTeamRating(newHashMap(), secondTeam, matchDataFeed, cachedMatchData,
      secondTeamData.getEventId());

    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM1_ID.toString()), any());
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM2_ID.toString()), any());

    verify(ratingsPublisherMock).publishAndStore(expectedFirstTeamRating, false, EMPTY_METADATA);
    verify(ratingsPublisherMock).publishAndStore(expectedSecondTeamRating, false, EMPTY_METADATA);
  }

  @Test
  public void whenASnapshotFeedIsReceived_andOnlyUpdateCacheIsFalse_andStatsHaveNotChanged_thenRatingsAreStoredAndSentToKafka() {
    // Arrange
    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM1_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_1_STATS).build());
    when(teamMatchDataCacheMock.get(anyString(), eq(TEAM2_ID.toString()))).thenReturn(
      teamMatchDataCanned().stats(TEAM_2_STATS).build());

    Team firstTeam = TeamCanned.defaultTeam(new ObjectId(TEAM1_ID.toString()));
    TeamDataDTO firstTeamData = teamDataDTOCanned().team(firstTeam).stats(TEAM_1_STATS).build();

    Team secondTeam = TeamCanned.defaultTeam(new ObjectId(TEAM2_ID.toString()));
    TeamDataDTO secondTeamData = teamDataDTOCanned().team(secondTeam).stats(TEAM_2_STATS).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .teamsData(List.of(firstTeamData, secondTeamData))
      .fixtureStatus(LIVE)
      .isSnapshot(true)
      .build();

    OngoingMatchData cachedMatchData = buildCachedData();

    Map<String, Number> firstTeamStats = new HashMap<>(TEAM_1_STATS);
    Map<String, Number> secondTeamStats = new HashMap<>(TEAM_2_STATS);

    addDefaultTeamStats(firstTeamStats);
    addDefaultTeamStats(secondTeamStats);

    when(teamStatsEnricherMock.enrichLiveStats(any(), eq(firstTeamData), any(), any())).thenReturn(firstTeamStats);
    when(teamStatsEnricherMock.enrichLiveStats(any(), eq(secondTeamData), any(), any())).thenReturn(secondTeamStats);

    // Act
    teamLiveRatingsProcessor.processFeed(matchDataFeed, cachedMatchData, false);

    // Assert
    TeamRating expectedFirstTeamRating = buildTeamRating(firstTeamStats, firstTeam, matchDataFeed, cachedMatchData,
      firstTeamData.getEventId());
    TeamRating expectedSecondTeamRating = buildTeamRating(secondTeamStats, secondTeam, matchDataFeed, cachedMatchData,
      secondTeamData.getEventId());

    ArgumentCaptor<TeamMatchData> capt = ArgumentCaptor.forClass(TeamMatchData.class);
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM1_ID.toString()), capt.capture());
    TeamMatchData team1CachedData = capt.getValue();
    assertThat(team1CachedData.getStats(), is(expectedFirstTeamRating.getStats()));

    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(TEAM2_ID.toString()), capt.capture());
    TeamMatchData team2CachedData = capt.getValue();
    assertThat(team2CachedData.getStats(), is(expectedSecondTeamRating.getStats()));

    verify(ratingsPublisherMock).publishAndStore(expectedFirstTeamRating, false, EMPTY_METADATA);
    verify(ratingsPublisherMock).publishAndStore(expectedSecondTeamRating, false, EMPTY_METADATA);
  }

  private static TeamRating buildTeamRating(Map<String, Number> secondTeamStats, Team secondTeam,
                                            MatchDataFeed matchDataFeed, OngoingMatchData cachedData, String eventId) {
    return TeamRating
      .builder()
      .fixture(FIXTURE)
      .team(secondTeam)
      .stats(secondTeamStats)
      .isFinal(false)
      .feedId(matchDataFeed.getFeedId())
      .eventId(eventId)
      .periodId(cachedData.getMatchPeriod().getPeriodId())
      .fixtureTimeMin(cachedData.getMatchTime())
      .timestamp(matchDataFeed.getReceivedTs())
      .build();
  }
}
