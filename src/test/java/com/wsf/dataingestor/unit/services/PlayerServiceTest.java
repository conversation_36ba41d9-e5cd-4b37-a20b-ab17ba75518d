package com.wsf.dataingestor.unit.services;

import lombok.RequiredArgsConstructor;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.repository.common.MasterPlayerRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.DEFAULT_OPTA_PLAYER_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultMasterPlayer;
import static com.wsf.domain.common.ExternalProvider.OPTA;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class PlayerServiceTest extends TestWithMocks {

  @Mock
  private RepositoryFactory.PlayerRepositoryFactory playerRepositoryFactoryMock;
  @Mock
  private MasterPlayerRepository masterPlayerRepositoryMock;
  @Mock
  private UnmappedEntityClient unmappedEntityClientMock;
  @Mock
  private DBUtils dbUtils;

  private PlayerService playerService;

  @Before
  public void setup() {
    playerService = new PlayerService(playerRepositoryFactoryMock, masterPlayerRepositoryMock, unmappedEntityClientMock,
      dbUtils);
  }

  @Test(expected = DateTimeParseException.class)
  public void whenAMasterPlayerIsLookedUpByExternalId_andTheBirthDateIsNotValid_thenAnExceptionIsThrown() {
    MasterPlayer respMasterPlayer = defaultMasterPlayer();
    MasterPlayerFinder masterPlayerFinder = new MasterPlayerFinder(Optional.of(respMasterPlayer));
    MasterPlayerBuilder masterPlayerBuilder = new MasterPlayerBuilder(respMasterPlayer);

    LocalDate nullBirthDate = null;
    MasterPlayer masterPlayer = callFindOrCreateUnmapped(masterPlayerFinder, masterPlayerBuilder, nullBirthDate);
    assertNull(masterPlayer);
    verifyNoInteractions(unmappedEntityClientMock);
    verify(masterPlayerRepositoryMock, never()).save(any());
  }

  @Test
  public void whenAMasterPlayerIsLookedUpByExternalId_andTheMasterPlayerIsFound_thenTheMasterPlayerIsReturned() {
    MasterPlayer respMasterPlayer = defaultMasterPlayer();
    MasterPlayerFinder masterPlayerFinder = new MasterPlayerFinder(Optional.of(respMasterPlayer));
    MasterPlayerBuilder masterPlayerBuilder = new MasterPlayerBuilder(respMasterPlayer);

    MasterPlayer masterPlayer = callFindOrCreateUnmapped(masterPlayerFinder, masterPlayerBuilder,
      LocalDate.of(1986, 12, 5));
    assertNotNull(masterPlayer);
    verifyNoInteractions(unmappedEntityClientMock);
    verify(masterPlayerRepositoryMock, never()).save(any());
  }

  @Test
  public void whenAMasterPlayerIsLookedUpByExternalId_andTheMasterPlayerIsNotFound_andAnUnmappedPlayerIsCreated_thenNullIsReturned() {
    MasterPlayer respMasterPlayer = defaultMasterPlayer();
    MasterPlayerFinder masterPlayerFinder = new MasterPlayerFinder(Optional.empty());
    MasterPlayerBuilder masterPlayerBuilder = new MasterPlayerBuilder(respMasterPlayer);

    when(unmappedEntityClientMock.createUnmappedPlayer(anyString(), anyString(), anyString(), any(LocalDate.class),
      anyString(), any(ExternalProvider.class))).thenReturn(true);

    MasterPlayer masterPlayer = callFindOrCreateUnmapped(masterPlayerFinder, masterPlayerBuilder,
      LocalDate.of(1986, 12, 5));
    assertNull(masterPlayer);
    verify(masterPlayerRepositoryMock, never()).save(any());
  }

  @Test
  public void whenAMasterPlayerIsLookedUpByExternalId_andTheMasterPlayerIsNotFound_andAnUnmappedPlayerIsNotCreated_thenTheMasterPlayerIsSaved() {
    MasterPlayer respMasterPlayer = defaultMasterPlayer();
    MasterPlayerFinder masterPlayerFinder = new MasterPlayerFinder(Optional.empty());
    MasterPlayerBuilder masterPlayerBuilder = new MasterPlayerBuilder(respMasterPlayer);

    when(unmappedEntityClientMock.createUnmappedPlayer(anyString(), anyString(), anyString(), any(LocalDate.class),
      anyString(), any(ExternalProvider.class))).thenReturn(false);
    when(masterPlayerRepositoryMock.save(any())).thenReturn(respMasterPlayer);

    MasterPlayer masterPlayer = callFindOrCreateUnmapped(masterPlayerFinder, masterPlayerBuilder,
      LocalDate.of(1986, 12, 5));
    assertNotNull(masterPlayer);
    verify(masterPlayerRepositoryMock).save(respMasterPlayer);
  }

  @Test
  public void whenAMasterPlayerIsLookedUpByExternalId_andTheMasterPlayerIsNotFound_andAnUnmappedPlayerIsNotCreated_andTheMasterPlayerIsFoundByPlayerMasterId_thenTheMasterPlayerIsUpdated() {
    MasterPlayer respMasterPlayer = defaultMasterPlayer();
    MasterPlayerFinder masterPlayerFinder = new MasterPlayerFinder(Optional.empty());
    MasterPlayerBuilder masterPlayerBuilder = new MasterPlayerBuilder(respMasterPlayer);

    when(unmappedEntityClientMock.createUnmappedPlayer(anyString(), anyString(), anyString(), any(LocalDate.class),
      anyString(), any(ExternalProvider.class))).thenReturn(false);
    when(masterPlayerRepositoryMock.findByPlayerMasterId(anyString())).thenReturn(Optional.of(respMasterPlayer));
    when(masterPlayerRepositoryMock.update(anyString(), anyMap())).thenReturn(respMasterPlayer);

    MasterPlayer masterPlayer = callFindOrCreateUnmapped(masterPlayerFinder, masterPlayerBuilder,
      LocalDate.of(1986, 12, 5));
    assertNotNull(masterPlayer);
    verify(masterPlayerRepositoryMock).update(respMasterPlayer.getPlayerMasterId(),
      Map.of("optaPlayerId", respMasterPlayer.getOptaPlayerId()));
  }

  private MasterPlayer callFindOrCreateUnmapped(MasterPlayerFinder masterPlayerFinder,
                                                MasterPlayerBuilder masterPlayerBuilder, LocalDate birthDate) {
    return playerService.findMasterPlayerByExternalIdOrCreateUnmapped("Lorenzo", "Pellegrini", "L. Pellegrini",
      birthDate, DEFAULT_OPTA_PLAYER_ID, "optaPlayerId", OPTA, masterPlayerFinder, masterPlayerBuilder);
  }

  @RequiredArgsConstructor
  static class MasterPlayerFinder implements Function<String, Optional<MasterPlayer>> {
    private final Optional<MasterPlayer> masterPlayer;

    @Override
    public Optional<MasterPlayer> apply(String s) {
      return masterPlayer;
    }
  }

  @RequiredArgsConstructor
  static class MasterPlayerBuilder implements Supplier<MasterPlayer> {
    private final MasterPlayer masterPlayer;

    @Override
    public MasterPlayer get() {
      return masterPlayer;
    }
  }
}
