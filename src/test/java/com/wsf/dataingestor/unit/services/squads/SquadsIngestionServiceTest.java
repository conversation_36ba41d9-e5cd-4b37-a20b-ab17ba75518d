package com.wsf.dataingestor.unit.services.squads;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.IntStream;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.squads.SquadsIngestionService;
import com.wsf.dataingestor.services.squads.SquadsRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.SquadPlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultMasterPlayer;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultMasterPlayerBuilder;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultPlayer;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.DELETED;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.NEW;
import static com.wsf.kafka.domain.PlayerTransferEvent.ActionType.TEAM_CHANGED;
import static java.util.stream.Collectors.toList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SquadsIngestionServiceTest extends TestWithMocks {

  private static final Tournament tournament = tournamentCanned().build();
  private static final Team team = TeamCanned.defaultTeam();

  private final ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();

  @Mock
  private SquadsRetriever squadsRetrieverMock;
  @Mock
  private PlayerService playerServiceMock;
  @Mock
  private KafkaService kafkaServiceMock;

  private SquadsIngestionService squadsIngestionService;

  @Before
  public void setUp() throws Exception {
    when(squadsRetrieverMock.enrichPlayer(any(), anyString())).thenAnswer(inv -> {
      var playerBuilder = inv.getArgument(0, Player.PlayerBuilder.class);
      String providerId = inv.getArgument(1, String.class);
      return playerBuilder.optaPlayerId(providerId);
    });
    when(squadsRetrieverMock.findTeamOrCreateUnmapped(anyString(), any())).thenReturn(team);
    squadsIngestionService = new SquadsIngestionService(playerServiceMock, kafkaServiceMock,
      new MetricsManager(new SimpleMeterRegistry()), objectMapper);
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMasterPlayerAlreadyExists_andTeamDoesntChange_thenPlayerUpdated_kafkaMessageNotSent() {
    // Arrange
    SquadPlayerDTO newPlayerFeed = SquadPlayerCanned
      .defaultSquadPlayer()
      .team(SquadPlayerDTO.TeamDTO.of("optaId", "ROMA", "ROM"))
      .build();

    MasterPlayer existingMasterPlayerToUpdate = defaultMasterPlayerBuilder().firstName("francesco")
      .name("totti").optaPlayerId("2").build();

    Player existingPlayerToUpdate = playerCanned().firstName("francesco")
      .name("totti").optaPlayerId("2").isActive(false).build();

    Team currentTeam = team;
    currentTeam.setId(new ObjectId());
    existingPlayerToUpdate.setTeam(currentTeam);

    Player playerSavedInTheDb = playerCanned().firstName("francesco")
      .name("totti").optaPlayerId("2").isActive(true).build();

    Map<String, Team> teamIdToTeam = Map.of(currentTeam.getOptaId(), currentTeam);

    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayerToUpdate));
    when(playerServiceMock.update(eq(existingPlayerToUpdate), any())).thenReturn(playerSavedInTheDb);

    // Act
    squadsIngestionService.checkCompetitionPlayerExistsAndUpdate(squadsRetrieverMock, tournament, newPlayerFeed,
      teamIdToTeam, "feedId", existingMasterPlayerToUpdate);

    // Assert
    verify(playerServiceMock).updateMasterPlayer(existingMasterPlayerToUpdate);
    verify(kafkaServiceMock, never()).sendPlayerTransferAndTeamChangeEvents(any(), any(), any(), any(), any(), any());
    verify(kafkaServiceMock, times(1)).sendPlayerTransferEvent(any(), any(), eq(existingPlayerToUpdate.getIdAsString()),
      eq(NEW), any());
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMasterPlayerAlreadyExists_andTeamChanges_thenOldTournamentPlayerDeleted_kafkaMessageSent() {
    // Arrange
    ObjectId playerId = new ObjectId();

    SquadPlayerDTO newPlayerFeed = SquadPlayerCanned
      .defaultSquadPlayer()
      .team(SquadPlayerDTO.TeamDTO.of("optaId", "LAZIO", "LAZ"))
      .build();

    MasterPlayer existingMasterPlayer = defaultMasterPlayerBuilder().id(playerId).firstName("francesco")
      .name("totti").optaPlayerId("2").build();

    Team oldTeam = teamCanned().id(TEAM1_ID).optaId("oldOptaId")
      .name("ROMA").build();

    Player existingPlayerWithOldTeam = playerCanned().id(playerId).firstName("francesco")
      .name("totti").optaPlayerId("2").team(oldTeam).build();

    Team newTeam = teamCanned().id(TEAM2_ID).optaId("optaId")
      .name("LAZIO").build();

    Map<String, Team> teamIdToTeam = Map.of(newTeam.getOptaId(), newTeam);

    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayerWithOldTeam));

    // Act
    squadsIngestionService.checkCompetitionPlayerExistsAndUpdate(squadsRetrieverMock, tournament, newPlayerFeed,
      teamIdToTeam, "feedId", existingMasterPlayer);

    // Assert
    verify(playerServiceMock).updateMasterPlayer(existingMasterPlayer);
    ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
    verify(playerServiceMock).update(eq(existingPlayerWithOldTeam), captor.capture());
    var fieldsToUpdateMap = captor.getValue();
    assertThat(fieldsToUpdateMap.get("team"), is(newTeam));

    verify(kafkaServiceMock).sendPlayerTransferAndTeamChangeEvents(eq(existingPlayerWithOldTeam), any(), eq("feedId"),
      eq(TEAM_CHANGED), any(), any());
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMasterDoesntExist_thenPlayerCreated_kafkaMessageSent() {
    // Arrange
    ObjectId playerId = new ObjectId();

    SquadPlayerDTO newPlayerFeed = SquadPlayerCanned
      .defaultSquadPlayer()
      .team(SquadPlayerDTO.TeamDTO.of("optaId", "ROMA", "ROM"))
      .build();

    MasterPlayer newMasterPlayer = defaultMasterPlayerBuilder().id(playerId).firstName("francesco")
      .name("totti").optaPlayerId("2").build();

    Map<String, Team> teamIdToTeam = Map.of(team.getOptaId(), team);

    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.empty());
    when(playerServiceMock.savePlayer(any(Player.class))).then(invocation -> invocation.getArguments()[0]);

    // Act
    Player player = squadsIngestionService.checkCompetitionPlayerExistsAndUpdate(squadsRetrieverMock, tournament,
      newPlayerFeed, teamIdToTeam, "feedId", newMasterPlayer);

    // Assert
    ArgumentCaptor<Player> captor = ArgumentCaptor.forClass(Player.class);
    verify(playerServiceMock).savePlayer(captor.capture());
    var tempPlayer = captor.getValue();
    assertThat(tempPlayer.getFirstName(), is("francesco"));

    verify(kafkaServiceMock).sendPlayerTransferEvent(any(), eq(tournament.getCompetitionId()),
      eq(player.getIdAsString()), eq(NEW), eq("feedId"));
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMasterPlayerAlreadyExists_thenANewTournamentPlayerIsCreatedAKafkaMessageIsSent() {
    // Arrange
    SquadPlayerDTO newPlayerFeed = buildSquadPlayer("francesco", "totti");
    Player existingPlayerToUpdate = buildPlayer("francesco", "totti", "2");

    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(defaultMasterPlayer());
    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.empty());
    when(playerServiceMock.savePlayer(any(Player.class))).thenReturn(existingPlayerToUpdate);

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(newPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(playerServiceMock).savePlayer(any(Player.class));
    verify(playerServiceMock).updateMasterPlayer(any(MasterPlayer.class));

    verify(kafkaServiceMock).sendPlayerTransferEvent(any(), eq(tournament.getCompetitionId()),
      eq(existingPlayerToUpdate.getIdAsString()), eq(NEW), eq(feed.getFeedId()));

  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMasterPlayerIsNotFound_thenThePlayerIsSkipped() {
    // Arrange
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(null);

    SquadPlayerDTO newPlayerFeed = buildSquadPlayer("francesco", "totti");

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(newPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(playerServiceMock, never()).savePlayer(any(Player.class));
    verify(playerServiceMock, never()).updateMasterPlayer(any(MasterPlayer.class));
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andATeamIsNotFound_thenThePlayersAreSkipped() {
    // Arrange
    when(squadsRetrieverMock.findTeamOrCreateUnmapped(anyString(), any())).thenReturn(null);
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(null);

    SquadPlayerDTO newPlayerFeed = buildSquadPlayer("francesco", "totti");

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(newPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(playerServiceMock, never()).savePlayer(any(Player.class));
    verify(playerServiceMock, never()).updateMasterPlayer(any(MasterPlayer.class));
  }

  @Test
  public void whenNewSquadsFeedIsReceived_thenPlayersWhoLeftTheTournamentAreDeleted() {
    // Arrange
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(defaultMasterPlayer());
    SquadPlayerDTO existingPlayerFeed = buildSquadPlayer("francesco", "totti");
    Player existingPlayer = buildPlayer("francesco", "totti", "1");

    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayer));
    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(existingPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    Player existingPlayerToDelete = buildPlayer("cristiano", "ronaldo", "3");

    List<Player> existingPlayers = List.of(existingPlayer, existingPlayerToDelete);

    when(playerServiceMock.getAllPlayersByTournamentId(anyString(), anyString())).thenReturn(existingPlayers);

    mockPlayerUpdateAndSave();

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(playerServiceMock, times(1)).update(any(Player.class), anyMap());
    verify(playerServiceMock).disablePlayer(any(Player.class));

    verify(kafkaServiceMock).sendPlayerTransferAndTeamChangeEvents(eq(existingPlayerToDelete), any(), eq("feedId"),
      eq(DELETED), any(), any());
  }

  @Test
  public void whenNewSquadsFeedIsReceived_thenThePlayerTeamChangeCallbackIsCalledForPlayersWhoseTeamChanged() {
    // Arrange
    Team aDifferentTeam = Team
      .builder().id(new ObjectId()).build();

    when(squadsRetrieverMock.findTeamOrCreateUnmapped(anyString(), any())).thenReturn(aDifferentTeam);

    SquadPlayerDTO existingPlayerFeed = buildSquadPlayer("daniele", "de rossi");
    Player existingPlayerToUpdate = buildPlayer("daniele", "de rossi", "2");
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(defaultMasterPlayer());
    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayerToUpdate));

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(existingPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    List<Player> existingPlayers = List.of(existingPlayerToUpdate);

    when(playerServiceMock.getAllPlayersByTournamentId(anyString(), anyString())).thenReturn(existingPlayers);

    mockPlayerUpdateAndSave();

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(kafkaServiceMock).sendPlayerTransferAndTeamChangeEvents(eq(existingPlayerToUpdate), any(), eq("feedId"),
      eq(TEAM_CHANGED), any(), any());
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMoreThan11PlayersAreSetToBeDeletedAndTheTournamentStartsInLessThan10Days_thenTheDeletionIsNotRun() {
    // Arrange
    int nrPlayersToBeDeleted = 16;

    var tournament = tournamentCanned().startDate(Instant.now().plus(5, ChronoUnit.DAYS)).build();
    SquadPlayerDTO existingPlayerFeed = buildSquadPlayer("daniele", "de rossi");
    Player existingPlayerToUpdate = buildPlayer("daniele", "de rossi", "2");
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(defaultMasterPlayer());
    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayerToUpdate));

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(existingPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    List<Player> allPlayers = IntStream
      .range(0, nrPlayersToBeDeleted).mapToObj(i -> defaultPlayer(ObjectId.get()))
      .collect(toList());

    when(playerServiceMock.getAllPlayersByTournamentId(anyString(), anyString())).thenReturn(allPlayers);

    when(playerServiceMock.savePlayer(any(Player.class))).then(invocation -> invocation.getArguments()[0]);

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(kafkaServiceMock, never()).sendPlayerTransferAndTeamChangeEvents(any(), any(), any(), any(), any(), any());
    verify(playerServiceMock, never()).disablePlayer(any(Player.class));
  }

  @Test
  public void whenNewSquadsFeedIsReceived_andMoreThan11PlayersAreSetToBeDeletedAndTheTournamentStartsInMoreThan10Days_thenTheDeletionIsRun() {
    // Arrange
    int nrPlayersToBeDeleted = 16;

    var tournament = tournamentCanned().startDate(Instant.now().plus(15, ChronoUnit.DAYS)).build();
    SquadPlayerDTO existingPlayerFeed = buildSquadPlayer("daniele", "de rossi");
    Player existingPlayerToUpdate = buildPlayer("daniele", "de rossi", "2");
    when(squadsRetrieverMock.findMasterPlayer(any())).thenReturn(defaultMasterPlayer());
    when(playerServiceMock.getById(anyString(), anyString())).thenReturn(Optional.of(existingPlayerToUpdate));

    SquadsFeed feed = SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(existingPlayerFeed)).build();
    when(squadsRetrieverMock.retrieveSquadsFeed(tournament)).thenReturn(feed);

    List<Player> allPlayers = IntStream
      .range(0, nrPlayersToBeDeleted).mapToObj(i -> defaultPlayer(ObjectId.get()))
      .collect(toList());

    when(playerServiceMock.getAllPlayersByTournamentId(anyString(), anyString())).thenReturn(allPlayers);

    when(playerServiceMock.savePlayer(any(Player.class))).then(invocation -> invocation.getArguments()[0]);

    // Act
    squadsIngestionService.processSquadsFeed(squadsRetrieverMock, tournament, false);

    // Assert
    verify(kafkaServiceMock, times(16)).sendPlayerTransferAndTeamChangeEvents(any(), any(), any(), any(), any(), any());
    verify(playerServiceMock, times(16)).disablePlayer(any(Player.class));
  }

  private void mockPlayerUpdateAndSave() {
    when(playerServiceMock.savePlayer(any(Player.class))).then(invocation -> invocation.getArguments()[0]);
    when(playerServiceMock.update(any(Player.class), anyMap())).then(invocation -> {
      Player player = invocation.getArgument(0, Player.class);
      player.setId(ObjectId.get());
      return player;
    });
  }

  private static Player buildPlayer(String firstName, String lastName, String optaPlayerId) {
    return Player
      .builder()
      .firstName(firstName)
      .name(lastName)
      .position(Player.Position.MIDFIELDER)
      .id(new ObjectId())
      .isActive(true)
      .playerMasterId("masterId")
      .tournament(tournament)
      .optaPlayerId(optaPlayerId)
      .team(team)
      .build();
  }

  private static SquadPlayerDTO buildSquadPlayer(String firstName, String lastName) {
    return SquadPlayerDTO
      .builder()
      .feedId("feedId")
      .playerId("playerId")
      .firstName(firstName)
      .lastName(lastName)
      .birthDate(LocalDate.now().minusYears(13))
      .position(Player.Position.MIDFIELDER)
      .team(SquadPlayerDTO.TeamDTO.of("optaId", "optaTeam", "optaAbbreviation"))
      .build();
  }
}
