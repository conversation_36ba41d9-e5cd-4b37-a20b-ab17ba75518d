package com.wsf.dataingestor.unit.services.events;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.events.HalfTimeNotifier;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.services.events.HalfTimeNotifier.HALF_TIME_PREFIX;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.domain.common.MatchPeriod.HALF_TIME;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.assertArg;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class HalfTimeNotifierTest extends TestWithMocks {
  @Mock
  private LiveMatchUtils liveMatchUtilsMock;
  @Mock
  private IndexCalculator soccerIndexCalculatorMock;
  @Mock
  private PlayerMatchDataCacheService playerMatchDataCacheServiceMock;
  @Mock
  private KafkaService kafkaServiceMock;

  private HalfTimeNotifier halfTimeNotifier;

  @Before
  public void setUp() {
    Utils utils = new Utils(soccerIndexCalculatorMock, new MetricsManager(new SimpleMeterRegistry()));
    halfTimeNotifier = new HalfTimeNotifier(liveMatchUtilsMock, utils, playerMatchDataCacheServiceMock,
      kafkaServiceMock);
  }

  @Test
  public void ratingsAreSentAtHalfTime() {
    // Arrange
    int matchTime = 46;
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().matchPeriod(HALF_TIME).matchTime(matchTime).build();
    MatchDataFeed halfTimeMA2 = matchDataFeedCanned()
      .fixtureStatus(LIVE)
      .matchPeriod(HALF_TIME)
      .matchTimeMin(matchTime)
      .build();
    Fixture fixture = halfTimeMA2.getFixture();

    Player playerOnThePitch = playerCanned().build();
    when(liveMatchUtilsMock.findPlayersOnThePitch(eq(fixture), eq(ongoingMatchData))).thenReturn(
      List.of(playerOnThePitch));

    PlayerMatchData playerMatchData = playerMatchDataCanned().build();
    when(playerMatchDataCacheServiceMock.get(eq(fixture.getIdAsString()),
      eq(playerOnThePitch.getIdAsString()))).thenReturn(playerMatchData);

    // Act
    halfTimeNotifier.notifyHalfTime(halfTimeMA2, ongoingMatchData);

    // Assert
    verify(kafkaServiceMock).sendLivePlayerRating(assertArg(playerRating -> {
      assertThat(playerRating.getPlayer()).isEqualTo(playerOnThePitch);
      assertThat(playerRating.getPeriodId()).isEqualTo(HALF_TIME.getPeriodId());
      assertThat(playerRating.getFixtureTimeMin()).isEqualTo(matchTime);
      assertThat(playerRating.getFeedId()).startsWith(HALF_TIME_PREFIX);
    }), eq(EMPTY_METADATA));
  }

}
