package com.wsf.dataingestor.unit.services.fixtures;

import java.util.Set;
import org.junit.Test;
import com.wsf.dataingestor.clients.PullAPIClient;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.services.fixtures.FixtureRetrieverUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixturesFeedCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.ExternalIds;
import com.wsf.domain.common.Tournament;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FixtureRetrieverUtilsTest extends TestWithMocks {

  @Test
  public void whenRetrievingFixturesFeedFromATournamentWithMultipleExternalIds_ThenAllTheFixturesAreReturned() {
    Set<String> externalIds = Set.of("20873", "22839");
    Tournament tournament = TournamentCanned
      .tournamentCanned()
      .externalIds(new ExternalIds(externalIds, Set.of(), null, null))
      .build();
    PullAPIClient<FixturesFeed> pullAPIClient = mock(PullAPIClient.class);
    when(pullAPIClient.retrieveParsedFeed(anyString())).thenReturn(FixturesFeedCanned.fixturesFeedCanned().build());

    FixturesFeed retrievedFixturesFeed = FixtureRetrieverUtils.retrieveFixturesFeed(tournament, pullAPIClient,
      externalIds);

    assertEquals(2, retrievedFixturesFeed.getFixtures()
      .size());
    assertTrue(retrievedFixturesFeed.getFeedId()
      .startsWith("multiple["));
  }

  @Test
  public void whenRetrievingFixturesFeedFromATournamentWithASingleExternalId_ThenTheCorrectFixtureIsProcessed() {
    Set<String> externalIds = Set.of("externalId");
    Tournament tournament = TournamentCanned
      .tournamentCanned()
      .externalIds(new ExternalIds(externalIds, Set.of(), null, null))
      .build();
    PullAPIClient<FixturesFeed> pullAPIClient = mock(PullAPIClient.class);
    when(pullAPIClient.retrieveParsedFeed(anyString())).thenReturn(FixturesFeedCanned.fixturesFeedCanned().build());

    FixturesFeed retrievedFixturesFeed = FixtureRetrieverUtils.retrieveFixturesFeed(tournament, pullAPIClient,
      externalIds);

    assertEquals(1, retrievedFixturesFeed.getFixtures()
      .size());
    assertFalse(retrievedFixturesFeed.getFeedId()
      .startsWith("multiple["));
  }

}
