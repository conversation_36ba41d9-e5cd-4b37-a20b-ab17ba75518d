package com.wsf.dataingestor.unit.services.squads;

import junit.framework.TestCase;

import java.util.Set;
import org.junit.Test;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.services.squads.SquadsRetrieverUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.SquadsFeedCanned;
import com.wsf.dataingestor.sportmonks.clients.TeamSquadsClient;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SquadsRetrieverUtilsTest extends TestWithMocks {
  @Test
  public void whenThereAreTwoSportmonksTournamentIds_thenSquadIngestionIsProcessedForEachOfThoseTournaments() {
    Set<String> externalIds = Set.of("20873", "22839");

    SquadsFeed squadsFeed1 = SquadsFeedCanned.defaultSquadsFeed().build();
    SquadsFeed squadsFeed2 = SquadsFeedCanned.defaultSquadsFeed().feedId("555").build();

    TeamSquadsClient teamSquadsClient = mock(TeamSquadsClient.class);
    when(teamSquadsClient.retrieveParsedFeed(eq("20873"))).thenReturn(squadsFeed1);
    when(teamSquadsClient.retrieveParsedFeed(eq("22839"))).thenReturn(squadsFeed2);

    var setOfSquads = SquadsRetrieverUtils.retrieveSquadsFeed(teamSquadsClient, externalIds,
      SquadsFeed.FeedProvider.SPORTMONKS);
    TestCase.assertEquals(2, setOfSquads.getSquadPlayers()
      .size());
  }
}
