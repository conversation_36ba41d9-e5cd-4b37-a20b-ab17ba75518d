package com.wsf.dataingestor.unit.services.events;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ratings.FinalFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.FixtureSummaryPublisher;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.EntityEventDTOCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.kafka.domain.ContestantType;
import com.wsf.kafka.domain.FixturePeriod;
import com.wsf.kafka.domain.FixtureStatus;
import com.wsf.kafka.domain.FixtureSummaryData;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FinalFixtureSummaryNotifierTest extends TestWithMocks {
  private static final Fixture fixture = FixtureCanned.defaultFixture();
  @Mock
  private FixtureSummaryPublisher fixtureSummaryPublisher;
  @Mock
  private PlayerService playerService;
  @Mock
  private FixtureSummaryRetriever fixtureSummaryRetriever;

  private FinalFixtureSummaryNotifier finalFixtureSummaryNotifier;


  @Before
  public void setUp() throws Exception {
    finalFixtureSummaryNotifier = new FinalFixtureSummaryNotifier(playerService, fixtureSummaryPublisher, fixtureSummaryRetriever);
  }

  @Test
  public void whenTheFixtureContainsSubstituteEvents_thenWeProcessThem() {
    // Arrange
    EntityEventDTO substituteOffEvent = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("eventId")
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SUB_OFF)
      .period(MatchPeriod.SECOND_HALF)
      .timeMin(5)
      .build();

    EntityEventDTO substituteOnEvent = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("eventId")
      .entityId(PLAYER_2_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SUB_ON)
      .period(MatchPeriod.SECOND_HALF)
      .timeMin(5)
      .build();

    var matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of(substituteOffEvent, substituteOnEvent))
      .supportedEventTypes(Set.of(SUB_ON, SUB_OFF))
      .build();

    // Act
    finalFixtureSummaryNotifier.sendSettlementSummary(matchDataFeed);

    // Assert
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(matchDataFeed.getFixture().getIdAsString());
      assertThat(fixtureSummary.getIsFinal()).isTrue();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName()));
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.PLAYED);
      assertThat(fixtureSummary.getEvents())
        .hasSize(2)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifySubstitutionOffEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifySubstitutionOnEvent);
      return true;
    }));
  }

  @Test
  public void whenTheFixtureHasBeenCancelled_thenWeSendAnEmptyFeedMessageForEachEntityInvolved() {
    // Arrange
    MatchDataFeed feed = MatchDataFeed
      .builder()
      .fixture(fixture)
      .date(Instant.now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.CANCELLED)
      .matchTimeMin(0)
      .matchPeriod(MatchPeriod.FIRST_HALF)
      .feedId("myId")
      .receivedTs(Instant.now().minusSeconds(10))
      .supportedEventTypes(Set.of())
      .provider(MatchDataFeed.FeedProvider.WSF)
      .build();
    Player playerWhoDidNotPlay = PlayerCanned.defaultPlayer();
    when(playerService.getAllPlayersForFixtureId(any())).thenReturn(List.of(playerWhoDidNotPlay));

    // Act
    finalFixtureSummaryNotifier.sendSettlementSummary(feed);

    // Assert
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(feed.getFixture().getIdAsString());
      assertThat(fixtureSummary.getIsFinal()).isTrue();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(Set.of());
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.CANCELLED);
      assertThat(fixtureSummary.getEvents()).isEmpty();
      assertThat(fixtureSummary.getContestantStats())
        .hasSize(3)
        .filteredOn(x -> x.getContestantType() == ContestantType.SOCCER_PLAYER)
        .hasSize(1);
      assertThat(fixtureSummary.getContestantStats())
        .hasSize(3)
        .filteredOn(x -> x.getContestantType() == ContestantType.SOCCER_PLAYER)
        .hasSize(1);

      return true;
    }));
  }

  @Test
  public void whenTheFixtureIsCompleted_AKafkaMessageIsSentWithPlayedFixtureStatus() {
    // Arrange
    EntityEventDTO shot = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SHOT)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(1)
      .build();

    EntityEventDTO shotOnGoal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SHOT_ON_GOAL)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(2)
      .build();

    EntityEventDTO goal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(GOAL)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(3)
      .build();

    MatchDataFeed feed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixture(fixture)
      .aggregatedPlayerMatchEvents(List.of(shot, shotOnGoal, goal))
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .build();

    // Act
    finalFixtureSummaryNotifier.sendSettlementSummary(feed);

    // Assert
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(feed.getFixture().getIdAsString());
      assertThat(fixtureSummary.getIsFinal()).isTrue();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(Set.of());
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.PLAYED);
      assertThat(fixtureSummary.getEvents())
        .hasSize(3)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotOnGoalEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyGoalEvent);

      return true;
    }));
  }

  @Test
  public void whenTheFixtureIsSuspended_AKafkaMessageIsSentWithSuspendedFixtureStatus() {
    // Arrange
    EntityEventDTO shot = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SHOT)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(1)
      .build();

    EntityEventDTO shotOnGoal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(SHOT_ON_GOAL)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(2)
      .build();

    EntityEventDTO goal = EntityEventDTOCanned
      .entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString())
      .eventType(GOAL)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(3)
      .build();

    MatchDataFeed feed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixture(fixture)
      .aggregatedPlayerMatchEvents(List.of(shot, shotOnGoal, goal))
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.SUSPENDED)
      .build();

    // Act
    finalFixtureSummaryNotifier.sendSettlementSummary(feed);

    // Assert
    Consumer<List<FixtureSummaryData.Event>> eventsVerifier = events -> {
      assertThat(events)
        .hasSize(3)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotOnGoalEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyGoalEvent);
    };
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(feed.getFixture().getIdAsString());
      assertThat(fixtureSummary.getIsFinal()).isTrue();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(Set.of());
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.SUSPENDED);
      assertThat(fixtureSummary.getEvents())
        .hasSize(3)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyShotOnGoalEvent)
        .anySatisfy(FinalFixtureSummaryNotifierTest::verifyGoalEvent);

      return true;
    }));
  }

  private static void verifyGoalEvent(FixtureSummaryData.Event goalEvent) {
    assertEquals(PLAYER_1_ID_STR, goalEvent.getContestantId());
    assertEquals(TEAM1_ID.toString(), goalEvent.getParentContestantId());
    assertEquals(GOAL.getStatisticName(), goalEvent.getEventType());
    assertEquals(FixturePeriod.FIRST_HALF, goalEvent.getFixturePeriod());
    assertEquals(3, (int) goalEvent.getMatchTime());
  }

  private static void verifyShotEvent(FixtureSummaryData.Event shotEvent) {
    assertEquals(PLAYER_1_ID_STR, shotEvent.getContestantId());
    assertEquals(TEAM1_ID.toString(), shotEvent.getParentContestantId());
    assertEquals(SHOT.getStatisticName(), shotEvent.getEventType());
    assertEquals(FixturePeriod.FIRST_HALF, shotEvent.getFixturePeriod());
    assertEquals(1, (int) shotEvent.getMatchTime());
  }

  private static void verifyShotOnGoalEvent(FixtureSummaryData.Event shotOnGoalEvent) {
    assertEquals(PLAYER_1_ID_STR, shotOnGoalEvent.getContestantId());
    assertEquals(TEAM1_ID.toString(), shotOnGoalEvent.getParentContestantId());
    assertEquals(SHOT_ON_GOAL.getStatisticName(), shotOnGoalEvent.getEventType());
    assertEquals(FixturePeriod.FIRST_HALF, shotOnGoalEvent.getFixturePeriod());
    assertEquals(2, (int) shotOnGoalEvent.getMatchTime());
  }

  private static void verifySubstitutionOffEvent(FixtureSummaryData.Event substitutionOffEvent) {
    assertEquals(PLAYER_1_ID_STR, substitutionOffEvent.getContestantId());
    assertEquals(TEAM1_ID.toString(), substitutionOffEvent.getParentContestantId());
    assertEquals(SUB_OFF.getStatisticName(), substitutionOffEvent.getEventType());
    assertEquals(FixturePeriod.SECOND_HALF, substitutionOffEvent.getFixturePeriod());
    assertEquals(5, (int) substitutionOffEvent.getMatchTime());
  }

  private static void verifySubstitutionOnEvent(FixtureSummaryData.Event substitutionOnEvent) {
    assertEquals(PLAYER_2_ID_STR, substitutionOnEvent.getContestantId());
    assertEquals(TEAM1_ID.toString(), substitutionOnEvent.getParentContestantId());
    assertEquals(SUB_ON.getStatisticName(), substitutionOnEvent.getEventType());
    assertEquals(FixturePeriod.SECOND_HALF, substitutionOnEvent.getFixturePeriod());
    assertEquals(5, (int) substitutionOnEvent.getMatchTime());
  }
}
