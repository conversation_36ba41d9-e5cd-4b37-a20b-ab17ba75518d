package com.wsf.dataingestor.unit.services.ratings;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.events.LiveCoverageService;
import com.wsf.dataingestor.services.ratings.FixtureStartDataProcessor;
import com.wsf.dataingestor.services.ratings.FixtureStartDataRetriever;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.dataingestor.shared.TestUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.MatchStatus;

import static com.wsf.dataingestor.shared.TestUtils.buildCachedData;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultPlayer;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamDataDTOCanned.teamDataDTOCanned;
import static java.util.Collections.emptyMap;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FixtureStartDataProcessorTest extends TestWithMocks {

  private static final Fixture FIXTURE = fixtureCanned().isRunningBallConnected(true).build();

  @Mock
  PlayerService playerServiceMock;
  @Mock
  KafkaService kafkaServiceMock;
  @Mock
  PlayerStatsEnricher playerStatsEnricherMock;
  @Mock
  TeamStatsEnricher teamStatsEnricherMock;
  @Mock
  OngoingMatchDataCacheService ongoingMatchDataCacheServiceMock;
  @Mock
  PlayerMatchDataCacheService playerMatchDataCacheMock;
  @Mock
  TeamMatchDataCacheService teamMatchDataCacheMock;
  @Mock
  FeedsConfigService feedsConfigServiceMock;
  @Mock
  FixtureStartDataRetriever fixtureStartDataRetrieverMock;
  @Mock
  LiveCoverageService liveCoverageService;

  FixtureStartDataProcessor fixtureStartDataProcessor;

  @Before
  public void setUp() throws Exception {
    when(feedsConfigServiceMock.getFixtureStartDataRetriever(anyString())).thenReturn(fixtureStartDataRetrieverMock);
    fixtureStartDataProcessor = new FixtureStartDataProcessor(playerServiceMock, kafkaServiceMock,
      feedsConfigServiceMock, playerStatsEnricherMock, teamStatsEnricherMock, ongoingMatchDataCacheServiceMock,
      playerMatchDataCacheMock, teamMatchDataCacheMock, new MetricsManager(new SimpleMeterRegistry()),
      liveCoverageService);
    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(new HashMap<>());
    when(teamStatsEnricherMock.enrichLiveStats(any(), any(), any(), any())).thenReturn(new HashMap<>());
  }

  @Test
  public void whenNoRunningBallFeedIsReceived_thenTheFixtureIsSentAsNotLiveEnabled() {
    // Arrange
    Fixture fixture = fixtureCanned().isRunningBallConnected(false).isLiveEnabled(true).build();
    Fixture liveDisabledFixture = fixtureCanned().isRunningBallConnected(false).isLiveEnabled(false).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).build();

    Player player1 = PlayerCanned.createPlayer().build();
    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1));

    var teamIdToLineup = new HashMap<String, Set<String>>();
    OngoingMatchData cachedMatchData = TestUtils.buildCachedDataWithLineUpPlayerIds(teamIdToLineup);
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);
    when(liveCoverageService.cancelLiveCoverageForFixture(eq(fixture))).thenReturn(liveDisabledFixture);

    // Act
    fixtureStartDataProcessor.processFeed(fixture, cachedMatchData);

    // Assert
    verify(liveCoverageService).cancelLiveCoverageForFixture(eq(fixture));
    verify(kafkaServiceMock).sendPlayerEventRating(argThat(playerEvent -> {
      boolean isCorrectPlayer = playerEvent.getPlayerLineUp().getPlayer().getId().equals(player1.getIdAsString());
      boolean isCorrectLiveEnabledFlag =
        playerEvent.getPlayerLineUp().getMatch().getIsLiveEnabled() == liveDisabledFixture.getIsLiveEnabled();

      return isCorrectPlayer && isCorrectLiveEnabledFlag;
    }));
    verify(kafkaServiceMock, times(2)).sendTeamEventRating(
      argThat(teamEvent -> !teamEvent.getTeamMatchStart().getMatch().getIsLiveEnabled()));
  }

  @Test
  public void whenWeReceiveLineupsForBothTeams_andAllPlayersForTheFixtureAreAlsoPlaying_thenTheLineUpEventIsSentAndCacheUpdatedForAll() {
    // Arrange
    ObjectId player1Id = ObjectId.get();
    ObjectId player2Id = ObjectId.get();
    Team team1 = TeamCanned.teamCanned().id(TEAM1_ID).build();
    Team team2 = TeamCanned.teamCanned().id(TEAM2_ID).build();
    Player player1 = PlayerCanned.createPlayer().id(player1Id).team(team1).build();
    Player player2 = PlayerCanned.createPlayer().id(player2Id).team(team2).build();

    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1, player2));

    PlayerDataDTO player1DataDTO = TestUtils.buildPlayerFeedData(player1, new HashMap<>());
    PlayerDataDTO player2DataDTO = TestUtils.buildPlayerFeedData(player2, new HashMap<>());

    TeamDataDTO homeTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getHomeTeam()).build();
    TeamDataDTO awayTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getAwayTeam()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .playersData(List.of(player1DataDTO, player2DataDTO))
      .teamsData(List.of(homeTeamDataDTO, awayTeamDataDTO))
      .build();
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);

    var teamIdToLineup = new HashMap<String, Set<String>>();
    OngoingMatchData cachedMatchData = TestUtils.buildCachedDataWithLineUpPlayerIds(teamIdToLineup);

    // Act
    fixtureStartDataProcessor.processFeed(FIXTURE, cachedMatchData);

    // Assert
    verifyTeamMatchStartWasSent(FIXTURE.getHomeTeam(), emptyMap());
    verifyTeamMatchStartWasSent(FIXTURE.getAwayTeam(), emptyMap());
    verifyTeamsStatsWereEnriched(homeTeamDataDTO, awayTeamDataDTO, cachedMatchData);

    verifyPlayerLineUpWasSent(player1, emptyMap(), true);
    verifyPlayerLineUpWasSent(player2, emptyMap(), true);
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player1DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player2DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));

    verifyLineUpStoredInCache(TEAM1_ID.toString(), Set.of(player1Id.toString()));
    verifyLineUpStoredInCache(TEAM2_ID.toString(), Set.of(player2Id.toString()));
  }

  @Test
  public void whenWeReceiveLineupsForBothTeams_andOnlySomePlayersForTheFixtureArePlaying_thenTheLineUpEventIsSentAndCacheUpdatedForThem() {
    // Arrange
    ObjectId player1Id = ObjectId.get();
    ObjectId player2Id = ObjectId.get();
    ObjectId player3Id = ObjectId.get();
    Team team1 = TeamCanned.teamCanned().id(TEAM1_ID).build();
    Team team2 = TeamCanned.teamCanned().id(TEAM2_ID).build();
    Player player1 = PlayerCanned.createPlayer().id(player1Id).team(team1).build();
    Player player2 = PlayerCanned.createPlayer().id(player2Id).team(team2).build();
    Player player3 = PlayerCanned.createPlayer().id(player3Id).team(team2).build();
    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1, player2, player3));

    PlayerDataDTO player1DataDTO = TestUtils.buildPlayerFeedData(player1, new HashMap<>());
    PlayerDataDTO player2DataDTO = TestUtils.buildPlayerFeedData(player2, new HashMap<>());

    TeamDataDTO homeTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getHomeTeam()).build();
    TeamDataDTO awayTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getAwayTeam()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .playersData(List.of(player1DataDTO, player2DataDTO))
      .teamsData(List.of(homeTeamDataDTO, awayTeamDataDTO))
      .build();
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);

    var teamIdToLineup = new HashMap<String, Set<String>>();
    OngoingMatchData cachedMatchData = TestUtils.buildCachedDataWithLineUpPlayerIds(teamIdToLineup);

    // Act
    fixtureStartDataProcessor.processFeed(FIXTURE, cachedMatchData);

    // Assert
    verifyTeamMatchStartWasSent(FIXTURE.getHomeTeam(), emptyMap());
    verifyTeamMatchStartWasSent(FIXTURE.getAwayTeam(), emptyMap());
    verifyTeamsStatsWereEnriched(homeTeamDataDTO, awayTeamDataDTO, cachedMatchData);

    verifyPlayerLineUpWasSent(player1, emptyMap(), true);
    verifyPlayerLineUpWasSent(player2, emptyMap(), true);
    verifyPlayerLineUpWasSent(player3, emptyMap(), false);
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player1DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player2DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));

    verifyLineUpStoredInCache(TEAM1_ID.toString(), Set.of(player1Id.toString()));
    verifyLineUpStoredInCache(TEAM2_ID.toString(), Set.of(player2Id.toString()));
  }

  @Test
  public void whenWeReceiveLineupsForOneTeamOnly_andAllPlayersForTheTeamAreAlsoPlaying_thenTheLineUpEventsAreSentForBothTeams_butCacheUpdatedForThatTeamOnly() {
    // Arrange
    ObjectId player1Id = ObjectId.get();
    ObjectId player2Id = ObjectId.get();
    ObjectId player3Id = ObjectId.get();
    Team team1 = TeamCanned.teamCanned().id(TEAM1_ID).build();
    Team team2 = TeamCanned.teamCanned().id(TEAM2_ID).build();
    Player player1 = PlayerCanned.createPlayer().id(player1Id).team(team1).build();
    Player player2 = PlayerCanned.createPlayer().id(player2Id).team(team1).build();
    Player player3 = PlayerCanned.createPlayer().id(player3Id).team(team2).build();

    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1, player2, player3));

    PlayerDataDTO player1DataDTO = TestUtils.buildPlayerFeedData(player1, new HashMap<>());
    PlayerDataDTO player2DataDTO = TestUtils.buildPlayerFeedData(player2, new HashMap<>());

    TeamDataDTO homeTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getHomeTeam()).build();
    TeamDataDTO awayTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getAwayTeam()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .playersData(List.of(player1DataDTO, player2DataDTO))
      .teamsData(List.of(homeTeamDataDTO, awayTeamDataDTO))
      .build();
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);

    var teamIdToLineup = new HashMap<String, Set<String>>();
    var firstTeamCachedPlayerIds = Set.of("cachedPlayer1Id", "cachedPlayer2Id");
    teamIdToLineup.put(TEAM1_ID.toString(), firstTeamCachedPlayerIds);
    var secondTeamCachedPlayerIds = Set.of("cachedPlayer3Id", "cachedPlayer4Id");
    teamIdToLineup.put(TEAM2_ID.toString(), secondTeamCachedPlayerIds);
    OngoingMatchData cachedMatchData = TestUtils.buildCachedDataWithLineUpPlayerIds(teamIdToLineup);

    // Act
    fixtureStartDataProcessor.processFeed(FIXTURE, cachedMatchData);

    // Assert
    verifyTeamMatchStartWasSent(FIXTURE.getHomeTeam(), emptyMap());
    verifyTeamMatchStartWasSent(FIXTURE.getAwayTeam(), emptyMap());
    verifyTeamsStatsWereEnriched(homeTeamDataDTO, awayTeamDataDTO, cachedMatchData);

    verifyPlayerLineUpWasSent(player1, emptyMap(), true);
    verifyPlayerLineUpWasSent(player2, emptyMap(), true);
    verifyPlayerLineUpWasSent(player3, emptyMap(), false);
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player1DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player2DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));

    verifyLineUpStoredInCache(TEAM1_ID.toString(), Set.of(player1Id.toString(), player2Id.toString()));
    verifyLineUpStoredInCache(TEAM2_ID.toString(), secondTeamCachedPlayerIds);
  }

  @Test
  public void whenWeReceiveLineupsForOneTeamOnly_andOnlySomePlayersForTheTeamAreAlsoPlaying_thenTheLineUpEventsAreSentForBothTeams_butCacheUpdatedForThatTeamAndPlayersOnly() {
    // Arrange
    ObjectId player1Id = ObjectId.get();
    ObjectId player2Id = ObjectId.get();
    Team team1 = TeamCanned.teamCanned().id(TEAM1_ID).build();
    Player player1 = PlayerCanned.createPlayer().id(player1Id).team(team1).build();
    Player player2 = PlayerCanned.createPlayer().id(player2Id).team(team1).build();

    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1, player2));

    PlayerDataDTO player1DataDTO = TestUtils.buildPlayerFeedData(player1, new HashMap<>());

    TeamDataDTO homeTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getHomeTeam()).build();
    TeamDataDTO awayTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getAwayTeam()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .playersData(List.of(player1DataDTO))
      .teamsData(List.of(homeTeamDataDTO, awayTeamDataDTO))
      .build();
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);

    var teamIdToLineup = new HashMap<String, Set<String>>();
    var firstTeamCachedPlayerIds = Set.of("cachedPlayer1Id", "cachedPlayer2Id");
    teamIdToLineup.put(TEAM1_ID.toString(), firstTeamCachedPlayerIds);
    var secondTeamCachedPlayerIds = Set.of("cachedPlayer3Id", "cachedPlayer4Id");
    teamIdToLineup.put(TEAM2_ID.toString(), secondTeamCachedPlayerIds);
    OngoingMatchData cachedMatchData = TestUtils.buildCachedDataWithLineUpPlayerIds(teamIdToLineup);

    // Act
    fixtureStartDataProcessor.processFeed(FIXTURE, cachedMatchData);

    // Assert
    verifyTeamMatchStartWasSent(FIXTURE.getHomeTeam(), emptyMap());
    verifyTeamMatchStartWasSent(FIXTURE.getAwayTeam(), emptyMap());
    verifyTeamsStatsWereEnriched(homeTeamDataDTO, awayTeamDataDTO, cachedMatchData);

    verifyPlayerLineUpWasSent(player1, emptyMap(), true);
    verifyPlayerLineUpWasSent(player2, emptyMap(), false);
    verify(playerStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(player1DataDTO), any(PlayerMatchData.class),
      eq(List.of()), eq(cachedMatchData));

    verifyLineUpStoredInCache(TEAM1_ID.toString(), Set.of(player1Id.toString()));
    verifyLineUpStoredInCache(TEAM2_ID.toString(), secondTeamCachedPlayerIds);
  }

  @Test
  public void whenEmptyLineupsAreReceivedForTheFixture_thenNoLineUpEventIsSentAndCacheIsNotUpdatedForThem() {
    // Arrange
    ObjectId player1Id = ObjectId.get();
    ObjectId player2Id = ObjectId.get();
    Player player1 = defaultPlayer(player1Id);
    Player player2 = defaultPlayer(player2Id);
    when(playerServiceMock.getActivePlayersForFixture(any())).thenReturn(List.of(player1, player2));

    TeamDataDTO homeTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getHomeTeam()).build();
    TeamDataDTO awayTeamDataDTO = teamDataDTOCanned().team(FIXTURE.getAwayTeam()).build();

    MatchDataFeed matchDataFeed = matchDataFeedCanned()
      .fixture(FIXTURE)
      .playersData(List.of())
      .teamsData(List.of(homeTeamDataDTO, awayTeamDataDTO))
      .build();
    when(fixtureStartDataRetrieverMock.retrieveFixtureStartData(any())).thenReturn(matchDataFeed);

    OngoingMatchData cachedMatchData = buildCachedData(emptyMap());

    // Act
    fixtureStartDataProcessor.processFeed(FIXTURE, cachedMatchData);

    // Assert
    verifyPlayerLineUpWasSent(player1, emptyMap(), false);
    verifyPlayerLineUpWasSent(player2, emptyMap(), false);
  }

  private void verifyLineUpStoredInCache(String teamId, Set<String> playerIds) {
    ArgumentCaptor<OngoingMatchData> cachedDataCaptor = ArgumentCaptor.forClass(OngoingMatchData.class);
    verify(ongoingMatchDataCacheServiceMock).set(eq(FIXTURE.getIdAsString()), cachedDataCaptor.capture());
    assertEquals(playerIds, cachedDataCaptor.getValue().getTeamIdToLineUp().get(teamId));
  }

  private void verifyPlayerLineUpWasSent(Player player, Map<String, Number> stats, boolean isPlaying) {
    String playerId = player.getId().toString();
    verify(kafkaServiceMock).sendPlayerEventRating(argThat(playerEvent -> {
      boolean isCorrectPlayer = playerEvent.getPlayerLineUp().getPlayer().getId().equals(playerId);
      boolean isCorrectMatchStatus = playerEvent.getPlayerLineUp().getMatch().getStatus() == MatchStatus.FIXTURE;
      boolean areStatsTheSame = playerEvent.getPlayerLineUp().getStats().equals(stats);
      boolean isPlayingCorrect = playerEvent.getPlayerLineUp().getIsPlaying() == isPlaying;

      return isCorrectPlayer && isCorrectMatchStatus && areStatsTheSame && isPlayingCorrect;
    }));

    if (isPlaying) {
      ArgumentCaptor<PlayerMatchData> capt = ArgumentCaptor.forClass(PlayerMatchData.class);
      verify(playerMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(playerId), capt.capture());
      PlayerMatchData dataToCache = capt.getValue();
      assertThat(dataToCache.getPlayerId(), is(playerId));
    }
  }

  private void verifyTeamMatchStartWasSent(Team team, Map<String, Number> stats) {
    String teamId = team.getId().toString();
    verify(kafkaServiceMock).sendTeamEventRating(argThat(teamEvent -> {
      boolean isStatusFixture = teamEvent.getTeamMatchStart().getMatch().getStatus() == MatchStatus.FIXTURE;
      boolean areStatsTheSame = teamEvent.getTeamMatchStart().getStats().equals(stats);
      boolean isCorrectTeam = teamEvent.getTeamMatchStart().getTeam().getId().equals(team.getIdAsString());

      return isStatusFixture && areStatsTheSame && isCorrectTeam;
    }));

    ArgumentCaptor<TeamMatchData> capt = ArgumentCaptor.forClass(TeamMatchData.class);
    verify(teamMatchDataCacheMock).set(eq(FIXTURE.getId().toString()), eq(teamId), capt.capture());
    TeamMatchData dataToCache = capt.getValue();
    assertThat(dataToCache.getTeamId(), is(teamId));
  }

  private void verifyTeamsStatsWereEnriched(TeamDataDTO homeTeamDataDTO, TeamDataDTO awayTeamDataDTO,
                                            OngoingMatchData cachedMatchData) {
    verify(teamStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(homeTeamDataDTO), any(TeamMatchData.class),
      eq(cachedMatchData));
    verify(teamStatsEnricherMock).enrichLiveStats(eq(FIXTURE), eq(awayTeamDataDTO), any(TeamMatchData.class),
      eq(cachedMatchData));
  }
}
