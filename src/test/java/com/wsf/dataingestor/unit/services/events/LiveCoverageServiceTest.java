package com.wsf.dataingestor.unit.services.events;

import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.events.BetStopService;
import com.wsf.dataingestor.services.events.LiveCoverageService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Provider;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.MatchEventDTOCanned.matchEventDTOCanned;
import static com.wsf.domain.soccer.SoccerMatchEvent.LIVE_COVERAGE_CANCELLED;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class LiveCoverageServiceTest extends TestWithMocks {
  @Mock
  FixtureService fixtureServiceMock;
  @Mock
  BetStopService betStopService;
  @Mock
  OngoingMatchDataCacheService ongoingMatchDataCacheService;
  @Mock
  PlayerMatchDataCacheService playerMatchDataCache;
  @Mock
  TeamMatchDataCacheService teamMatchDataCache;

  LiveCoverageService liveCoverageService;

  @Before
  public void setUp() {
    liveCoverageService = new LiveCoverageService(fixtureServiceMock, betStopService);
  }

  @Test
  public void whenRunningBallCancelsCoverage_thenItIsStoredAsNotLiveEnabled() {
    // Arrange
    Fixture fixture = fixtureCanned().isLiveEnabled(true).build();
    MatchEventDTO matchEventDTO = matchEventDTOCanned().event(LIVE_COVERAGE_CANCELLED).build();
    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(matchEventDTO)).build();

    Fixture updatedFixture = fixtureCanned().isLiveEnabled(false).build();
    when(fixtureServiceMock.storeFixtureAsLiveCancelled(fixture, Provider.RUNNINGBALL)).thenReturn(updatedFixture);

    // Act
    liveCoverageService.suspendLiveCoverageForFixture(fixture, matchEventDTO, matchDataFeed.getFeedId());

    // Assert
    verify(fixtureServiceMock).storeFixtureAsLiveCancelled(fixture, Provider.RUNNINGBALL);
  }

  @Test
  public void whenRunningBallCancelsCoverage_andOngoingMatchDataIsNotPresent_thenNoBetStopIsSent() {
    // Arrange
    Fixture fixture = fixtureCanned().isLiveEnabled(true).build();
    MatchEventDTO matchEventDTO = matchEventDTOCanned().event(LIVE_COVERAGE_CANCELLED).build();
    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(matchEventDTO)).build();

    Fixture updatedFixture = fixtureCanned().isLiveEnabled(false).build();
    when(fixtureServiceMock.storeFixtureAsLiveCancelled(fixture, Provider.RUNNINGBALL)).thenReturn(updatedFixture);
    when(ongoingMatchDataCacheService.get(fixture.getIdAsString())).thenReturn(null);

    // Act
    liveCoverageService.suspendLiveCoverageForFixture(fixture, matchEventDTO, matchDataFeed.getFeedId());

    // Assert
    verifyNoInteractions(playerMatchDataCache);
    verifyNoInteractions(teamMatchDataCache);
  }

}
