package com.wsf.dataingestor.unit.services.stats;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.services.stats.StatsDuplicatorFactory;
import com.wsf.dataingestor.services.stats.TeamStatsEnricher;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.EntityEventDTOCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned;
import com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Team;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedProvider.SPORTMONKS;
import static com.wsf.dataingestor.shared.TestUtils.addDefaultTeamStats;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamDataDTOCanned.teamDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamMatchDataCanned.teamMatchDataCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS_REGULAR_TIMES;
import static com.wsf.domain.common.MatchPeriod.EXTRA_FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.customer.soccer.Constants.AWAY_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.customer.soccer.Constants.HOME_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static org.assertj.core.api.Assertions.assertThat;

public class TeamStatsEnricherTest extends TestWithMocks {

  TeamStatsEnricher teamStatsEnricher;

  @Before
  public void setup() {
    teamStatsEnricher = new TeamStatsEnricher(new StatsDuplicatorFactory());
  }

  @Test
  public void whenLiveStatsAreEnriched_theyAreSetCorrectly() {
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO teamData = teamDataDTOCanned().team(team).build();

    TeamMatchData cachedTeamData = teamMatchDataCanned().stats(newHashMap()).build();
    PlayerMatchEventDTO cornerEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .event(CORNER)
      .teamId(team.getIdAsString())
      .build();
    PlayerMatchEventDTO foulEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .event(FOUL)
      .teamId(team.getIdAsString())
      .build();
    OngoingMatchData cachedMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(CORNER.getStatisticName(), Set.of(cornerEvent), FOUL.getStatisticName(), Set.of(foulEvent)))
      .build();

    Map<String, Number> updatedStats = teamStatsEnricher.enrichLiveStats(fixtureCanned().build(), teamData,
      cachedTeamData, cachedMatchData);

    Map<String, Number> expectedStats = newHashMap();
    addDefaultTeamStats(expectedStats);
    expectedStats.put(TEAM_CORNERS, 1);
    expectedStats.put(TEAM_CORNERS_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_FOULS, 1);
    expectedStats.put(TEAM_FOULS_REGULAR_TIMES, 1);

    assertThat(updatedStats).isEqualTo(expectedStats);
  }

  @Test
  public void whenLiveStatsAreEnriched_andHomeTeamGotARedCard_thenHomeSentOffStatIsUpdated() {
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO teamData = teamDataDTOCanned().team(team).build();

    TeamMatchData cachedTeamData = teamMatchDataCanned().stats(newHashMap()).build();
    PlayerMatchEventDTO cornerEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .event(RED_CARD)
      .teamId(team.getIdAsString())
      .build();
    OngoingMatchData cachedMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of("redCard", Set.of(cornerEvent)))
      .build();

    Map<String, Number> updatedStats = teamStatsEnricher.enrichLiveStats(fixtureCanned().build(), teamData,
      cachedTeamData, cachedMatchData);

    Map<String, Number> expectedStats = newHashMap();
    addDefaultTeamStats(expectedStats);
    expectedStats.put(TEAM_RED_CARDS, 1);
    expectedStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 1);
    expectedStats.put(HOME_TEAM_SENT_OFF_STAT, 1);
    expectedStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);

    assertThat(updatedStats).isEqualTo(expectedStats);
  }

  @Test
  public void whenFinalStatsAreEnriched_thenStatsAreSetCorrectly() {
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO teamData = teamDataDTOCanned().team(team).build();

    EntityEventDTO firstCorner = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("firstCorner")
      .eventType(CORNER)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO firstGoalKick = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("firstGoalKick")
      .eventType(GOALKICK)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO yellowCard = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("yellowCard")
      .eventType(YELLOW_CARD)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO foul = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("foulEventId")
      .eventType(FOUL)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO shot = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("shotEventId")
      .eventType(SHOT)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO sog = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("sogEventId")
      .eventType(SHOT_ON_GOAL)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO tackleWon = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("tackleWonEventId")
      .eventType(TACKLE_WON)
      .teamId(team.getIdAsString())
      .build();

    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of(firstCorner, firstGoalKick, yellowCard, foul, shot, sog, tackleWon))
      .build();

    Map<String, Number> updatedStats = teamStatsEnricher.enrichFinalStats(matchDataFeed, teamData);

    Map<String, Number> expectedStats = newHashMap();
    expectedStats.put(TEAM_YELLOW_CARDS, 1);
    expectedStats.put(TEAM_FOULS, 1);
    expectedStats.put(TEAM_FOULS_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_SHOTS, 1);
    expectedStats.put(TEAM_SHOTS_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_SHOTS_ON_GOAL, 1);
    expectedStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_OFFSIDES, 0);
    expectedStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_RED_CARDS, 0);
    expectedStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_TACKLES_WON, 1);
    expectedStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 1);

    assertThat(updatedStats).isEqualTo(expectedStats);
  }

  @Test
  public void whenSpormonksFeed_thenSomeEventsAreNotAvailable_soTheirRegularTimeStatsAreDuplicatedFromExtraTimeStats_andArePresentInFinalTeamStats() {
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    Map<String, Number> teamStats = new HashMap<>(Map.of(TEAM_OFFSIDES, 1, TEAM_FOULS, 2));
    TeamDataDTO teamData = teamDataDTOCanned().team(team).stats(teamStats).build();

    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of())
      .provider(SPORTMONKS)
      .build();

    Map<String, Number> updatedStats = teamStatsEnricher.enrichFinalStats(matchDataFeed, teamData);
    assertThat(updatedStats.get(TEAM_FOULS_REGULAR_TIMES)).isEqualTo(2);
    assertThat(updatedStats.get(TEAM_OFFSIDES_REGULAR_TIMES)).isEqualTo(1);
  }

  @Test
  public void whenFinalStatsAreEnriched_andOneYellowCardHappenedToAPlayerOnTheBench_thenTheYellowCardIsExcluded() {
    // Arrange
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO teamData = teamDataDTOCanned().team(team).build();

    EntityEventDTO yellowCard = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("yellowCard")
      .eventType(YELLOW_CARD)
      .teamId(team.getIdAsString())
      .isOnBench(true)
      .build();

    EntityEventDTO redCard = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("redCard")
      .eventType(RED_CARD)
      .teamId(team.getIdAsString())
      .isOnBench(true)
      .build();

    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of(yellowCard, redCard))
      .supportedEventTypes(Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, SUB_ON, SUB_OFF))
      .build();

    // Act
    Map<String, Number> updatedStats = teamStatsEnricher.enrichFinalStats(matchDataFeed, teamData);

    // Assert
    Map<String, Number> expectedStats = newHashMap();
    expectedStats.put(TEAM_YELLOW_CARDS, 0);
    expectedStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_FOULS, 0);
    expectedStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_SHOTS, 0);
    expectedStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_OFFSIDES, 0);
    expectedStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_RED_CARDS, 0);
    expectedStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_TACKLES_WON, 0);
    expectedStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    assertThat(updatedStats).isEqualTo(expectedStats);
  }

  @Test
  public void extraTimeEventsAreCountedCorrectylInStats() {
    // Arrange
    Team team = TeamCanned.teamCanned().id(new ObjectId(TEAM1_ID.toString())).build();
    TeamDataDTO teamData = teamDataDTOCanned().team(team).build();

    EntityEventDTO shot = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("shotEventId")
      .eventType(SHOT)
      .period(FIRST_HALF)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO extraTimeShot = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("sogEventId")
      .eventType(SHOT)
      .period(EXTRA_FIRST_HALF)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO sog = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("shotEventId")
      .eventType(SHOT_ON_GOAL)
      .period(FIRST_HALF)
      .teamId(team.getIdAsString())
      .build();
    EntityEventDTO extraTimeSOG = EntityEventDTOCanned
      .entityEventDTOCanned()
      .eventId("sogEventId")
      .eventType(SHOT_ON_GOAL)
      .period(EXTRA_FIRST_HALF)
      .teamId(team.getIdAsString())
      .build();

    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of(shot, extraTimeShot, sog, extraTimeSOG))
      .supportedEventTypes(Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, SUB_ON, SUB_OFF))
      .build();

    // Act
    Map<String, Number> updatedStats = teamStatsEnricher.enrichFinalStats(matchDataFeed, teamData);

    // Assert
    Map<String, Number> expectedStats = newHashMap();
    expectedStats.put(TEAM_YELLOW_CARDS, 0);
    expectedStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_FOULS, 0);
    expectedStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_SHOTS, 2);
    expectedStats.put(TEAM_SHOTS_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_SHOTS_ON_GOAL, 2);
    expectedStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 1);
    expectedStats.put(TEAM_OFFSIDES, 0);
    expectedStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_RED_CARDS, 0);
    expectedStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_TACKLES_WON, 0);
    expectedStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);

    assertThat(updatedStats).isEqualTo(expectedStats);
  }
}
