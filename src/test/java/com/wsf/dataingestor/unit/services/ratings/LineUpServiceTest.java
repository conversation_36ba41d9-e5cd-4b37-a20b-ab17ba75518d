package com.wsf.dataingestor.unit.services.ratings;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.google.common.collect.ImmutableList;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ratings.LineUpService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_3_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.defaultPlayer;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM3_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.domain.common.Player.DetailedPosition.ATTACKING_MIDFIELDER;
import static com.wsf.domain.common.Player.DetailedPosition.DEFENSIVE_MIDFIELDER;
import static java.util.Collections.emptyMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

public class LineUpServiceTest extends TestWithMocks {

  LineUpService lineUpService;
  @Mock
  private PlayerService playerService;

  @Before
  public void setUp() throws Exception {
    this.lineUpService = new LineUpService(playerService);
  }

  @Test
  public void whenLineupsAreProcessed_andPlayerHasNewPosition_thenPositionIsUpdatedInDB() {
    PlayerDataDTO playerData = feedPlayer(buildPlayerWithPosition(DEFENSIVE_MIDFIELDER), ATTACKING_MIDFIELDER);

    Fixture fixture = FixtureCanned.fixtureCanned().build();

    MatchDataFeed feed = MatchDataFeed
      .builder()
      .feedId("feedId")
      .fixture(fixture)
      .receivedTs(Instant.now())
      .playersData(ImmutableList.of(playerData))
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();

    lineUpService.updatePlayersPosition(feed);

    verify(playerService).updatePlayerDetailedPosition(any(Player.class), eq(ATTACKING_MIDFIELDER));
  }

  @Test
  public void whenLineupsAreProcessed_andPlayerHasSamePosition_thenPositionIsNotUpdatedInDB() {
    PlayerDataDTO playerData = feedPlayer(buildPlayerWithPosition(ATTACKING_MIDFIELDER), ATTACKING_MIDFIELDER);

    Fixture fixture = FixtureCanned.fixtureCanned().build();

    MatchDataFeed feed = MatchDataFeed
      .builder()
      .feedId("feedId")
      .fixture(fixture)
      .receivedTs(Instant.now())
      .playersData(List.of(playerData))
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();

    lineUpService.updatePlayersPosition(feed);

    verify(playerService, never()).savePlayer(any());
  }

  @Test
  public void whenLineupsAreProcessed_andPlayerDoesNotBelongToAnyTeamInTheFixture_thenPlayerIsSkipped() {
    // Arrange
    Player homeTeamPlayer = defaultPlayer(PLAYER_1_ID, defaultTeam(TEAM1_ID, "optaId1"));
    Player awayTeamPlayer = defaultPlayer(PLAYER_2_ID, defaultTeam(TEAM2_ID, "optaId2"));
    Player wrongTeamPlayer = defaultPlayer(PLAYER_3_ID, defaultTeam(TEAM3_ID, "optaId3"));

    PlayerDataDTO homePlayerData = playerDataDTOCanned().player(homeTeamPlayer).isPlaying(true).build();
    PlayerDataDTO awayPlayerData = playerDataDTOCanned().player(awayTeamPlayer).isPlaying(true).build();
    PlayerDataDTO wrongPlayerData = playerDataDTOCanned().player(wrongTeamPlayer).isPlaying(true).build();

    Fixture fixture = FixtureCanned.fixtureCanned().build();

    MatchDataFeed feed = MatchDataFeed
      .builder()
      .feedId("feedId")
      .fixture(fixture)
      .receivedTs(Instant.now())
      .playersData(List.of(homePlayerData, awayPlayerData, wrongPlayerData))
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();

    // Act
    Set<Player> retrievedPlayers = LineUpService.retrievePlayers(feed);

    // Assert
    assertThat(retrievedPlayers).hasSize(2);
    assertEquals(2, retrievedPlayers.size());
    assertTrue(retrievedPlayers.contains(homeTeamPlayer));
    assertTrue(retrievedPlayers.contains(awayTeamPlayer));
    assertFalse(retrievedPlayers.contains(wrongTeamPlayer));
  }

  private static PlayerDataDTO feedPlayer(Player player, Player.DetailedPosition playerNewPosition) {
    return PlayerDataDTO
      .builder().matchPosition(playerNewPosition).player(player).stats(emptyMap()).build();
  }

  private static Player buildPlayerWithPosition(Player.DetailedPosition playerOldPosition) {
    return Player
      .builder().id(ObjectId.get()).detailedPosition(playerOldPosition).build();
  }
}
