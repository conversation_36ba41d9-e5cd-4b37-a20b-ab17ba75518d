package com.wsf.dataingestor.unit.services;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.RequiredArgsConstructor;

import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.services.TeamService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.canned.UnmappedTeamCanned;
import com.wsf.domain.common.MasterTeam;
import com.wsf.domain.common.Team;
import com.wsf.repository.common.MasterTeamRepository;
import com.wsf.repository.common.TeamRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.MATCHES_FEED_UNMAPPED_TEAM;
import static com.wsf.domain.common.ExternalProvider.OPTA;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class TeamServiceTest extends TestWithMocks {

  @Mock
  private RepositoryFactory.TeamRepositoryFactory teamRepositoryFactoryMock;
  @Mock
  private MasterTeamRepository masterTeamRepositoryMock;
  @Mock
  private UnmappedEntityClient unmappedEntityClientMock;
  @Mock
  private TeamRepository teamRepositoryMock;
  @Mock
  private DBUtils dbUtilsMock;

  private TeamService teamService;

  private MetricsManager metricsManager;

  private MeterRegistry simple;

  @Before
  public void setUp() throws Exception {
    when(dbUtilsMock.retrieveRepo(any(), anyString())).thenReturn(teamRepositoryMock);
    simple = new SimpleMeterRegistry();
    metricsManager = new MetricsManager(simple);
    this.teamService = new TeamService(teamRepositoryFactoryMock, masterTeamRepositoryMock, unmappedEntityClientMock,
      dbUtilsMock, metricsManager);
  }

  @Test
  public void whenATeamIsLookedUpByExternalId_andTheTeamIsFound_thenTheTeamIsReturned() {
    when(teamRepositoryMock.findById(anyString())).thenReturn(Optional.of(TeamCanned.defaultTeam()));
    when(teamRepositoryMock.save(any())).thenAnswer(inv -> inv.getArgument(0, Team.class));

    MasterTeam respMasterTeam = TeamCanned.defaultMasterTeam();
    MasterTeamFinder masterTeamFinder = new MasterTeamFinder(Optional.of(respMasterTeam));

    Team team = callFindOrCreateUnmapped(masterTeamFinder);
    assertNotNull(team);
    verifyNoInteractions(unmappedEntityClientMock);
    verify(masterTeamRepositoryMock, never()).save(any());
  }

  @Test
  public void whenATeamIsLookedUpByExternalId_andTheTeamIsNotFound_thenAnUnmappedTeamIsCreatedAndAnExceptionIsThrown() {
    when(unmappedEntityClientMock.createUnmappedTeam(anyString(), anyString(), anyString(), eq(OPTA))).thenReturn(
      UnmappedTeamCanned.createUnmappedTeam().build());

    MasterTeamFinder masterTeamFinder = new MasterTeamFinder(Optional.empty());

    Team notFoundTeam = callFindOrCreateUnmapped(masterTeamFinder);
    assertNull(notFoundTeam);
  }

  @Test
  public void whenATeamIsLookedUpByExternalId_andTheTeamIsNotFound_andTheUnmappedTeamIsSetToIgnore_thenAnUnmappedTeamIsCreated() {
    when(unmappedEntityClientMock.createUnmappedTeam(anyString(), anyString(), anyString(), eq(OPTA))).thenReturn(
      UnmappedTeamCanned.createUnmappedTeam().ignore(true).build());

    MasterTeamFinder masterTeamFinder = new MasterTeamFinder(Optional.empty());

    var team = callFindOrCreateUnmapped(masterTeamFinder);
    
    var unmappedTeamCount = simple.get(MATCHES_FEED_UNMAPPED_TEAM.value()).counter()
      .count();
    assertThat(unmappedTeamCount).isZero();
    assertNull(team);
  }

  @Test
  public void whenATeamIsLookedUpByExternalId_andTheTeamIsNotFound_andAnUnmappedTeamIsNotCreated_thenAMasterTeamAndATournamentTeamAreCreated() {
    when(unmappedEntityClientMock.createUnmappedTeam(anyString(), anyString(), anyString(), eq(OPTA))).thenReturn(null);
    when(teamRepositoryMock.findById(anyString())).thenReturn(Optional.empty());

    MasterTeam masterTeam = TeamCanned.defaultMasterTeam();
    when(masterTeamRepositoryMock.save(any())).thenReturn(masterTeam);
    when(teamRepositoryMock.save(any())).thenReturn(TeamCanned.defaultTeam());

    MasterTeamFinder masterTeamFinder = new MasterTeamFinder(Optional.empty());

    Team team = callFindOrCreateUnmapped(masterTeamFinder);
    assertNull(team);
  }

  @Test
  public void whenATeamIsLookedUpByExternalId_andAMasterTeamIsFound_andATournamentTeamIsNotFound_thenATournamentTeamIsCreated() {
    when(teamRepositoryMock.findById(anyString())).thenReturn(Optional.empty());
    when(teamRepositoryMock.save(any())).thenReturn(TeamCanned.defaultTeam());

    MasterTeam masterTeam = TeamCanned.defaultMasterTeam();
    MasterTeamFinder masterTeamFinder = new MasterTeamFinder(Optional.of(masterTeam));

    Team team = callFindOrCreateUnmapped(masterTeamFinder);
    assertNotNull(team);
    verify(unmappedEntityClientMock, never()).createUnmappedTeam(anyString(), anyString(), anyString(), any());
    verify(masterTeamRepositoryMock, never()).save(masterTeam);
    verify(teamRepositoryMock).save(any(Team.class));
  }

  private Team callFindOrCreateUnmapped(MasterTeamFinder masterTeamFinder) {
    return teamService.findByExternalIdOrCreateUnmapped("competitionId", "externalId", "Roma", "ROM", OPTA,
      masterTeamFinder);
  }

  @RequiredArgsConstructor
  static class MasterTeamFinder implements Function<String, Optional<MasterTeam>> {
    private final Optional<MasterTeam> masterTeam;

    @Override
    public Optional<MasterTeam> apply(String s) {
      return masterTeam;
    }
  }

  @RequiredArgsConstructor
  static class MasterTeamBuilder implements Supplier<MasterTeam> {
    private final MasterTeam masterTeam;

    @Override
    public MasterTeam get() {
      return masterTeam;
    }
  }
}
