package com.wsf.dataingestor.unit.services.ratings.player;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.EntityMatchData.BetStopInfo;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.config.ExecutorServiceConfig;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.publishers.RatingsPublisher;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingFilter;
import com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingsProcessor;
import com.wsf.dataingestor.services.stats.PlayerStatsEnricher;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.soccer.SoccerMatchEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.entityEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.FIXTURE_ID;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.dataingestor.sports.soccer.Constants.OPPONENT_TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SCORE;
import static com.wsf.dataingestor.utils.SoccerConstants.SUBBED_IN_PLAYER_ID;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static java.time.Instant.now;
import static java.util.Collections.emptyMap;
import static java.util.concurrent.CompletableFuture.runAsync;
import static java.util.concurrent.Executors.newSingleThreadExecutor;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@Slf4j
public class PlayerLiveRatingsProcessorTest extends TestWithMocks {

  private static final Map<String, Number> STATS = Map.of(PASS.getStatisticName(), 1);
  private static final Map<String, Number> NEW_STATS = Map.of(PASS.getStatisticName(), 1, ASSIST_GOAL.getStatisticName(), 2);
  @Mock
  PlayerLiveRatingFilter playerLiveRatingFilterMock;
  @Mock
  private PlayerMatchDataCacheService playerMatchDataCacheServiceMock;
  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheServiceMock;
  @Mock
  private PlayerService playerServiceMock;
  @Mock
  private RatingsPublisher ratingsPublisherMock;
  @Mock
  private PlayerStatsEnricher playerStatsEnricherMock;
  @Mock
  private IndexCalculator soccerIndexCalculatorMock;
  private PlayerLiveRatingsProcessor playerLiveRatingsProcessor;

  @Before
  public void setUp() throws Exception {
    when(playerServiceMock.getOrThrowById(anyString(), anyString())).thenReturn(PlayerCanned.defaultPlayer());

    when(soccerIndexCalculatorMock.calculate(any(), any())).thenReturn(IndexPerformance
      .builder().isValid(true)
      .index(50D).build());

    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(newHashMap());

    MetricsManager metricsManager = new MetricsManager(new SimpleMeterRegistry());
    Utils utils = new Utils(soccerIndexCalculatorMock, metricsManager);

    playerLiveRatingsProcessor = new PlayerLiveRatingsProcessor(playerMatchDataCacheServiceMock,
      playerStatsEnricherMock, playerServiceMock, ratingsPublisherMock, playerLiveRatingFilterMock, utils,
      new ThreadPoolService(newSingleThreadExecutor(), newSingleThreadExecutor(), metricsManager), metricsManager);
  }

  @Test
  public void whenAFeedIsProcessed_andItIsFinalForThePlayer_andContainsNoSubOffEvents_thenCacheIsUpdatedAndFinalRatingIsStoredAndSentToKafka() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(STATS).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(false)
      .hasPlayed(true)
      .stats(STATS)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    MatchDataFeed feed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .fixtureStatus(LIVE)
      .isSnapshot(false)
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .build();

    var metadata = new KafkaMetadata(true, Map.of());

    // Act
    playerLiveRatingsProcessor.processFeed(feed, ongoingMatchData, false);

    //Assert
    verify(playerMatchDataCacheServiceMock).set(anyString(), anyString(), any());
    verify(ratingsPublisherMock).publishAndStore(ArgumentMatchers.<PlayerRating>argThat(argument -> argument.getStats()
      .isEmpty()), eq(true), eq(metadata));
  }

  @Test
  public void whenAFeedWithSubOffIsProcessed_andRelatedSubOnContainsPlayerId_thenFinalRatingIsSentWithSubbedInPlayerIdInMetadataMap() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(STATS).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(false)
      .hasPlayed(true)
      .stats(STATS)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    var subOffEventId = SUB_OFF.getStatisticName() + PLAYER_1_ID_STR;
    var relatedSubOnEventId = SUB_ON.getStatisticName() + PLAYER_2_ID_STR;
    MatchDataFeed feed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .fixtureStatus(LIVE)
      .isSnapshot(false)
      .build();

    var subOffCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(subOffEventId)
      .relatedEventId(relatedSubOnEventId)
      .event(SUB_OFF)
      .entityId(PLAYER_1_ID_STR)
      .build();
    var subOnCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(relatedSubOnEventId)
      .relatedEventId(subOffEventId)
      .event(SoccerMatchEvent.SUB_ON)
      .entityId(PLAYER_2_ID_STR)
      .build();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .eventIdToPlayerMatchEvents(
        Map.of(subOffEventId, Set.of(subOffCachedMatchEvent), relatedSubOnEventId, Set.of(subOnCachedMatchEvent)))
      .build();

    var metadata = new KafkaMetadata(true, Map.of(SUBBED_IN_PLAYER_ID, subOnCachedMatchEvent.getEntityId()));

    // Act
    playerLiveRatingsProcessor.processFeed(feed, ongoingMatchData, false);

    //Assert
    verify(playerMatchDataCacheServiceMock).set(anyString(), anyString(), any());
    verify(ratingsPublisherMock).publishAndStore(ArgumentMatchers.<PlayerRating>argThat(argument -> argument.getStats()
      .isEmpty()), eq(true), eq(metadata));
  }

  @Test
  public void whenAFeedWithSubOffIsProcessed_andRelatedSubOnContainsUnknownPlayerId_thenFinalRatingIsSentWithoutSubbedInPlayerIdInMetadataMap() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(STATS).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(false)
      .hasPlayed(true)
      .stats(STATS)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    var subOnPlayerId = "UNKNOWN_PLAYER_OPTA_%s".formatted(PLAYER_2_ID_STR);
    var subOffEventId = SUB_OFF.getStatisticName() + PLAYER_1_ID_STR;
    var relatedSubOnEventId = SUB_ON.getStatisticName() + subOnPlayerId;
    MatchDataFeed feed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .fixtureStatus(LIVE)
      .isSnapshot(false)
      .build();

    var subOffCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(subOffEventId)
      .relatedEventId(relatedSubOnEventId)
      .event(SUB_OFF)
      .entityId(PLAYER_1_ID_STR)
      .build();
    var subOnCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(relatedSubOnEventId)
      .relatedEventId(subOffEventId)
      .event(SoccerMatchEvent.SUB_ON)
      .entityId(subOnPlayerId)
      .build();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(subOnPlayerId)))
      .eventIdToPlayerMatchEvents(
        Map.of(subOffEventId, Set.of(subOffCachedMatchEvent), relatedSubOnEventId, Set.of(subOnCachedMatchEvent)))
      .build();

    var metadata = new KafkaMetadata(true, Map.of());

    // Act
    playerLiveRatingsProcessor.processFeed(feed, ongoingMatchData, false);

    //Assert
    verify(playerMatchDataCacheServiceMock).set(anyString(), anyString(), any());
    verify(ratingsPublisherMock).publishAndStore(ArgumentMatchers.<PlayerRating>argThat(argument -> argument.getStats()
      .isEmpty()), eq(true), eq(metadata));
  }

  @Test
  public void whenAFeedIsReceived_andStatsHaveChanged_andThePlayerHasABetStop_thenRatingIsStoredAndSentToKafka() {
    // Arrange
    var betStopInfos = new TreeSet<BetStopInfo>();
    betStopInfos.add(BetStopInfo.of(GOAL.getStatisticName(), "eventId", Instant.now()));

    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerMatchData playerCachedData = playerMatchDataCanned()
      .playerId(PLAYER_1_ID_STR)
      .stats(STATS)
      .betStopsInfo(betStopInfos)
      .build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    Map<String, Number> expectedStoredStats = newHashMap(NEW_STATS);
    expectedStoredStats.put(MINS_PLAYED, 10);
    expectedStoredStats.put(TEAM_SCORE, 0);
    expectedStoredStats.put(OPPONENT_TEAM_SCORE, 0);
    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(expectedStoredStats);

    List<PlayerDataDTO> playerDataDTOS = List.of(buildPlayerDTO(expectedStoredStats));
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .matchTimeMin(10)
      .fixtureStatus(LIVE)
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    verify(ratingsPublisherMock).publishAndStore(
      ArgumentMatchers.<PlayerRating>argThat(argument -> argument.getStats().equals(expectedStoredStats)), eq(false),
      eq(EMPTY_METADATA));
  }

  @Test
  public void whenTwoFeedsAreReceived_andStatsHaveChanged_andThePlayerIsPlaying_thenRatingIsStoredAndSentToKafka_NotDuplicated() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(STATS).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    ExecutorServiceConfig executorServiceConfig = new ExecutorServiceConfig();

    MetricsManager metricsManager = new MetricsManager(new SimpleMeterRegistry());
    Utils utils = new Utils(soccerIndexCalculatorMock, metricsManager);

    ThreadPoolService threadPoolService = new ThreadPoolService(executorServiceConfig.playersPool(metricsManager),
      newSingleThreadExecutor(), metricsManager);
    playerLiveRatingsProcessor = new PlayerLiveRatingsProcessor(playerMatchDataCacheServiceMock,
      playerStatsEnricherMock, playerServiceMock, ratingsPublisherMock, playerLiveRatingFilterMock, utils,
      threadPoolService, metricsManager);

    /*
    We are trying to verify whether of two different thread are trying to run the same storeRatings method at the same
    time.
    This is checked with an atomic boolean. If the value is true, then an atomic integer is incremented. If the atomic
    integer value is > 0, then two concurrent storeRatings calls are being made
     */

    AtomicBoolean isMethodRunning = new AtomicBoolean(false);
    AtomicInteger concurrentOps = new AtomicInteger(0);

    doAnswer(invocation -> {
      if (isMethodRunning.getAndSet(true)) {
        concurrentOps.incrementAndGet();
      }
      Thread.sleep(200);
      isMethodRunning.set(false);
      return null;
    })
      .when(ratingsPublisherMock).publishAndStore(any(PlayerRating.class), anyBoolean(), any(KafkaMetadata.class));

    Map<String, Number> expectedStoredStats = newHashMap(NEW_STATS);
    expectedStoredStats.put(MINS_PLAYED, 10);
    expectedStoredStats.put(TEAM_SCORE, 0);
    expectedStoredStats.put(OPPONENT_TEAM_SCORE, 0);

    Map<String, Number> expectedStoredStatsNew = newHashMap(NEW_STATS);
    expectedStoredStats.put(MINS_PLAYED, 11);
    expectedStoredStats.put(TEAM_SCORE, 0);
    expectedStoredStats.put(OPPONENT_TEAM_SCORE, 0);

    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any()))
      .thenReturn(expectedStoredStats)
      .thenReturn(expectedStoredStatsNew);

    List<PlayerDataDTO> playerDataDTOS = List.of(buildPlayerDTO(expectedStoredStats));
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .matchTimeMin(10)
      .fixtureStatus(LIVE)
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)))
      .build();

    // Act
    CompletableFuture<Void> completable = runAsync(
      () -> playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false));

    CompletableFuture<Void> completable2 = runAsync(
      () -> playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false));

    completable.join();
    completable2.join();

    // Assert
    assertThat(concurrentOps.get()).isEqualTo(0);
  }

  @Test
  public void whenASubOffEventIsReceived_andThePlayerIsInBetStop_thenTheEventIsProcessed() {
    // Arrange
    TreeSet<BetStopInfo> betstopInfo = new TreeSet<>();
    betstopInfo.add(BetStopInfo.of("betstop", "eventId", now()));
    var playerMatchData = playerMatchDataCanned().stats(STATS).betStopsInfo(betstopInfo).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);
    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(STATS);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);
    when(playerLiveRatingFilterMock.hasLineUpChangeEventHappened(any(), any())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_2_ID).build())
      .isPlaying(false)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    var subOffEventId = SUB_OFF.getStatisticName() + PLAYER_2_ID_STR;
    var relatedSubOnEventId = SUB_ON.getStatisticName() + PLAYER_1_ID_STR;
    MatchDataFeed feed = matchDataFeedCanned()
      .playersData(playerDataDTOS)
      .fixtureStatus(LIVE)
      .isSnapshot(false)
      .build();

    var subOffCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(subOffEventId)
      .relatedEventId(relatedSubOnEventId)
      .event(SUB_OFF)
      .entityId(PLAYER_2_ID_STR)
      .build();
    var subOnCachedMatchEvent = PlayerMatchEventDTOCanned
      .playerMatchEventDTOCanned()
      .eventId(relatedSubOnEventId)
      .relatedEventId(subOffEventId)
      .event(SoccerMatchEvent.SUB_ON)
      .entityId(PLAYER_1_ID_STR)
      .build();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR)))
      .eventIdToPlayerMatchEvents(
        Map.of(subOffEventId, Set.of(subOffCachedMatchEvent), relatedSubOnEventId, Set.of(subOnCachedMatchEvent)))
      .build();
    var metadata = new KafkaMetadata(true, Map.of(SUBBED_IN_PLAYER_ID, subOnCachedMatchEvent.getEntityId()));

    // Act
    playerLiveRatingsProcessor.processFeed(feed, ongoingMatchData, false);

    // Assert
    verify(ratingsPublisherMock).publishAndStore(any(PlayerRating.class), eq(true), eq(metadata));
  }

  @Test
  public void whenATempShotIsReceived_thenTheEventAddedToTheProcessedEvents() {
    // Arrange
    var playerMatchData = playerMatchDataCanned()
      .playerId(PLAYER_1_ID_STR)
      .stats(STATS)
      .processedEvents(new HashSet<>())
      .build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);
    Map<String, Number> newStats = new HashMap<>(STATS);
    newStats.put(SHOT.getStatisticName(), 2);
    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(newStats);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    EntityEventDTO tempShotEvent = entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .eventType(SHOT)
      .eventId(SHOT.getStatisticName() + PLAYER_1_ID_STR)
      .build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixture(fixtureCanned().build())
      .fixtureStatus(LIVE)
      .playersData(List.of(buildPlayerDTO(emptyMap())))
      .feedPlayerMatchEvents(List.of(tempShotEvent))
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    assertThat(playerMatchData.getProcessedEvents()).contains(tempShotEvent.getEventId());
    verify(playerMatchDataCacheServiceMock).set(anyString(), anyString(), eq(playerMatchData));
  }

  @Test
  public void whenAnEmptyFeedIsReceived_andFixtureStatusIsNull_thenItIsNotProcessed() {
    // Arrange
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixtureStatus(null)
      .matchTimeMin(null)
      .playersData(List.of())
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    verifyNoMoreInteractions(ratingsPublisherMock);
  }

  @Test
  public void whenAFeedIsReceived_andOnlyUpdateCacheIsTrue_thenOnlyCacheIsUpdated() {
    // Arrange
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(STATS).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    List<PlayerDataDTO> playerDataDTOS = List.of(buildPlayerDTO(emptyMap()));

    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .fixtureStatus(LIVE)
      .matchTimeMin(10)
      .playersData(playerDataDTOS)
      .build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR, PLAYER_2_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, true);

    // Assert
    verify(playerMatchDataCacheServiceMock).set(eq(FIXTURE_ID), eq(PLAYER_1_ID_STR), any(PlayerMatchData.class));
    verifyNoMoreInteractions(ongoingMatchDataCacheServiceMock);
    verifyNoMoreInteractions(ratingsPublisherMock);
  }

  @Test
  public void whenStatsHaveChanged_ProcessorShouldPublishRatings() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(Map.of()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    Map<String, Number> expectedStoredStats = Map.of(MINS_PLAYED, 10);

    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(expectedStoredStats);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(true)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    MatchDataFeed parsedFeed = matchDataFeedCanned().playersData(playerDataDTOS).build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    verify(ratingsPublisherMock).publishAndStore(any(PlayerRating.class), eq(false), eq(EMPTY_METADATA));
  }

  @Test
  public void whenStatsHaveNotChanged_ProcessorShouldNotPublishRatings() {
    // Arrange
    Map<String, Number> stats = Map.of();
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).stats(stats).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    when(playerStatsEnricherMock.enrichLiveStats(any(), any(), any(), any(), any())).thenReturn(stats);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(true)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);

    MatchDataFeed parsedFeed = matchDataFeedCanned().playersData(playerDataDTOS).build();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    verifyNoMoreInteractions(ratingsPublisherMock);
  }

  @Test
  public void whenAnUnfilteredFeedIsReceived_thenCacheIsUpdated() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned()
      .playerId(PLAYER_1_ID_STR)
      .stats(Map.of(PASS.getStatisticName(), 1))
      .build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);
    when(playerLiveRatingFilterMock.shouldProcessPlayerStatusOnThePitch(any(), any(), anyString())).thenReturn(true);

    PlayerDataDTO playerDataDTO = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).build())
      .isPlaying(true)
      .build();
    List<PlayerDataDTO> playerDataDTOS = List.of(playerDataDTO);
    MatchDataFeed parsedFeed = matchDataFeedCanned().playersData(playerDataDTOS).build();

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR)))
      .build();

    // Act
    playerLiveRatingsProcessor.processFeed(parsedFeed, ongoingMatchData, false);

    // Assert
    verify(playerMatchDataCacheServiceMock).set(anyString(), anyString(), any());
    verify(ratingsPublisherMock).publishAndStore(ArgumentMatchers.<PlayerRating>argThat(argument -> argument.getStats()
      .isEmpty()), eq(false), eq(EMPTY_METADATA));
  }

  private static PlayerDataDTO buildPlayerDTO(Map<String, Number> stats) {
    return playerDataDTOCanned()
      .player(playerCanned().id(new ObjectId(PlayerCanned.PLAYER_1_ID_STR)).build())
      .isPlaying(true)
      .hasPlayed(true)
      .stats(stats)
      .timestamp(now())
      .build();
  }
}
