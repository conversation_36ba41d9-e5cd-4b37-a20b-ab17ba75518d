package com.wsf.dataingestor.unit.services.ratings;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ratings.MatchSettlementProcessor;
import com.wsf.dataingestor.services.ratings.SettlementValidatorFactory;
import com.wsf.dataingestor.services.ratings.player.PlayerFinalRatingsProcessor;
import com.wsf.dataingestor.services.ratings.team.TeamFinalRatingsProcessor;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class MatchSettlementProcessorTest extends TestWithMocks {

  @Mock
  private FixtureService fixtureService;

  @Mock
  private PlayerFinalRatingsProcessor playerFinalRatingsProcessor;

  @Mock
  private TeamFinalRatingsProcessor teamFinalRatingsProcessor;

  private MatchSettlementProcessor matchSettlementProcessor;

  @Before
  public void setup() {
    matchSettlementProcessor = new MatchSettlementProcessor(fixtureService, playerFinalRatingsProcessor,
      teamFinalRatingsProcessor, new SettlementValidatorFactory());
  }

  @Test
  public void whenProcessingOptaSettlement_andNoExtraTimeHappened_thenReturnTrue() {
    var matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .isFinalData(true)
      .extraTimeHappened(false)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();

    var result = matchSettlementProcessor.processFeed(matchDataFeed, false);

    assertThat(result).isTrue();
  }

  @Test
  public void whenProcessingOptaSettlement_andExtraTimeHappened_thenReturnTrue() {
    var matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .isFinalData(true)
      .extraTimeHappened(true)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();

    var result = matchSettlementProcessor.processFeed(matchDataFeed, false);

    assertThat(result).isTrue();
  }

  @Test
  public void whenProcessingSportmonksSettlement_andNoExtraTimeHappened_thenReturnTrue() {
    var matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .isFinalData(true)
      .extraTimeHappened(false)
      .provider(MatchDataFeed.FeedProvider.SPORTMONKS)
      .build();

    var result = matchSettlementProcessor.processFeed(matchDataFeed, false);

    assertThat(result).isTrue();
  }

  @Test
  public void whenProcessingSportmonksSettlement_andExtraTimeHappened_thenThrowAnException() {
    var matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .isFinalData(true)
      .extraTimeHappened(true)
      .provider(MatchDataFeed.FeedProvider.SPORTMONKS)
      .build();

    assertThrows(IllegalStateException.class, () -> matchSettlementProcessor.processFeed(matchDataFeed, false));
  }
}