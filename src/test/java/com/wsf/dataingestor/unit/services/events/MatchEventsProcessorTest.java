package com.wsf.dataingestor.unit.services.events;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.BetStartService;
import com.wsf.dataingestor.services.events.BetStopService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.dataingestor.services.events.MatchLocker;
import com.wsf.dataingestor.services.ratings.FixtureStartDataProcessor;
import com.wsf.dataingestor.services.ratings.LineUpService;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.util.concurrent.MoreExecutors.directExecutor;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.MatchEventDTOCanned.matchEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static java.time.Instant.now;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

public class MatchEventsProcessorTest extends TestWithMocks {

  private static final Fixture fixture = fixtureCanned().build();

  @Mock
  BetStopService betStopService;
  @Mock
  BetStartService betStartService;
  @Mock
  LineUpService lineUpService;
  @Mock
  LiveFixtureSummaryNotifier liveFixtureSummaryNotifierMock;
  @Mock
  FixtureStartDataProcessor fixtureStartDataProcessor;
  @Mock
  MetricsManager metricsManagerMock;
  @Mock
  MatchLocker matchLockerMock;

  private MatchEventsProcessor matchEventsProcessor;

  @Before
  public void setUp() throws Exception {
    var threadPoolService = new ThreadPoolService(directExecutor(), directExecutor(), metricsManagerMock);
    matchEventsProcessor = new MatchEventsProcessor(betStopService, betStartService, lineUpService,
      liveFixtureSummaryNotifierMock, fixtureStartDataProcessor, threadPoolService, matchLockerMock);
  }

  @Test
  public void whenProcessingMatchEvents_andThereAreTwoEventsInTheFeed_thenBothAreProcessed() {
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    MatchDataFeed.MatchEventDTO foul = new MatchDataFeed.MatchEventDTO("foul1", SoccerMatchEvent.FOUL, now());
    MatchDataFeed.MatchEventDTO dangerousFreeKick = new MatchDataFeed.MatchEventDTO("dangerousfreekick1",
      SoccerMatchEvent.DANGEROUS_FREEKICK, now());
    MatchDataFeed feed = MatchDataFeed
      .builder()
      .feedId("feedId")
      .fixture(fixture)
      .receivedTs(now())
      .matchEvents(List.of(foul, dangerousFreeKick))
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
    matchEventsProcessor.processMatchEvents(feed, ongoingMatchData);

    verify(betStopService, times(2)).processBetStop(eq(feed), any());
  }

  @Test
  public void whenALineUpEventIsReceived_aLiveFixtureSummaryDataIsPublished() {
    // Arrange
    var ongoingMatchData = ongoingMatchDataCanned().build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .matchEvents(List.of(matchEventDTOCanned().event(SoccerMatchEvent.LINEUPS_AVAILABLE).build()))
      .build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    verify(liveFixtureSummaryNotifierMock).sendLiveSummary(ongoingMatchData);
  }

  @Test
  public void whenALineUpEventIsReceived_andTheLineUpForTheTeamIsAlreadyInTheCache_thenTheEventIsIgnored() {
    // Arrange
    String teamId = "team1";
    Map<String, Set<String>> lineUp = new HashMap<>();
    lineUp.put(teamId, Set.of("player1"));
    var ongoingMatchData = ongoingMatchDataCanned().teamIdToLineUp(lineUp).build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .matchEvents(List.of(matchEventDTOCanned().event(SoccerMatchEvent.LINEUPS_AVAILABLE).teamId(teamId).build()))
      .playersData(List.of(playerDataDTOCanned().build()))
      .build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    assertEquals(lineUp, ongoingMatchData.getTeamIdToLineUp());
    verifyNoInteractions(lineUpService);
    verifyNoInteractions(liveFixtureSummaryNotifierMock);
  }

  @Test
  public void whenALineUpEventIsReceived_andTheLineUpForTheTeamIsNotInTheCache_thenTheEventIsProcessed() {
    // Arrange
    String team1Id = "team1";
    String team2Id = "team2";
    Map<String, Set<String>> lineUp = new HashMap<>();
    lineUp.put(team2Id, Set.of("player1"));
    var ongoingMatchData = ongoingMatchDataCanned().teamIdToLineUp(lineUp).build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .matchEvents(List.of(matchEventDTOCanned().event(SoccerMatchEvent.LINEUPS_AVAILABLE).teamId(team1Id).build()))
      .playersData(List.of(playerDataDTOCanned().build()))
      .build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    assertEquals(lineUp, ongoingMatchData.getTeamIdToLineUp());
    verify(lineUpService).updatePlayersPosition(matchDataFeed);
  }

  @Test
  public void whenALineUpEventIsReceived_andThereArePlayersFromBothTeamsInTheFeed_andTheLineUpForTheTeamIsNotInTheCache_thenTheEventIsProcessed() {
    // Arrange
    var ongoingMatchData = ongoingMatchDataCanned().teamIdToLineUp(new HashMap<>()).build();
    var playerTeam1 = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_1_ID).team(teamCanned().id(TEAM1_ID).build()).build())
      .isPlaying(true)
      .build();
    var playerTeam2 = playerDataDTOCanned()
      .player(playerCanned().id(PLAYER_2_ID).team(teamCanned().id(TEAM2_ID).build()).build())
      .isPlaying(true)
      .build();
    var lineUpEventTeam1 = matchEventDTOCanned()
      .event(SoccerMatchEvent.LINEUPS_AVAILABLE)
      .teamId(TEAM1_ID.toString())
      .build();
    var lineUpEventTeam2 = matchEventDTOCanned()
      .event(SoccerMatchEvent.LINEUPS_AVAILABLE)
      .teamId(TEAM2_ID.toString())
      .build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .matchEvents(List.of(lineUpEventTeam1, lineUpEventTeam2))
      .playersData(List.of(playerTeam1, playerTeam2))
      .build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    Assertions
      .assertThat(ongoingMatchData.getTeamIdToLineUp())
      .hasSize(2)
      .containsEntry(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR))
      .containsEntry(TEAM2_ID.toString(), Set.of(PLAYER_2_ID_STR));
    verify(lineUpService, times(2)).updatePlayersPosition(matchDataFeed);
  }

  @Test
  public void whenTheFirstHalfEnds_thenTheBetsAreStarted() {
    // Arrange
    var ongoingMatchData = ongoingMatchDataCanned().build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .matchEvents(List.of(matchEventDTOCanned().event(SoccerMatchEvent.BET_START).build()))
      .build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    verify(betStartService).processBetStart(matchDataFeed, matchDataFeed.getMatchEvents().get(0));
  }

  @Test
  public void whenAPossibleVarIsReceived_thenTheMatchIsLocked_andBetStopIsSent() {
    // Arrange
    String fixtureId = fixture.getIdAsString();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().matchId(fixtureId).build();
    MatchDataFeed.MatchEventDTO event = matchEventDTOCanned().event(SoccerMatchEvent.POSSIBLE_VAR).build();
    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(event)).build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    verify(matchLockerMock).lockMatch(fixtureId);
    verify(betStopService).processBetStop(matchDataFeed, event);
    verifyNoInteractions(betStartService);
  }

  @Test
  public void whenAVarCheckStartIsReceived_thenTheMatchIsUnlocked_andBetStopIsSent() {
    // Arrange
    String fixtureId = fixture.getIdAsString();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().matchId(fixtureId).build();
    MatchDataFeed.MatchEventDTO event = matchEventDTOCanned().event(SoccerMatchEvent.VAR_CHECK_START).build();
    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(event)).build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    verify(matchLockerMock).unlockMatch(fixtureId);
    verify(betStopService).processBetStop(matchDataFeed, event);
    verifyNoInteractions(betStartService);
  }

  @Test
  public void whenANoVarCheckIsReceived_thenTheMatchIsUnlocked() {
    // Arrange
    String fixtureId = fixture.getIdAsString();
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().matchId(fixtureId).build();
    MatchDataFeed.MatchEventDTO event = matchEventDTOCanned().event(SoccerMatchEvent.NO_VAR_CHECK).build();
    MatchDataFeed matchDataFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(event)).build();

    // Act
    matchEventsProcessor.processMatchEvents(matchDataFeed, ongoingMatchData);

    // Assert
    verify(matchLockerMock).unlockMatch(fixtureId);
    verifyNoInteractions(betStartService);
    verifyNoInteractions(betStopService);
  }

}
