package com.wsf.dataingestor.unit.services.events;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.google.common.util.concurrent.MoreExecutors;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.EntityMatchData.BetStopInfo;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.BetStopService;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.shared.TestUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.services.events.Utils.CARABAO_CUP_COMPETITION_ID;
import static com.wsf.dataingestor.services.events.Utils.PREMIER_LEAGUE_COMPETITION_ID;
import static com.wsf.dataingestor.shared.TestUtils.BETSTOP_EVENT_ID;
import static com.wsf.dataingestor.shared.TestUtils.buildCachedData;
import static com.wsf.dataingestor.shared.TestUtils.buildOptaFeed;
import static com.wsf.dataingestor.shared.TestUtils.buildRunningballFeed;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamMatchDataCanned.teamMatchDataCanned;
import static com.wsf.domain.soccer.SoccerMatchEvent.BET_STOP;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.MINUTES;
import static java.util.Comparator.comparing;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class BetStopServiceTest extends TestWithMocks {

  private static final ObjectId playerId1 = ObjectId.get();

  private static final ObjectId competitionId = ObjectId.get();
  private static final ObjectId tournamentId = ObjectId.get();
  private static final ObjectId homeTeamId = ObjectId.get();
  private static final ObjectId awayTeamId = ObjectId.get();

  private static final Player playerOnThePitch = PlayerCanned.playerCanned().id(playerId1).build();

  private static final ExecutorService executorService = MoreExecutors.newDirectExecutorService();
  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheMock;
  @Mock
  private PlayerMatchDataCacheService playerMatchDataCacheServiceMock;
  @Mock
  private TeamMatchDataCacheService teamMatchDataCacheServiceMock;
  @Mock
  private LiveMatchUtils liveMatchUtilsMock;
  @Mock
  private KafkaService kafkaServiceMock;
  @Mock
  private IndexCalculator soccerIndexCalculatorMock;
  @Mock
  private MetricsManager metricsManagerMock;

  private BetStopService betStopService;

  @Before
  public void setUp() throws Exception {
    Utils utils = new Utils(soccerIndexCalculatorMock, new MetricsManager(new SimpleMeterRegistry()));
    this.betStopService = new BetStopService(ongoingMatchDataCacheMock, playerMatchDataCacheServiceMock,
      teamMatchDataCacheServiceMock, liveMatchUtilsMock, kafkaServiceMock,
      new ThreadPoolService(executorService, executorService, metricsManagerMock), utils);

    List<Player> players = List.of(playerOnThePitch);
    when(liveMatchUtilsMock.findPlayersOnThePitch(any(), any())).thenReturn(players);
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchDataCanned().build());
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(
      teamMatchDataCanned().build());
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(
      teamMatchDataCanned().build());
  }

  @Test
  public void whenABetStopEventIsReceivedFromRunningball_andItIsNotPremierLeague_thenBetStopIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(competitionId.toString()));
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));
  }

  @Test
  public void whenABetStopEventIsReceivedFromOpta_andItIsPremierLeague_thenBetStopIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID));
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildOptaFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));
  }

  @Test
  public void whenABetStopEventIsReceivedFromOpta_andItIsCarabao_thenBetStopIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(CARABAO_CUP_COMPETITION_ID));
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildOptaFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));
  }

  @Test
  public void whenABetStopEventIsReceivedFromRunningball_andItIsPremierLeague_thenBetStopIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID));
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenABetStopEventIsReceivedFromRunningball_andItIsCarabao_thenBetStopIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(CARABAO_CUP_COMPETITION_ID));
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenABetStopEventIsReceivedFromOpta_andItIsNotPremierLeague_thenBetStopIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    betStopService.processBetStop(buildOptaFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenABetStopEventIsReceivedFromRunningball_andItIsNotPremierLeague_andBetStopEventHasAlreadyBeenProcessed_thenBetStopIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(
      TestUtils.buildCachedDataWithBetStopEvents(Set.of(BETSTOP_EVENT_ID)));

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  @Test
  public void whenABetStopEventForTeamsIsReceivedFromRunningball_andItIsNotPremierLeague_thenBetStopIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStopOnlyTeams(buildRunningballFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock, never()).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));
  }

  @Test
  public void whenABetStopEventIsReceived_andTheTeamAndPlayersAreInBetstopForTheSameEvent_thenBetStopIsIgnored() {
    TreeSet<BetStopInfo> betStopsInfo = buildBetStopInfos();
    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(betStopsInfo).build();
    TeamMatchData teamMatchData = teamMatchDataCanned().betStopsInfo(betStopsInfo).build();

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(teamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(teamMatchData);
    when(playerMatchDataCacheServiceMock.get(anyString(), eq(playerId1.toString()))).thenReturn(playerMatchData);

    Fixture fixture = buildFixture(competitionId);
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  @Test
  public void whenABetStopEventIsReceived_andTheFixtureIsNotLive_thenBetStopIsIgnored() {
    TreeSet<BetStopInfo> betStopsInfo = buildBetStopInfos();
    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(betStopsInfo).build();
    TeamMatchData teamMatchData = teamMatchDataCanned().betStopsInfo(betStopsInfo).build();

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(teamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(teamMatchData);
    when(playerMatchDataCacheServiceMock.get(anyString(), eq(playerId1.toString()))).thenReturn(playerMatchData);

    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(Fixture.FixtureStatus.PLAYED)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  @Test
  public void whenABetStopEventIsReceived_andTheFixtureMatchPeriodIsHalfTime_thenBetStopIsIgnored() {
    TreeSet<BetStopInfo> betStopsInfo = buildBetStopInfos();
    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(betStopsInfo).build();
    TeamMatchData teamMatchData = teamMatchDataCanned().betStopsInfo(betStopsInfo).build();

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(teamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(teamMatchData);
    when(playerMatchDataCacheServiceMock.get(anyString(), eq(playerId1.toString()))).thenReturn(playerMatchData);

    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(Fixture.FixtureStatus.LIVE)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    ongoingMatchData.setMatchPeriod(MatchPeriod.HALF_TIME);
    ongoingMatchData.setProcessedBetStopEventIds(Set.of());
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  @Test
  public void whenABetStopEventForTeamsIsReceived_andTheFixtureMatchPeriodIsHalfTime_thenBetStopIsIgnored() {
    TreeSet<BetStopInfo> betStopsInfo = buildBetStopInfos();
    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(betStopsInfo).build();
    TeamMatchData teamMatchData = teamMatchDataCanned().betStopsInfo(betStopsInfo).build();

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(teamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(teamMatchData);
    when(playerMatchDataCacheServiceMock.get(anyString(), eq(playerId1.toString()))).thenReturn(playerMatchData);

    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(Fixture.FixtureStatus.PLAYED)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP, now());

    OngoingMatchData ongoingMatchData = buildCachedData();
    ongoingMatchData.setMatchPeriod(MatchPeriod.HALF_TIME);
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStopOnlyTeams(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  @Test
  public void whenABetStopEventIsReceived_andThereWasAlreadyAMoreRecentBetstartProcessed_thenBetStopIsIgnored() {
    Instant betStartTimestamp = Instant.now();
    Instant betStopTimestamp = betStartTimestamp.minus(10, MINUTES);

    PlayerMatchData playerMatchData = playerMatchDataCanned().build();
    TeamMatchData teamMatchData = teamMatchDataCanned().build();

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(homeTeamId.toString()))).thenReturn(teamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(awayTeamId.toString()))).thenReturn(teamMatchData);
    when(playerMatchDataCacheServiceMock.get(anyString(), eq(playerId1.toString()))).thenReturn(playerMatchData);

    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(Fixture.FixtureStatus.PLAYED)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO(BETSTOP_EVENT_ID, BET_STOP,
      betStopTimestamp);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().latestBetStartTimestamp(betStartTimestamp).build();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    betStopService.processBetStop(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
  }

  private static TreeSet<BetStopInfo> buildBetStopInfos() {
    BetStopInfo goal = BetStopInfo.of("goal", BETSTOP_EVENT_ID, now());
    TreeSet<BetStopInfo> betStopsInfo = new TreeSet<>(comparing(BetStopInfo::getTimestamp));
    betStopsInfo.add(goal);
    return betStopsInfo;
  }

  private static Fixture buildFixture(ObjectId competitionId) {
    return FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(Fixture.FixtureStatus.LIVE)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();
  }
}
