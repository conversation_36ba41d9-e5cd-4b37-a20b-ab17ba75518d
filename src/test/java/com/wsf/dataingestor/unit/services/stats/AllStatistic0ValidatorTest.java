package com.wsf.dataingestor.unit.services.stats;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import com.wsf.dataingestor.services.stats.AllStatistic0Validator;
import com.wsf.dataingestor.shared.canned.CompetitionConfigCanned;
import com.wsf.domain.common.CompetitionConfig;

import static com.wsf.dataingestor.shared.canned.CompetitionConfigCanned.marketCanned;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static org.junit.Assert.assertThrows;

public class AllStatistic0ValidatorTest {

  private static final Set<String> STATS_TO_CHECK = Set.of(SHOT.getStatisticName(), SHOT_ON_GOAL.getStatisticName());

  @Test
  public void whenAllStatsAreZero_thenThrowException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of(Map.of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0),
      Map.of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0));
    List<CompetitionConfig.CompetitionMarketConfig> supportedMarket = List.of(marketCanned().propName("shots").build());
    CompetitionConfig competitionConfig = CompetitionConfigCanned
      .competitionConfigCanned()
      .markets(supportedMarket)
      .build();

    // Act
    assertThrows(IllegalArgumentException.class, () -> validator.validateStats(contestantsStats, competitionConfig));
  }

  @Test
  public void whenSomeStatsAreNonZero_thenNoException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of(Map.of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0),
      Map.of(SHOT.getStatisticName(), 1, SHOT_ON_GOAL.getStatisticName(), 0));
    List<CompetitionConfig.CompetitionMarketConfig> supportedMarket = List.of(marketCanned().propName("shots").build());
    CompetitionConfig competitionConfig = CompetitionConfigCanned
      .competitionConfigCanned()
      .markets(supportedMarket)
      .build();

    // Act
    validator.validateStats(contestantsStats, competitionConfig);
  }

  @Test
  public void whenStatsAreZero_butNoneIsSupported_thenNoException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of(Map.of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0),
      Map.of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0));
    List<CompetitionConfig.CompetitionMarketConfig> supportedMarket = List.of(
      marketCanned().propName("lina_souloukou").build());
    CompetitionConfig competitionConfig = CompetitionConfigCanned
      .competitionConfigCanned()
      .markets(supportedMarket)
      .build();

    // Act
    validator.validateStats(contestantsStats, competitionConfig);
  }

  @Test
  public void whenSomeStatsAreMissing_thenThrowException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of(Map.of(SHOT.getStatisticName(), 0), Map.of(SHOT_ON_GOAL.getStatisticName(), 0));
    List<CompetitionConfig.CompetitionMarketConfig> supportedMarket = List.of(marketCanned().propName("shots").build());
    CompetitionConfig competitionConfig = CompetitionConfigCanned
      .competitionConfigCanned()
      .markets(supportedMarket)
      .build();

    // Act
    assertThrows(IllegalArgumentException.class, () -> validator.validateStats(contestantsStats, competitionConfig));
  }

  @Test
  public void whenAllStatsAreMissing_thenThrowException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of(new HashMap<>(), new HashMap<>());
    List<CompetitionConfig.CompetitionMarketConfig> supportedMarket = List.of(marketCanned().propName("shots").build());
    CompetitionConfig competitionConfig = CompetitionConfigCanned
      .competitionConfigCanned()
      .markets(supportedMarket)
      .build();

    // Act
    assertThrows(IllegalArgumentException.class, () -> validator.validateStats(contestantsStats, competitionConfig));
  }

  @Test
  public void whenNoContestatStats_thenThrowException() {
    // Arrange
    AllStatistic0Validator validator = new AllStatistic0Validator(STATS_TO_CHECK);
    List<Map<String, Number>> contestantsStats = List.of();
    CompetitionConfig competitionConfig = CompetitionConfigCanned.competitionConfigCanned().build();

    // Act
    assertThrows(IllegalArgumentException.class, () -> validator.validateStats(contestantsStats, competitionConfig));
  }
}
