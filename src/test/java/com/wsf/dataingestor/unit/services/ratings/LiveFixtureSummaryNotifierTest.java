package com.wsf.dataingestor.unit.services.ratings;

import java.util.Map;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;
import com.wsf.dataingestor.services.ratings.FixtureSummaryPublisher;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.kafka.domain.FixtureStatus;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;

public class LiveFixtureSummaryNotifierTest extends TestWithMocks {

  LiveFixtureSummaryNotifier liveFixtureSummaryNotifier;

  @Mock
  FixtureSummaryPublisher fixtureSummaryPublisher;
  @Mock
  FixtureSummaryRetriever fixtureSummaryRetriever;

  @Before
  public void init() {
    liveFixtureSummaryNotifier = new LiveFixtureSummaryNotifier(fixtureSummaryPublisher, fixtureSummaryRetriever);
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreNoEvents_thenSendFixtureSummaryDataWithNoEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var ongoingMatchData = OngoingMatchDataCanned.ongoingMatchDataCanned().matchId(fixtureId).build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getEvents()).isEmpty();
      assertThat(fixtureSummary.getContestantStats()).isEmpty();

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreNoValidEvents_thenSendFixtureSummaryDataWithNoEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";
    var subOnPlayerMatchEvent = OngoingMatchDataCanned.createPlayerMatchEvent().eventId(eventId).event(SHOT).build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(subOnPlayerMatchEvent)))
      .matchId(fixtureId)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getEvents()).isEmpty();
      assertThat(fixtureSummary.getContestantStats()).isEmpty();

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreSubOffEvents_thenSendFixtureSummaryWithEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var subOffPlayerMatchEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(SUB_OFF)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(subOffPlayerMatchEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(SUB_OFF.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreGoalEvents_thenSendFixtureSummaryWithEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var playerGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(GOAL)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(playerGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(GOAL.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreSubOnEvents_thenSendFixtureSummaryWithEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var playerGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(SUB_ON)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(playerGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(SUB_ON.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreYellowCardsEvents_thenSendFixtureSummaryWithEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var playerGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(YELLOW_CARD)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(playerGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(YELLOW_CARD.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereAreRedCardsEvents_thenSendFixtureSummaryWithEventsToKafka() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var playerGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(RED_CARD)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(playerGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(RED_CARD.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

  @Test
  public void whenProcessingOngoingMatchData_ifThereIsAnOwnGoal_thenItIsDiscarded() {
    //Arrange
    var fixtureId = "fixtureId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var ownGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(GOAL)
      .ignore(true)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(ownGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents()).isEmpty();

      return true;
    }));
  }

  @Test
  public void ifThereAreAnOwnGoalAndAGoal_theGoalScorerIsSettledAsOne() {
    //Arrange
    var fixtureId = "fixtureId";
    var ownGoalEventId = "ownGoalEventId";
    var eventId = "eventId";

    String entityId = PlayerCanned.DEFAULT_ID.toString();
    String ownGoalScorerId = PlayerCanned.PLAYER_2_ID.toString();
    String teamId = TeamCanned.TEAM1_ID.toString();
    var ownGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(ownGoalEventId)
      .entityId(ownGoalScorerId)
      .teamId(teamId)
      .event(GOAL)
      .ignore(true)
      .timeMin(1)
      .build();
    var playerGoalEvent = OngoingMatchDataCanned
      .createPlayerMatchEvent()
      .eventId(eventId)
      .entityId(entityId)
      .teamId(teamId)
      .event(GOAL)
      .timeMin(5)
      .build();
    var ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(Map.of(eventId, Set.of(ownGoalEvent, playerGoalEvent)))
      .matchId(fixtureId)
      .fixtureStatus(LIVE)
      .build();

    //Act
    liveFixtureSummaryNotifier.sendLiveSummary(ongoingMatchData);

    //Assert
    Set<String> validEvents = Set.of(SUB_OFF.getStatisticName(), SUB_ON.getStatisticName(), GOAL.getStatisticName(),
      ASSIST_GOAL.getStatisticName(), CORNER.getStatisticName(), YELLOW_CARD.getStatisticName(),
      RED_CARD.getStatisticName());
    verify(fixtureSummaryPublisher).send(argThat(fixtureSummary -> {
      assertThat(fixtureSummary.getFixtureId()).isEqualTo(fixtureId);
      assertThat(fixtureSummary.getIsFinal()).isFalse();
      assertThat(fixtureSummary.getSupportedEventTypes()).isEqualTo(validEvents);
      assertThat(fixtureSummary.getFixtureStatus()).isEqualTo(FixtureStatus.LIVE);
      assertThat(fixtureSummary.getContestantStats()).isEmpty();
      assertThat(fixtureSummary.getEvents())
        .hasSize(1)
        .first()
        .satisfies(event -> {
          assertThat(event.getEventId()).isEqualTo(eventId);
          assertThat(event.getContestantId()).isEqualTo(entityId);
          assertThat(event.getParentContestantId()).isEqualTo(teamId);
          assertThat(event.getEventType()).isEqualTo(GOAL.getStatisticName());
          assertThat(event.getMatchTime()).isEqualTo(5);
        });

      return true;
    }));
  }

}
