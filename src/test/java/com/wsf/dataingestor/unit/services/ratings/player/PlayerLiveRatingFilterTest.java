package com.wsf.dataingestor.unit.services.ratings.player;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.ratings.player.PlayerLiveRatingFilter;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Player;

import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.goalMadePlayerEvent;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.redCardPlayerEvent;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.subOffPlayerEvent;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.PlayerDataDTOCanned.playerDataDTOCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class PlayerLiveRatingFilterTest extends TestWithMocks {
  PlayerLiveRatingFilter playerLiveRatingFilter;
  @Mock
  private PlayerMatchDataCacheService playerMatchDataCacheServiceMock;
  @Mock
  private PlayerService playerServiceMock;

  @Before
  public void setUp() throws Exception {
    playerLiveRatingFilter = new PlayerLiveRatingFilter(playerMatchDataCacheServiceMock, playerServiceMock);
  }

  @Test
  public void whenThePlayerIsInTheLineup_heIsConsideredActive() {
    // Arrange
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_1_ID_STR)))
      .build();

    MatchDataFeed parsedFeed = matchDataFeedCanned().build();

    // Act
    boolean playerActive = playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, ongoingMatchData,
      PLAYER_1_ID_STR);

    // Assert
    assertThat(playerActive).isTrue();
  }

  @Test
  public void whenThePlayerReceivedARedCardInTheCurrentFeed_heIsConsideredActive() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .build();

    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .feedPlayerMatchEvents(List.of(redCardPlayerEvent().entityId(PLAYER_1_ID_STR).build()))
      .playersData(List.of(playerDataDTOCanned().player(playerCanned().id(PLAYER_1_ID).build()).build()))
      .build();

    // Act
    boolean playerActive = playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, ongoingMatchData,
      PLAYER_1_ID_STR);

    // Assert
    assertThat(playerActive).isTrue();
  }

  @Test
  public void whenThePlayerHasBeenSubstitutedInTheCurrentFeed_heIsConsideredActive() {
    // Arrange
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(PLAYER_1_ID_STR).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .build();

    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .feedPlayerMatchEvents(List.of(subOffPlayerEvent().entityId(PLAYER_1_ID_STR).build()))
      .playersData(List.of(playerDataDTOCanned().player(playerCanned().id(PLAYER_1_ID).build()).build()))
      .build();

    // Act
    boolean playerActive = playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, ongoingMatchData,
      PLAYER_1_ID_STR);

    // Assert
    assertThat(playerActive).isTrue();
  }

  @Test
  public void whenThePlayerAlreadyGotSubstituted_heIsConsideredInactive() {
    // Arrange
    EntityEventDTO substitution = subOffPlayerEvent().entityId(PLAYER_1_ID_STR).build();
    PlayerMatchData playerCachedData = playerMatchDataCanned()
      .playerId(PLAYER_1_ID_STR)
      .processedEvents(Set.of(substitution.getEventId()))
      .build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .build();

    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .feedPlayerMatchEvents(List.of(substitution))
      .playersData(List.of(playerDataDTOCanned().player(playerCanned().id(PLAYER_1_ID).build()).build()))
      .build();

    // Act
    boolean playerActive = playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, ongoingMatchData,
      PLAYER_1_ID_STR);

    // Assert
    assertThat(playerActive).isFalse();
  }

  @Test
  public void whenThePlayerAlreadyReceivedARedCard_heIsConsideredInactive() {
    // Arrange
    EntityEventDTO redCard = redCardPlayerEvent().entityId(PLAYER_1_ID_STR).build();
    PlayerMatchData playerCachedData = playerMatchDataCanned()
      .playerId(PLAYER_1_ID_STR)
      .processedEvents(Set.of(redCard.getEventId()))
      .build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned()
      .teamIdToLineUp(Map.of(TEAM1_ID.toString(), Set.of(PLAYER_2_ID_STR)))
      .build();

    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .feedPlayerMatchEvents(List.of(redCard))
      .playersData(List.of(playerDataDTOCanned().player(playerCanned().id(PLAYER_1_ID).build()).build()))
      .build();

    // Act
    boolean playerActive = playerLiveRatingFilter.shouldProcessPlayerStatusOnThePitch(parsedFeed, ongoingMatchData,
      PLAYER_1_ID_STR);

    // Assert
    assertThat(playerActive).isFalse();
  }

  @Test
  public void whenAPlayerReceivesARedCard_thenTheLineUpChangeIsDetected() {
    // Arrange
    Player player = playerCanned().id(PLAYER_1_ID).build();
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(player.getIdAsString()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    EntityEventDTO subOffPlayerEvent = subOffPlayerEvent().entityId(player.getIdAsString()).build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(subOffPlayerEvent))
      .build();

    // Act
    boolean shouldPublishRatings = playerLiveRatingFilter.hasLineUpChangeEventHappened(parsedFeed, player);

    // Assert
    assertThat(shouldPublishRatings).isTrue();
  }

  @Test
  public void whenAPlayerGetsEjected_thenTheLineUpChangeIsDetected() {
    // Arrange
    Player player = playerCanned().id(PLAYER_1_ID).build();
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(player.getIdAsString()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    EntityEventDTO redCardEvent = redCardPlayerEvent().entityId(player.getIdAsString()).build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(redCardEvent))
      .build();

    // Act
    boolean hasLineUpChangeEventHappened = playerLiveRatingFilter.hasLineUpChangeEventHappened(parsedFeed, player);

    // Assert
    assertThat(hasLineUpChangeEventHappened).isTrue();
  }

  @Test
  public void whenNoRelevantEventHappens_thenNoLineUpChangeIsDetected() {
    // Arrange
    Player player = playerCanned().id(PLAYER_1_ID).build();
    PlayerMatchData playerCachedData = playerMatchDataCanned().playerId(player.getIdAsString()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerCachedData);

    EntityEventDTO goalMadeEvent = goalMadePlayerEvent().entityId(player.getIdAsString()).build();
    MatchDataFeed parsedFeed = matchDataFeedCanned()
      .playersData(List.of(playerDataDTOCanned().player(player).build()))
      .feedPlayerMatchEvents(List.of(goalMadeEvent))
      .build();

    // Act
    boolean hasLineUpChangeEventHappened = playerLiveRatingFilter.hasLineUpChangeEventHappened(parsedFeed, player);

    // Assert
    assertThat(hasLineUpChangeEventHappened).isFalse();
  }

}
