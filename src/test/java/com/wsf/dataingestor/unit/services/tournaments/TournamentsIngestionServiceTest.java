package com.wsf.dataingestor.unit.services.tournaments;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.services.tournaments.TournamentsIngestionService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.ExternalIds;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.shared.canned.CompetitionCanned.competitionCanned;
import static com.wsf.dataingestor.shared.canned.CurrentTournamentFeedCanned.currentTournamentFeedCanned;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class TournamentsIngestionServiceTest extends TestWithMocks {

  private static final String EXTERNAL_SEASON_ID = "2124";

  @Mock
  TournamentService tournamentServiceMock;
  @Mock
  PlayerService playerServiceMock;
  @Mock
  FeedsConfigService feedsConfigService;

  TournamentsIngestionService ingestionService;

  @Before
  public void setUp() throws Exception {
    ingestionService = new TournamentsIngestionService(tournamentServiceMock, playerServiceMock, feedsConfigService);
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andWsfTournamentIsAlreadyInTheDb_andWeAreProcessingTheFirstTournamentForTheFirstTime_thenWeShouldAddTheExternalIdToTheWsfTournament() {
    Tournament existingWsfTournament = tournamentCanned().build();
    when(tournamentServiceMock.findByCompetitionIdAndYear(anyString(), anyString())).thenReturn(
      List.of(existingWsfTournament));
    when(tournamentServiceMock.buildExternalProviderIdFieldName(anyString())).thenReturn("externalIds.sportmonksIds");
    when(feedsConfigService.getTournamentExternalIds(any())).thenReturn(Set.of());

    CurrentTournamentFeed feed = buildFeed(null, "2022-2023");
    feed.getCompetition().setExternalIds(new ExternalIds(Set.of("sm1", "sm2"), Set.of(), null, null));
    ingestionService.processTournamentFeed(List.of(feed));

    verifyRolloverNotCalled();
    Set<String> expectedExternalIds = Set.of(feed.getExternalSeasonId());
    verify(tournamentServiceMock).update(existingWsfTournament.getIdAsString(),
      Map.of("externalIds.sportmonksIds", expectedExternalIds, "active", true, "current", true));
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andWsfTournamentIsAlreadyInTheDb_andWeAreProcessingTheFirstTournamentForTheNTime_thenWeShouldDoNothing() {
    Tournament existingWsfTournament = tournamentCanned().build();

    CurrentTournamentFeed feed = buildFeed(existingWsfTournament, "2022-2023");
    ingestionService.processTournamentFeed(List.of(feed));

    verifyNoInteractions(tournamentServiceMock);
    verifyNoInteractions(playerServiceMock);
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andWsfTournamentIsAlreadyInTheDb_andWeAreProcessingTheSecondTournamentForTheFirstTime_thenWeShouldAddTheExternalIdToTheWsfTournament() {
    Tournament existingWsfTournament = tournamentCanned().build();
    when(feedsConfigService.getTournamentExternalIds(any())).thenReturn(Set.of("firstTournament"));
    when(tournamentServiceMock.findByCompetitionIdAndYear(anyString(), anyString())).thenReturn(
      List.of(existingWsfTournament));
    when(tournamentServiceMock.buildExternalProviderIdFieldName(anyString())).thenReturn("externalIds.sportmonksIds");

    CurrentTournamentFeed feed = buildFeed(null, "2022-2023");
    ingestionService.processTournamentFeed(List.of(feed));

    verifyRolloverNotCalled();
    Set<String> expectedExternalIds = Set.of("firstTournament", feed.getExternalSeasonId());
    verify(tournamentServiceMock).update(existingWsfTournament.getIdAsString(),
      Map.of("externalIds.sportmonksIds", expectedExternalIds, "active", true, "current", true));
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andWsfTournamentIsAlreadyInTheDb_andWeAreProcessingTheSecondTournamentForTheNTime_thenWeShouldDoNothing() {
    Tournament existingWsfTournament = tournamentCanned().build();

    CurrentTournamentFeed feed = buildFeed(existingWsfTournament, "2022-2023");
    ingestionService.processTournamentFeed(List.of(feed));

    verifyNoInteractions(tournamentServiceMock);
    verifyNoInteractions(playerServiceMock);
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andProviderSendNewTournamentForTheNewYear_andStartDateIsIn3Days_andWsfTournamentIsNotInTheDb_thenWeSetOldTournamentsToInactive_andWeSendTheExceptionToExpectWyscoutRollover() {
    when(tournamentServiceMock.findByCompetitionIdAndYear(anyString(), anyString())).thenReturn(List.of());
    when(tournamentServiceMock.findByIsActiveTrue(anyString())).thenReturn(tournamentCanned().build());

    CurrentTournamentFeed feed = currentTournamentFeedCanned()
      .existingTournament(null)
      .year("2022-2023")
      .startDate(Instant.now().plus(3, ChronoUnit.DAYS))
      .build();

    assertThrows(IllegalStateException.class, () -> ingestionService.processTournamentFeed(List.of(feed)));
    verifyRolloverCalled();
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andProviderSendNewTournamentForTheNewYear_andTheStartDateIsOver10DaysInTheFuture_andWsfTournamentIsNotInTheDb_thenWeSetOldTournamentsToInactive_andWeDoNotSendTheExceptionToExpectWyscoutRollover() {
    when(tournamentServiceMock.findByCompetitionIdAndYear(anyString(), anyString())).thenReturn(List.of());
    when(tournamentServiceMock.findByIsActiveTrue(anyString())).thenReturn(tournamentCanned().build());

    CurrentTournamentFeed feed = currentTournamentFeedCanned()
      .existingTournament(null)
      .year("2022-2023")
      .startDate(Instant.now().plus(15, ChronoUnit.DAYS))
      .build();

    ingestionService.processTournamentFeed(List.of(feed));
    verifyRolloverCalled();
  }

  @Test
  public void whenTheTournamentIsHandledWithMultipleExternalIds_andProviderSendNewTournamentForTheNewYear_andWsfTournamentIsAlreadyInTheDb_andThereArentAnyExternalIds_thenWeSetOldTournamentsToInactive_andWeShouldAddTheExternalIdToTheWsfTournament() {
    Tournament existingWsfTournament = tournamentCanned().build();
    when(tournamentServiceMock.findByCompetitionIdAndYear(anyString(), anyString())).thenReturn(
      List.of(existingWsfTournament));
    when(tournamentServiceMock.findByIsActiveTrue(anyString())).thenReturn(tournamentCanned().build());
    when(tournamentServiceMock.buildExternalProviderIdFieldName(anyString())).thenReturn("externalIds.sportmonksIds");
    when(feedsConfigService.getTournamentExternalIds(any())).thenReturn(Set.of());

    CurrentTournamentFeed feed = buildFeed(null, "2022-2023");
    feed.getCompetition().setExternalIds(new ExternalIds(Set.of("sm1", "sm2"), Set.of(), null, null));
    ingestionService.processTournamentFeed(List.of(feed));

    verifyRolloverCalled();
    Set<String> expectedExternalIds = Set.of(feed.getExternalSeasonId());
    verify(tournamentServiceMock).update(existingWsfTournament.getIdAsString(),
      Map.of("externalIds.sportmonksIds", expectedExternalIds, "active", true, "current", true));
  }

  @Test
  public void whenTheTournamentIsHandledWithOneExternalId_andProviderSendsNoCurrentTournament_andWsfTournamentIsAlreadyInTheDb_thenWeSetOldTournamentsToInactive() {
    // Arrange
    when(tournamentServiceMock.findByIsActiveTrue(anyString())).thenReturn(tournamentCanned().build());

    CurrentTournamentFeed feed = currentTournamentFeedCanned()
      .competition(competitionCanned().externalIds(new ExternalIds(Set.of("sm1"), Set.of(), null, null)).build())
      .existingTournament(null)
      .year(null)
      .externalSeasonId(null)
      .provider(CurrentTournamentFeed.Provider.SPORTMONKS)
      .feedId("feedId")
      .build();

    // Act
    ingestionService.processTournamentFeed(List.of(feed));

    // Assert
    verifyRolloverCalled();
  }

  private void verifyRolloverNotCalled() {
    verify(tournamentServiceMock, never()).updateToInactive(any());
    verify(playerServiceMock, never()).disablePlayersForTournament(any(), any());
  }

  private void verifyRolloverCalled() {
    verify(tournamentServiceMock).updateToInactive(any());
    verify(playerServiceMock).disablePlayersForTournament(any(), any());
  }

  private static CurrentTournamentFeed buildFeed(Tournament existingTournament, String year) {
    return CurrentTournamentFeed
      .builder()
      .competition(competitionCanned().build())
      .existingTournament(existingTournament)
      .year(year)
      .externalSeasonId(EXTERNAL_SEASON_ID)
      .provider(CurrentTournamentFeed.Provider.SPORTMONKS)
      .feedId("feedId")
      .build();
  }
}
