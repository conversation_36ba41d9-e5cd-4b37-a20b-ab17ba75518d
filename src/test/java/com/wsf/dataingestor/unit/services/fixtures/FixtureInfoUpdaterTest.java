package com.wsf.dataingestor.unit.services.fixtures;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Provider;
import com.wsf.domain.common.Team;

import static com.wsf.dataingestor.mappers.ProviderMapper.mapProvider;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.BETRADAR_FIXTURE_ID;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.FixtureDTOCanned.fixtureDTOCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.unit.services.fixtures.FixturesIngestionServiceTest.PROVIDER_FIELD;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED;
import static com.wsf.domain.common.Fixture.DB_FIELD_IS_LIVE_ENABLED_BY;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.CONNECTED;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static com.wsf.domain.common.Fixture.FixtureStatus.CANCELLED;
import static com.wsf.domain.common.Fixture.FixtureStatus.FIXTURE;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.common.Fixture.FixtureStatus.PLAYED;
import static com.wsf.domain.common.Fixture.FixtureStatus.SUSPENDED;
import static com.wsf.domain.common.Fixture.Relation.SECOND_LEG;
import static com.wsf.domain.common.Fixture.Relation.SINGLE;
import static com.wsf.domain.common.Provider.OPTA;
import static com.wsf.domain.common.Provider.RUNNINGBALL;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;
import static java.time.Instant.now;
import static java.util.Calendar.SEPTEMBER;
import static java.util.Objects.nonNull;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.assertArg;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class FixtureInfoUpdaterTest extends TestWithMocks {

  @Mock
  private FixtureService fixtureServiceMock;
  @Mock
  private FixturesRetriever fixturesRetrieverMock;
  @Mock
  private KafkaService kafkaService;

  private FixtureInfoUpdater fixtureInfoUpdater;


  @Before
  public void setUp() throws Exception {
    when(fixturesRetrieverMock.getExternalFixtureFieldName()).thenReturn(PROVIDER_FIELD);
    fixtureInfoUpdater = new FixtureInfoUpdater(fixtureServiceMock, kafkaService);
  }

  @Test
  public void ifTheMatchIsFixtureAndWeAreConnectedAndTheMatchFeedIsLive_thenTheFixtureIsUpdatedInTheDb() {
    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(now())
      .status(FIXTURE)
      .processStatus(CONNECTED)
      .build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, LIVE, newFixtureDate, "");

    HashMap<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("date", newFixtureDate);
    expectedResults.put("status", LIVE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheMatchIsLiveAndWeAreConnectedAndTheMatchFeedIsFixture_ThenTheFixtureIsUpdatedInTheDb() {
    Instant fixtureDate = now();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(fixtureDate)
      .status(LIVE)
      .processStatus(CONNECTED)
      .build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, FIXTURE, fixtureDate, "");

    HashMap<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("date", fixtureDate);
    expectedResults.put("status", FIXTURE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheMatchIsLiveAndItGetsSuspended_andTheDateDoesNotChange_ThenTheFixtureIsUpdatedInTheDb() {
    Calendar calendar = Calendar.getInstance();
    calendar.set(2024, SEPTEMBER, 28, 15, 0);
    Instant fixtureDate = calendar.toInstant();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(fixtureDate)
      .status(LIVE)
      .processStatus(CONNECTED)
      .build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, SUSPENDED, fixtureDate, "");

    HashMap<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("date", fixtureDate);
    expectedResults.put("wasSuspended", true);
    expectedResults.put("status", SUSPENDED);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheMatchIsFixtureAndWeAreNotConnectedAndTheMatchFeedIsLive_ThenTheFixtureIsUpdatedInTheDb() {
    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);

    Fixture dbFixture = fixtureCanned().processStatus(null).date(now()).status(FIXTURE).processStatus(null).build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, LIVE, newFixtureDate, "");

    HashMap<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("date", newFixtureDate);
    expectedResults.put("status", LIVE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheMatchFeedIsLive_andFixtureIsMappedToBetradar_thenTheFixtureIsUpdatedInTheDb_butDateIsNotUpdated() {
    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(now())
      .status(LIVE)
      .processStatus(CONNECTED)
      .betradarFixtureId(BETRADAR_FIXTURE_ID)
      .build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, LIVE, newFixtureDate, "");

    HashMap<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("status", LIVE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheDateDidNotChange_allTheFieldsAreUpdatedAccordingly() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(3, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(dto.getTime())
      .status(FIXTURE)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dto.getTime(), true, false);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheDateDidNotChangeAndTheTeamIsMissing_thenFixtureIsNotUpdated() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(3, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(dto.getTime())
      .status(FIXTURE)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenThrow(
      new IllegalArgumentException("The db does not find it"));
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);

    assertThrows(IllegalArgumentException.class,
      () -> fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, ""));
    verifyNoInteractions(fixtureServiceMock);
  }

  @Test
  public void ifTheDateDidNotChange_andAFutureFixtureIsPlayedAndSettled_allTheFieldsAreUpdatedAccordingly() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .time(now().plus(3, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(dto.getTime())
      .status(PLAYED)
      .processStatus(SETTLED)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dto.getTime(), true, false);
    expectedResults.put("processStatus", null);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheDateDidNotChange_andAFutureFixtureIsCancelled_andInTheDbIsAlreadyCancelled_allTheFieldsAreUpdatedAccordingly_butTheProcessStatusIsNotUpdated() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.CANCELLED)
      .time(now().plus(3, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .active(true)
      .date(dto.getTime())
      .status(CANCELLED)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dto.getTime(), true, false);
    expectedResults.put("status", CANCELLED);

    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheFixtureIsLiveOnTheFeed_andTheSystemIsNotConnected_thenScheduleLiveProcessAndUpdateTheFixture() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.LIVE)
      .time(now().minus(1, ChronoUnit.HOURS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(now().plus(5, ChronoUnit.DAYS))
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .status(FIXTURE)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("active", true);
    expectedResults.put("date", dto.getTime());
    expectedResults.put("status", LIVE);
    expectedResults.put("leg", SINGLE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifFixtureIsCancelled_thenWeUpdateStatusDateAndProcessStatus() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.CANCELLED)
      .time(now().plus(5, ChronoUnit.DAYS))
      .isActive(false)
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(now().plus(3, ChronoUnit.DAYS))
      .status(FIXTURE)
      .processStatus(SETTLED)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("active", false);
    expectedResults.put("status", CANCELLED);
    expectedResults.put("date", dto.getTime());
    expectedResults.put("leg", SINGLE);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), assertArg(map -> {
      assertThat(map).containsAllEntriesOf(expectedResults);
    }));
  }


  @Test
  public void ifFixtureIsSuspended_thenWeUpdateStatusDateAndProcessStatus() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.SUSPENDED)
      .leg(FixtureDTO.Relation.SECOND_LEG)
      .time(now().plus(5, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(now().plus(3, ChronoUnit.DAYS))
      .status(FIXTURE)
      .processStatus(SETTLED)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("active", true);
    expectedResults.put("status", SUSPENDED);
    expectedResults.put("date", dto.getTime());
    expectedResults.put("wasSuspended", true);
    expectedResults.put("leg", Fixture.Relation.SECOND_LEG);

    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));

  }

  @Test
  public void ifFixtureIsPostponedAndHadToBePlayedInTheNextWeek_thenWeUpdateStatusDateAndProcessStatus() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(7, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .active(true)
      .date(now().plus(3, ChronoUnit.DAYS))
      .status(FIXTURE)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);

    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("active", true);
    expectedResults.put("status", FIXTURE);
    expectedResults.put("date", dto.getTime());

    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), assertArg(updateMap -> {
      assertThat(updateMap).containsAllEntriesOf(expectedResults);
    }));
  }

  @Test
  public void ifFixtureIsPostponed_andHadToBePlayedInOneMonth_thenWeUpdateStatusAndDate() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(50, ChronoUnit.DAYS))
      .leg(null)
      .build();

    Fixture dbFixture = fixtureCanned()
      .active(true)
      .date(now().plus(30, ChronoUnit.DAYS))
      .status(FIXTURE)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);

    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = new HashMap<>();
    expectedResults.put("active", true);
    expectedResults.put("status", FIXTURE);
    expectedResults.put("date", dto.getTime());
    expectedResults.put("processStatus", null);
    expectedResults.put("leg", SINGLE);

    verify(fixtureServiceMock).updateFixture(eq(dbFixture), assertArg(updateMap -> {
      assertThat(updateMap).containsAllEntriesOf(expectedResults);
    }));
  }

  @Test
  public void ifTheDateDidNotChange_andFixtureIsMappedToBetradar_thenSkipUpdateOfDateAndTeams() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(3, ChronoUnit.DAYS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(dto.getTime())
      .status(FIXTURE)
      .optaFixtureId(dto.getExternalFixtureId())
      .betradarFixtureId(BETRADAR_FIXTURE_ID)
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dbFixture.getDate(), true, true);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheDateChanged_andFixtureIsMappedToBetradar_actAsDateHasNotBeenUpdated_thenSkipUpdateOfDateAndTeams() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.LIVE)
      .time(now().minus(1, ChronoUnit.HOURS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(now().plus(5, ChronoUnit.DAYS))
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .status(FIXTURE)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .betradarFixtureId(BETRADAR_FIXTURE_ID)
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dbFixture.getDate(), false, true);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void ifTheDateOfAnActiveFixtureChanged_andFixtureIsNotActiveInTheDB_thenTheExternalIdIsAddedToTheFixture() {
    // Arrange
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(1, ChronoUnit.HOURS))
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(now().plus(5, ChronoUnit.DAYS))
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .status(FIXTURE)
      .processStatus(null)
      .optaFixtureId(dto.getExternalFixtureId())
      .betradarFixtureId(null)
      .active(false)
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, false, "");

    // Assert
    Map<String, Object> expectedResults = buildResults(dto, homeTeam, awayTeam, dto.getTime(), false, false);
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), eq(expectedResults));
  }

  @Test
  public void whenTheFeedFixtureIsLiveEnabled_andTheCompetitionConfigIsLiveEnabled_thenTheFixtureIsLiveEnabled() {
    // Arrange
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).build();
    var fixtureDTO = fixtureDTOCanned().isLiveSupported(true).time(fixture.getDate()).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, true, "");

    // Assertions
    verify(fixtureServiceMock).updateFixture(any(), assertArg(map -> {
      assertThat(map).containsEntry(DB_FIELD_IS_LIVE_ENABLED, true);
    }));
  }

  @Test
  public void whenTheFeedFixtureIsNotLiveEnabled_andTheCompetitionConfigIsLiveEnabled_thenTheFixtureIsNotLiveEnabled() {
    // Arrange
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).build();
    var fixtureDTO = fixtureDTOCanned().isLiveSupported(false).time(fixture.getDate()).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, true, "");

    // Assertions
    verify(fixtureServiceMock).updateFixture(any(), assertArg(map -> {
      assertThat(map).containsEntry(DB_FIELD_IS_LIVE_ENABLED, false);
    }));
  }

  @Test
  public void whenTheFeedFixtureILiveEnabled_andTheCompetitionConfigIsNotLiveEnabled_thenTheFixtureIsNotLiveEnabled() {
    // Arrange
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).build();
    var fixtureDTO = fixtureDTOCanned().isLiveSupported(true).time(fixture.getDate()).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, false, "");

    // Assertions
    verify(fixtureServiceMock).updateFixture(any(), assertArg(map -> {
      assertThat(map).containsEntry(DB_FIELD_IS_LIVE_ENABLED, false);
    }));
  }

  @Test
  public void whenTheFixtureHasChangedDateDuringLiveFlow_thenSendFixtureChangeIntoKafka() {
    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);
    Fixture dbFixture = fixtureCanned().date(now()).status(FIXTURE).processStatus(CONNECTED).build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, LIVE, newFixtureDate, "");

    verify(kafkaService, times(1)).sendFixtureChange(any(), any());
  }

  @Test
  public void whenTheFixtureHasNotChangedDateOrStatusDuringLiveFlow_thenSendFixtureChangeIntoKafka() {
    Instant date = now().minus(1, ChronoUnit.HOURS);
    Fixture dbFixture = fixtureCanned().date(date).status(FIXTURE).processStatus(CONNECTED).build();

    fixtureInfoUpdater.processFixtureInfoDuringLive(dbFixture, FIXTURE, date, "");

    verify(kafkaService, times(0)).sendFixtureChange(any(), any());
  }

  @Test
  public void whenTheFixtureHasBetradarIdDuringIngestionFlow_thenFixtureChangeIsNeverSentIntoKafka() {
    // Arrange
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).date(now()).betradarFixtureId("123456abc").build();

    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);
    var fixtureDTO = fixtureDTOCanned().isLiveSupported(true).time(newFixtureDate).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, false, "");

    // Assertions
    verify(kafkaService, times(0)).sendFixtureChange(any(), any());
  }

  @Test
  public void whenTheFixtureHasChangedDateDuringIngestionFlow_thenSendFixtureChangeIntoKafka() {
    // Arrange
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).date(now()).build();

    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);
    var fixtureDTO = fixtureDTOCanned().isLiveSupported(true).time(newFixtureDate).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, false, "");

    // Assertions
    verify(kafkaService, times(1)).sendFixtureChange(any(), any());
  }

  @Test
  public void whenTheFixtureHasChangedHomeTeamDuringIngestionFlow_thenSendFixtureChangeIntoKafka() {
    // Arrange
    var homeTeam = TeamCanned.teamCanned().optaId("123abc456sbc").build();
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).date(now()).homeTeam(homeTeam).build();

    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);
    var fixtureDTO = fixtureDTOCanned()
      .isLiveSupported(true)
      .time(newFixtureDate)
      .externalHomeTeamId("123foobar")
      .build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, false, "");

    // Assertions
    verify(kafkaService, times(1)).sendFixtureChange(any(), any());
  }

  @Test
  public void whenTheFixtureHasChangedAwayTeamDuringIngestionFlow_thenSendFixtureChangeIntoKafka() {
    // Arrange
    var awayTeam = TeamCanned.teamCanned().optaId("123abc456sbc").build();
    var fixture = FixtureCanned.fixtureCanned().isLiveEnabled(false).date(now()).awayTeam(awayTeam).build();

    Instant newFixtureDate = now().minus(1, ChronoUnit.HOURS);
    var fixtureDTO = fixtureDTOCanned()
      .isLiveSupported(true)
      .time(newFixtureDate)
      .externalAwayTeamId("123foobar")
      .build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(fixture, fixtureDTO, fixturesRetrieverMock, false, "");

    // Assertions
    verify(kafkaService, times(1)).sendFixtureChange(any(), any());
  }

  @Test
  public void ifTheMatchIsNotSupportedByRunningBall_thenLiveDoesNotGetEnabled() {
    //Arrange
    Map<Provider, Boolean> allProvidersLiveDisabled = Map.of(OPTA, false, RUNNINGBALL, false);
    Map<Provider, Boolean> optaLiveEnabled = Map.of(OPTA, true, RUNNINGBALL, false);

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .status(FIXTURE)
      .processStatus(CONNECTED)
      .isLiveEnabled(false)
      .isLiveEnabledBy(allProvidersLiveDisabled)
      .build();

    var fixtureDTO = fixtureDTOCanned().time(dbFixture.getDate()).isLiveSupported(true).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, fixtureDTO, fixturesRetrieverMock, true, "");

    //Assert
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), ArgumentMatchers.argThat(
      fieldsMap -> fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED).equals(FALSE) &&
        fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED_BY).equals(optaLiveEnabled)));
  }

  @Test
  public void oldFixturesAreCompliant() {
    //Arrange
    Map<Provider, Boolean> optaLiveEnabled = Map.of(OPTA, true);

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .status(FIXTURE)
      .processStatus(CONNECTED)
      .isLiveEnabled(false)
      .build();

    var fixtureDTO = fixtureDTOCanned().time(dbFixture.getDate()).isLiveSupported(true).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, fixtureDTO, fixturesRetrieverMock, true, "");

    //Assert
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), ArgumentMatchers.argThat(
      fieldsMap -> fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED).equals(TRUE) &&
        fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED_BY).equals(optaLiveEnabled)));
  }

  @Test
  public void oldLiveSupportedFixturesAreCompliant() {
    //Arrange
    Map<Provider, Boolean> optaLiveDisabled = Map.of(OPTA, false);

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .status(FIXTURE)
      .processStatus(CONNECTED)
      .isLiveEnabled(true)
      .build();

    var fixtureDTO = fixtureDTOCanned().time(dbFixture.getDate()).isLiveSupported(false).build();

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, fixtureDTO, fixturesRetrieverMock, true, "");

    //Assert
    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), ArgumentMatchers.argThat(
      fieldsMap -> fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED).equals(FALSE) &&
        fieldsMap.get(DB_FIELD_IS_LIVE_ENABLED_BY).equals(optaLiveDisabled)));
  }

  @Test
  public void whenTheLegIsNullOnTheFeed_butIsPopulatedOnTheDb_thenItIsNotUpdated() {
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .time(now().plus(3, ChronoUnit.DAYS))
      .leg(null)
      .build();

    Fixture dbFixture = fixtureCanned()
      .date(dto.getTime())
      .status(PLAYED)
      .processStatus(SETTLED)
      .optaFixtureId(dto.getExternalFixtureId())
      .leg(SECOND_LEG)
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    verify(fixtureServiceMock, times(1)).updateFixture(eq(dbFixture), assertArg(map -> {
      assertThat(map).doesNotContainKey("leg");
    }));
  }

  private static Map<String, Object> buildResults(FixtureDTO fixtureDTO, Team homeTeam, Team awayTeam,
                                                  Instant fixtureDate, boolean isLiveEnabledForCompetition,
                                                  boolean isBetradarFixture) {
    Map<String, Object> res = new HashMap<>();
    res.put("active", true);
    res.put("optaFixtureId", fixtureDTO.getExternalFixtureId());
    res.put("leg", SINGLE);
    if (!isBetradarFixture) {
      res.put("homeTeam", homeTeam);
      res.put("awayTeam", awayTeam);
      res.put("date", fixtureDate);
    }

    res.put("isNeutralVenue", fixtureDTO.getIsNeutralVenue());
    res.put("canGoExtraTime", fixtureDTO.getCanGoExtraTime());

    if (nonNull(fixtureDTO.getIsLiveSupported())) {
      var isLiveEnabled = isLiveEnabledForCompetition && fixtureDTO.getIsLiveSupported();
      res.put("isLiveEnabled", isLiveEnabled);
      Map<Provider, Boolean> isLiveEnabledBy = Map.of(mapProvider(fixtureDTO.getProvider()), isLiveEnabled);
      res.put("isLiveEnabledBy", isLiveEnabledBy);
    }

    if (fixtureDate.isAfter(now()) && fixtureDTO.getFixtureStatus() != MatchDataFeed.FeedFixtureStatus.CANCELLED &&
      fixtureDTO.getFixtureStatus() != MatchDataFeed.FeedFixtureStatus.POSTPONED) {
      res.put("status", FIXTURE);
    }
    return res;
  }

  @Test
  public void whenTheFixtureBecameActive_thenSendFixtureChangeIntoKafka() {
    // Arrange
    Team homeTeam = TeamCanned.teamCanned().id(TEAM1_ID)
      .name("Real Grisignano").optaId("opta1").build();
    Team awayTeam = TeamCanned.teamCanned().id(TEAM2_ID)
      .name("Roma").optaId("opta2").build();

    var dto = fixtureDTOCanned()
      .externalHomeTeamId(homeTeam.getOptaId())
      .externalAwayTeamId(awayTeam.getOptaId())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(now().plus(3, ChronoUnit.DAYS))
      .isActive(true)
      .build();

    Fixture dbFixture = fixtureCanned()
      .processStatus(null)
      .date(dto.getTime())
      .status(FIXTURE)
      .active(false)
      .optaFixtureId(dto.getExternalFixtureId())
      .build();

    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalHomeTeamId()), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(dto.getExternalAwayTeamId()), any())).thenReturn(awayTeam);

    // Act
    fixtureInfoUpdater.processFixtureInfoDuringIngestion(dbFixture, dto, fixturesRetrieverMock, true, "");

    // Assert
    verify(kafkaService, times(1)).sendFixtureChange(any(), any());
  }
}
