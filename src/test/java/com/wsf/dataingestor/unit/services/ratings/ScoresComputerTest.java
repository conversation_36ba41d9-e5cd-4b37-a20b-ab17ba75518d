package com.wsf.dataingestor.unit.services.ratings;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.ratings.ScoresComputer;
import com.wsf.dataingestor.services.ratings.ScoresComputer.Score;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;

import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static org.junit.Assert.assertEquals;

public class ScoresComputerTest {

  @Test
  public void whenTheFeedContainsAnOwnGoal_thenItCountsAsAGoalForTheOpponentTeam() {
    MatchDataFeed matchDataFeed = MatchDataFeedCanned.createMatchDataFeedWithOwnGoal().build();
    Score score = ScoresComputer.computeScoreFromFeed(matchDataFeed);
    assertEquals(1, score.getHomeScore());
    assertEquals(1, score.getAwayScore());
  }

  @Test
  public void whenTheFeedContainsAGoal_thenItCountsAsAGoalForTheCorrectTeam() {
    MatchDataFeed matchDataFeed = MatchDataFeedCanned.matchDataFeedCanned().build();
    Score score = ScoresComputer.computeScoreFromFeed(matchDataFeed);
    assertEquals(1, score.getHomeScore());
    assertEquals(0, score.getAwayScore());
  }

  @Test
  public void whenTheFeedContainsNoGoals_thenTheScoreIsZeroDraw() {
    MatchDataFeed matchDataFeed = MatchDataFeedCanned
      .matchDataFeedCanned()
      .aggregatedPlayerMatchEvents(List.of())
      .build();
    Score score = ScoresComputer.computeScoreFromFeed(matchDataFeed);
    assertEquals(0, score.getHomeScore());
    assertEquals(0, score.getAwayScore());
  }

  @Test
  public void whenTheOngoingMatchDataContainsNoGoals_thenTheScoreIsZeroDraw() {
    Fixture fixture = FixtureCanned.defaultFixture();
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned.ongoingMatchDataCanned().build();
    ScoresComputer.computeScoreFromCache(fixture, ongoingMatchData);

    assertEquals(0, ongoingMatchData.getHomeScore());
    assertEquals(0, ongoingMatchData.getAwayScore());
  }

  @Test
  public void whenTheOngoingMatchDataContainsAGoal_thenItCountsAsAGoalForTheCorrectTeam() {
    Fixture fixture = FixtureCanned.defaultFixture();
    OngoingMatchData.PlayerMatchEventDTO playerMatchEventDTO = OngoingMatchData.PlayerMatchEventDTO
      .builder().eventId("eventId").teamId(TeamCanned.TEAM1_ID.toString()).event(GOAL).build();
    Map<String, Set<OngoingMatchData.PlayerMatchEventDTO>> eventIdToPlayerMatchEvents = Map.of(
      playerMatchEventDTO.getEventId(), Set.of(playerMatchEventDTO));
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(eventIdToPlayerMatchEvents)
      .build();
    Score score = ScoresComputer.computeScoreFromCache(fixture, ongoingMatchData);

    assertEquals(1, score.getHomeScore());
    assertEquals(0, score.getAwayScore());
  }

  @Test
  public void whenTheOngoingMatchDataContainsAnOwnGoal_thenItCountsAsAGoalForTheOpponentTeam() {
    Fixture fixture = FixtureCanned.defaultFixture();
    OngoingMatchData.PlayerMatchEventDTO playerMatchEventDTO = OngoingMatchData.PlayerMatchEventDTO
      .builder().eventId("eventId").teamId(TeamCanned.TEAM1_ID.toString()).event(GOAL).build();
    OngoingMatchData.PlayerMatchEventDTO playerMatchEventDTOOwnGoal = OngoingMatchData.PlayerMatchEventDTO
      .builder().eventId("eventId2").teamId(TeamCanned.TEAM1_ID.toString()).event(GOAL).ignore(true).build();
    Map<String, Set<OngoingMatchData.PlayerMatchEventDTO>> eventIdToPlayerMatchEvents = Map.of(
      playerMatchEventDTO.getEventId(), Set.of(playerMatchEventDTO), playerMatchEventDTOOwnGoal.getEventId(),
      Set.of(playerMatchEventDTOOwnGoal));
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .eventIdToPlayerMatchEvents(eventIdToPlayerMatchEvents)
      .build();
    Score score = ScoresComputer.computeScoreFromCache(fixture, ongoingMatchData);

    assertEquals(1, score.getHomeScore());
    assertEquals(1, score.getAwayScore());
  }
}
