package com.wsf.dataingestor.unit.services.fixtures;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.crons.FixturesWatcherUpdater;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater;
import com.wsf.dataingestor.services.fixtures.FixtureRelationshipService;
import com.wsf.dataingestor.services.fixtures.FixturesIngestionService;
import com.wsf.dataingestor.services.fixtures.FixturesRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionConfigCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Stage;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TournamentConfig;
import com.wsf.repository.common.TournamentConfigRepository;
import com.wsf.repository.customer.CompetitionConfigRepository;

import static com.wsf.dataingestor.models.FixtureDTO.Provider.SPORTMONKS;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.FixtureDTOCanned.fixtureDTOCanned;
import static com.wsf.dataingestor.shared.canned.FixturesFeedCanned.fixturesFeedCanned;
import static com.wsf.dataingestor.shared.canned.StagesCanned.DEFAULT_STAGE1_ID;
import static com.wsf.dataingestor.shared.canned.StagesCanned.DEFAULT_STAGE2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.dataingestor.shared.canned.TournamentConfigCanned.tournamentConfigCanned;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.HOURS;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FixturesIngestionServiceTest extends TestWithMocks {

  public static final String PROVIDER_FIELD = "optaFixtureId";
  public static final String OPTA_TEAM_ID_1 = "optaId1";
  public static final String OPTA_TEAM_ID_2 = "optaId2";
  public static final String SPORTMONKS_TEAM_ID_1 = "sportmonksId1";
  public static final String SPORTMONKS_TEAM_ID_2 = "sportmonksId2";
  MetricsManager metrics = new MetricsManager(new SimpleMeterRegistry());
  @Mock
  private FixturesRetriever fixturesRetrieverMock;
  @Mock
  private FixtureRelationshipService fixtureRelationshipService;
  @Mock
  private FixtureInfoUpdater fixtureInfoUpdater;
  @Mock
  private FixturesWatcherUpdater fixturesWatcherUpdater;
  @Mock
  private CompetitionConfigRepository competitionConfigRepository;
  @Mock
  private TournamentConfigRepository tournamentConfigRepository;
  private FixturesIngestionService fixtureIngestionService;

  @Before
  public void setUp() throws Exception {
    when(fixturesRetrieverMock.getExternalFixtureFieldName()).thenReturn(PROVIDER_FIELD);

    fixtureIngestionService = new FixturesIngestionService(metrics, fixtureRelationshipService, fixtureInfoUpdater,
      fixturesWatcherUpdater, competitionConfigRepository, tournamentConfigRepository);
  }

  @Test
  public void whenANewMatchIsRetrieved_andAnExceptionIsThrown_thenItIsIgnored() {
    doThrow(new IllegalArgumentException("the errors"))
      .when(fixtureInfoUpdater).processFixtureInfoDuringIngestion(any(), any(), any(), any(), any());

    FixturesFeed fixturesFeed = fixturesFeedCanned().fixtures(List.of(fixtureDTOCanned().time(now()).build())).build();

    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(
      CompetitionConfigCanned.competitionConfigCannedWithLiveOdds().build());

    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);

    double count = metrics.MATCHES_FEED_PARSING_ERROR.count();
    assertEquals(1, (int) count);
    verify(fixtureRelationshipService).storeRelations(any(), eq(fixturesFeed.getTournament()),
      eq(fixturesFeed.getFixtures()), eq(List.of()));
  }

  @Test
  public void whenAFixtureIsRetrieved_andTheProviderIsNotOpta_butTheStageIsNew_thenTheFixtureIsIgnored_andAnAlertToMapTheStageIsRaised() throws FixtureNotFoundException {
    Team homeTeam = defaultTeam(ObjectId.get());
    Team awayTeam = defaultTeam(ObjectId.get());
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned()
        .externalFixtureId("sportmonksId")
        .externalHomeTeamId(SPORTMONKS_TEAM_ID_1)
        .externalAwayTeamId(SPORTMONKS_TEAM_ID_2)
        .externalStageName("stage")
        .externalStageId("stageId")
        .provider(SPORTMONKS)
        .build()))
      .build();
    TournamentConfig.SupportedStage supportedStage = TournamentConfig.SupportedStage
      .builder()
      .name("stage").id(DEFAULT_STAGE1_ID).build();
    TournamentConfig tournamentConfig = tournamentConfigCanned().supportedStages(List.of(supportedStage)).build();

    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_1), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_2), any())).thenReturn(awayTeam);
    when(fixturesRetrieverMock.shouldProcessStages()).thenReturn(true);
    when(fixturesRetrieverMock.findStage(any(), any())).thenThrow(new IllegalArgumentException());
    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(
      CompetitionConfigCanned.competitionConfigCannedWithLiveOdds().build());
    when(tournamentConfigRepository.findByTournamentId(anyString())).thenReturn(Optional.of(tournamentConfig));

    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);
    verify(fixturesRetrieverMock, never()).findFixture(any(), any());
    verify(fixtureInfoUpdater, never()).processFixtureInfoDuringIngestion(any(), any(), any(), any(), anyString());
    assertEquals(1, metrics.MATCHES_FEED_UNMAPPED_STAGE.count(), 0.0);
  }

  @Test
  public void whenAFixtureIsRetrieved_andTheProviderIsNotOpta_butTheStageIsNotSupported_thenItIsIgnored() throws FixtureNotFoundException {
    Team homeTeam = defaultTeam(ObjectId.get());
    Team awayTeam = defaultTeam(ObjectId.get());
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned()
        .externalFixtureId("sportmonksId")
        .externalHomeTeamId(SPORTMONKS_TEAM_ID_1)
        .externalAwayTeamId(SPORTMONKS_TEAM_ID_2)
        .externalStageName("stage")
        .externalStageId("stageId")
        .provider(SPORTMONKS)
        .build()))
      .build();
    TournamentConfig.SupportedStage supportedStage = TournamentConfig.SupportedStage
      .builder()
      .name("stage").id(DEFAULT_STAGE1_ID).build();
    TournamentConfig tournamentConfig = tournamentConfigCanned().supportedStages(List.of(supportedStage)).build();

    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_1), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_2), any())).thenReturn(awayTeam);
    when(fixturesRetrieverMock.shouldProcessStages()).thenReturn(true);
    when(fixturesRetrieverMock.findStage(any(), any())).thenReturn(Stage
      .builder().id(DEFAULT_STAGE2_ID).build());
    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(
      CompetitionConfigCanned.competitionConfigCannedWithLiveOdds().build());
    when(tournamentConfigRepository.findByTournamentId(anyString())).thenReturn(Optional.of(tournamentConfig));

    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);
    verify(fixturesRetrieverMock, never()).findFixture(any(), any());
    verify(fixtureInfoUpdater, never()).processFixtureInfoDuringIngestion(any(), any(), any(), any(), anyString());
    assertEquals(0, metrics.MATCHES_FEED_UNMAPPED_STAGE.count(), 0.0);
  }

  @Test
  public void whenAFixtureIsRetrieved_andTheProviderIsNotOpta_andTheStageIsSupported_thenItIsProcessed() throws FixtureNotFoundException {
    Team homeTeam = defaultTeam(ObjectId.get());
    Team awayTeam = defaultTeam(ObjectId.get());
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned()
        .externalFixtureId("sportmonksId")
        .externalHomeTeamId(SPORTMONKS_TEAM_ID_1)
        .externalAwayTeamId(SPORTMONKS_TEAM_ID_2)
        .externalStageName("stage")
        .externalStageId("stageId")
        .provider(SPORTMONKS)
        .build()))
      .build();
    TournamentConfig.SupportedStage supportedStage = TournamentConfig.SupportedStage
      .builder()
      .name("stage").id(DEFAULT_STAGE1_ID).build();
    TournamentConfig tournamentConfig = tournamentConfigCanned().supportedStages(List.of(supportedStage)).build();

    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_1), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(SPORTMONKS_TEAM_ID_2), any())).thenReturn(awayTeam);
    Fixture existingFixture = fixtureCanned().build();
    when(fixturesRetrieverMock.findFixture(any(), any())).thenReturn(existingFixture);
    when(fixturesRetrieverMock.shouldProcessStages()).thenReturn(true);
    when(fixturesRetrieverMock.findStage(any(), any())).thenReturn(Stage
      .builder().id(DEFAULT_STAGE1_ID).build());
    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(
      CompetitionConfigCanned.competitionConfigCannedWithLiveOdds().build());
    when(tournamentConfigRepository.findByTournamentId(anyString())).thenReturn(Optional.of(tournamentConfig));

    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);
    verify(fixtureInfoUpdater).processFixtureInfoDuringIngestion(existingFixture, fixturesFeed.getFixtures().get(0),
      fixturesRetrieverMock, true, "feedId");
    assertEquals(0, metrics.MATCHES_FEED_UNMAPPED_STAGE.count(), 0.0);
  }

  @Test
  public void whenAFixtureIsRetrieved_andTheProviderIsOpta_thenItIsProcessed_regardlessOfTheStage() throws FixtureNotFoundException {
    Team homeTeam = defaultTeam(ObjectId.get());
    Team awayTeam = defaultTeam(ObjectId.get());
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned().externalStageName("stage").externalStageId("stageId").build()))
      .build();
    TournamentConfig.SupportedStage supportedStage = TournamentConfig.SupportedStage
      .builder()
      .name("stage").id(DEFAULT_STAGE1_ID).build();
    TournamentConfig tournamentConfig = tournamentConfigCanned().supportedStages(List.of(supportedStage)).build();

    when(fixturesRetrieverMock.findTeam(eq(OPTA_TEAM_ID_1), any())).thenReturn(homeTeam);
    when(fixturesRetrieverMock.findTeam(eq(OPTA_TEAM_ID_2), any())).thenReturn(awayTeam);
    Fixture existingFixture = fixtureCanned().build();
    when(fixturesRetrieverMock.findFixture(any(), any())).thenReturn(existingFixture);
    when(fixturesRetrieverMock.shouldProcessStages()).thenReturn(false);
    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(
      CompetitionConfigCanned.competitionConfigCannedWithLiveOdds().build());
    when(tournamentConfigRepository.findByTournamentId(anyString())).thenReturn(Optional.of(tournamentConfig));

    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);
    verify(fixtureInfoUpdater).processFixtureInfoDuringIngestion(existingFixture, fixturesFeed.getFixtures().get(0),
      fixturesRetrieverMock, true, "feedId");

  }

  @Test
  public void whenAFixtureIsNotFound_andTheFixtureTimeIsOverCompetitionLeadIn_thenItIsIgnored() throws FixtureNotFoundException {
    // Arrange
    int leadInHours = 24;
    int twoDays = 48;
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned().time(now().plus(leadInHours + twoDays + 1, HOURS)).build()))
      .build();

    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(CompetitionConfigCanned
      .competitionConfigCanned()
      .oddsGeneration(
        CompetitionConfigCanned.OddsGenerationCanned.oddsGenerationCanned().leadInHours(leadInHours).build())
      .build());
    when(fixturesRetrieverMock.findFixture(any(), any())).thenThrow(new FixtureNotFoundException("fixture not found"));

    // Act
    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);

    // Assert
    verify(fixtureInfoUpdater, never()).processFixtureInfoDuringIngestion(any(), any(), any(), any(), anyString());
    assertEquals(0, metrics.MATCHES_FEED_UNMAPPED_FIXTURES.count(), 0.0);
  }

  @Test
  public void whenAFixtureIsNotFound_andTheFixtureTimeIsWithinCompetitionLeadIn_thenItIsAlerted() throws FixtureNotFoundException {
    // Arrange
    int leadInHours = 24;
    int twoDays = 48;
    FixturesFeed fixturesFeed = fixturesFeedCanned()
      .fixtures(List.of(fixtureDTOCanned().time(now().plus(leadInHours + twoDays - 1, HOURS)).build()))
      .build();

    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(CompetitionConfigCanned
      .competitionConfigCanned()
      .oddsGeneration(
        CompetitionConfigCanned.OddsGenerationCanned.oddsGenerationCanned().leadInHours(leadInHours).build())
      .build());
    when(fixturesRetrieverMock.findFixture(any(), any())).thenThrow(new FixtureNotFoundException("fixture not found"));

    // Act
    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);

    // Assert
    assertEquals(1, metrics.MATCHES_FEED_UNMAPPED_FIXTURES.count(), 0.0);
  }

  @Test
  public void whenLeadInIsAbsent_anyFixtureIsAlerted() throws FixtureNotFoundException {
    // Arrange
    FixturesFeed fixturesFeed = fixturesFeedCanned().fixtures(List.of(fixtureDTOCanned().time(now()).build())).build();

    when(competitionConfigRepository.findByCompetitionId(any())).thenReturn(CompetitionConfigCanned
      .competitionConfigCanned()
      .oddsGeneration(CompetitionConfigCanned.OddsGenerationCanned.oddsGenerationCanned().leadInHours(null).build())
      .build());
    when(fixturesRetrieverMock.findFixture(any(), any())).thenThrow(new FixtureNotFoundException("fixture not found"));

    // Act
    fixtureIngestionService.processFixturesFeed(fixturesRetrieverMock, fixturesFeed);

    // Assert
    assertEquals(1, metrics.MATCHES_FEED_UNMAPPED_FIXTURES.count(), 0.0);
  }
}
