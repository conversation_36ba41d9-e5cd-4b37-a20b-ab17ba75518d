package com.wsf.dataingestor.unit.services.events;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import com.google.common.util.concurrent.MoreExecutors;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.TeamMatchDataCacheService;
import com.wsf.dataingestor.cache.models.EntityMatchData.BetStopInfo;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.PlayerMatchData;
import com.wsf.dataingestor.cache.models.TeamMatchData;
import com.wsf.dataingestor.index.IndexCalculator;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.LiveMatchUtils;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.BetStartService;
import com.wsf.dataingestor.services.events.MatchLocker;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.services.ratings.Utils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.TeamRating;

import static com.wsf.dataingestor.services.events.Utils.PREMIER_LEAGUE_COMPETITION_ID;
import static com.wsf.dataingestor.shared.TestUtils.BETSTOP_EVENT_ID;
import static com.wsf.dataingestor.shared.TestUtils.buildOptaFeed;
import static com.wsf.dataingestor.shared.TestUtils.buildRunningballFeed;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchDataCanned.playerMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamMatchDataCanned.teamMatchDataCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RESTART;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static java.time.Instant.now;
import static java.util.Comparator.comparing;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class BetStartServiceTest extends TestWithMocks {

  private static final ObjectId competitionId = ObjectId.get();
  private static final ObjectId tournamentId = ObjectId.get();
  private static final ObjectId homeTeamId = ObjectId.get();
  private static final ObjectId awayTeamId = ObjectId.get();

  private static final ObjectId playerId1 = ObjectId.get();
  private static final Player playerOnThePitch = PlayerCanned.playerCanned().id(playerId1).build();
  private static final ExecutorService executorService = MoreExecutors.newDirectExecutorService();
  private static final ScheduledExecutorService betStartScheduler = new DirectSchedulerExecutor();
  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheMock;
  @Mock
  private PlayerMatchDataCacheService playerMatchDataCacheServiceMock;
  @Mock
  private TeamMatchDataCacheService teamMatchDataCacheServiceMock;
  @Mock
  private KafkaService kafkaServiceMock;
  @Mock
  private FixtureService fixtureServiceMock;
  @Mock
  private FeedsConfigService feedsConfigServiceMock;
  @Mock
  private FixtureDataProcessor fixtureDataProcessorMock;
  @Mock
  private LiveMatchUtils liveMatchUtilsMock;
  @Mock
  private IndexCalculator soccerIndexCalculatorMock;
  @Mock
  private MetricsManager metricsManagerMock;
  @Mock
  private MatchLocker matchLockerMock;
  private BetStartService betStartService;

  @Before
  public void setUp() throws Exception {
    when(feedsConfigServiceMock.getFixtureDataProcessor(anyString())).thenReturn(fixtureDataProcessorMock);
    when(fixtureServiceMock.getFixture(anyString())).thenReturn(FixtureCanned.fixtureCanned().status(LIVE).build());
    Utils utils = new Utils(soccerIndexCalculatorMock, new MetricsManager(new SimpleMeterRegistry()));
    this.betStartService = new BetStartService(ongoingMatchDataCacheMock, playerMatchDataCacheServiceMock,
      teamMatchDataCacheServiceMock, fixtureServiceMock, kafkaServiceMock, liveMatchUtilsMock,
      new ThreadPoolService(executorService, executorService, metricsManagerMock), betStartScheduler, matchLockerMock,
      utils);

    List<Player> players = List.of(playerOnThePitch);
    when(liveMatchUtilsMock.findPlayersOnThePitch(any(), any())).thenReturn(players);
  }

  @Test
  public void whenBetStartIsProcessedFromRunningball_andItIsNotPremierLeague_thenBetStartIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();

    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    Instant betStartTimestamp = now();
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, betStartTimestamp);

    betStartService.processBetStart(buildRunningballFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));

    verifyLiveTeamRatings();

    verifyPlayerLiveRatings();

    verifyBetStopEventIsStoredInCache();
    verifyLatestBetStartEventTimestampIsStoredInCache(betStartTimestamp);
  }

  @Test
  public void whenBetStartIsProcessedFromRunningball_andItIsNotPremierLeague_andNoBetStopWasSet_thenBetStartIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    PlayerMatchData playerMatchData = playerMatchDataCanned().build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    TeamMatchData homeTeamMatchData = teamMatchDataCanned().build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().build();
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, now());

    betStartService.processBetStart(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(fixtureDataProcessorMock);
    verify(ongoingMatchDataCacheMock, never()).set(anyString(), any());
  }

  @Test
  public void whenBetStartIsProcessedFromOpta_andItIsPremierLeague_thenBetStartIsSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID));

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, now());

    betStartService.processBetStart(buildOptaFeed(fixture), matchEventDTO);

    verify(kafkaServiceMock).sendPlayerEventRating(any());

    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getHomeTeam().getIdAsString())));
    verify(kafkaServiceMock).sendTeamEventRating(
      argThat(teamEvent -> teamEvent.getTeamId().equals(fixture.getAwayTeam().getIdAsString())));

    verifyLiveTeamRatings();
    verifyPlayerLiveRatings();

    verifyBetStopEventIsStoredInCache();
  }

  @Test
  public void whenBetStartIsProcessedFromRunningball_andItIsPremierLeague_thenBetStartIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID));

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, now());

    betStartService.processBetStart(buildRunningballFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenBetStartIsProcessedFromOpta_andItIsNotPremierLeague_thenBetStartIsNotSentAsKafkaEvent() {
    Fixture fixture = buildFixture(competitionId);

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, now());

    betStartService.processBetStart(buildOptaFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenBetStartIsProcessed_andTheMatchIsFinished_thenBetStartIsNotSentAsKafkaEvent() {
    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .status(Fixture.FixtureStatus.PLAYED)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID)).build())
        .build())
      .build();

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, now());

    betStartService.processBetStart(buildOptaFeed(fixture), matchEventDTO);

    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  @Test
  public void whenMatchIsLocked_thenBetStartIsIgnored() {
    // Arrange
    Fixture fixture = buildFixture(competitionId);

    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheMock.get(anyString())).thenReturn(ongoingMatchData);

    PlayerMatchData playerMatchData = playerMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData homeTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();
    TeamMatchData awayTeamMatchData = teamMatchDataCanned().betStopsInfo(buildBetStopInfos()).build();

    when(playerMatchDataCacheServiceMock.get(anyString(), anyString())).thenReturn(playerMatchData);

    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getHomeTeam().getId().toString()))).thenReturn(
      homeTeamMatchData);
    when(teamMatchDataCacheServiceMock.get(anyString(), eq(fixture.getAwayTeam().getId().toString()))).thenReturn(
      awayTeamMatchData);

    when(matchLockerMock.isMatchLocked(fixture.getIdAsString())).thenReturn(true);

    // Act
    Instant betStartTimestamp = now();
    MatchDataFeed.MatchEventDTO matchEventDTO = new MatchDataFeed.MatchEventDTO("1", RESTART, betStartTimestamp);
    betStartService.processBetStart(buildRunningballFeed(fixture), matchEventDTO);

    // Assert
    verifyNoInteractions(kafkaServiceMock);
    verifyNoInteractions(ongoingMatchDataCacheMock);
  }

  private void verifyLatestBetStartEventTimestampIsStoredInCache(Instant betStartTimestamp) {
    ArgumentCaptor<OngoingMatchData> captor = ArgumentCaptor.forClass(OngoingMatchData.class);
    verify(ongoingMatchDataCacheMock, times(1)).mergeIfExists(anyString(), captor.capture(), anyBoolean());
    OngoingMatchData storedCachedData = captor.getValue();
    assertThat(storedCachedData.getLatestBetStartTimestamp(), is(betStartTimestamp));
  }

  private void verifyBetStopEventIsStoredInCache() {
    ArgumentCaptor<OngoingMatchData> captor = ArgumentCaptor.forClass(OngoingMatchData.class);
    verify(ongoingMatchDataCacheMock, times(1)).mergeIfExists(anyString(), captor.capture(), eq(false));
    OngoingMatchData storedCachedData = captor.getValue();
    assertThat(storedCachedData.getProcessedBetStopEventIds(), is(Set.of(BETSTOP_EVENT_ID)));
  }

  private void verifyPlayerLiveRatings() {
    ArgumentCaptor<PlayerRating> playerRatingArgumentCaptor = ArgumentCaptor.forClass(PlayerRating.class);
    verify(kafkaServiceMock).sendLivePlayerRating(playerRatingArgumentCaptor.capture(), eq(EMPTY_METADATA));
    PlayerRating playerRating = playerRatingArgumentCaptor.getValue();
    assertEquals(playerOnThePitch, playerRating.getPlayer());
  }

  private void verifyLiveTeamRatings() {
    ArgumentCaptor<TeamRating> teamRatingArgumentCaptor = ArgumentCaptor.forClass(TeamRating.class);
    verify(kafkaServiceMock, times(2)).sendLiveTeamRating(teamRatingArgumentCaptor.capture(), eq(EMPTY_METADATA));
    List<TeamRating> teamRatings = teamRatingArgumentCaptor.getAllValues();

    TeamRating homeTeamRating = teamRatings.get(0);
    assertEquals(homeTeamId, homeTeamRating.getTeam().getId());
    TeamRating awayTeamRating = teamRatings.get(1);
    assertEquals(awayTeamId, awayTeamRating.getTeam().getId());
  }

  private static Fixture buildFixture(ObjectId competitionId) {
    return FixtureCanned
      .fixtureCanned()
      .homeTeam(TeamCanned.teamCanned().id(homeTeamId).build())
      .awayTeam(TeamCanned.teamCanned().id(awayTeamId).build())
      .status(LIVE)
      .tournament(TournamentCanned
        .tournamentCanned()
        .id(tournamentId)
        .competition(CompetitionCanned.competitionCanned().id(competitionId).build())
        .build())
      .build();
  }

  private static TreeSet<BetStopInfo> buildBetStopInfos() {
    BetStopInfo betstop = BetStopInfo.of("betstop", BETSTOP_EVENT_ID, now());
    TreeSet<BetStopInfo> betStopsInfo = new TreeSet<>(comparing(BetStopInfo::getTimestamp));
    betStopsInfo.add(betstop);
    return betStopsInfo;
  }

  static class DirectSchedulerExecutor extends ScheduledThreadPoolExecutor {

    public DirectSchedulerExecutor() {
      super(1);
    }

    @Override
    public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
      command.run();
      return Mockito.mock(ScheduledFuture.class);
    }
  }

}
