package com.wsf.dataingestor.unit.opta.service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.repository.ContestantUnavailabilityRepository;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.service.ContestantUnavailabilityLogicService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.ContestantUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FeedContestantUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FeedUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_9_ID;
import static com.wsf.dataingestor.shared.canned.UnavailabilityCanned.unavailabilityCanned;
import static com.wsf.domain.common.ContestantUnavailability.UnavailabilityReason.SUSPENSION;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.AVAILABLE;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.UNAVAILABLE;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ContestantUnavailabilityLogicServiceTest extends TestWithMocks {

  @Mock
  private ContestantUnavailabilityRepository repository;

  private ContestantUnavailabilityLogicService contestantUnavailabilityLogicService;

  @Before
  public void init() {
    contestantUnavailabilityLogicService = new ContestantUnavailabilityLogicService(repository);
  }

  @Test
  public void whenThereAreNewContestantUnavailabilities_thenAddToDatabase_thanReturnDto() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();
    var player = PlayerCanned.playerCanned().build();

    var feedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(feedUnavailability))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of());

    //Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    verify(repository, times(1)).upsert(any());

    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(player.getIdAsString());
        assertThat(dto.action()).isEqualTo(UNAVAILABLE);
        assertThat(dto.isSuspended()).isTrue();
        assertThat(dto.isInjured()).isFalse();
      });
  }

  @Test
  public void whenThereIsANewContestantInjury_andTheInjuryEndDateIsOneDayAfterTheMatch_thenTheInjuryIsNotStored() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();
    var player = PlayerCanned.playerCanned().build();

    var feedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY)
      .startDate(Instant.now())
      .endDate(fixture.getDate().plus(1, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(feedUnavailability))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of());

    //Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    verify(repository, never()).upsert(any());
    verify(repository, never()).remove(any());

    assertThat(contestantUnavailabilityDtos).isEmpty();
  }

  @Test
  public void whenThereIsANewContestantSuspension_andTheSuspensionEndDateIsOneDayAfterTheMatch_thenTheSuspensionIsStored() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();
    var player = PlayerCanned.playerCanned().build();

    var feedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("Red card")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(fixture.getDate().plus(1, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(feedUnavailability))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of());

    //Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    verify(repository, times(1)).upsert(any());

    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(player.getIdAsString());
        assertThat(dto.action()).isEqualTo(UNAVAILABLE);
        assertThat(dto.isSuspended()).isTrue();
        assertThat(dto.isInjured()).isFalse();
      });
  }

  @Test
  public void whenInTheFeedThereIsNotAContestantPresentInDb_thenRemoveTheOneContainedInDb_thenReturnDto() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();

    var databaseContestantUnavailability = ContestantUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .contestantId(PLAYER_9_ID.toString()) // different id from the one in feedPlayer
      .fixtureId(fixture.getIdAsString())
      .unavailabilities(Set.of(unavailabilityCanned().reason(SUSPENSION).build()))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of(databaseContestantUnavailability));

    // Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of());

    // Assert
    verify(repository, times(1)).remove(any());

    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(PLAYER_9_ID.toString());
        assertThat(dto.action()).isEqualTo(AVAILABLE);
        assertThat(dto.isSuspended()).isFalse();
        assertThat(dto.isInjured()).isFalse();
      });
  }

  @Test
  public void whenInTheFeedThereIsANewUnavailabilityForAContestant_thenTheContestantIsAlsoUpdatedInDb_thenReturnDto() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();
    var player = PlayerCanned.playerCanned().build();

    var firstFeedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var secondFeedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke his shin-bone while breaking the opponent's shin-bone")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(firstFeedUnavailability, secondFeedUnavailability))
      .build();

    var databaseContestantUnavailability = ContestantUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .contestantId(player.getIdAsString())
      .fixtureId(fixture.getIdAsString())
      .unavailabilities(Set.of(unavailabilityCanned().reason(SUSPENSION).build()))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of(databaseContestantUnavailability));

    // Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    verify(repository, times(1)).upsert(any());

    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(player.getIdAsString());
        assertThat(dto.action()).isEqualTo(UNAVAILABLE);
        assertThat(dto.isSuspended()).isTrue();
        assertThat(dto.isInjured()).isTrue();
      });
  }

  @Test
  public void whenInNewFeedThereIsNotADifferenceForAContestantFromPrecedingFeed_thanReturnEmptyDtoList() {
    //Arrange
    var fixture = FixtureCanned.fixtureCanned().build();
    var player = PlayerCanned.playerCanned().build();
    var startDate = Instant.now();
    var endDate = Instant.now().plus(10, ChronoUnit.DAYS);
    var description = "He broke the opponent's shin-bone like there was not tomorrow";

    var firstFeedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description(description)
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(startDate)
      .endDate(endDate)
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(firstFeedUnavailability))
      .build();

    var databaseContestantUnavailability = ContestantUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .contestantId(player.getIdAsString())
      .fixtureId(fixture.getIdAsString())
      .unavailabilities(Set.of(unavailabilityCanned()
        .reason(SUSPENSION)
        .startDate(startDate)
        .endDate(endDate)
        .description(description)
        .build()))
      .build();

    when(repository.findByFixture(fixture.getIdAsString())).thenReturn(List.of(databaseContestantUnavailability));

    // Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    assertThat(contestantUnavailabilityDtos).isEmpty();
  }
}
