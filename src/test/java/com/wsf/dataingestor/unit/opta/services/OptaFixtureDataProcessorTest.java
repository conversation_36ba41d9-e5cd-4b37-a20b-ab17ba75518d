package com.wsf.dataingestor.unit.opta.services;

import java.time.Instant;
import java.util.List;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.exceptions.FeedException;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.services.OptaFixtureDataProcessor;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.events.HalfTimeNotifier;
import com.wsf.dataingestor.services.ratings.MatchFinalDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.HALF_TIME;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.SECONDS;
import static java.util.Collections.emptyList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class OptaFixtureDataProcessorTest extends TestWithMocks {

  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheServiceMock;

  @Mock
  private MA2Client matchDataPullClientMock;

  @Mock
  private OptaFixturePushDataRetriever matchPushDataRetrieverMock;

  @Mock
  private WebSocketManager webSocketManagerMock;

  @Mock
  private MatchLiveDataProcessor matchLiveDataProcessorMock;

  @Mock
  private MatchFinalDataProcessor matchFinalDataProcessorMock;

  @Mock
  private HalfTimeNotifier halfTimeNotifierMock;

  private OptaFixtureDataProcessor optaFixtureDataProcessor;

  @Before
  public void setUp() throws Exception {
    this.optaFixtureDataProcessor = new OptaFixtureDataProcessor(ongoingMatchDataCacheServiceMock,
      matchDataPullClientMock, matchPushDataRetrieverMock, webSocketManagerMock, matchLiveDataProcessorMock,
      matchFinalDataProcessorMock, halfTimeNotifierMock);
  }

  @Test
  public void whenAFeedIsReceived_andItsTsIsOlderThanTheCachedDataTs_thenTheFeedIsNotProcessed() throws FeedException {

    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    Instant ts = now().minus(30, SECONDS);
    MatchDataFeed parsedFeed = buildMatchDataFeed(5, emptyList(), ts, FeedFixtureStatus.LIVE);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).optaFixtureId("id").build();
    MatchDataFeed feed = optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    FeedFixtureStatus fixtureStatus = feed.getFixtureStatus();

    assertEquals("Match is not finished", FeedFixtureStatus.LIVE, fixtureStatus);
    verify(ongoingMatchDataCacheServiceMock, never()).mergeIfExists(anyString(), any(), anyBoolean());
    verify(ongoingMatchDataCacheServiceMock, never()).remove(anyString());
    verify(matchFinalDataProcessorMock, never()).processFeed(any());
  }

  @Test
  public void whenAFeedIsReceived_andTheMatchIsFinished_thenTheFinalDataProcessorIsCalled() throws FeedException {
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    MatchDataFeed parsedFeed = buildMatchDataFeed(5, emptyList(), now().plus(1, SECONDS), FeedFixtureStatus.PLAYED);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).optaFixtureId("id").isLiveEnabled(true).build();
    MatchDataFeed feed = optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    FeedFixtureStatus fixtureStatus = feed.getFixtureStatus();

    assertEquals("Match is finished", FeedFixtureStatus.PLAYED, fixtureStatus);
    verify(matchFinalDataProcessorMock).processFeed(any());
  }

  @Test
  public void whenAFeedIsReceived_andTheMatchIsCancelled_thenTheMatchIsSetToPostponed() throws FeedException {
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    MatchDataFeed parsedFeed = buildMatchDataFeed(5, emptyList(), now().plus(1, SECONDS), FeedFixtureStatus.CANCELLED);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).optaFixtureId("id").build();
    MatchDataFeed feed = optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    FeedFixtureStatus fixtureStatus = feed.getFixtureStatus();

    assertEquals("Match is finished", FeedFixtureStatus.CANCELLED, fixtureStatus);
  }

  @Test
  @Ignore("BetEnd feature might be removed soon")
  public void whenAFeedIsReceived_andTheMatchTimeIsAbove90_thenTheMatchIsSetToBetEnd() throws FeedException {
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().matchTime(91).build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    MatchDataFeed parsedFeed = buildMatchDataFeed(92, emptyList(), now().plus(1, SECONDS), FeedFixtureStatus.LIVE);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.defaultFixture(LIVE);
    MatchDataFeed feed = optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    FeedFixtureStatus fixtureStatus = feed.getFixtureStatus();

    assertEquals("Bets are closed", FeedFixtureStatus.BET_END, fixtureStatus);
    verify(matchFinalDataProcessorMock, Mockito.times(1)).processFeed(eq(parsedFeed));

    ArgumentCaptor<OngoingMatchData> captor = ArgumentCaptor.forClass(OngoingMatchData.class);
    verify(ongoingMatchDataCacheServiceMock).set(any(), captor.capture());

    assertThat(captor.getValue().getFixtureStatus(), is(FeedFixtureStatus.BET_END));
  }

  @Test
  @Ignore("BetEnd feature might be removed soon")
  public void whenAFeedIsReceived_andTheMatchIsAlreadyBetEnd_thenFinalDataProcessorIsNotCalled() throws FeedException {
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned()
      .matchTime(91)
      .fixtureStatus(FeedFixtureStatus.BET_END)
      .build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    MatchDataFeed parsedFeed = buildMatchDataFeed(92, emptyList(), now().plus(1, SECONDS), FeedFixtureStatus.LIVE);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.defaultFixture(LIVE);
    MatchDataFeed feed = optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    FeedFixtureStatus fixtureStatus = feed.getFixtureStatus();

    assertEquals("Bets are closed", FeedFixtureStatus.LIVE, fixtureStatus);
    verify(matchFinalDataProcessorMock, Mockito.never()).processFeed(eq(parsedFeed));
  }

  @Test
  public void whenAFeedIsReceived_andTheFixtureIsNotEnabledForLive_theFeedIsNotProcessed() {
    MatchDataFeed parsedFeed = buildMatchDataFeed(92, emptyList(), now().plus(1, SECONDS), FeedFixtureStatus.LIVE);

    when(matchDataPullClientMock.retrieveParsedFeed(anyString())).thenReturn(parsedFeed);

    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).optaFixtureId("id").isLiveEnabled(false).build();
    optaFixtureDataProcessor.processFixtureStatsFeed(fixture);
    verifyNoInteractions(matchLiveDataProcessorMock);
  }

  @Test
  public void whenAWebSocketHasToBeEstablished_andTheFixtureIsNotEnabledForLive_thenTheWebSocketIsNotEstablished() {
    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).isLiveEnabled(false).build();
    optaFixtureDataProcessor.manageWebSocket(fixture, LIVE);
    verifyNoInteractions(matchPushDataRetrieverMock);
  }

  @Test
  public void whenAWebSocketHasToBeEstablished_andTheFixtureIsEnabledForLive_thenTheWebSocketIsEstablished() {
    Fixture fixture = FixtureCanned.fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    optaFixtureDataProcessor.manageWebSocket(fixture, LIVE);
    verify(matchPushDataRetrieverMock).establishLiveConnectionIfNotExists(fixture);
  }

  @Test
  public void halfTimeIsNotified() {
    //Arrange
    OngoingMatchData cachedMatchData = ongoingMatchDataCanned().matchPeriod(FIRST_HALF).build();
    when(ongoingMatchDataCacheServiceMock.getOrCompute(anyString(), any())).thenReturn(cachedMatchData);

    MatchDataFeed halfTimeMA2 = matchDataFeedCanned()
      .fixtureStatus(FeedFixtureStatus.LIVE)
      .matchPeriod(HALF_TIME)
      .build();

    // Act
    optaFixtureDataProcessor.processMA2(halfTimeMA2);

    // Assert
    verify(halfTimeNotifierMock).notifyHalfTime(eq(halfTimeMA2), eq(cachedMatchData));
  }

  private MatchDataFeed buildMatchDataFeed(Integer matchTime, List<PlayerDataDTO> playersData, Instant ts,
                                           FeedFixtureStatus fixtureStatus) {
    return MatchDataFeed
      .builder()
      .feedId("testfeed")
      .fixture(FixtureCanned.fixtureCanned().build())
      .matchTimeMin(matchTime)
      .playersData(playersData)
      .receivedTs(ts)
      .latestUpdateTs(ts)
      .fixtureStatus(fixtureStatus)
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
  }
}
