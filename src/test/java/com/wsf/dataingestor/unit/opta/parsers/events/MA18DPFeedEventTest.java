package com.wsf.dataingestor.unit.opta.parsers.events;

import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import com.wsf.dataingestor.opta.parsers.errors.MissingRelatedQualifierValueException;
import com.wsf.dataingestor.shared.TestWithMocks;

import static com.wsf.dataingestor.shared.canned.MA18DPFeedEventCanned.ma18dpEventCanned;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class MA18DPFeedEventTest extends TestWithMocks {

  @Test
  public void whenEventHasRelatedQualifier_thenReturnCorrectValue() {
    // Arrange
    String relatedEvent = "213456789";
    int qualifier = 55;
    var event = ma18dpEventCanned()
      .typeId(16)
      .outcome((short) 1)
      .qualifiers(Map.of(qualifier, relatedEvent))
      .build();

    // Act
    var result = event.getRelatedQualifierValue();

    // Assert
    assertThat(result)
      .isPresent()
      .contains(relatedEvent);
  }

  @Test
  public void whenQualifierKeyIsGivenButEventHasNoQualifiers_thenReturnEmpty() {
    // Arrange
    var event = ma18dpEventCanned().typeId(16).outcome((short) 1).qualifiers(null).build();

    // Act
    var result = event.getRelatedQualifierValue();

    // Assert
    assertThat(result).isEmpty();
  }

  @Test
  public void whenThereIsTheGivenQualifier_ButTheValueIsEmpty_thenReturnEmpty() {
    // Arrange
    var qualifierMap = new HashMap<Integer, String>();
    int qualifier = 55;
    qualifierMap.put(qualifier, "");
    var event = ma18dpEventCanned().typeId(16).outcome((short) 1).qualifiers(qualifierMap).build();

    // Act and Assert
    assertThrows(MissingRelatedQualifierValueException.class, event::getRelatedQualifierValue);
  }

  @Test
  public void whenThereIsNotTheGivenQualifier_thenReturnEmpty() {
    // Arrange
    var event = ma18dpEventCanned().typeId(16).outcome((short) 1).qualifiers(Map.of()).build();

    // Act
    var result = event.getRelatedQualifierValue();

    // Assert
    assertThat(result).isEmpty();
  }

  @Test
  public void whenThereIsTheGivenQualifier_andValueIsNotEmpty_thenReturnTheValue() {
    // Arrange
    var qualifierMap = new HashMap<Integer, String>();
    int qualifier = 55;
    qualifierMap.put(qualifier, "1");
    var event = ma18dpEventCanned().typeId(16).outcome((short) 1).qualifiers(qualifierMap).build();

    // Act
    var result = event.getRelatedQualifierValue();

    // Assert
    assertThat(result)
      .isPresent()
      .contains("1");
  }

  @Test
  public void whenQualifierValueIsNULLString_thenReturnEmpty() {
    // Arrange
    var qualifierMap = new HashMap<Integer, String>();
    int qualifier = 55;
    qualifierMap.put(qualifier, "NULL");
    var event = ma18dpEventCanned().typeId(16).outcome((short) 1).qualifiers(qualifierMap).build();

    // Act and Assert
    assertThrows(MissingRelatedQualifierValueException.class, event::getRelatedQualifierValue);
  }
}
