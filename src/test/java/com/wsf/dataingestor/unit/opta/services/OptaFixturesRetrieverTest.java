package com.wsf.dataingestor.unit.opta.services;

import java.time.Instant;
import java.util.List;
import java.util.function.Predicate;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.exceptions.FixtureNotFoundException;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.opta.clients.MA1Client;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.services.OptaFixturesRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.FixtureDTOCanned;
import com.wsf.dataingestor.shared.canned.FixturesFeedCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class OptaFixturesRetrieverTest extends TestWithMocks {

  @Mock
  private MA1Client ma1ClientBetting;

  @Mock
  private MA1Client ma1ClientNoBetting;

  @Mock
  private OptaFeedParserUtils optaFeedParserUtils;

  private OptaFixturesRetriever optaFixturesRetriever;

  @Before
  public void setUp() {
    optaFixturesRetriever = new OptaFixturesRetriever(ma1ClientBetting, ma1ClientNoBetting, optaFeedParserUtils);
  }

  @Test
  public void whenRetrieveFixturesFeed_thenReturnMergeFeeds() {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    FixtureDTO fixtureDTO = FixtureDTOCanned.fixtureDTOCanned().build();

    FixturesFeed allFixtures = FixturesFeedCanned.fixturesFeedCanned().fixtures(List.of(fixtureDTO)).build();

    FixturesFeed validatedFixtures = FixturesFeedCanned
      .fixturesFeedCanned()
      .fixtures(List.of(fixtureDTO.withActiveStatus(true)))
      .build();

    when(ma1ClientNoBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(allFixtures);
    when(ma1ClientBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(validatedFixtures);

    // Act
    FixturesFeed result = optaFixturesRetriever.retrieveFixturesFeed(tournament);

    // Assert
    assertThat(result).isNotNull();
    verify(ma1ClientNoBetting).retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID);
    verify(ma1ClientBetting).retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID);
  }

  @Test
  public void whenFindFixtureWithValidTeams_thenReturnFixture() throws FixtureNotFoundException {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    Team homeTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM1_ID).build();
    Team awayTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM2_ID).build();
    FixtureDTO fixtureDTO = FixtureDTOCanned.fixtureDTOCanned().build();
    Fixture expectedFixture = FixtureCanned
      .fixtureCanned()
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .tournament(tournament)
      .build();

    when(optaFeedParserUtils.getTeamOrCreateUnmapped(eq(tournament.getCompetitionId()),
      eq(fixtureDTO.getExternalHomeTeamId()), eq(fixtureDTO.getExternalHomeTeamName()),
      eq(fixtureDTO.getExternalHomeTeamAbbreviation()))).thenReturn(homeTeam);
    when(optaFeedParserUtils.getTeamOrCreateUnmapped(eq(tournament.getCompetitionId()),
      eq(fixtureDTO.getExternalAwayTeamId()), eq(fixtureDTO.getExternalAwayTeamName()),
      eq(fixtureDTO.getExternalAwayTeamAbbreviation()))).thenReturn(awayTeam);
    when(optaFeedParserUtils.getFixtureByOptaIdOrTournamentAndTeamsAndDate(eq(fixtureDTO.getExternalFixtureId()),
      eq(tournament.getIdAsString()), eq(TeamCanned.TEAM1_ID.toString()), eq(TeamCanned.TEAM2_ID.toString()),
      any(Instant.class))).thenReturn(expectedFixture);

    // Act
    Fixture result = optaFixturesRetriever.findFixture(fixtureDTO, tournament);

    // Assert
    assertThat(result).isEqualTo(expectedFixture);
  }

  @Test
  public void whenFindRelatedFixtureWithValidExternalId_thenReturnFixture() throws FixtureNotFoundException {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    String relatedFixtureId = "relatedFixtureId";
    FixtureDTO fixtureWithRelated = FixtureDTOCanned
      .fixtureDTOCanned()
      .externalRelatedFixtureId(relatedFixtureId)
      .build();
    Fixture expectedFixture = FixtureCanned.fixtureCanned().build();

    when(optaFeedParserUtils.getFixtureByOptaFixtureId(relatedFixtureId)).thenReturn(expectedFixture);

    // Act
    Fixture result = optaFixturesRetriever.findRelatedFixture(fixtureWithRelated, tournament);

    // Assert
    assertThat(result).isEqualTo(expectedFixture);
  }

  @Test
  public void whenFindTeam_thenReturnTeam() {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    Team expectedTeam = TeamCanned.teamCanned().build();
    String teamId = "externalTeamId";

    when(optaFeedParserUtils.getTeam(tournament.getCompetitionId(), teamId)).thenReturn(expectedTeam);

    // Act
    Team result = optaFixturesRetriever.findTeam(teamId, tournament);

    // Assert
    assertThat(result).isEqualTo(expectedTeam);
  }

  @Test
  public void whenGetExternalFixtureProvider_thenReturnOpta() {
    // Act
    ExternalProvider result = optaFixturesRetriever.getExternalFixtureProvider();

    // Assert
    assertThat(result).isEqualTo(ExternalProvider.OPTA);
  }

  @Test
  public void whenRetrieveFixturesFeedWithSomeValidatedFixtures_thenOnlyValidatedFixturesMarkedActive() {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    FixtureDTO fixture1 = FixtureDTOCanned.fixtureDTOCanned().externalFixtureId("fixture1").build();
    FixtureDTO fixture2 = FixtureDTOCanned.fixtureDTOCanned().externalFixtureId("fixture2").build();
    FixtureDTO fixture3 = FixtureDTOCanned.fixtureDTOCanned().externalFixtureId("fixture3").build();

    FixturesFeed allFixtures = FixturesFeedCanned
      .fixturesFeedCanned()
      .fixtures(List.of(fixture1, fixture2, fixture3))
      .build();

    FixturesFeed validatedFixtures = FixturesFeedCanned
      .fixturesFeedCanned()
      .fixtures(List.of(fixture1, fixture3))
      .build();

    when(ma1ClientNoBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(allFixtures);
    when(ma1ClientBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(validatedFixtures);

    // Act
    FixturesFeed result = optaFixturesRetriever.retrieveFixturesFeed(tournament);

    // Assert
    assertThat(result.getFixtures())
      .hasSize(3)
      .anySatisfy(fixtureDTO -> {
        assertThat(fixtureDTO.getExternalFixtureId()).isEqualTo(fixture1.getExternalFixtureId());
        assertThat(fixtureDTO.getIsActive()).isTrue();
      })
      .anySatisfy(fixtureDTO -> {
        assertThat(fixtureDTO.getExternalFixtureId()).isEqualTo(fixture2.getExternalFixtureId());
        assertThat(fixtureDTO.getIsActive()).isFalse();
      })
      .anySatisfy(fixtureDTO -> {
        assertThat(fixtureDTO.getExternalFixtureId()).isEqualTo(fixture3.getExternalFixtureId());
        assertThat(fixtureDTO.getIsActive()).isTrue();
      });
  }

  @Test
  public void whenRetrieveFixturesFeedWithNoValidatedFixtures_thenAllFixturesMarkedInactive() {
    // Arrange
    Tournament tournament = TournamentCanned.tournamentCanned().build();
    FixtureDTO fixture1 = FixtureDTOCanned.fixtureDTOCanned().externalFixtureId("fixture1").build();
    FixtureDTO fixture2 = FixtureDTOCanned.fixtureDTOCanned().externalFixtureId("fixture2").build();

    FixturesFeed allFixtures = FixturesFeedCanned.fixturesFeedCanned().fixtures(List.of(fixture1, fixture2)).build();

    FixturesFeed validatedFixtures = FixturesFeedCanned.fixturesFeedCanned().fixtures(List.of()).build();

    when(ma1ClientNoBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(allFixtures);
    when(ma1ClientBetting.retrieveParsedFeed(TournamentCanned.DEFAULT_OPTA_CALENDAR_ID)).thenReturn(validatedFixtures);

    // Act
    FixturesFeed result = optaFixturesRetriever.retrieveFixturesFeed(tournament);

    // Assert
    assertThat(result.getFixtures())
      .hasSize(2)
      .allMatch(Predicate.not(FixtureDTO::getIsActive));
  }
}
