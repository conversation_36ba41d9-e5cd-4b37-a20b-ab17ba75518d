package com.wsf.dataingestor.unit.opta.parsers.events;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed.Event;
import com.wsf.dataingestor.opta.parsers.events.EntityEventFilter;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPEventsMapper;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18ValidEvents;
import com.wsf.dataingestor.opta.parsers.ma18dp.Ma18DPSubstitutionsHandler;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Player;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.cache.OptaCachedEventService.EventEntity;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_OFF;
import static com.wsf.dataingestor.opta.parsers.events.OptaEvents.EVENT_ID_PLAYER_SUB_ON;
import static com.wsf.dataingestor.opta.parsers.events.OptaQualifiers.QUALIFIER_ID_RELATED_EVENT_ID;
import static com.wsf.dataingestor.shared.canned.MA18DPFeedEventCanned.ma18dpEventCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.UNKNOWN_EVENT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class MA18DPEventsMapperTest extends TestWithMocks {
  private static final Player PLAYER = PlayerCanned.createPlayer().build();
  @Mock
  public OptaCachedEventService cacheEventService;
  MetricsManager manager = new MetricsManager(new SimpleMeterRegistry());
  private MA18DPEventsMapper eventsMapper;

  @Before
  public void setUp() throws Exception {
    Multimap<Integer, EntityEventFilter> eventTypeIdToEventInfo = ArrayListMultimap.create();
    var value = EntityEventFilter
      .builder().stat(SHOT).outcomeHasToBeOne(true).build();
    eventTypeIdToEventInfo.put(39, value);
    eventsMapper = MA18DPEventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(MA18ValidEvents.playerEventTypeIdToFilters)
      .matchEventTypeIdToQualifiersId(MA18ValidEvents.matchEventTypeIdToFilters)
      .metricsManager(manager)
      .optaCachedEventService(cacheEventService)
      .ma18DPSubstitutionsHandler(new Ma18DPSubstitutionsHandler(cacheEventService, manager))
      .build();
  }

  @Test
  public void whenASubEventIsReceivedWithARelatedEventId_andRelatedEventIsNotFoundInTheCache_thenOneUnknownEventIsGenerated() {
    // Arrange
    String subOffPlayerId = PLAYER.getIdAsString();
    String teamId = PLAYER.getTeam().getIdAsString();
    var relatedEventId = "eventSubOn";
    Event eventSubOff = ma18dpEventCanned()
      .typeId(EVENT_ID_PLAYER_SUB_OFF)
      .outcome((short) 1)
      .qualifiers(Map.of(55, relatedEventId))
      .playerId("externalPlayerSubOffId")
      .eventId("eventSubOff")
      .build();
    var feedEvents = List.of(eventSubOff);
    when(cacheEventService.get(eq(teamId), eq(relatedEventId))).thenReturn(null);

    // Act
    List<EntityEventDTO> entityEvents = eventsMapper.translatePlayerEvents(subOffPlayerId, teamId, false, feedEvents);

    // Assert
    Assertions
      .assertThat(entityEvents)
      .hasSize(1)
      .anySatisfy(entityEvent -> {
        assertThat(entityEvent.isUnknown()).isTrue();
        assertThat(entityEvent.getEventType()).isEqualTo(UNKNOWN_EVENT);
      });
  }

  @Test
  public void whenASubEventIsReceivedWithARelatedEventId_andRelatedEventIsFoundInTheCache_thenTwoWsfEventsAreGenerated() {
    // Arrange
    String relatedEventId = "subOffRelatedId";
    Event event = ma18dpEventCanned()
      .typeId(EVENT_ID_PLAYER_SUB_ON)
      .outcome((short) 1)
      .qualifiers(Map.of(55, relatedEventId))
      .id("subOnExternalEventId")
      .build();
    var subOnExternalEventId = "subOnExternalEventId";
    var feedEvents = List.of(event);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();
    String playerSubOffId = PLAYER_2_ID_STR;
    var subOffExternalEventId = "subOffExternalEventId";
    var eventEntitySubOff = EventEntity
      .builder()
      .externalEventId(subOffExternalEventId)
      .externalPlayerId("externalPlayerSubOffId")
      .playerId(playerSubOffId)
      .timestamp(Instant.now())
      .build();
    when(cacheEventService.get(eq(playerTeamId), eq(relatedEventId))).thenReturn(eventEntitySubOff);

    // Act
    List<EntityEventDTO> entityEvents = eventsMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    Assertions
      .assertThat(entityEvents)
      .hasSize(2)
      .anySatisfy(entityEvent -> {
        assertThat(entityEvent.getEventId()).isEqualTo(subOffExternalEventId);
        assertThat(entityEvent.getRelatedEventId()).isEqualTo(subOnExternalEventId);
        assertThat(entityEvent.getEventType()).isEqualTo(SUB_OFF);
        assertThat(entityEvent.getEntityId()).isEqualTo(playerSubOffId);
      })
      .anySatisfy(entityEvent -> {
        assertThat(entityEvent.getEventId()).isEqualTo(subOnExternalEventId);
        assertThat(entityEvent.getRelatedEventId()).isEqualTo(subOffExternalEventId);
        assertThat(entityEvent.getEventType()).isEqualTo(SUB_ON);
        assertThat(entityEvent.getEntityId()).isEqualTo(playerId);
      });
  }

  @Test
  public void whenASingleSubEventIsReceivedWithoutARelatedEventId_thenTheMessageIsDiscarded() {
    // Arrange
    Event event = ma18dpEventCanned().typeId(EVENT_ID_PLAYER_SUB_OFF).outcome((short) 1).qualifiers(Map.of()).build();
    var feedEvents = List.of(event);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    // Act
    List<EntityEventDTO> entityEvents = eventsMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    Assertions
      .assertThat(entityEvents)
      .isEmpty();
  }

  @Test
  public void whenANotSupportedEventIsProcessed_thenAnUnknownEventIsReturned() {
    // Arrange
    Event notSupportedEvent = ma18dpEventCanned().typeId(5).outcome((short) 1).build();
    var feedEvents = List.of(notSupportedEvent);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    // Act
    List<EntityEventDTO> entityEvents = eventsMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    EntityEventDTO unknownEvent = entityEvents.get(0);
    assertThat(entityEvents.size()).isEqualTo(1);
    assertTrue(unknownEvent.isUnknown());
    assertThat(unknownEvent.getEventType()).isEqualTo(SoccerMatchEvent.UNKNOWN_EVENT);
    verify(cacheEventService, times(1)).put(playerTeamId, notSupportedEvent, playerId);
  }

  @Test
  public void whenASupportedEventIsProcessed_butTheOutcomeIsNotTheExpected_thenAnUnknownEventIsReturned() {
    // Arrange
    Event notSupportedEvent = ma18dpEventCanned().typeId(39).outcome((short) 0).build();
    var feedEvents = List.of(notSupportedEvent);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    // Act
    List<EntityEventDTO> entityEvents = eventsMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    assertThat(entityEvents.size()).isEqualTo(1);
    EntityEventDTO unknownEvent = entityEvents.get(0);
    assertTrue(unknownEvent.isUnknown());
    assertThat(unknownEvent.getEventType()).isEqualTo(UNKNOWN_EVENT);
    verify(cacheEventService, times(1)).put(playerTeamId, notSupportedEvent, playerId);
  }

  @Test
  public void whenASupportedEventIsProcessed_andItHasToBeIgnored_thenAEventToIgnoreIsReturned() {
    // Arrange
    int qualifierToIgnore = 14;
    Multimap<Integer, EntityEventFilter> eventTypeIdToEventInfo = ArrayListMultimap.create();
    var localMapper = MA18DPEventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(eventTypeIdToEventInfo)
      .metricsManager(manager)
      .optaCachedEventService(cacheEventService)
      .ma18DPSubstitutionsHandler(new Ma18DPSubstitutionsHandler(cacheEventService, manager))
      .build();
    var value = EntityEventFilter
      .builder().stat(SHOT).outcomeHasToBeOne(true).qualifiersToIgnore(Set.of(qualifierToIgnore)).build();
    eventTypeIdToEventInfo.put(39, value);

    Event event = ma18dpEventCanned().typeId(39).outcome((short) 1).qualifiers(Map.of(qualifierToIgnore, "")).build();
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    // Act
    List<EntityEventDTO> entityEvents = localMapper.translatePlayerEvents(playerId, playerTeamId, false,
      List.of(event));

    // Assert
    assertThat(entityEvents.size()).isEqualTo(1);
    EntityEventDTO unknownEvent = entityEvents.get(0);
    assertTrue(unknownEvent.isIgnore());
    assertThat(unknownEvent.getEventType()).isEqualTo(SHOT);
    verify(cacheEventService, times(1)).put(playerTeamId, event, playerId);
  }

  @Test
  public void whenASupportedEventIsProcessed_thenTheMappedEventIsReturned() {
    // Arrange
    int qualifierToIgnore = 14;
    Multimap<Integer, EntityEventFilter> eventTypeIdToEventInfo = ArrayListMultimap.create();
    var localMapper = MA18DPEventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(eventTypeIdToEventInfo)
      .metricsManager(manager)
      .optaCachedEventService(cacheEventService)
      .ma18DPSubstitutionsHandler(new Ma18DPSubstitutionsHandler(cacheEventService, manager))
      .build();
    var value = EntityEventFilter
      .builder().stat(SHOT).outcomeHasToBeOne(true).qualifiersToIgnore(Set.of(qualifierToIgnore)).build();
    eventTypeIdToEventInfo.put(39, value);
    Event supportedEvent = ma18dpEventCanned().typeId(39).outcome((short) 1).build();
    var feedEvents = List.of(supportedEvent);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    // Act
    List<EntityEventDTO> entityEvents = localMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    assertThat(entityEvents.size()).isEqualTo(1);
    EntityEventDTO shotEvent = entityEvents.get(0);
    assertThat(shotEvent.getEventId()).isEqualTo(supportedEvent.getId());
    assertThat(shotEvent.getRelatedEventId()).isNull();
    assertThat(shotEvent.getEntityId()).isEqualTo(PLAYER.getIdAsString());
    assertThat(shotEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
    assertThat(shotEvent.getEventType()).isEqualTo(SHOT);
    assertThat(shotEvent.getPeriod()).isEqualTo(FIRST_HALF);
    assertThat(shotEvent.getTimeMin()).isEqualTo(Integer.valueOf(supportedEvent.getTimeMin()));
    assertFalse(shotEvent.isIgnore());
    assertFalse(shotEvent.isUnknown());
    assertThat(shotEvent.getTimestamp()).isEqualTo(supportedEvent.getTimestamp());
    verify(cacheEventService).put(playerTeamId, supportedEvent, playerId);
  }

  @Test
  public void whenAGoalEventIsProcessed_andItHasRelatedAssistEventId_thenTheAssistEventIsReturnedWithRelatedEventIdSetAsGoalEvent() {
    // Arrange
    Multimap<Integer, EntityEventFilter> eventTypeIdToEventInfo = ArrayListMultimap.create();
    var value = EntityEventFilter
      .builder()
      .stat(ASSIST_GOAL)
      .outcomeHasToBeOne(true)
      .parseRelatedEventInsteadOfMain(true)
      .shouldUseMainEventId(true)
      .build();
    eventTypeIdToEventInfo.put(16, value);
    var localMapper = MA18DPEventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(eventTypeIdToEventInfo)
      .metricsManager(manager)
      .optaCachedEventService(cacheEventService)
      .ma18DPSubstitutionsHandler(new Ma18DPSubstitutionsHandler(cacheEventService, manager))
      .build();

    String relatedEventIdFromQualifier = "292";
    Event supportedEvent = ma18dpEventCanned()
      .typeId(16)
      .outcome((short) 1)
      .qualifiers(Map.of(QUALIFIER_ID_RELATED_EVENT_ID, relatedEventIdFromQualifier))
      .build();
    var feedEvents = List.of(supportedEvent);
    String playerId = PLAYER.getIdAsString();
    String playerTeamId = PLAYER.getTeam().getIdAsString();

    String assistEventId = "213456789";
    var cachedEntity = EventEntity
      .builder()
      .externalEventId(assistEventId)
      .externalPlayerId("externalAssistPlayer")
      .playerId(PLAYER_2_ID.toString())
      .build();
    when(cacheEventService.get(eq(playerTeamId), eq(relatedEventIdFromQualifier))).thenReturn(cachedEntity);

    // Act
    List<EntityEventDTO> entityEvents = localMapper.translatePlayerEvents(playerId, playerTeamId, false, feedEvents);

    // Assert
    assertThat(entityEvents)
      .hasSize(1)
      .first()
      .satisfies(assistEvent -> {
        assertThat(assistEvent.getEventId()).isEqualTo(supportedEvent.getId());
        assertThat(assistEvent.getRelatedEventId()).isEqualTo(supportedEvent.getId());
        assertThat(assistEvent.getEntityId()).isEqualTo(PLAYER_2_ID.toString());
        assertThat(assistEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
        assertThat(assistEvent.getEventType()).isEqualTo(ASSIST_GOAL);
        assertThat(assistEvent.getPeriod()).isEqualTo(FIRST_HALF);
        assertThat(assistEvent.getTimeMin()).isEqualTo(Integer.valueOf(supportedEvent.getTimeMin()));
        assertThat(assistEvent.isIgnore()).isFalse();
        assertThat(assistEvent.isUnknown()).isFalse();
        assertThat(assistEvent.getTimestamp()).isEqualTo(supportedEvent.getTimestamp());
      });

    verify(cacheEventService).put(playerTeamId, supportedEvent, playerId);
  }
}
