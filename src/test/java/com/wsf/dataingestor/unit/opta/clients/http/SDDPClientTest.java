package com.wsf.dataingestor.unit.opta.clients.http;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.clients.FeedStore;
import com.wsf.dataingestor.clients.stores.FileStore;
import com.wsf.dataingestor.clients.ws.factories.WebSocketConnectionFactory;
import com.wsf.dataingestor.clients.ws.factories.WebSocketHandlerFactory;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.clients.MA2DPClient;
import com.wsf.dataingestor.opta.clients.ws.MA2DPWebSocketClient;
import com.wsf.dataingestor.opta.parsers.MA2DPFeedParser;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.services.FeedStoreService;
import com.wsf.dataingestor.shared.TestWithMocks;

import static java.util.concurrent.Executors.newFixedThreadPool;
import static java.util.concurrent.Executors.newSingleThreadExecutor;

@Slf4j
public class SDDPClientTest extends TestWithMocks {

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  @Ignore
  @Test
  public void connectWebsocket() {

    String fixtureId = "6be9ks0fepa9i0axlbfpvlld6";
    String outletId = "t2vho64fveax13yf6b6a7s9tg";

    ObjectMapper objectMapper = new JsonConfig().jsonObjectMapper();

    MA2DPFeedParser ma2dpFeedParser = new MA2DPFeedParser(objectMapper, optaFeedParserUtilsMock);

    Consumer<MatchDataFeed> handler = feed -> {
      log.info("feed parsed: {}", feed.getReceivedTs());
    };

    BiConsumer<String, Exception> exhandler = log::error;

    MetricsManager metricsManager = new MetricsManager(new SimpleMeterRegistry());
    MA2DPWebSocketClient ma2DPWebSocketClient = new MA2DPWebSocketClient("wss://content.performgroup.io", outletId,
      new WebSocketConnectionFactory(), new WebSocketHandlerFactory(new ObjectMapper(), metricsManager),
      newSingleThreadExecutor());

    FeedStore feedStore = new FileStore(false, "opta_feeds/");
    FeedStoreService feedStoreService = new FeedStoreService(feedStore, newFixedThreadPool(1));

    new MA2DPClient(ma2DPWebSocketClient, ma2dpFeedParser, feedStoreService).startConnectionAndProcessFeed(fixtureId, 1,
      handler, exhandler, t -> {}, () -> {});

    try {
      Thread.sleep(10800000); // 3h
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
  }
}
