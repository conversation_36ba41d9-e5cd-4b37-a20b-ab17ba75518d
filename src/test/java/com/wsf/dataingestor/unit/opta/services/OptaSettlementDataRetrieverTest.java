package com.wsf.dataingestor.unit.opta.services;

import java.time.Instant;
import java.util.List;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.clients.MA2Client;
import com.wsf.dataingestor.opta.clients.MA3Client;
import com.wsf.dataingestor.opta.services.OptaSettlementDataRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;

import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class OptaSettlementDataRetrieverTest extends TestWithMocks {

  private static final Fixture fixture = FixtureCanned.defaultFixture();

  @Mock
  private MA2Client ma2Client;

  @Mock
  private MA3Client ma3Client;

  private OptaSettlementDataRetriever settlementDataRetriever;

  @Before
  public void setUp() throws Exception {
    this.settlementDataRetriever = new OptaSettlementDataRetriever(ma2Client, ma3Client);
  }

  @Test
  public void whenSettlementDataIsRetrieved_andItIsFinal_thenTheMatchDataFeedIsReturned() {
    MatchDataFeed feed = mockFeedRetrieval(fixture, true);
    MatchDataFeed matchDataFeed = settlementDataRetriever.retrieveSettlementData(fixture);
    assertThat(matchDataFeed, is(feed));
  }

  @Test
  public void whenSettlementDataIsRetrieved_andTheFixtureWasCancelledAndThereIsAnErrorProcessingTheMA3Feed_thenTheMatchDataFeedIsReturned() {
    MatchDataFeed feed = mockFeedRetrievalPostponedMatchAndMA3Exception(fixture, FeedFixtureStatus.CANCELLED, true);
    MatchDataFeed matchDataFeed = settlementDataRetriever.retrieveSettlementData(fixture);
    assertThat(matchDataFeed, is(feed));
  }

  @Test(expected = IllegalStateException.class)
  public void whenSettlementDataIsRetrieved_andThereIsAnErrorProcessingTheMA3Feed_thenTheExceptionIsRethrown() {
    mockFeedRetrievalPostponedMatchAndMA3Exception(fixture, FeedFixtureStatus.FIXTURE, true);
    settlementDataRetriever.retrieveSettlementData(fixture);
  }

  private MatchDataFeed mockFeedRetrieval(Fixture fixture, boolean isFinalData) {
    var team = TeamCanned.defaultTeam();
    MatchDataFeed dataFeed = mockMA2Retrieval(fixture, FeedFixtureStatus.PLAYED, isFinalData, team);
    mockMA3Retrieval(fixture, team);
    return dataFeed;
  }

  private MatchDataFeed mockFeedRetrievalPostponedMatchAndMA3Exception(Fixture fixture, FeedFixtureStatus fixtureStatus,
                                                                       boolean isFinalData) {
    var team = TeamCanned.defaultTeam();
    MatchDataFeed dataFeed = mockMA2Retrieval(fixture, fixtureStatus, isFinalData, team);
    when(ma3Client.retrieveParsedFeed(anyString())).thenThrow(IllegalStateException.class);
    return dataFeed;
  }

  private void mockMA3Retrieval(Fixture fixture, Team team) {
    var event = EntityEventDTO
      .builder().eventType(FOUL).teamId(team.getId().toString()).build();
    var eventsFeed = MatchEventsFeed
      .builder()
      .receivedTs(Instant.now())
      .feedId("feedId")
      .fixture(fixture)
      .events(List.of(event))
      .supportedEventTypes(Set.of())
      .build();
    when(ma3Client.retrieveParsedFeed(any())).thenReturn(eventsFeed);
  }

  private MatchDataFeed mockMA2Retrieval(Fixture fixture, FeedFixtureStatus fixtureStatus, boolean isFinalData,
                                         Team team) {
    var dataFeed = MatchDataFeed
      .builder()
      .receivedTs(Instant.now())
      .feedId("feedId")
      .fixture(fixture)
      .teamsData(List.of(TeamDataDTO
        .builder().team(team).build(), TeamDataDTO
        .builder().team(team).build()))
      .isFinalData(isFinalData)
      .fixtureStatus(fixtureStatus)
      .supportedEventTypes(Set.of())
      .provider(MatchDataFeed.FeedProvider.OPTA)
      .build();
    when(ma2Client.retrieveParsedFeed(anyString())).thenReturn(dataFeed);
    return dataFeed;
  }
}
