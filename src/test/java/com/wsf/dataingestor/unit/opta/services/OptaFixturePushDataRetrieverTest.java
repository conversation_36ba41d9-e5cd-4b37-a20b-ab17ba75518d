package com.wsf.dataingestor.unit.opta.services;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.neovisionaries.ws.client.WebSocket;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.clients.PushAPIClient;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.handlers.WSHandler;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.models.NewAndDeletedEvents;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.dataingestor.services.ratings.LiveFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.MatchLiveDataProcessor;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.google.common.util.concurrent.MoreExecutors.newDirectExecutorService;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.entityEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned.playerMatchEventDTOCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static java.time.Instant.now;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class OptaFixturePushDataRetrieverTest extends TestWithMocks {

  @Mock
  OngoingMatchDataCacheService ongoingMatchDataCache;
  @Mock
  PushAPIClient matchDataPushClient;
  @Mock
  MatchEventsProcessor matchEventsProcessor;
  @Mock
  MatchLiveDataProcessor matchLiveDataProcessor;
  @Mock
  LiveFixtureSummaryNotifier liveFixtureSummaryNotifier;
  @Mock
  FixtureService fixtureService;
  @Mock
  WebSocketManager webSocketManager;
  @Mock
  MetricsManager metrics;

  OptaFixturePushDataRetriever pushDataRetriever;

  @Before
  public void setUp() throws Exception {
    var threadPoolService = new ThreadPoolService(newDirectExecutorService(), newDirectExecutorService(), metrics);
    pushDataRetriever = new OptaFixturePushDataRetriever(ongoingMatchDataCache, matchDataPushClient,
      matchEventsProcessor, matchLiveDataProcessor, liveFixtureSummaryNotifier, fixtureService, threadPoolService,
      webSocketManager, metrics);
  }

  @Test
  public void whenLiveConnectionIsBeingEstablished_thenLiveConnectionIsEstablishedWithGivenSeqId() {
    Fixture fixture = fixtureCanned().build();

    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(null);

    int seqId = 10;
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().seqId(seqId).build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));

    pushDataRetriever.establishLiveConnectionIfNotExists(fixture);

    verify(matchDataPushClient).startConnectionAndProcessFeed(eq(fixture.getOptaFixtureId()), eq(seqId),
      any(Consumer.class), any(BiConsumer.class), any(Consumer.class), any(Runnable.class));
  }

  @Test
  public void whenLiveConnectionIsBeingEstablished_andAConnectionAlreadyExistsButItIsNotOpen_thenSocketIsDisconnectedAndReconnected() {
    Fixture fixture = fixtureCanned().build();

    WebSocketManager.WSData wsData = buildWebSocket(false);
    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(wsData);

    int seqId = 10;
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().seqId(seqId).build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));

    pushDataRetriever.establishLiveConnectionIfNotExists(fixture);

    verify(webSocketManager).disconnectWebSocket(fixture.getIdAsString());
    verify(matchDataPushClient).startConnectionAndProcessFeed(eq(fixture.getOptaFixtureId()), eq(seqId),
      any(Consumer.class), any(BiConsumer.class), any(Consumer.class), any(Runnable.class));
  }

  @Test
  public void whenLiveConnectionIsBeingEstablished_andAConnectionAlreadyExistsAndItIsOpen_thenNothingIsDone() {
    Fixture fixture = fixtureCanned().build();

    WebSocketManager.WSData wsData = buildWebSocket(true);
    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(wsData);
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));

    pushDataRetriever.establishLiveConnectionIfNotExists(fixture);

    verify(webSocketManager, never()).disconnectWebSocket(anyString());
    verifyNoInteractions(matchDataPushClient);
  }

  @Test
  public void whenAFeedIsProcessed_thenItsSeqIdIsUpdatedInTheCacheAndInTheWSHandler() {
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);

    WSHandler wsHandler = mock(WSHandler.class);
    WebSocketManager.WSData wsData = buildWebSocket(true, wsHandler);
    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(wsData);

    Fixture fixture = fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    int seqId = 10;
    MatchDataFeed feed = matchDataFeedCanned().fixture(fixture).seqId(seqId).build();

    pushDataRetriever.processMA18DPFeed(feed);

    verify(wsHandler).updateSeqId(seqId);

    ArgumentCaptor<OngoingMatchData> ongoingMatchDataCaptor = ArgumentCaptor.forClass(OngoingMatchData.class);
    verify(ongoingMatchDataCache).mergeIfExists(anyString(), ongoingMatchDataCaptor.capture(), anyBoolean());

    OngoingMatchData cachedMatchData = ongoingMatchDataCaptor.getValue();
    assertThat(cachedMatchData.getSeqId(), is(seqId));
  }

  @Test
  public void whenAFeedIsProcessed_andItContainsEvents_thenTheLiveDataProcessorProcessesIt() {
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);

    WSHandler wsHandler = mock(WSHandler.class);
    WebSocketManager.WSData wsData = buildWebSocket(true, wsHandler);
    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(wsData);

    Fixture fixture = fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .seqId(10)
      .matchEvents(List.of(buildMatchEvent()))
      .build();

    pushDataRetriever.processMA18DPFeed(feed);

    verify(matchEventsProcessor).processMatchEvents(feed, ongoingMatchData);
    verify(matchLiveDataProcessor).processFeed(eq(feed), eq(ongoingMatchData), any());
    verify(ongoingMatchDataCache).mergeIfExists(fixture.getIdAsString(), ongoingMatchData, true);
  }

  @Test
  public void whenAFeedIsProcessed_andItContainsStats_thenTheLiveDataProcessorProcessesIt() {
    when(matchLiveDataProcessor.processFeed(any(), any(), any())).thenReturn(
      new NewAndDeletedEvents(List.of(), List.of()));
    OngoingMatchData ongoingMatchData = ongoingMatchDataCanned().build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);

    WSHandler wsHandler = mock(WSHandler.class);
    WebSocketManager.WSData wsData = buildWebSocket(true, wsHandler);
    when(webSocketManager.getMatchWebSocket(anyString())).thenReturn(wsData);

    Fixture fixture = fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .seqId(10)
      .playersData(List.of(buildPlayerDataDTO()))
      .build();

    pushDataRetriever.processMA18DPFeed(feed);

    verify(matchEventsProcessor).processMatchEvents(feed, ongoingMatchData);
    verify(matchLiveDataProcessor).processFeed(eq(feed), eq(ongoingMatchData), any());
    verify(ongoingMatchDataCache).mergeIfExists(fixture.getIdAsString(), ongoingMatchData, true);
  }

  @Test
  public void whenAFeedIsProcessed_andMeaningfulEventsAreIncluded_thenALiveFixtureSummaryIsPublished() {
    // Arrange
    Fixture fixture = fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .feedPlayerMatchEvents(List.of(entityEventDTOCanned().eventType(SUB_OFF).build()))
      .build();
    var ongoingMatchData = ongoingMatchDataCanned().matchId(fixture.getIdAsString()).build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);
    var newSubOffEvent = playerMatchEventDTOCanned().event(SUB_OFF).build();
    var newAndDeletedEvents = new NewAndDeletedEvents(List.of(newSubOffEvent), List.of());

    // Act
    pushDataRetriever.processMA18DPFeed(matchDataFeed);

    // Assert
    ArgumentCaptor<Consumer<NewAndDeletedEvents>> captor = ArgumentCaptor.forClass(Consumer.class);
    verify(matchLiveDataProcessor).processFeed(any(), any(), captor.capture());
    captor.getValue().accept(newAndDeletedEvents);
    verify(liveFixtureSummaryNotifier).sendLiveSummary(ongoingMatchData);
  }

  @Test
  public void whenAFeedIsProcessed_andAnExistingEventIsDeleted_thenALiveFixtureSummaryIsPublished() {
    // Arrange
    Fixture fixture = fixtureCanned().status(LIVE).isLiveEnabled(true).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    String existingEventId = "eventId";
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .feedPlayerMatchEvents(
        List.of(entityEventDTOCanned().eventId(existingEventId).eventType(SoccerMatchEvent.DELETED_EVENT).build()))
      .build();
    PlayerMatchEventDTO existingGoalMadeEvent = playerMatchEventDTOCanned()
      .eventId(existingEventId)
      .event(GOAL)
      .build();
    var ongoingMatchData = ongoingMatchDataCanned()
      .matchId(fixture.getIdAsString())
      .eventIdToPlayerMatchEvents(Map.of(existingEventId, Set.of(existingGoalMadeEvent)))
      .build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);
    var deletedGoalsMadeEvent = playerMatchEventDTOCanned().event(GOAL).build();
    var newAndDeletedEvents = new NewAndDeletedEvents(List.of(), List.of(deletedGoalsMadeEvent));

    // Act
    pushDataRetriever.processMA18DPFeed(matchDataFeed);

    // Assert
    ArgumentCaptor<Consumer<NewAndDeletedEvents>> captor = ArgumentCaptor.forClass(Consumer.class);
    verify(matchLiveDataProcessor).processFeed(any(), any(), captor.capture());
    captor.getValue().accept(newAndDeletedEvents);
    verify(liveFixtureSummaryNotifier).sendLiveSummary(ongoingMatchData);
  }

  @Test
  public void whenAFeedIsProcessed_andNoMeaningfulEventsAreIncluded_thenALiveFixtureSummaryIsNotPublished() {
    // Arrange
    var fixture = fixtureCanned().status(LIVE).build();
    var matchDataFeed = matchDataFeedCanned()
      .fixture(fixture)
      .feedPlayerMatchEvents(List.of(entityEventDTOCanned().eventType(SHOT).build()))
      .build();
    var ongoingMatchData = ongoingMatchDataCanned().matchId(fixture.getIdAsString()).build();
    when(ongoingMatchDataCache.get(anyString())).thenReturn(ongoingMatchData);
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    var newAndDeletedEvents = new NewAndDeletedEvents(List.of(), List.of());

    // Act
    pushDataRetriever.processMA18DPFeed(matchDataFeed);

    // Assert
    verifyNoInteractions(matchLiveDataProcessor);
    verifyNoInteractions(liveFixtureSummaryNotifier);
  }

  private static WebSocketManager.WSData buildWebSocket(boolean isOpen) {
    WebSocket ws = mock(WebSocket.class);
    when(ws.isOpen()).thenReturn(isOpen);
    return new WebSocketManager.WSData(ws, null);
  }

  private static WebSocketManager.WSData buildWebSocket(boolean isOpen, WSHandler wsHandler) {
    WebSocket ws = mock(WebSocket.class);
    when(ws.isOpen()).thenReturn(isOpen);
    return new WebSocketManager.WSData(ws, wsHandler);
  }

  private static MatchEventDTO buildMatchEvent() {
    return new MatchEventDTO("eventId", GOAL, "teamId", now());
  }

  private static PlayerDataDTO buildPlayerDataDTO() {
    return PlayerDataDTO
      .builder().player(PlayerCanned.createPlayer().build()).build();
  }
}
