package com.wsf.dataingestor.unit.opta.parsers.events;

import java.time.Instant;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed;
import com.wsf.dataingestor.opta.parsers.events.EventInfo;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class EventInfoTest {

  @Test
  public void whenAnEventWithOutcome0IsTested_andTheOutcomeHasToBe1_thenTheEventIsDiscarded() {
    TestEventInfo eventInfo = new TestEventInfo(false, true, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventWithOutcome1IsTested_andTheOutcomeHasToBe1_thenTheEventIsProcessed() {
    TestEventInfo eventInfo = new TestEventInfo(false, true, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 1;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertTrue(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventWithOutcome0IsTested_andTheOutcomeDoesNotHaveToBe1_thenTheEventIsProcessed() {
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertTrue(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventWithOutcome0IsTested_andTheOutcomeHasToBe0_thenTheEventIsProcessed() {
    TestEventInfo eventInfo = new TestEventInfo(true, false, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertTrue(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventWithOutcome1IsTested_andTheOutcomeHasToBe0_thenTheEventIsNotProcessed() {
    TestEventInfo eventInfo = new TestEventInfo(true, false, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 1;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventWithOutcome1IsTested_andTheOutcomeHasToBe0And1_thenTheEventIsNotProcessed() {
    TestEventInfo eventInfo = new TestEventInfo(true, true, Map.of(), Set.of());
    int typeId = 1;
    short outcome = 1;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventIsTested_andTheQualifierHasToBeExcluded_thenTheEventIsDiscarded() {
    int qualifierId = 4;
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(), Set.of(qualifierId));
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of(4, "value");
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventIsTested_andTheQualifierHasToBeIncluded_thenTheEventIsProcessed() {
    int qualifierId = 4;
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(qualifierId, Set.of()), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of(4, "value");
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertTrue(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventIsTested_andItDoesNotContainTheQualifierThatHasToBeIncluded_thenTheEventIsDiscarded() {
    int qualifierId = 4;
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(qualifierId, Set.of()), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of();
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventIsTested_andItContainsTheQualifierAndValueThatHaveToBeIncluded_thenTheEventIsProcessed() {
    int qualifierId = 4;
    String value = "value";
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(qualifierId, Set.of(value)), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of(4, "value");
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertTrue(eventInfo.includeEvent(event));
  }

  @Test
  public void whenAnEventIsTested_andItDoesNotContainTheQualifierAndValueThatHaveToBeIncluded_thenTheEventIsDiscarded() {
    int qualifierId = 4;
    String value = "value";
    TestEventInfo eventInfo = new TestEventInfo(false, false, Map.of(qualifierId, Set.of(value)), Set.of());
    int typeId = 1;
    short outcome = 0;
    Map<Integer, String> qualifiers = Map.of(4, "not-value");
    MA18DPFeed.Event event = buildEvent(typeId, outcome, qualifiers);

    assertFalse(eventInfo.includeEvent(event));
  }

  private MA18DPFeed.Event buildEvent(int typeId, short outcome, Map<Integer, String> qualifiers) {
    return new MA18DPFeed.Event("id", 3, "eventId", typeId, "playerId", "contestantId", "24", 1, outcome, Instant.now(),
      Instant.now(), qualifiers);
  }

  static class TestEventInfo extends EventInfo {
    public TestEventInfo(boolean outcomeHasToBeZero, boolean outcomeHasToBeOne,
                         Map<Integer, Set<String>> qualifiersIdAndValuesToInclude, Set<Integer> qualifiersIdToExclude) {
      super(outcomeHasToBeZero, outcomeHasToBeOne, qualifiersIdAndValuesToInclude, qualifiersIdToExclude, Set.of(),
        EventInfo::defaultIncludeEventFn, false, false, false);
    }
  }
}
