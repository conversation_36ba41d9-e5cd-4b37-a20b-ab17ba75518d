package com.wsf.dataingestor.unit.crons;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.NoSuchElementException;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.crons.SchedulerService;
import com.wsf.dataingestor.crons.SettlementUpdater;
import com.wsf.dataingestor.exceptions.UnsettlableFixtureException;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ratings.FinalFixtureSummaryNotifier;
import com.wsf.dataingestor.services.ratings.MatchSettlementProcessor;
import com.wsf.dataingestor.services.ratings.SettlementDataRetriever;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.NO_SETTLEMENT_ERROR;
import static com.wsf.dataingestor.metrics.MetricsManager.Metrics.SETTLEMENT_UPDATE_ERROR;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class SettlementUpdaterTest extends TestWithMocks {

  private static final ObjectId FIXTURE_ID = ObjectId.get();

  private static final JobKey SETTLEMENT_UPDATER_JOB_KEY = JobKey.jobKey("test");

  @Mock
  private SchedulerService schedulerService;
  @Mock
  private FeedsConfigService feedsConfigService;
  @Mock
  private SettlementDataRetriever settlementDataRetriever;
  @Mock
  private MatchSettlementProcessor matchSettlementProcessor;
  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;
  @Mock
  private FixtureService fixtureService;
  @Mock
  private JobExecutionContext jobExecutionContext;
  @Mock
  private JobDetail jobDetail;
  @Mock
  private JobDataMap jobDataMap;
  @Mock
  private Trigger trigger;
  @Mock
  private FinalFixtureSummaryNotifier finalFixtureSummaryNotifierMock;

  private SettlementUpdater settlementUpdater;
  private MeterRegistry metricsRegistry;

  @Before
  public void setUp() throws Exception {
    when(feedsConfigService.getSettlementDataRetriever(anyString())).thenReturn(settlementDataRetriever);
    metricsRegistry = new SimpleMeterRegistry();
    settlementUpdater = new SettlementUpdater(5, 5, schedulerService, feedsConfigService, matchSettlementProcessor,
      fixtureService, ongoingMatchDataCacheService, finalFixtureSummaryNotifierMock,
      new MetricsManager(metricsRegistry));
    mockJobExecution();
  }

  @Test
  public void whenSettlementIsScheduled_thenTheJobIsScheduled() {
    Fixture fixture = mockFixture();

    settlementUpdater.scheduleSettlementJob(fixture);

    verify(schedulerService).scheduleJob(any(), any(), any());
  }

  @Test
  public void whenSettlementIsScheduled_andFixtureIsAlreadySettled_thenTheJobIsStopped() throws SchedulerException {
    mockSettledFixture();

    settlementUpdater.execute(jobExecutionContext);

    verifyNoInteractions(matchSettlementProcessor);
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andTheDataIsNotFinal_thenTheJobIsNotStopped() throws JobExecutionException {
    Fixture fixture = mockFixture();
    mockFeedRetrieval(fixture, false);

    settlementUpdater.execute(jobExecutionContext);

    verify(ongoingMatchDataCacheService, never()).remove(FIXTURE_ID.toString());
    verifyNoMoreInteractions(schedulerService);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andAnExceptionIsThrown_thenTheJobIsNotStoppedAndMetricsAreIncreased() throws JobExecutionException {
    mockMatchNotFound();

    settlementUpdater.execute(jobExecutionContext);

    assertEquals(1, metricsRegistry.get(SETTLEMENT_UPDATE_ERROR.value()).counter()
      .count(), 0.01);
    verifyNoMoreInteractions(ongoingMatchDataCacheService);
    verifyNoMoreInteractions(schedulerService);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andAnUnsettlableExceptionIsThrown_thenTheJobIsStoppedAndMetricsAreIncreased() throws SchedulerException {
    doThrow(new UnsettlableFixtureException("roma juve", "roma is too good"))
      .when(fixtureService).getFixture(anyString());

    settlementUpdater.execute(jobExecutionContext);

    assertEquals(1, metricsRegistry.get(NO_SETTLEMENT_ERROR.value()).counter()
      .count(), 0.01);
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andTheDataIsFinal_thenJobIsStopped() throws SchedulerException {
    when(matchSettlementProcessor.processFeed(any(), anyBoolean())).thenReturn(true);
    Fixture fixture = mockFixture();
    MatchDataFeed dataFeed = mockFeedRetrieval(fixture, true);

    settlementUpdater.execute(jobExecutionContext);

    verify(matchSettlementProcessor).processFeed(dataFeed, false);
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andSettlementIsNotDone_andJobHasNoMoreRepetitions_thenTheSettlementIsNotForced() throws SchedulerException {
    // Arrange
    when(trigger.mayFireAgain()).thenReturn(false);
    when(matchSettlementProcessor.processFeed(any(), eq(false))).thenReturn(false);
    when(matchSettlementProcessor.processFeed(any(), eq(true))).thenReturn(true);

    Fixture fixture = mockFixture();
    MatchDataFeed dataFeed = mockFeedRetrieval(fixture, false);

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    verify(matchSettlementProcessor).processFeed(dataFeed, false);
    verifyNoMoreInteractions(matchSettlementProcessor);
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenAnExceptionIsThrownDuringSettlement_andTheMaxRetriesAreReached_thenACounterIsIncreased_andJobStopped() throws SchedulerException {
    // Arrange
    when(trigger.mayFireAgain()).thenReturn(false);
    mockMatchNotFound();

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    double settlementUpdateErrorCounter = metricsRegistry.get(NO_SETTLEMENT_ERROR.value()).counter()
      .count();
    assertThat(settlementUpdateErrorCounter, is(1D));
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenTheSettlementIsForced_andSettlementIsNotDone_thenACounterIsIncreased() throws SchedulerException {
    // Arrange
    when(trigger.mayFireAgain()).thenReturn(false);
    when(matchSettlementProcessor.processFeed(any(), eq(false))).thenReturn(false);
    when(matchSettlementProcessor.processFeed(any(), eq(true))).thenReturn(false);

    Fixture fixture = mockFixture();
    MatchDataFeed dataFeed = mockFeedRetrieval(fixture, false);

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    double settlementUpdateErrorCounter = metricsRegistry.get(NO_SETTLEMENT_ERROR.value()).counter()
      .count();
    assertThat(settlementUpdateErrorCounter, is(1D));
    verify(ongoingMatchDataCacheService).remove(FIXTURE_ID.toString());
    verify(schedulerService).deleteJob(SETTLEMENT_UPDATER_JOB_KEY);
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andSettlementIsDone_thenFixtureSummaryIsNotified() throws JobExecutionException {
    // Arrange
    when(trigger.mayFireAgain()).thenReturn(false);
    boolean isFinalData = true;
    when(matchSettlementProcessor.processFeed(any(), anyBoolean())).thenReturn(isFinalData);
    Fixture fixture = mockFixture();
    mockFeedRetrieval(fixture, isFinalData);

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    double settlementUpdateErrorCounter = metricsRegistry.get(NO_SETTLEMENT_ERROR.value()).counter()
      .count();
    assertThat(settlementUpdateErrorCounter, is(0D));
    verify(finalFixtureSummaryNotifierMock, times(1)).sendSettlementSummary(any());
  }

  @Test
  public void whenTheSettlementJobIsExecuted_andSettlementIsNotDone_thenFixtureSummaryIsNotNotified() throws JobExecutionException {
    // Arrange
    when(trigger.mayFireAgain()).thenReturn(false);
    boolean isFinalData = false;
    when(matchSettlementProcessor.processFeed(any(), anyBoolean())).thenReturn(isFinalData);
    Fixture fixture = mockFixture();
    mockFeedRetrieval(fixture, isFinalData);

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    double settlementUpdateErrorCounter = metricsRegistry.get(NO_SETTLEMENT_ERROR.value()).counter()
      .count();
    assertThat(settlementUpdateErrorCounter, is(1D));
    verifyNoInteractions(finalFixtureSummaryNotifierMock);
  }

  @Test
  public void whenASettlementIsProcessed_butTheMatchIsStartedLessThan45MinutesAgo_thenAnExceptionsIsThrown() throws JobExecutionException {
    Instant fixtureDate = Instant.now().minus(20, ChronoUnit.MINUTES);

    var fixture = FixtureCanned
      .defaultFixtureBuilder()
      .id(FIXTURE_ID)
      .tournament(Tournament
        .builder()
        .competition(Competition
          .builder().id(ObjectId.get()).build())
        .build())
      .optaFixtureId("id")
      .status(Fixture.FixtureStatus.PLAYED)
      .date(fixtureDate)
      .build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    var isFinalData = true;
    mockFeedRetrieval(fixture, isFinalData);

    // Act
    settlementUpdater.execute(jobExecutionContext);

    // Assert
    verifyNoInteractions(matchSettlementProcessor);
  }

  private MatchDataFeed mockFeedRetrieval(Fixture fixture, boolean isFinalData) {
    var team = TeamCanned.defaultTeam();
    var event = EntityEventDTO
      .builder().eventType(FOUL).teamId(team.getId().toString()).build();
    MatchDataFeed dataFeed;
    if (isFinalData) {
      dataFeed = MatchDataFeedCanned
        .matchDataFeedCanned()
        .fixture(fixture)
        .fixtureStatus(FeedFixtureStatus.PLAYED)
        .aggregatedPlayerMatchEvents(List.of(event))
        .teamsData(List.of(TeamDataDTO
          .builder().team(team).build(), TeamDataDTO
          .builder().team(team).build()))
        .isFinalData(true)
        .build();
    } else {
      dataFeed = MatchDataFeedCanned
        .matchDataFeedCanned()
        .fixture(fixture)
        .fixtureStatus(FeedFixtureStatus.PLAYED)
        .aggregatedPlayerMatchEvents(List.of())
        .teamsData(List.of(TeamDataDTO
          .builder().team(team).build(), TeamDataDTO
          .builder().team(team).build()))
        .isFinalData(false)
        .build();
    }
    when(settlementDataRetriever.retrieveSettlementData(any())).thenReturn(dataFeed);
    return dataFeed;
  }

  private Fixture mockFixture() {
    var fixture = FixtureCanned
      .defaultFixtureBuilder()
      .id(FIXTURE_ID)
      .tournament(Tournament
        .builder()
        .competition(Competition
          .builder().id(ObjectId.get()).build())
        .build())
      .optaFixtureId("id")
      .status(Fixture.FixtureStatus.PLAYED)
      .build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    return fixture;
  }

  private void mockSettledFixture() {
    var fixture = FixtureCanned.defaultFixtureBuilder().processStatus(Fixture.FixtureProcessStatus.SETTLED).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
  }

  private void mockMatchNotFound() {
    doThrow(new NoSuchElementException())
      .when(fixtureService).getFixture(anyString());
  }

  private void mockJobExecution() {
    when(jobDetail.getKey()).thenReturn(SETTLEMENT_UPDATER_JOB_KEY);
    when(jobDataMap.getString("fixtureId")).thenReturn(FIXTURE_ID.toString());
    when(jobExecutionContext.getMergedJobDataMap()).thenReturn(jobDataMap);
    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
    when(jobExecutionContext.getTrigger()).thenReturn(trigger);
    when(trigger.mayFireAgain()).thenReturn(true);
  }
}
