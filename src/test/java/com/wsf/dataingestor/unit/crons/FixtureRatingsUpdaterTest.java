package com.wsf.dataingestor.unit.crons;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Instant;
import java.util.Date;
import java.util.NoSuchElementException;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerKey;
import com.wsf.dataingestor.crons.FixtureRatingsUpdater;
import com.wsf.dataingestor.crons.FixtureStartUpdater;
import com.wsf.dataingestor.crons.SchedulerService;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.ThreadPoolService;
import com.wsf.dataingestor.services.fixtures.FixtureInfoUpdater;
import com.wsf.dataingestor.services.ratings.FixtureDataProcessor;
import com.wsf.dataingestor.services.ratings.MatchSettlementLogic;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Fixture;

import static com.google.common.util.concurrent.MoreExecutors.directExecutor;
import static com.wsf.dataingestor.crons.FixtureStartUpdater.FIXTURE_START_JOB_TRIGGER_GROUP;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.SUSPENDED;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static java.time.Instant.now;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.HOURS;
import static java.time.temporal.ChronoUnit.MINUTES;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class FixtureRatingsUpdaterTest extends TestWithMocks {

  private static final String FIXTURE_ID = FixtureCanned.DEFAULT_ID.toString();
  private static final JobKey RATINGS_UPDATER_JOB_KEY = JobKey.jobKey("test");

  @Mock
  private SchedulerService schedulerService;

  @Mock
  private FixtureService fixtureService;

  @Mock
  private FixtureInfoUpdater fixtureInfoUpdater;

  @Mock
  private FeedsConfigService feedsConfigService;

  @Mock
  private FixtureDataProcessor fixtureDataProcessor;

  @Mock
  private OptaFixturePushDataRetriever matchPushDataRetriever;

  @Mock
  private MatchSettlementLogic matchSettlementLogic;

  @Mock
  private JobExecutionContext jobExecutionContext;

  @Mock
  private KafkaService kafkaService;

  @Mock
  private JobDetail jobDetail;

  @Mock
  private JobDataMap jobDataMap;

  private FixtureRatingsUpdater fixtureRatingsUpdater;

  private final MetricsManager metricsManager = new MetricsManager(new SimpleMeterRegistry());

  @Before
  public void setUp() throws Exception {
    var threadPoolService = new ThreadPoolService(directExecutor(), directExecutor(), metricsManager);
    when(feedsConfigService.getFixtureDataProcessor(anyString())).thenReturn(fixtureDataProcessor);
    fixtureRatingsUpdater = new FixtureRatingsUpdater(schedulerService, fixtureService, feedsConfigService,
      matchSettlementLogic, fixtureInfoUpdater, threadPoolService, metricsManager, kafkaService);
    mockJobExecution();
  }

  @Test
  public void whenJobIsExecuted_andMatchWasPlayed_thenTheJobIsStopped() throws SchedulerException {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.LIVE, now());

    when(fixtureService.storeFixtureFinished(any())).thenReturn(fixture);
    MatchDataFeed feed = matchDataFeedCanned().fixture(fixture).fixtureStatus(PLAYED).build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);
    mockPreMatchJobKey();

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verify(schedulerService).deleteJob(RATINGS_UPDATER_JOB_KEY);
    verify(schedulerService).deleteJob(
      new JobKey(fixture.getIdAsString(), FixtureStartUpdater.FIXTURE_START_JOB_GROUP));

    verify(matchSettlementLogic).processPlayedFixtureSettlement(fixture);
    verify(fixtureService).storeFixtureFinished(eq(fixture));

    verify(kafkaService).sendFixtureChange(any(), any());
  }

  @Test
  public void whenJobIsExecuted_andMatchWasPostponed_thenTheJobIsStoppedAndSettlementProcessed() throws SchedulerException {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.LIVE, now());

    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.CANCELLED)
      .build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);
    mockPreMatchJobKey();

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verify(schedulerService).deleteJob(RATINGS_UPDATER_JOB_KEY);
    verify(schedulerService).deleteJob(
      new JobKey(fixture.getIdAsString(), FixtureStartUpdater.FIXTURE_START_JOB_GROUP));
  }

  @Test
  public void whenJobIsExecuted_andMatchIsNotFound_thenTheJobIsStopped() throws SchedulerException {
    mockFixtureNotFound();

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verify(schedulerService).deleteJob(RATINGS_UPDATER_JOB_KEY);
    verify(schedulerService).deleteJob(new JobKey(FIXTURE_ID, FixtureStartUpdater.FIXTURE_START_JOB_GROUP));

    verifyNoMoreInteractions(fixtureDataProcessor);
    verifyNoMoreInteractions(matchSettlementLogic);
    verifyNoMoreInteractions(matchPushDataRetriever);
  }

  @Test
  public void whenJobIsExecuted_andFeedMatchTimeIsDifferentByMoreThan3HThanDbMatchTime_thenTheJobIsStoppedAndFixtureUpdated() throws SchedulerException {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.FIXTURE, now());
    Instant newDate = now().plus(10, DAYS);
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .date(newDate)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .feedId("")
      .build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);

    fixtureRatingsUpdater.execute(jobExecutionContext);

    assertThat(metricsManager.CHANGED_FIXTURE_SCHEDULE.count(), is(1.0));

    verify(schedulerService).deleteJob(RATINGS_UPDATER_JOB_KEY);
    verify(schedulerService).deleteJob(
      new JobKey(fixture.getIdAsString(), FixtureStartUpdater.FIXTURE_START_JOB_GROUP));

    verify(fixtureInfoUpdater).processFixtureInfoDuringLive(fixture, Fixture.FixtureStatus.FIXTURE, newDate, "");
    verify(fixtureService).removeFixtureProcessStatus(fixture);
  }

  @Test
  public void whenJobIsExecuted_andFeedMatchTimeIsDifferentByLessThan3HThanDbMatchTime_thenTheJobIsRescheduledAndFixtureUpdated() {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.FIXTURE, now());
    Instant newDate = now().plus(5, MINUTES);
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .date(newDate)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .feedId("")
      .build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);
    JobKey expectedPreMatchJobKey = mockPreMatchJobKey();

    fixtureRatingsUpdater.execute(jobExecutionContext);

    TriggerKey expectedTriggerKey = new TriggerKey(fixture.getIdAsString(), FIXTURE_START_JOB_TRIGGER_GROUP);
    ArgumentCaptor<Trigger> captor = ArgumentCaptor.forClass(Trigger.class);
    verify(schedulerService).rescheduleTrigger(eq(expectedTriggerKey), captor.capture());
    Trigger rescheduledTrigger = captor.getValue();
    assertThat(rescheduledTrigger.getStartTime(), is(new Date(newDate.toEpochMilli())));
    assertThat(rescheduledTrigger.getJobKey(), is(expectedPreMatchJobKey));

    verify(fixtureInfoUpdater).processFixtureInfoDuringLive(fixture, Fixture.FixtureStatus.FIXTURE, newDate, "");
    verify(fixtureService, never()).removeFixtureProcessStatus(any());
  }

  @Test
  public void whenJobIsExecuted_andFeedMatchTimeIsDifferentByLessThan3HThanDbMatchTimeAndInThePast_thenTheJobIsNotRescheduledAndFixtureUpdated() {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.FIXTURE, now());
    Instant newDate = now().minus(5, MINUTES);
    MatchDataFeed feed = matchDataFeedCanned()
      .fixture(fixture)
      .date(newDate)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .feedId("")
      .build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verify(schedulerService, never()).rescheduleTrigger(any(), any());
    verify(fixtureInfoUpdater).processFixtureInfoDuringLive(fixture, Fixture.FixtureStatus.FIXTURE, newDate, "");
    verify(fixtureService, never()).removeFixtureProcessStatus(any());
  }

  @Test
  public void whenJobIsExecuted_andAnExceptionIsThrown_thenTheRelatedMetricIsIncremented() {
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenThrow(
      new IllegalArgumentException("exception"));

    fixtureRatingsUpdater.execute(jobExecutionContext);

    assertThat(metricsManager.RATINGS_FEED_PROCESSING_ERROR.count(), is(1.0));

    verifyNoInteractions(schedulerService);
    verifyNoInteractions(fixtureInfoUpdater);
  }

  @Test
  public void whenJobIsExecuted_andFixtureIsSuspended_andTheFixtureStartWasTwoHoursAgo_thenTheJobIsNotStopped() {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.LIVE, now().minus(2, HOURS));

    MatchDataFeed feed = matchDataFeedCanned().fixture(fixture).fixtureStatus(SUSPENDED).feedId("").build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifyNoInteractions(schedulerService);
    verify(fixtureService, never()).removeFixtureProcessStatus(any());
  }

  @Test
  public void whenJobIsExecuted_andFixtureIsSuspended_andTheFixtureStartWasSevenHoursAgo_thenTheJobIsStopped() throws SchedulerException {
    Fixture fixture = mockFixture(Fixture.FixtureStatus.LIVE, now().minus(7, HOURS));

    MatchDataFeed feed = matchDataFeedCanned().fixture(fixture).fixtureStatus(SUSPENDED).feedId("").build();
    when(fixtureDataProcessor.processFixtureStatsFeed(any(Fixture.class))).thenReturn(feed);

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verify(schedulerService, times(2)).deleteJob(any());
    verify(fixtureService).removeFixtureProcessStatus(fixture);
  }

  private JobKey mockPreMatchJobKey() {
    JobDetail preMatchJobDetail = mock(JobDetail.class);
    JobKey preMatchJobKey = new JobKey("prematch", "jobkey");
    when(preMatchJobDetail.getKey()).thenReturn(preMatchJobKey);
    when(schedulerService.getJobDetail(any())).thenReturn(preMatchJobDetail);
    return preMatchJobKey;
  }

  private Fixture mockFixture(Fixture.FixtureStatus status, Instant date) {
    Fixture fixture = fixtureCanned().id(new ObjectId(FIXTURE_ID)).date(date).status(status).build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);
    return fixture;
  }

  private void mockFixtureNotFound() {
    doThrow(new NoSuchElementException())
      .when(fixtureService).getFixture(anyString());
  }

  private void mockJobExecution() {
    when(jobDetail.getKey()).thenReturn(RATINGS_UPDATER_JOB_KEY);
    when(jobDataMap.getString(anyString())).thenReturn(FIXTURE_ID);
    when(jobExecutionContext.getMergedJobDataMap()).thenReturn(jobDataMap);
    when(jobExecutionContext.getJobDetail()).thenReturn(jobDetail);
  }
}
