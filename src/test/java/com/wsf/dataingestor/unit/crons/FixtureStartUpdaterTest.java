package com.wsf.dataingestor.unit.crons;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.crons.FixtureStartUpdater;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.services.events.MatchEventsProcessor;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FixtureStartUpdaterTest extends TestWithMocks {

  @Mock
  private MatchEventsProcessor matchEventsProcessor;

  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;

  @Mock
  private FixtureService fixtureService;

  private FixtureStartUpdater fixtureStartUpdater;

  @Before
  public void setUp() throws Exception {
    fixtureStartUpdater = new FixtureStartUpdater(matchEventsProcessor, ongoingMatchDataCacheService, fixtureService,
      new MetricsManager(new SimpleMeterRegistry()));
  }

  @Test
  public void sendPreMatchTrigger() {
    // Arrange
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned.ongoingMatchDataCanned().build();
    when(ongoingMatchDataCacheService.get(anyString())).thenReturn(ongoingMatchData);

    Fixture fixture = fixtureCanned().build();
    when(fixtureService.getFixture(anyString())).thenReturn(fixture);

    // Act
    fixtureStartUpdater.sendPreFixtureTrigger("fixtureId");

    // Assert
    ArgumentCaptor<MatchDataFeed> captor = ArgumentCaptor.forClass(MatchDataFeed.class);
    verify(matchEventsProcessor).processMatchEvents(captor.capture(), eq(ongoingMatchData));
    MatchDataFeed matchDataFeed = captor.getValue();
    MatchDataFeed.MatchEventDTO matchEventDTO = matchDataFeed.getMatchEvents().get(0);
    assertThat(matchEventDTO.getEvent(), is(SoccerMatchEvent.MATCH_ABOUT_TO_START));
  }
}
