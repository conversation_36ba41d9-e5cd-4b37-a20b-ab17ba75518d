package com.wsf.dataingestor.unit.crons;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.List;
import java.util.Set;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.crons.CurrentTournamentUpdater;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.services.CompetitionService;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.tournaments.CurrentTournamentRetriever;
import com.wsf.dataingestor.services.tournaments.TournamentsIngestionService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.shared.canned.CurrentTournamentFeedCanned;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class CurrentTournamentUpdaterTest extends TestWithMocks {

  @Mock
  CompetitionService competitionService;
  @Mock
  FeedsConfigService feedsConfigService;
  @Mock
  TournamentsIngestionService tournamentsIngestionService;
  MetricsManager metricsRegistry = new MetricsManager(new SimpleMeterRegistry());

  private CurrentTournamentUpdater currentTournamentUpdater;

  @Before
  public void setUp() throws Exception {
    currentTournamentUpdater = new CurrentTournamentUpdater(competitionService, feedsConfigService,
      tournamentsIngestionService, metricsRegistry);
  }

  @Test
  public void whenTheCurrentTournamentForACompetitionIsIngested_andThereAreTwoCompetitionExternalIds_andTheFirstDoesNotHaveACurrentSeason_thenBothExternalIdsAreProcessed() {
    var smCompetitionId1 = "smCompetitionId1";
    var smCompetitionId2 = "smCompetitionId2";
    var competition = CompetitionCanned.competitionCanned().build();

    when(feedsConfigService.getCompetitionExternalIds(competition)).thenReturn(
      Set.of(smCompetitionId1, smCompetitionId2));

    var firstCurrentTournamentFeed = CurrentTournamentFeedCanned.currentTournamentFeedCanned()
      .externalSeasonId(null)
      .startDate(null)
      .build();
    var secondCurrentTournamentFeed = CurrentTournamentFeedCanned.currentTournamentFeedCanned().build();
    var tournamentRetriever = mock(CurrentTournamentRetriever.class);
    when(tournamentRetriever.retrieveTournamentFeed(anyString()))
      .thenReturn(firstCurrentTournamentFeed)
      .thenReturn(secondCurrentTournamentFeed);
    when(feedsConfigService.getCurrentTournamentRetriever(anyString())).thenReturn(tournamentRetriever);

    currentTournamentUpdater.ingestCurrentTournamentForCompetition(competition);

    verify(tournamentsIngestionService).processTournamentFeed(List.of(firstCurrentTournamentFeed, secondCurrentTournamentFeed));
  }

  @Test
  public void whenTheCurrentTournamentForACompetitionIsIngested_andThereAreTwoCompetitionExternalIds_andTheSecondDoesNotHaveACurrentSeason_thenBothExternalIdsAreProcessedInTheExpectedOrder() {
    var smCompetitionId1 = "smCompetitionId1";
    var smCompetitionId2 = "smCompetitionId2";
    var competition = CompetitionCanned.competitionCanned().build();

    when(feedsConfigService.getCompetitionExternalIds(competition)).thenReturn(
      Set.of(smCompetitionId1, smCompetitionId2));

    var firstCurrentTournamentFeed = CurrentTournamentFeedCanned.currentTournamentFeedCanned().build();
    var secondCurrentTournamentFeed = CurrentTournamentFeedCanned.currentTournamentFeedCanned()
      .externalSeasonId(null)
      .build();
    var tournamentRetriever = mock(CurrentTournamentRetriever.class);
    when(tournamentRetriever.retrieveTournamentFeed(anyString()))
      .thenReturn(firstCurrentTournamentFeed)
      .thenReturn(secondCurrentTournamentFeed);
    when(feedsConfigService.getCurrentTournamentRetriever(anyString())).thenReturn(tournamentRetriever);

    currentTournamentUpdater.ingestCurrentTournamentForCompetition(competition);

    verify(tournamentsIngestionService).processTournamentFeed(List.of(firstCurrentTournamentFeed, secondCurrentTournamentFeed));
  }

  @Test
  public void whenTheCurrentTournamentForACompetitionIsIngested_andThereAreTwoCompetitionExternalIds_andTheFirstThrowsAnException_thenTheDataForTheSecondExternalIdIsIsNotProcessed() {
    var smCompetitionId1 = "smCompetitionId1";
    var smCompetitionId2 = "smCompetitionId2";
    var competition = CompetitionCanned.competitionCanned().build();

    when(feedsConfigService.getCompetitionExternalIds(competition)).thenReturn(
      Set.of(smCompetitionId1, smCompetitionId2));

    var currentTournamentFeed = CurrentTournamentFeedCanned.currentTournamentFeedCanned().build();
    var tournamentRetriever = mock(CurrentTournamentRetriever.class);
    when(tournamentRetriever.retrieveTournamentFeed(anyString())).thenThrow(RuntimeException.class)
      .thenReturn(currentTournamentFeed);
    when(feedsConfigService.getCurrentTournamentRetriever(anyString())).thenReturn(tournamentRetriever);

    currentTournamentUpdater.ingestCurrentTournamentForCompetition(competition);

    verifyNoInteractions(tournamentsIngestionService);
  }
}
