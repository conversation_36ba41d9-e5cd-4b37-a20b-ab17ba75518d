package com.wsf.dataingestor.unit.utils.http;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.wsf.dataingestor.utils.http.RetryableHttpClient;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = RetryableHttpClientTest.Configuration.class)
public class RetryableHttpClientTest {

  @Autowired
  HttpClient httpClientMock;

  @Autowired
  RetryableHttpClient retryableHttpClient;

  @Before
  public void setup() {
    Mockito.reset(httpClientMock);
  }

  @Test
  public void twoRetries_success() throws IOException, InterruptedException {
    HttpResponse<Object> resp = Mockito.mock(HttpResponse.class);
    when(httpClientMock.send(any(), any())).thenThrow(new IOException()).thenThrow(new IOException())
      .thenReturn(resp);
    HttpResponse<byte[]> clientResp = retryableHttpClient.send(
      HttpRequest.newBuilder().uri(URI.create("http://test")).build());
    assertThat(clientResp, is(resp));
    verify(httpClientMock, times(3)).send(any(), any());
  }

  @Test(expected = IOException.class)
  public void threeRetries_fail() throws IOException, InterruptedException {
    when(httpClientMock.send(any(), any()))
      .thenThrow(new IOException())
      .thenThrow(new IOException())
      .thenThrow(new IOException());
    retryableHttpClient.send(HttpRequest.newBuilder().uri(URI.create("http://test")).build());
    verify(httpClientMock, times(3)).send(any(), any());
  }

  @EnableRetry
  public static class Configuration {
    @Bean
    public HttpClient httpClient() {
      return Mockito.mock(HttpClient.class);
    }

    @Bean
    public RetryableHttpClient retryableHttpClient(HttpClient httpClient) {
      return new RetryableHttpClient(httpClient);
    }
  }
}
