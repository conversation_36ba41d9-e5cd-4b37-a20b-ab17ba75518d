package com.wsf.dataingestor.unit.utils;

import java.util.HashMap;
import java.util.Map;
import org.junit.Test;

import static com.wsf.dataingestor.cache.CacheUtils.mergeStats;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.hasEntry;
import static org.junit.Assert.assertEquals;

public class CacheUtilsTest {

  @Test
  public void mergeStats_whenExistingDataIsEmpty_thenItShouldReturnANewMapEqualsTheNewData() {
    Map<String, Number> existingData = Map.of();
    Map<String, Number> newData = Map.of("goals", 1, "assists", 0);

    var result = mergeStats(existingData, newData);

    assertEquals(2, result.size());
    assertThat(result, hasEntry("goals", 1));
    assertThat(result, hasEntry("assists", 0));
  }

  @Test
  public void mergeStats_whenExistingDataHasEntriesAndNewDataIsEmpty_thenItShouldReturnANewMapEqualsTheExistingData() {
    Map<String, Number> existingData = Map.of("goals", 1, "assists", 0);
    Map<String, Number> newData = Map.of();

    var result = mergeStats(existingData, newData);

    assertEquals(2, result.size());
    assertThat(result, hasEntry("goals", 1));
    assertThat(result, hasEntry("assists", 0));
  }

  @Test
  public void mergeStats_whenBothMapsHaveData_thenItShouldReturnANewMapWithMergedData() {
    Map<String, Number> existingData = Map.of("goals", 1, "assists", 0);
    Map<String, Number> newData = Map.of("passes", 5, "shots", 3);

    var result = mergeStats(existingData, newData);

    assertEquals(4, result.size());
    assertThat(result, hasEntry("goals", 1));
    assertThat(result, hasEntry("assists", 0));
    assertThat(result, hasEntry("passes", 5));
    assertThat(result, hasEntry("shots", 3));
  }

  @Test
  public void mergeStats_whenBothMapsHaveDataWithSameKeys_thenItShouldKeepNewData() {
    Map<String, Number> existingData = Map.of("goals", 1, "assists", 0);
    Map<String, Number> newData = Map.of("passes", 5, "assists", 1);

    var result = mergeStats(existingData, newData);

    assertEquals(3, result.size());
    assertThat(result, hasEntry("goals", 1));
    assertThat(result, hasEntry("passes", 5));
    assertThat(result, hasEntry("assists", 1));
  }

  @Test
  public void mergeStats_whenTheNewDataIs0_andTheOldDataIsGreaterThan2_thenWeKeepTheOldData() {
    Map<String, Number> existingData = Map.of("goals", 3);
    Map<String, Number> newData = Map.of("goals", 0);

    var result = mergeStats(existingData, newData);

    assertEquals(1, result.size());
    assertThat(result, hasEntry("goals", 3));
  }

  @Test
  public void mergeStats_whenTheNewDataIsNull_andTheOldDataIsGreaterThan1_thenWeKeepTheOldData() {
    Map<String, Number> existingData = Map.of("goals", 2);
    Map<String, Number> newData = new HashMap<>() {{
      put("goals", null);
    }};

    var result = mergeStats(existingData, newData);

    assertEquals(1, result.size());
    assertThat(result, hasEntry("goals", 2));
  }

  @Test
  public void mergeStats_whenTheNewDataIs0_andTheOldDataIs1_thenWeKeepTheNewData() {
    Map<String, Number> existingData = Map.of("goals", 1);
    Map<String, Number> newData = Map.of("goals", 0);

    var result = mergeStats(existingData, newData);

    assertEquals(1, result.size());
    assertThat(result, hasEntry("goals", 0));
  }

  @Test
  public void mergeStats_whenTheNewDataIsNull_andTheOldDataIs1_thenWeKeep1() {
    Map<String, Number> existingData = Map.of("goals", 1);
    Map<String, Number> newData = new HashMap<>() {{
      put("goals", null);
    }};

    var result = mergeStats(existingData, newData);

    assertEquals(1, result.size());
    assertThat(result, hasEntry("goals", 1));
  }

  @Test
  public void mergeStats_shouldReturnAMutableMap() {
    Map<String, Number> existingData = Map.of();
    Map<String, Number> newData = Map.of("goals", 1, "assists", 2);

    var result = mergeStats(existingData, newData);

    result.put("passes", 3);
  }
}
