package com.wsf.dataingestor.unit.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import org.junit.Test;
import com.wsf.dataingestor.utils.TimeUtils;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class TimeUtilsTest {

  @Test
  public void testTomorrowAt() {
    LocalTime time = LocalTime.of(2, 0);
    LocalDateTime now = LocalDateTime.of(2021, 9, 13, 14, 20);

    Date tomorrowAt = TimeUtils.tomorrowAt(now, time);
    Date expectedDate = Date.from(ZonedDateTime
      .of(2021, 9, 14, 2, 0, 0, 0, ZoneId.of("UTC")).toInstant());
    assertThat(tomorrowAt, is(expectedDate));
  }

}
