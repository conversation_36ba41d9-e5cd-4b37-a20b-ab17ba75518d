package com.wsf.dataingestor.unit.utils;

import junitparams.JUnitParamsRunner;
import junitparams.Parameters;

import org.junit.Test;
import org.junit.runner.RunWith;

import com.wsf.dataingestor.utils.NumberUtils;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

@RunWith(JUnitParamsRunner.class)
public class NumberUtilsTest {

  private Object[] fromFloat() {
    return new Object[] {new Object[] {0F, 2, 0}, new Object[] {1.00F, 2, 100}, new Object[] {20F, 2, 2000},
                         new Object[] {2F, 2, 200}, new Object[] {2.45902F, 2, 246}, new Object[] {2.1F, 2, 210},};
  }

  private Object[] toFloat() {
    return new Object[] {new Object[] {100, 2, 1.00F}, new Object[] {2000, 2, 20.00F}, new Object[] {2, 2, 0.02F},
                         new Object[] {201, 2, 2.01F}, new Object[] {0, 2, 0.00F},};
  }

  @Test
  @Parameters(method = "fromFloat")
  public void testRemoveComma(float floatInput, int nDec, int expected) {
    assertThat(NumberUtils.removeCommaAndReturnInt(floatInput, nDec), is(expected));
  }

  @Test
  @Parameters(method = "toFloat")
  public void testAddComma(int input, int nDec, float expected) {
    assertThat(NumberUtils.addCommaToInt(input, nDec), is(expected));
  }

}
