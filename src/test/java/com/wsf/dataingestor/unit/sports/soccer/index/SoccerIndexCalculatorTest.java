package com.wsf.dataingestor.unit.sports.soccer.index;

import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import com.wsf.dataingestor.sports.soccer.index.SoccerIndexCalculator;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.Player;

import static com.wsf.domain.common.Player.Position.DEFENDER;
import static com.wsf.domain.common.Player.Position.FORWARD;
import static com.wsf.domain.common.Player.Position.GOALKEEPER;
import static com.wsf.domain.common.Player.Position.MIDFIELDER;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class SoccerIndexCalculatorTest {

  SoccerIndexCalculator calculator = new SoccerIndexCalculator();

  @Test
  public void whenTheIndexIsCalculatedForADefender_TheExpectedValueIsRetrieved() {
    Player.Position position = DEFENDER;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 1);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 1);
    stats.put("passes", 24);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 2);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 24);
    stats.put("gkGoalsConceded", 1);
    IndexPerformance index = calculator.calculate(position, stats);
    assertFalse(index.isValid());
    assertEquals(60.2, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAMidfielder_TheExpectedValueIsRetrieved() {
    Player.Position position = MIDFIELDER;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 1);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 1);
    stats.put("passes", 24);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 2);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 24);
    IndexPerformance index = calculator.calculate(position, stats);
    assertFalse(index.isValid());
    assertEquals(60.2, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAForward_TheExpectedValueIsRetrieved() {
    Player.Position position = FORWARD;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 1);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 1);
    stats.put("passes", 24);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 2);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 24);
    IndexPerformance index = calculator.calculate(position, stats);
    assertFalse(index.isValid());
    assertEquals(60.2, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAGoalkeeper_TheExpectedValueIsRetrieved() {
    Player.Position position = GOALKEEPER;
    Map<String, Number> stats = new HashMap<>();
    stats.put("gkPenaltiesSaved", 0);
    stats.put("headShots", 0);
    stats.put("backwardPasses", 2);
    stats.put("crosses", 0);
    stats.put("assistsGoal", 0);
    stats.put("missedBalls", 2);
    stats.put("passes", 11);
    stats.put("shots", 0);
    stats.put("gkGoalsConceded", 2);
    stats.put("completedLongPasses", 0);
    stats.put("longPasses", 0);
    stats.put("minsPlayed", 87);
    stats.put("gkExits", 0);
    stats.put("duels", 6);
    stats.put("penalties", 6);
    stats.put("lostBalls", 5);
    stats.put("redCards", 0);
    stats.put("yellowCards", 0);
    stats.put("aerialDuelsWon", 1);
    stats.put("opponentTeamScore", 2);
    stats.put("fouls", 0);
    stats.put("dribblesWon", 1);
    stats.put("shotsOnGoal", 0);
    stats.put("midAttRecoveries", 0);
    stats.put("completedCrosses", 0);
    stats.put("forwardPasses", 4);
    stats.put("passesFinalThird", 1);
    stats.put("corners", 0);
    stats.put("goalsMade", 0);
    stats.put("gkCleanSheet", 0);
    stats.put("completedPassesFinalThird", 1);
    stats.put("penaltiesScored", 0);
    stats.put("dribbles", 1);
    stats.put("duelsWon", 2);
    stats.put("directFreeKicksOnTarget", 0);
    stats.put("touchesInBox", 0);
    stats.put("gkSaves", 0);
    stats.put("interceptions", 1);
    stats.put("completedPasses", 10);
    stats.put("cleanSheet", 0);
    stats.put("keyPasses", 0);
    stats.put("penaltiesMissed", 0);
    stats.put("teamScore", 1);
    stats.put("aerialDuels", 2);
    stats.put("ownGoals", 1);
    IndexPerformance index = calculator.calculate(position, stats);
    assertTrue(index.isValid());
    assertEquals(46.7, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAGoalkeeper_AndHeSavedAPenalty_TheExpectedValueIsRetrieved() {
    Player.Position position = GOALKEEPER;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 1);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 1);
    stats.put("passes", 24);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 2);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 24);
    stats.put("gkSaves", 10);
    stats.put("gkPenaltiesSaved", 1);
    IndexPerformance index = calculator.calculate(position, stats);
    assertFalse(index.isValid());
    assertEquals(76.2, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAForward_AndHeScoredTwice_TheExpectedValueIsRetrieved() {
    Player.Position position = FORWARD;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 1);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 1);
    stats.put("passes", 24);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 2);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 24);
    stats.put("goalsMade", 2);
    IndexPerformance index = calculator.calculate(position, stats);
    assertFalse(index.isValid());
    assertEquals(72.2, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAForward_AndHeScoredAndServedAnAssistInLessThan30Mins_TheExpectedValueIsRetrieved() {
    Player.Position position = FORWARD;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 0);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 0);
    stats.put("passes", 1);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 0);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 1);
    stats.put("goalsMade", 1);
    stats.put("assistsGoal", 1);
    stats.put("minsPlayed", 29);
    IndexPerformance index = calculator.calculate(position, stats);
    assertTrue(index.isValid());
    assertEquals(73.05, index.getIndex(), 0.0);
  }

  @Test
  public void whenTheIndexIsCalculatedForAForward_AndHeScoredAndServedAnAssistInMoreThan30Mins_TheExpectedValueIsRetrieved() {
    Player.Position position = FORWARD;
    Map<String, Number> stats = new HashMap<>();
    stats.put("teamScore", 0);
    stats.put("opponentTeamScore", 0);
    stats.put("fouls", 0);
    stats.put("passes", 4);
    stats.put("penaltiesMissed", 0);
    stats.put("shots", 0);
    stats.put("keyPasses", 0);
    stats.put("completedPasses", 3);
    stats.put("goalsMade", 1);
    stats.put("assistsGoal", 1);
    stats.put("minsPlayed", 31);
    IndexPerformance index = calculator.calculate(position, stats);
    assertTrue(index.isValid());
    assertEquals(69.35, index.getIndex(), 0.0);
  }

}
