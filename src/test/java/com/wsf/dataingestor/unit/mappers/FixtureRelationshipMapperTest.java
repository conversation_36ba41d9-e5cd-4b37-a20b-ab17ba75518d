package com.wsf.dataingestor.unit.mappers;

import org.junit.Test;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.domain.common.Fixture.Relation;

import static com.wsf.dataingestor.mappers.FixtureRelationshipMapper.mapDtoRelationToDbRelation;
import static com.wsf.domain.common.Fixture.Relation.FIRST_LEG;
import static com.wsf.domain.common.Fixture.Relation.SECOND_LEG;
import static com.wsf.domain.common.Fixture.Relation.SINGLE;
import static org.assertj.core.api.Assertions.assertThat;

public class FixtureRelationshipMapperTest {

  @Test
  public void whenFirstLeg_thenMapsToDbFirstLeg() {
    Relation mapped = mapDtoRelationToDbRelation(FixtureDTO.Relation.FIRST_LEG);
    assertThat(mapped).isEqualTo(FIRST_LEG);
  }

  @Test
  public void whenSecondLeg_thenMapsToDbSecondLeg() {
    Relation mapped = mapDtoRelationToDbRelation(FixtureDTO.Relation.SECOND_LEG);
    assertThat(mapped).isEqualTo(SECOND_LEG);
  }

  @Test
  public void whenSingle_thenMapsToDbSingle() {
    Relation mapped = mapDtoRelationToDbRelation(FixtureDTO.Relation.SINGLE);
    assertThat(mapped).isEqualTo(SINGLE);
  }

  @Test
  public void whenNull_thenDefaultsToDbSingle() {
    Relation mapped = mapDtoRelationToDbRelation(null);
    assertThat(mapped).isEqualTo(SINGLE);
  }
}

