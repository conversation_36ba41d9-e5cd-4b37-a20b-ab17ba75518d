package com.wsf.dataingestor.unit.performance.cache;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import org.openjdk.jmh.annotations.Benchmark;
import org.openjdk.jmh.annotations.BenchmarkMode;
import org.openjdk.jmh.annotations.Fork;
import org.openjdk.jmh.annotations.Level;
import org.openjdk.jmh.annotations.Mode;
import org.openjdk.jmh.annotations.OutputTimeUnit;
import org.openjdk.jmh.annotations.Scope;
import org.openjdk.jmh.annotations.Setup;
import org.openjdk.jmh.annotations.State;
import org.openjdk.jmh.annotations.TearDown;
import org.springframework.cache.CacheManager;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import com.wsf.dataingestor.cache.PlayerMatchDataCacheService;
import com.wsf.dataingestor.cache.models.PlayerMatchData;

import static com.google.common.collect.Maps.newHashMap;

public class OngoingMatchesCacheTest {

  @Fork(value = 1)
  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  @OutputTimeUnit(TimeUnit.NANOSECONDS)
  public PlayerMatchData mergeCache(CacheTest cacheTest) {
    return cacheTest.cache.mergeIfExists("match1", "player1", null, cacheTest.newPlayerMatchData);
  }

  @State(Scope.Benchmark)
  public static class CacheTest {
    PlayerMatchDataCacheService cache;
    PlayerMatchData newPlayerMatchData;

    @Setup(Level.Trial)
    public void setupCache() {
      CacheManager cacheManager = new ConcurrentMapCacheManager();
      cache = new PlayerMatchDataCacheService(cacheManager);
    }

    @TearDown(Level.Trial)
    public void shutdownCache() {
    }

    @Setup(Level.Invocation)
    public void setUp() {
      Map<String, Number> stats = buildStats();
      PlayerMatchData playerMatchData = generateOngoingMatchData(stats);

      cache.set("match1", "player1", playerMatchData);

      Map.Entry<String, Number> entry = stats.entrySet()
        .iterator()
        .next();

      stats.put(entry.getKey(), 99);
      newPlayerMatchData = generateOngoingMatchData(stats);
    }
  }

  private static PlayerMatchData generateOngoingMatchData(Map<String, Number> stats) {
    return PlayerMatchData
      .builder().stats(stats).build();
  }

  private static Map<String, Number> buildStats() {
    Map<String, Number> stats = newHashMap();

    for (int i = 0; i < 25; i++) {
      stats.put(generateRandomString(7), Math.ceil(Math.random() * 100));
    }

    return stats;
  }

  private static String generateRandomString(int length) {
    byte[] array = new byte[length];
    new Random().nextBytes(array);
    return new String(array, StandardCharsets.UTF_8);
  }

}
