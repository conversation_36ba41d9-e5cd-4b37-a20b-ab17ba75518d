//package com.wsf.dataingestor.performance.parsers;
//
//import java.io.IOException;
//import java.util.concurrent.TimeUnit;
//import org.apache.commons.io.IOUtils;
//import org.openjdk.jmh.annotations.Benchmark;
//import org.openjdk.jmh.annotations.BenchmarkMode;
//import org.openjdk.jmh.annotations.Fork;
//import org.openjdk.jmh.annotations.Level;
//import org.openjdk.jmh.annotations.Mode;
//import org.openjdk.jmh.annotations.OutputTimeUnit;
//import org.openjdk.jmh.annotations.Scope;
//import org.openjdk.jmh.annotations.Setup;
//import org.openjdk.jmh.annotations.State;
//import com.wsf.dataingestor.config.JsonConfig;
//import com.wsf.dataingestor.models.PlayerIndexFeed;
//import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser;
//import com.wsf.dataingestor.opta.parsers.ma2.StatsConstants;
//import com.wsf.dataingestor.opta.parsers.ma2.StatsMapper;
//
//public class MA2FeedPerformanceTest {
//
//  @Fork(value = 1)
//  @Benchmark
//  @BenchmarkMode(Mode.AverageTime)
//  @OutputTimeUnit(TimeUnit.NANOSECONDS)
//  public PlayerIndexFeed parseFeed(FeedTest feedTest) throws IOException {
//    return feedTest.ma2FeedParser.parseFeed(feedTest.bytes);
//  }
//
//  @State(Scope.Benchmark)
//  public static class FeedTest {
//    byte[] bytes;
//    MA2FeedParser ma2FeedParser;
//
//    @Setup(Level.Invocation)
//    public void setUp() throws IOException {
//      ma2FeedParser = new MA2FeedParser(new JsonConfig().jsonObjectMapper(), new StatsMapper(StatsConstants.OPTA_TO_WSF_STATS_FINAL),
//        playerService,
//        matchService,
//        metricsRegistry);
//      bytes = IOUtils.resourceToByteArray("ma2.json", getClass().getClassLoader());
//    }
//  }
//}
