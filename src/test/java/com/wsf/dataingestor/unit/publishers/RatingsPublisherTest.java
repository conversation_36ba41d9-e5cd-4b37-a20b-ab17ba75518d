package com.wsf.dataingestor.unit.publishers;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.publishers.RatingsPublisher;
import com.wsf.dataingestor.services.LivePlayerRatingService;
import com.wsf.dataingestor.services.LiveTeamRatingService;
import com.wsf.dataingestor.services.PlayerRatingService;
import com.wsf.dataingestor.services.TeamRatingService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.TeamRating;

import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static org.mockito.Mockito.verify;

public class RatingsPublisherTest extends TestWithMocks {

  @Mock
  private PlayerRatingService playerRatingService;
  @Mock
  private TeamRatingService teamRatingService;
  @Mock
  private LivePlayerRatingService livePlayerRatingService;
  @Mock
  private LiveTeamRatingService liveTeamRatingService;
  @Mock
  private KafkaService kafkaService;

  private RatingsPublisher ratingsPublisher;

  @Before
  public void setUp() {
    ratingsPublisher = new RatingsPublisher(playerRatingService, teamRatingService, livePlayerRatingService,
      liveTeamRatingService, kafkaService);
  }

  @Test
  public void testPublishAndStorePlayerRating_Final() {
    PlayerRating rating = new PlayerRating();
    boolean isFinal = true;
    var metadata = EMPTY_METADATA;

    ratingsPublisher.publishAndStore(rating, isFinal, metadata);

    verify(kafkaService).sendFinalPlayerRating(rating, Fixture.FixtureStatus.LIVE, false, metadata);
    verify(playerRatingService).storeRating(rating);
    verify(livePlayerRatingService).storeRating(rating, isFinal);
  }

  @Test
  public void testPublishAndStorePlayerRating_Live() {
    PlayerRating rating = new PlayerRating();
    boolean isFinal = false;
    var metadata = EMPTY_METADATA;

    ratingsPublisher.publishAndStore(rating, isFinal, metadata);

    verify(kafkaService).sendLivePlayerRating(rating, metadata);
    verify(playerRatingService).storeRating(rating);
    verify(livePlayerRatingService).storeRating(rating, isFinal);
  }

  @Test
  public void testPublishAndStoreTeamRating() {
    TeamRating teamRating = new TeamRating();
    boolean isFinal = true;
    var metadata = EMPTY_METADATA;

    ratingsPublisher.publishAndStore(teamRating, isFinal, metadata);

    verify(kafkaService).sendLiveTeamRating(teamRating, metadata);
    verify(teamRatingService).storeRating(teamRating);
    verify(liveTeamRatingService).storeRating(teamRating, isFinal);
  }
}
