package com.wsf.dataingestor.unit.clients.http;

import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.time.LocalDate;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.EntityMapperConfig;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.UnmappedPlayerCanned;

import static com.wsf.domain.common.ExternalProvider.WYSCOUT;
import static java.nio.charset.Charset.defaultCharset;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
public class UnmappedEntityClientTest extends TestWithMocks {

  @Mock
  private RestTemplate restTemplateMock;

  private UnmappedEntityClient entityClient;

  @Before
  public void setup() {
    entityClient = new UnmappedEntityClient(new EntityMapperConfig("localhost", "6666"), restTemplateMock);
  }

  @Test
  public void whenAPlayerIsNotFound_thenFalseIsReturned() {
    when(restTemplateMock.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class),
      anyMap())).thenThrow(
      new HttpClientErrorException(HttpStatus.NOT_FOUND, "", new HttpHeaders(), new byte[] {}, defaultCharset()));
    boolean resp = callFindPlayerOrCreateUnmapped();
    assertFalse(resp);
  }

  @Test(expected = IllegalStateException.class)
  public void whenAnExceptionIsThrown_thenAnIllegalStateExceptionIsRethrown() {
    when(restTemplateMock.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class),
      anyMap())).thenThrow(
      new HttpClientErrorException(HttpStatus.SERVICE_UNAVAILABLE, "", new HttpHeaders(), new byte[] {},
        defaultCharset()));
    callFindPlayerOrCreateUnmapped();
  }

  @Test
  public void whenAnUnmappedPlayerWasCreated_thenTrueIsReturned() {
    when(restTemplateMock.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class),
      anyMap())).thenReturn(ResponseEntity.created(URI.create("")).build());
    boolean resp = callFindPlayerOrCreateUnmapped();
    assertNotNull(resp);
  }

  @Test(expected = IllegalArgumentException.class)
  public void whenANotSupportedResponseIsReceived_thenAnExceptionIsThrown() {
    when(restTemplateMock.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class), any(Class.class),
      anyMap())).thenReturn(ResponseEntity.ok().body(UnmappedPlayerCanned.createUnmappedPlayer().build()));
    callFindPlayerOrCreateUnmapped();
  }

  private boolean callFindPlayerOrCreateUnmapped() {
    return entityClient.createUnmappedPlayer("Lorenzo", "Pellegrini", "L. Pellegrini", LocalDate.of(1996, 9, 19),
      "602618", WYSCOUT);
  }
}
