package com.wsf.dataingestor.unit.web.controllers;

import java.util.List;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.web.servlet.ResultActions;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.web.controllers.FixtureController;
import com.wsf.domain.common.Fixture;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class FixtureControllerTest extends ControllerTestMvc {
  @InjectMocks
  private FixtureController fixtureController;

  @Mock
  private FixtureService fixtureService;

  @Test
  public void whenAllTheMatchesAreRetrieved_thenAListOfMatchesIsReturned() throws Exception {
    List<Fixture> fixtures = FixtureCanned.random(3);
    when(fixtureService.getAllFixturesOrderedBySequentialId(anyString())).thenReturn(fixtures);

    ResultActions resultActions = request.perform(get("/v1/fixtures?tournamentId=234"));

    resultActions.andExpect(content().json(json(fixtures))).andExpect(status().isOk());
  }

  @Override
  protected Object getControllerUnderTest() {
    return fixtureController;
  }
}
