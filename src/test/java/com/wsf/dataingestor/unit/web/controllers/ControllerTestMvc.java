package com.wsf.dataingestor.unit.web.controllers;

import java.io.IOException;
import java.nio.charset.Charset;
import org.junit.Before;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.config.JsonConfig;

import static org.mockito.MockitoAnnotations.initMocks;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.standaloneSetup;

public abstract class ControllerTestMvc {

  protected static ObjectMapper objectMapper = new JsonConfig().jsonConf().getObjectMapper();

  protected MediaType contentType = new MediaType(MediaType.APPLICATION_JSON.getType(),
    MediaType.APPLICATION_JSON.getSubtype(), Charset.forName("utf8"));

  protected MockMvc request;

  @Before
  public void setUp() {
    initMocks(this);
    request = standaloneSetup(getControllerUnderTest()).setMessageConverters(new JsonConfig().jsonConf()).build();
  }

  protected abstract Object getControllerUnderTest();

  protected String json(Object o) throws IOException {
    return objectMapper.writeValueAsString(o);
  }

}
