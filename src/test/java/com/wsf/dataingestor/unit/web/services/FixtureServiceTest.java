package com.wsf.dataingestor.unit.web.services;

import java.util.List;
import java.util.Optional;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.ExternalProvider;
import com.wsf.domain.common.Fixture;
import com.wsf.repository.common.FixtureRepository;

import static com.wsf.domain.common.Fixture.FixtureProcessStatus.CONNECTED;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.assertArg;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FixtureServiceTest extends TestWithMocks {

  private FixtureService fixtureService;

  @Mock
  private FixtureRepository fixtureRepository;

  @Before
  public void setup() {
    fixtureService = new FixtureService(fixtureRepository);
  }

  @Test
  public void whenASingleMatchIsRetrieved_thenTheMatchRepositoryIsCalled() {
    Fixture fixtureCanned = FixtureCanned.defaultFixture();
    when(fixtureRepository.findById(any(String.class))).thenReturn(Optional.of(fixtureCanned));

    Fixture fixture = fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString());

    assertThat(fixture).isEqualTo(fixtureCanned);
  }

  @Test
  public void whenAllMatchesOrderedBySequentialIdAreRetrieved_thenTheMatchRepositoryIsCalled() {
    List<Fixture> randomFixtures = FixtureCanned.random(3);
    when(fixtureRepository.findByTournamentIdOrderBySequentialIdDesc(any(String.class))).thenReturn(randomFixtures);

    List<Fixture> allMatchesOrderedBySequentialId = fixtureService.getAllFixturesOrderedBySequentialId(
      TournamentCanned.TOURNAMENT1_ID.toString());

    assertThat(allMatchesOrderedBySequentialId).isEqualTo(randomFixtures);
  }

  @Test
  public void whenFixtureProcessStatusIsUpdatedToConnected_thenTheFixtureRepositoryUpdateIsCalled_andExternalProviderHasAValue() {
    Fixture fixture = FixtureCanned.defaultFixtureBuilder().build();

    fixtureService.storeFixtureProcessStatusConnected(fixture, ExternalProvider.OPTA);

    verify(fixtureRepository).update(
      assertArg(f -> assertThat(fixture.getIdAsString()).isEqualTo(fixture.getIdAsString())),
      assertArg(fixtureProcessStatus -> assertThat(fixtureProcessStatus).isEqualTo(CONNECTED)),
      assertArg(externalProvider -> assertThat(externalProvider).isEqualTo(ExternalProvider.OPTA)));
  }

  @Test
  public void whenFixtureProcessStatusIsUpdatedToSettled_thenTheFixtureRepositoryUpdateIsCalled_andExternalProviderHasNoValue() {
    Fixture fixture = FixtureCanned.defaultFixtureBuilder().build();

    fixtureService.storeFixtureProcessStatusSettled(fixture);

    verify(fixtureRepository).update(
      assertArg(f -> assertThat(fixture.getIdAsString()).isEqualTo(fixture.getIdAsString())),
      assertArg(fixtureProcessStatus -> assertThat(fixtureProcessStatus).isEqualTo(SETTLED)),
      assertArg(externalProvider -> assertThat(externalProvider).isNull()));
  }

  @Test
  public void whenFixtureProcessStatusIsRemoved_thenTheFixtureRepositoryUpdateIsCalled_andExternalProviderHasNoValue() {
    Fixture fixture = FixtureCanned.defaultFixtureBuilder().build();

    fixtureService.removeFixtureProcessStatus(fixture);

    verify(fixtureRepository).update(
      assertArg(f -> assertThat(fixture.getIdAsString()).isEqualTo(fixture.getIdAsString())),
      assertArg(fixtureProcessStatus -> assertThat(fixtureProcessStatus).isNull()),
      assertArg(externalProvider -> assertThat(externalProvider).isNull()));
  }
}
