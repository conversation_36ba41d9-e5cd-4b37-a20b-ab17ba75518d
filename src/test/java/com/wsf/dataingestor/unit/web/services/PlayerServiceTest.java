package com.wsf.dataingestor.unit.web.services;

import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.clients.http.UnmappedEntityClient;
import com.wsf.dataingestor.config.db.DBUtils;
import com.wsf.dataingestor.services.PlayerService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.Player;
import com.wsf.repository.common.MasterPlayerRepository;
import com.wsf.repository.common.PlayerRepository;
import com.wsf.repository.common.factories.RepositoryFactory;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PlayerServiceTest extends TestWithMocks {

  private PlayerService playerService;

  @Mock
  private RepositoryFactory.PlayerRepositoryFactory playerRepositoryFactory;

  @Mock
  private UnmappedEntityClient unmappedEntityClientMock;

  @Mock
  private PlayerRepository playerRepository;

  @Mock
  private MasterPlayerRepository masterPlayerRepository;

  @Mock
  private DBUtils dbUtils;

  @Before
  public void setup() {
    when(dbUtils.retrieveRepo(any(), anyString())).thenReturn(playerRepository);
    playerService = spy(
      new PlayerService(playerRepositoryFactory, masterPlayerRepository, unmappedEntityClientMock, dbUtils));
  }

  @Test
  public void whenAllPlayersAreRetrieved_thenTheExpectedPlayersAreReturned() {
    List<Player> players = PlayerCanned.random(2);
    when(playerRepository.findByTournamentIdAndIsActiveTrue(any(String.class))).thenReturn(players);

    List<Player> allPlayers = playerService.getAllPlayersByTournamentId("competitionId",
      TournamentCanned.TOURNAMENT1_ID.toString());

    verify(playerRepository).findByTournamentIdAndIsActiveTrue(TournamentCanned.TOURNAMENT1_ID.toString());
    assertThat(allPlayers, is(players));
  }

}
