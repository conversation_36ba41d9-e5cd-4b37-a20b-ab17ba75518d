package com.wsf.dataingestor.unit.web.services;

import java.util.Map;
import java.util.Set;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.MatchStateCanned;
import com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned;
import com.wsf.dataingestor.shared.canned.PlayerMatchEventDTOCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.web.model.MatchStateDto;
import com.wsf.dataingestor.web.services.MatchStateService;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_AWAY_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_HOME_TEAM;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.common.Fixture.FixtureStatus.PLAYED;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class MatchStateServiceTest extends TestWithMocks {

  @InjectMocks
  private MatchStateService matchStateService;

  @Mock
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;

  @Test
  public void whenMatchStatusDataIsRequestedForAFixture_thenTheInformationIsReturned() {
    // Arrange
    String fixtureId = FixtureCanned.DEFAULT_ID.toString();
    var fixture = FixtureCanned
      .defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM, tournamentCanned().build(), PLAYED)
      .status(LIVE)
      .build();
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .homeScore(1)
      .awayScore(2)
      .matchTime(54)
      .build();
    when(ongoingMatchDataCacheService.get(eq(fixtureId))).thenReturn(ongoingMatchData);

    // Act
    MatchStateDto matchStateDTO = matchStateService.createMatchStatusData(fixture);

    // Assert
    var data = MatchStateCanned.matchDataCanned().homeScore(1).matchPeriod(1).awayScore(2).matchTime(54).build();

    MatchStateDto expectedMatchStateDto = MatchStateCanned
      .matchStateCanned()
      .status(fixture.getStatus())
      .data(data)
      .build();
    assertEquals(expectedMatchStateDto, matchStateDTO);
  }

  @Test
  public void whenThereAreSentOffPlayers_thenTheResultsIncludeThem() {
    // Arrange
    String fixtureId = FixtureCanned.DEFAULT_ID.toString();
    var fixture = FixtureCanned
      .defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM, tournamentCanned().build(), PLAYED)
      .status(LIVE)
      .build();

    Map<String, Set<OngoingMatchData.PlayerMatchEventDTO>> redCards = Map.of("redCard1",
      Set.of(PlayerMatchEventDTOCanned.playerMatchEventDTOCanned().event(RED_CARD).build(),
        PlayerMatchEventDTOCanned
          .playerMatchEventDTOCanned()
          .event(RED_CARD)
          .teamId(TeamCanned.TEAM2_ID.toString())
          .build()));
    OngoingMatchData ongoingMatchData = OngoingMatchDataCanned
      .ongoingMatchDataCanned()
      .matchPeriod(FIRST_HALF)
      .homeScore(1)
      .awayScore(2)
      .matchTime(54)
      .eventIdToPlayerMatchEvents(redCards)
      .build();
    when(ongoingMatchDataCacheService.get(eq(fixtureId))).thenReturn(ongoingMatchData);

    // Act
    MatchStateDto matchStateDTO = matchStateService.createMatchStatusData(fixture);

    // Assert
    var data = MatchStateCanned
      .matchDataCanned()
      .homeScore(1)
      .matchPeriod(FIRST_HALF.getPeriodId())
      .awayScore(2)
      .awaySentOff(1)
      .homeSentOff(1)
      .matchTime(54)
      .build();

    MatchStateDto expectedMatchStateDto = MatchStateCanned
      .matchStateCanned()
      .status(fixture.getStatus())
      .data(data)
      .build();
    assertEquals(expectedMatchStateDto, matchStateDTO);
  }
}
