package com.wsf.dataingestor.shared.db;

import de.flapdoodle.embed.mongo.MongodExecutable;
import de.flapdoodle.embed.mongo.MongodStarter;
import de.flapdoodle.embed.mongo.config.Defaults;
import de.flapdoodle.embed.mongo.config.ImmutableMongodConfig;
import de.flapdoodle.embed.mongo.config.Net;
import de.flapdoodle.embed.mongo.distribution.Version;
import de.flapdoodle.embed.mongo.packageresolver.Command;
import de.flapdoodle.embed.process.config.RuntimeConfig;
import de.flapdoodle.embed.process.config.process.ProcessOutput;
import de.flapdoodle.embed.process.runtime.Network;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class DBUtils {

  public static MongodExecutable startEmbeddedMongo(String ip, int port) throws IOException {
    RuntimeConfig runtimeConfig = Defaults
      .runtimeConfigFor(Command.MongoD)
      .processOutput(ProcessOutput.silent())
      .build();

    ImmutableMongodConfig mongodConfig = ImmutableMongodConfig
      .builder().version(Version.Main.PRODUCTION).net(new Net(ip, port, Network.localhostIsIPv6())).build();

    MongodStarter starter = MongodStarter.getInstance(runtimeConfig);
    MongodExecutable mongodExecutable = starter.prepare(mongodConfig);
    mongodExecutable.start();

    return mongodExecutable;
  }
}
