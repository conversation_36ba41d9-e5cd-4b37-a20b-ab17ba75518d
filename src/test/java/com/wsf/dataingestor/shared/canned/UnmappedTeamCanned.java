package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.common.UnmappedTeam;

import static com.wsf.domain.common.ExternalProvider.OPTA;

public class UnmappedTeamCanned {

  private static final ObjectId UNMAPPED_TEAM_ID = new ObjectId("6581a5da08bfaae37620c788");

  public static UnmappedTeam.UnmappedTeamBuilder createUnmappedTeam() {
    return UnmappedTeam
      .builder()
      .id(UNMAPPED_TEAM_ID)
      .externalTeamId("externalTeamId")
      .externalTeamName("externalTeamName")
      .externalTeamAbbreviation("externalTeamAbbreviation")
      .provider(OPTA);
  }

}
