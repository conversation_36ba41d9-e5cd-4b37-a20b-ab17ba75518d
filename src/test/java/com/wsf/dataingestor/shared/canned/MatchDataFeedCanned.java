package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import java.util.List;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.unit.services.fixtures.FixturesIngestionServiceTest;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.time.Instant.now;

public class MatchDataFeedCanned {

  public static final String FEED_ID = "feedId";

  public static MatchDataFeed.MatchDataFeedBuilder matchDataFeedCanned() {
    Fixture fixture = FixtureCanned.fixtureCanned().build();
    var goalEvent = EntityEventDTOCanned.entityEventDTOCanned().teamId(fixture.getHomeTeam().getIdAsString()).build();
    return MatchDataFeed
      .builder()
      .receivedTs(now())
      .feedId(FEED_ID)
      .fixture(fixture)
      .date(now())
      .matchPeriod(MatchPeriod.END_MATCH)
      .matchTimeMin(95)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .aggregatedPlayerMatchEvents(List.of(goalEvent))
      .isSnapshot(false)
      .isSingleEventFeed(false)
      .provider(MatchDataFeed.FeedProvider.OPTA);
  }

  public static MatchDataFeed.MatchDataFeedBuilder createMatchDataFeedWithOwnGoal() {
    Fixture fixture = FixtureCanned.fixtureCanned().awayTeam(defaultTeam(TEAM2_ID)).build();
    var goalEvent = EntityEventDTOCanned.entityEventDTOCanned().teamId(fixture.getHomeTeam().getIdAsString()).build();
    var ownGoalEvent = EntityEventDTOCanned
      .entityEventDTOCanned()
      .ignore(true)
      .teamId(fixture.getHomeTeam().getIdAsString())
      .build();
    return MatchDataFeed
      .builder()
      .receivedTs(now())
      .feedId(FEED_ID)
      .fixture(fixture)
      .date(now())
      .matchPeriod(MatchPeriod.END_MATCH)
      .matchTimeMin(95)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .aggregatedPlayerMatchEvents(List.of(goalEvent, ownGoalEvent))
      .isSnapshot(false)
      .isSingleEventFeed(false)
      .provider(MatchDataFeed.FeedProvider.OPTA);
  }

  public static FixturesFeed buildMatchesFeed(String externalId, String externalRelatedId, Instant dateFirstLeg,
                                              Instant dateSecondLeg) {
    FixtureDTO firstLeg = FixtureDTO
      .builder()
      .externalFixtureId(externalId)
      .externalRelatedFixtureId(externalRelatedId)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .leg(FixtureDTO.Relation.FIRST_LEG)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateFirstLeg)
      .isLiveSupported(true)
      .build();

    FixtureDTO secondLeg = FixtureDTO
      .builder()
      .externalFixtureId(externalRelatedId)
      .externalRelatedFixtureId(externalId)
      .provider(FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .leg(FixtureDTO.Relation.SECOND_LEG)
      .lastUpdated(now())
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.FIXTURE)
      .time(dateSecondLeg)
      .isLiveSupported(true)
      .build();

    return FixturesFeed
      .builder().feedId("feedId").tournament(tournamentCanned().build()).fixtures(List.of(firstLeg, secondLeg)).build();
  }
}
