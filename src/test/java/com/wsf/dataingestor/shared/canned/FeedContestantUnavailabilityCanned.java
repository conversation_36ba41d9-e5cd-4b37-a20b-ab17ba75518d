package com.wsf.dataingestor.shared.canned;

import java.util.Set;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedContestantUnavailability;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedContestantUnavailability.FeedContestantUnavailabilityBuilder;

public class FeedContestantUnavailabilityCanned {

  public static FeedContestantUnavailabilityBuilder contestantUnavailabilitiesCanned() {
    return FeedContestantUnavailability
      .builder().unavailabilities(Set.of());
  }
}
