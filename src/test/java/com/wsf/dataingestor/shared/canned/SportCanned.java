package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.common.Sport;

import static com.wsf.domain.common.Sport.SportType.SOCCER;

public class SportCanned {

  public static final ObjectId SPORT_ID = new ObjectId("5d3a33fe999fc4072ab560af");

  public static Sport.SportBuilder<?, ?> sportCanned() {
    return Sport
      .builder().id(SPORT_ID).desc("Soccer").dbName("all_leagues").active(true).publicId("1").type(SOCCER);
  }
}
