package com.wsf.dataingestor.shared.canned;

import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;

public class EntityEventDTOCanned {

  public static EntityEventDTO.EntityEventDTOBuilder goalMadePlayerEvent() {
    String playerId = PlayerCanned.DEFAULT_ID.toString();
    return entityEventDTOCanned()
      .entityId(playerId)
      .eventType(GOAL)
      .eventId(GOAL.getStatisticName() + playerId)
      .teamId(TEAM1_ID.toString());
  }

  public static EntityEventDTO.EntityEventDTOBuilder subOnPlayerEvent() {
    String playerId = PlayerCanned.DEFAULT_ID.toString();
    return entityEventDTOCanned()
      .entityId(playerId)
      .eventType(SUB_ON)
      .eventId(SUB_ON.getStatisticName() + playerId)
      .teamId(TEAM1_ID.toString());
  }

  public static EntityEventDTO.EntityEventDTOBuilder subOffPlayerEvent() {
    String playerId = PlayerCanned.DEFAULT_ID.toString();
    return entityEventDTOCanned()
      .entityId(playerId)
      .eventType(SUB_OFF)
      .eventId(SUB_OFF.getStatisticName() + playerId)
      .teamId(TEAM1_ID.toString());
  }

  public static EntityEventDTO.EntityEventDTOBuilder redCardPlayerEvent() {
    String playerId = PlayerCanned.DEFAULT_ID.toString();
    return entityEventDTOCanned()
      .entityId(playerId)
      .eventType(RED_CARD)
      .eventId(RED_CARD.getStatisticName() + playerId)
      .teamId(TEAM1_ID.toString());
  }

  public static EntityEventDTO.EntityEventDTOBuilder offsidePlayerEvent() {
    return entityEventDTOCanned()
      .entityId(PLAYER_1_ID_STR)
      .eventType(OFFSIDE)
      .eventId(OFFSIDE.getStatisticName() + PLAYER_1_ID_STR)
      .teamId(TEAM1_ID.toString());
  }

  public static EntityEventDTO.EntityEventDTOBuilder entityEventDTOCanned() {
    String playerId = PlayerCanned.DEFAULT_ID.toString();
    return EntityEventDTO
      .builder()
      .entityId(playerId)
      .eventType(GOAL)
      .eventId(GOAL.getStatisticName() + playerId)
      .period(MatchPeriod.FIRST_HALF)
      .timeMin(34);
  }
}
