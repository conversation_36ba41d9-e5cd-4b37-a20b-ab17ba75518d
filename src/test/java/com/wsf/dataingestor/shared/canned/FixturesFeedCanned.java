package com.wsf.dataingestor.shared.canned;

import java.util.List;
import com.wsf.dataingestor.models.FixturesFeed;

public class FixturesFeedCanned {

  public static FixturesFeed.FixturesFeedBuilder fixturesFeedCanned() {
    return FixturesFeed
      .builder()
      .feedId("feedId")
      .tournament(TournamentCanned.tournamentCanned().build())
      .fixtures(List.of(FixtureDTOCanned.fixtureDTOCanned().build()));
  }
}
