package com.wsf.dataingestor.shared.canned;

import java.util.HashMap;
import java.util.HashSet;
import java.util.TreeSet;
import com.wsf.dataingestor.cache.models.TeamMatchData;

public class TeamMatchDataCanned {

  public static TeamMatchData.TeamMatchDataBuilder<?, ?> teamMatchDataCanned() {
    return TeamMatchData
      .builder().teamId("teamId").fixtureId("fixtureId")
      .timestamp(3L).stats(new HashMap<>()).processedEvents(new HashSet<>()).betStopsInfo(new TreeSet<>());
  }
}
