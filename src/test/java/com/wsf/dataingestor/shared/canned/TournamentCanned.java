package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import java.util.Set;
import org.bson.types.ObjectId;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.shared.canned.CompetitionCanned.competitionCanned;
import static java.time.temporal.ChronoUnit.DAYS;

public class TournamentCanned {

  public static final ObjectId TOURNAMENT1_ID = new ObjectId("5d3a33fe999fc4072ab589c4");
  public static final String DEFAULT_OPTA_CALENDAR_ID = "34fdsf34tf";
  public static final String DEFAULT_SM_SEASON_ID = "636";

  public static Tournament.TournamentBuilder<?, ?> tournamentCanned() {
    return Tournament
      .builder()
      .id(TOURNAMENT1_ID)
      .active(true)
      .competition(competitionCanned().build())
      .active(true)
      .startDate(Instant.now().plus(15, DAYS))
      .externalIds(ExternalIdsCanned
        .externalIdsCanned()
        .sportmonksIds(Set.of(DEFAULT_SM_SEASON_ID))
        .optaIds(Set.of(DEFAULT_OPTA_CALENDAR_ID))
        .build());
  }
}
