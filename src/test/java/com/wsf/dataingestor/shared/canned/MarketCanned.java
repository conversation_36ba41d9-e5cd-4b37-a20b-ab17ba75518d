package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.main.Market;

import static com.wsf.domain.main.Market.MarketType.PLAYER;

public class MarketCanned {

  public static final String MARKET_ID = "66841e57a5fe97834633566d";

  public static Market.MarketBuilder<?, ?> marketCanned() {
    return Market
      .builder().id(new ObjectId(MARKET_ID))
      .name("Market name").propName("marketDbName").type(PLAYER).isDerived(false).isActive(true);
  }
}
