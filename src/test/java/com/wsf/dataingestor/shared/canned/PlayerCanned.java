package com.wsf.dataingestor.shared.canned;

import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import com.wsf.domain.common.MasterPlayer;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;

import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static com.wsf.domain.common.Player.Position.FORWARD;
import static java.lang.System.nanoTime;
import static org.apache.commons.lang3.RandomUtils.nextInt;

public class PlayerCanned {

  public static final ObjectId PLAYER_1_ID = new ObjectId("111111111111111111111111");
  public static final String PLAYER_1_ID_STR = PLAYER_1_ID.toString();
  public static final ObjectId PLAYER_2_ID = new ObjectId("111111111111111111111122");
  public static final String PLAYER_2_ID_STR = PLAYER_2_ID.toString();
  public static final ObjectId PLAYER_3_ID = new ObjectId("111111111111111111111133");
  public static final String PLAYER_3_ID_STR = PLAYER_3_ID.toString();
  public static final ObjectId PLAYER_4_ID = new ObjectId("111111111111111111111144");
  public static final ObjectId PLAYER_5_ID = new ObjectId("111111111111111111111155");
  public static final ObjectId PLAYER_6_ID = new ObjectId("111111111111111111111166");
  public static final ObjectId PLAYER_7_ID = new ObjectId("111111111111111111111177");
  public static final ObjectId PLAYER_8_ID = new ObjectId("111111111111111111111188");
  public static final ObjectId PLAYER_9_ID = new ObjectId("111111111111111111111199");
  public static final ObjectId PLAYER_10_ID = new ObjectId("111111111111111111111200");
  public static final ObjectId DEFAULT_ID = new ObjectId();
  public static final String DEFAULT_PLAYER_MASTER_ID = "1234";
  public static final String DEFAULT_NAME = "Totti";
  public static final Team DEFAULT_TEAM = Team
    .builder().id(TeamCanned.TEAM1_ID)
    .name("AS Roma").build();
  public static final Player.Position DEFAULT_POSITION = FORWARD;
  public static final Boolean DEFAULT_IS_ACTIVE = true;
  public static final String DEFAULT_OPTA_PLAYER_ID = "p48332";

  public static Player.PlayerBuilder<?, ?> createPlayer() {
    return Player
      .builder()
      .id(PLAYER_1_ID)
      .firstName("Francesco")
      .name("Totti")
      .playerMasterId(new ObjectId().toString())
      .isActive(true)
      .position(Player.Position.MIDFIELDER)
      .team(teamCanned().build())
      .tournament(tournamentCanned().build());
  }

  public static Player defaultPlayer(ObjectId id) {
    return defaultPlayer(id, DEFAULT_TEAM);
  }

  public static Player defaultPlayer() {
    return defaultPlayer(DEFAULT_ID, DEFAULT_TEAM);
  }

  public static Player defaultPlayer(Team team) {
    return defaultPlayer(DEFAULT_ID, team);
  }

  public static Player defaultPlayer(ObjectId id, Team team) {
    return Player
      .builder()
      .id(id)
      .playerMasterId(DEFAULT_PLAYER_MASTER_ID)
      .name(DEFAULT_NAME)
      .team(team)
      .tournament(tournamentCanned().build())
      .position(DEFAULT_POSITION)
      .isActive(DEFAULT_IS_ACTIVE)
      .build();
  }

  public static Player.PlayerBuilder<?, ?> playerCanned() {
    return Player
      .builder()
      .id(DEFAULT_ID)
      .playerMasterId(DEFAULT_PLAYER_MASTER_ID)
      .name(DEFAULT_NAME)
      .team(DEFAULT_TEAM)
      .tournament(tournamentCanned().build())
      .position(DEFAULT_POSITION)
      .isActive(DEFAULT_IS_ACTIVE);
  }

  public static MasterPlayer defaultMasterPlayer() {
    return defaultMasterPlayer(DEFAULT_ID);
  }

  public static MasterPlayer defaultMasterPlayer(ObjectId id) {
    MasterPlayer.MasterPlayerBuilder active = defaultMasterPlayerBuilder(id);
    return active.build();
  }

  public static MasterPlayer.MasterPlayerBuilder defaultMasterPlayerBuilder() {
    return defaultMasterPlayerBuilder(new ObjectId());
  }

  public static MasterPlayer.MasterPlayerBuilder defaultMasterPlayerBuilder(ObjectId id) {
    return MasterPlayer
      .builder().id(id).playerMasterId(DEFAULT_PLAYER_MASTER_ID)
      .name(DEFAULT_NAME).optaPlayerId(DEFAULT_OPTA_PLAYER_ID).isActive(DEFAULT_IS_ACTIVE);
  }

  public static List<Player> random(int quantity) {
    List<Player> players = new ArrayList<>();
    for (int i = 0; i < quantity; i++) {
      ObjectId id = new ObjectId();
      String playerId = Long.toString(nanoTime());
      String name = "name-" + nanoTime();
      Team team = Team
        .builder()
        .name("team-" + nanoTime()).build();
      Player.Position[] positions = Player.Position.values();
      Player.Position role = positions[nextInt(0, positions.length)];
      Player player = Player
        .builder()
        .id(id)
        .playerMasterId(playerId)
        .name(name)
        .team(team)
        .tournament(tournamentCanned().build())
        .position(role)
        .isActive(DEFAULT_IS_ACTIVE)
        .build();

      players.add(player);
    }

    return players;
  }
}
