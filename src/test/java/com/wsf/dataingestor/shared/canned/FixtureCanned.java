package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import org.bson.types.ObjectId;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.PLAYED;
import static java.time.Instant.now;

public class FixtureCanned {

  public static final ObjectId DEFAULT_ID = new ObjectId();
  public static final int DEFAULT_SEQUENTIAL_ID = 1;
  public static final Instant DEFAULT_DATE_TIME = now().minus(10, ChronoUnit.DAYS);
  public static final Team DEFAULT_HOME_TEAM = defaultTeam(TEAM1_ID, "optaId1");
  public static final Team DEFAULT_AWAY_TEAM = defaultTeam(TEAM2_ID, "optaId2");
  public static final String OPTA_FIXTURE_ID = "optaId";
  public static final String SM_FIXTURE_ID = "smId";
  public static final String BETRADAR_FIXTURE_ID = "sr:match:123456";
  public static final String FIXTURE_ID = "67b31c701762c5726b7c2a1f";

  public static Fixture.FixtureBuilder<?, ?> fixtureCanned() {
    Instant date = LocalDate.parse("2016-04-17").atTime(18, 0, 0).toInstant(ZoneOffset.UTC);

    return Fixture
      .builder()
      .id(new ObjectId(FIXTURE_ID))
      .active(true)
      .status(PLAYED)
      .processStatus(null)
      .date(date)
      .homeTeam(defaultTeam(TEAM1_ID))
      .awayTeam(defaultTeam(TEAM2_ID))
      .tournament(tournamentCanned().build())
      .optaFixtureId(OPTA_FIXTURE_ID)
      .sportmonksFixtureId(SM_FIXTURE_ID);
  }

  @Deprecated
  public static Fixture.FixtureBuilder<?, ?> defaultFixtureBuilder() {
    return defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM, tournamentCanned().build(), PLAYED);
  }

  @Deprecated
  public static Fixture defaultFixture() {
    return defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM);
  }

  @Deprecated
  public static Fixture defaultFixture(Tournament tournament) {
    return defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM, tournament, PLAYED).build();
  }

  @Deprecated
  public static Fixture defaultFixture(Team homeTeam, Team awayTeam) {
    return defaultFixture(homeTeam, awayTeam, tournamentCanned().build(), PLAYED).build();
  }

  @Deprecated
  public static Fixture defaultFixture(Fixture.FixtureStatus fixtureStatus) {
    return defaultFixture(DEFAULT_HOME_TEAM, DEFAULT_AWAY_TEAM, tournamentCanned().build(), fixtureStatus).build();
  }

  @Deprecated
  public static Fixture.FixtureBuilder<?, ?> defaultFixture(Team homeTeam, Team awayTeam, Tournament tournament,
                                                            Fixture.FixtureStatus status) {
    return Fixture
      .builder()
      .id(DEFAULT_ID)
      .sequentialId(DEFAULT_SEQUENTIAL_ID)
      .tournament(tournament)
      .date(DEFAULT_DATE_TIME)
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .status(status)
      .optaFixtureId(OPTA_FIXTURE_ID);
  }

  public static List<Fixture> random(int quantity) {
    List<Fixture> fixtures = new ArrayList<>();
    for (int i = 0; i < quantity; i++) {
      Fixture fixture = fixtureCanned().sequentialId(fixtures.size() + 1).build();

      fixtures.add(fixture);
    }

    return fixtures;
  }

  @Deprecated
  public static Fixture.FixtureBuilder<?, ?> buildFixture(int seqId) {
    return Fixture
      .builder()
      .id(new ObjectId())
      .date(now().plus(15, ChronoUnit.DAYS))
      .tournament(tournamentCanned().build())
      .homeTeam(Team
        .builder().id(new ObjectId()).build())
      .awayTeam(Team
        .builder().id(new ObjectId()).build())
      .sequentialId(seqId)
      .active(true);
  }
}
