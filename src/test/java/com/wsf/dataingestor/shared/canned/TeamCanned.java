package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.common.MasterTeam;
import com.wsf.domain.common.Team;

public class TeamCanned {

  public static final ObjectId TEAM1_ID = new ObjectId("6123c226c1575f85c7053150");
  public static final ObjectId TEAM2_ID = new ObjectId("6123c226c1575f85c7053151");
  public static final ObjectId TEAM3_ID = new ObjectId("6123c226c1575f85c7053152");
  public static final ObjectId TEAM4_ID = new ObjectId("6123c226c1575f85c7053153");

  public static Team.TeamBuilder<?, ?> teamCanned() {
    return Team
      .builder().id(TEAM1_ID)
      .name("Team name").optaId("optaId");
  }

  public static Team defaultTeam() {
    return defaultTeam(TEAM1_ID, "optaId");
  }

  public static Team defaultTeam(ObjectId teamId) {
    return defaultTeam(teamId, "optaId");
  }

  public static Team defaultTeam(ObjectId teamId, String optaId) {
    return teamCanned().id(teamId).optaId(optaId).sportmonksId("sportmonksId").build();
  }

  public static MasterTeam defaultMasterTeam() {
    return defaultMasterTeam(ObjectId.get(), "optaId");
  }

  public static MasterTeam defaultMasterTeam(ObjectId teamId, String optaId) {
    return MasterTeam
      .builder().id(teamId)
      .name("ROMA").optaId(optaId).build();
  }
}
