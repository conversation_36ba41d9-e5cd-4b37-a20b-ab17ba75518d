package com.wsf.dataingestor.shared.canned;

import java.util.List;
import java.util.Map;
import java.util.Set;
import com.wsf.dataingestor.models.ContestantType;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryDto;
import com.wsf.domain.common.MatchPeriod;

public class FixtureSummaryDataDtoCanned {

  public static FixtureSummaryDto.FixtureSummaryDtoBuilder fixtureSummaryDataCanned() {
    return FixtureSummaryDto
      .builder()
      .fixtureId(FixtureCanned.FIXTURE_ID)
      .fixtureStatus(MatchDataFeed.FeedFixtureStatus.PLAYED)
      .contestantStats(List.of(contestantStatsCanned().build()))
      .events(List.of(fixtureEventsCanned().build()))
      .supportedEventTypes(Set.of())
      .isFinal(true);
  }

  public static FixtureSummaryDto.ContestantStats.ContestantStatsBuilder contestantStatsCanned() {
    return FixtureSummaryDto.ContestantStats
      .builder()
      .contestantId(PlayerCanned.DEFAULT_ID.toString())
      .contestantType(ContestantType.SOCCER_PLAYER)
      .hasPlayed(true)
      .stats(Map.of("goals", 1, "assists", 0));
  }

  public static FixtureSummaryDto.Event.EventBuilder fixtureEventsCanned() {
    return FixtureSummaryDto.Event
      .builder()
      .eventId("eventId")
      .contestantId(PlayerCanned.DEFAULT_ID.toString())
      .parentContestantId(TeamCanned.TEAM1_ID.toString())
      .eventType("goal")
      .matchTime(45)
      .fixturePeriod(MatchPeriod.FIRST_HALF);
  }
}
