package com.wsf.dataingestor.shared.canned;

import java.util.List;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;

public class SquadsFeedCanned {
  public static SquadsFeed.SquadsFeedBuilder defaultSquadsFeed() {
    SquadPlayerDTO newPlayerFeed = SquadPlayerCanned.defaultSquadPlayer().build();
    return SquadsFeed
      .builder().feedId("feedId").squadPlayers(List.of(newPlayerFeed)).provider(SquadsFeed.FeedProvider.SPORTMONKS);
  }
}
