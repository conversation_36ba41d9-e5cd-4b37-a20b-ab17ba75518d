package com.wsf.dataingestor.shared.canned;

import java.util.List;
import org.bson.types.ObjectId;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.CompetitionConfig.Competition;
import com.wsf.domain.common.CompetitionConfig.FeedsConfig;
import com.wsf.domain.common.CompetitionConfig.SettlementConfig;

import static com.wsf.dataingestor.services.events.Utils.PREMIER_LEAGUE_COMPETITION_ID;
import static com.wsf.dataingestor.shared.canned.CompetitionConfigCanned.OddsGenerationCanned.oddsGenerationCanned;
import static com.wsf.domain.common.CompetitionConfig.Provider.OPTA;

public class CompetitionConfigCanned {

  public static final String COMPETITION_CONFIG_ID = new ObjectId().toString();

  public static CompetitionConfig.CompetitionConfigBuilder<?, ?> competitionConfigCanned() {
    return CompetitionConfig
      .builder()
      .id(new ObjectId(COMPETITION_CONFIG_ID))
      .competition(competitionCanned().build())
      .markets(List.of(marketCanned().build()))
      .oddsGeneration(oddsGenerationCanned().isLiveEnabled(false).build());
  }

  public static CompetitionConfig.CompetitionConfigBuilder<?, ?> competitionConfigCannedWithLiveOdds() {
    return competitionConfigCanned().oddsGeneration(oddsGenerationCanned().isLiveEnabled(true).build());
  }

  public static Competition.CompetitionBuilder competitionCanned() {
    return Competition
      .builder().id(CompetitionCanned.DEFAULT_ID).desc("Competition 1");
  }

  public static CompetitionConfig.CompetitionMarketConfig.CompetitionMarketConfigBuilder marketCanned() {
    return CompetitionConfig.CompetitionMarketConfig
      .builder().id(new ObjectId(MarketCanned.MARKET_ID)).propName("team_shots");
  }

  public static CompetitionConfig.CompetitionConfigBuilder<?, ?> premierCompetitionConfigCanned() {
    SettlementConfig settlementConfig = SettlementConfig
      .builder().provider(OPTA).build();
    FeedsConfig feedsConfig = FeedsConfig
      .builder().settlementConfig(settlementConfig).build();
    Competition competition = Competition
      .builder().id(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID)).build();
    return competitionConfigCanned().competition(competition).feedsConfig(feedsConfig);
  }

  public static class OddsGenerationCanned {

    public static CompetitionConfig.OddsGeneration.OddsGenerationBuilder oddsGenerationCanned() {
      return CompetitionConfig.OddsGeneration
        .builder().hours(List.of(8)).isLiveEnabled(false);
    }
  }
}
