package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.common.Competition;

import static com.wsf.dataingestor.shared.canned.SportCanned.sportCanned;

public class CompetitionCanned {

  public static final ObjectId DEFAULT_ID = new ObjectId("5d3a33fe999fc4072ab589af");

  public static final Competition DEFAULT_COMPETITION = competitionCanned().build();

  public static Competition.CompetitionBuilder<?, ?> competitionCanned() {
    return Competition
      .builder()
      .id(DEFAULT_ID)
      .active(true)
      .dbName("seriea")
      .externalIds(ExternalIdsCanned.externalIdsCanned().build())
      .sport(sportCanned().build());
  }
}
