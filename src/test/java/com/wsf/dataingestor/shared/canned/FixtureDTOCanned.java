package com.wsf.dataingestor.shared.canned;

import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixtureDTO.FixtureDTOBuilder;
import com.wsf.dataingestor.unit.services.fixtures.FixturesIngestionServiceTest;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static java.time.Instant.now;

public class FixtureDTOCanned {

  public static FixtureDTOBuilder fixtureDTOCanned() {
    return FixtureDTO
      .builder()
      .externalFixtureId("optaId")
      .provider(com.wsf.dataingestor.models.FixtureDTO.Provider.OPTA)
      .externalHomeTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_1)
      .externalAwayTeamId(FixturesIngestionServiceTest.OPTA_TEAM_ID_2)
      .externalStageName("externalStageName")
      .externalStageId("externalStageId")
      .isNeutralVenue(false)
      .lastUpdated(now())
      .fixtureStatus(LIVE)
      .time(now())
      .isLiveSupported(true)
      .isActive(true);
  }
}
