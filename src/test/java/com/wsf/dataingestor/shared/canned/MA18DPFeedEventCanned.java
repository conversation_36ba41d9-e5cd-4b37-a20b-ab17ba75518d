package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import java.util.Map;
import com.wsf.dataingestor.opta.models.ma18dp.MA18DPFeed;

public final class MA18DPFeedEventCanned {

  public static final String DEFAULT_ID = "id";
  public static final int DEFAULT_SEQ_ID = 1;
  public static final String DEFAULT_EVENT_ID = "eventId";
  public static final int DEFAULT_TYPE_ID = 16;
  public static final String DEFAULT_PLAYER_ID = "playerId";
  public static final String DEFAULT_CONTESTANT_ID = "contestantId";
  public static final String DEFAULT_TIME_MIN = "45";
  public static final int DEFAULT_PERIOD_ID = 1;
  public static final short DEFAULT_OUTCOME = 1;
  public static final Instant DEFAULT_TIMESTAMP = Instant.parse("2024-01-01T12:00:00Z");
  public static final Instant DEFAULT_LAST_MODIFIED = Instant.parse("2024-01-01T12:01:00Z");
  public static final Map<Integer, String> DEFAULT_QUALIFIERS = Map.of();

  public static MA18DPFeed.Event.EventBuilder ma18dpEventCanned() {
    return MA18DPFeed.Event
      .builder()
      .id(DEFAULT_ID)
      .seqId(DEFAULT_SEQ_ID)
      .eventId(DEFAULT_EVENT_ID)
      .typeId(DEFAULT_TYPE_ID)
      .playerId(DEFAULT_PLAYER_ID)
      .contestantId(DEFAULT_CONTESTANT_ID)
      .timeMin(DEFAULT_TIME_MIN)
      .periodId(DEFAULT_PERIOD_ID)
      .outcome(DEFAULT_OUTCOME)
      .timestamp(DEFAULT_TIMESTAMP)
      .lastModified(DEFAULT_LAST_MODIFIED)
      .qualifiers(DEFAULT_QUALIFIERS);
  }
}
