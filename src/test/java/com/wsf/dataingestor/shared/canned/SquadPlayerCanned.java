package com.wsf.dataingestor.shared.canned;

import java.time.LocalDate;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.domain.common.Player;

public class SquadPlayerCanned {

  public static SquadPlayerDTO.SquadPlayerDTOBuilder defaultSquadPlayer() {
    return SquadPlayerDTO
      .builder()
      .feedId("feedId")
      .playerId("playerId")
      .firstName("francesco")
      .lastName("totti")
      .birthDate(LocalDate.now().minusYears(13))
      .position(Player.Position.MIDFIELDER);
  }
}
