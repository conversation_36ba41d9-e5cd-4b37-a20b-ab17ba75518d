package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import com.wsf.dataingestor.models.CurrentTournamentFeed;

public class CurrentTournamentFeedCanned {
  public static CurrentTournamentFeed.CurrentTournamentFeedBuilder currentTournamentFeedCanned() {
    return CurrentTournamentFeed
      .builder()
      .competition(CompetitionCanned.competitionCanned().build())
      .existingTournament(TournamentCanned.tournamentCanned().build())
      .externalSeasonId("externalSeasonId")
      .year("2024")
      .startDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .provider(CurrentTournamentFeed.Provider.OPTA);
  }
}
