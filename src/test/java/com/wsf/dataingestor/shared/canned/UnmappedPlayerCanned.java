package com.wsf.dataingestor.shared.canned;

import java.time.LocalDate;
import org.bson.types.ObjectId;
import com.wsf.domain.common.UnmappedPlayer;

import static com.wsf.domain.common.ExternalProvider.OPTA;

public class UnmappedPlayerCanned {

  private static final ObjectId UNMAPPED_PLAYER_ID = new ObjectId("6581a5da08bfaae37620c789");

  public static UnmappedPlayer.UnmappedPlayerBuilder createUnmappedPlayer() {
    return UnmappedPlayer
      .builder()
      .id(UNMAPPED_PLAYER_ID)
      .externalPlayerId("externalPlayerId")
      .externalFullName("externalPlayerName")
      .dateOfBirth(LocalDate.now())
      .provider(OPTA);
  }
}
