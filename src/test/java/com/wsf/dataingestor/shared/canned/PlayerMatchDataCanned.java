package com.wsf.dataingestor.shared.canned;

import java.util.HashMap;
import java.util.HashSet;
import java.util.TreeSet;
import com.wsf.dataingestor.cache.models.PlayerMatchData;

public class PlayerMatchDataCanned {

  public static PlayerMatchData.PlayerMatchDataBuilder<?, ?> playerMatchDataCanned() {
    return PlayerMatchData
      .builder().playerId("playerId").fixtureId("fixtureId")
      .timestamp(3L).stats(new HashMap<>()).processedEvents(new HashSet<>()).betStopsInfo(new TreeSet<>());
  }
}
