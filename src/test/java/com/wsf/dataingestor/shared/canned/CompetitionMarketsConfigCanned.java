package com.wsf.dataingestor.shared.canned;

import org.bson.types.ObjectId;
import com.wsf.domain.common.CompetitionConfig.CompetitionMarketConfig;

public class CompetitionMarketsConfigCanned {

  public static final String DEFAULT_PROP_NAME = "shots";

  public static CompetitionMarketConfig.CompetitionMarketConfigBuilder createCompetitionMarketConfig() {
    return CompetitionMarketConfig
      .builder().id(ObjectId.get()).propName(DEFAULT_PROP_NAME);
  }
}
