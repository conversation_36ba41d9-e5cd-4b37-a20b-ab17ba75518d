package com.wsf.dataingestor.shared.canned;

import com.wsf.dataingestor.web.model.MatchStateDto;

import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;

public class MatchStateCanned {

  public static MatchStateDto.MatchStateDtoBuilder matchStateCanned() {
    return MatchStateDto
      .builder().data(matchDataCanned().build()).status(LIVE);
  }

  public static MatchStateDto.MatchDataDto.MatchDataDtoBuilder matchDataCanned() {
    return MatchStateDto.MatchDataDto
      .builder().homeScore(0).awayScore(0).homeSentOff(0).awaySentOff(0);
  }

}
