package com.wsf.dataingestor.shared.canned;

import com.wsf.dataingestor.cache.models.OngoingMatchData;

import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;

public class PlayerMatchEventDTOCanned {

  public static OngoingMatchData.PlayerMatchEventDTO.PlayerMatchEventDTOBuilder playerMatchEventDTOCanned() {
    return OngoingMatchData.PlayerMatchEventDTO
      .builder()
      .eventId("eventId")
      .event(SHOT)
      .ignore(false)
      .isOnBench(false)
      .teamId(TeamCanned.TEAM1_ID.toString())
      .entityId(PlayerCanned.PLAYER_1_ID_STR)
      .periodId(1)
      .timeMin(10);
  }
}
