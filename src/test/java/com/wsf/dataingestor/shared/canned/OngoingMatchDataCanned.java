package com.wsf.dataingestor.shared.canned;

import java.time.Instant;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.domain.common.MatchPeriod;

import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;

public class OngoingMatchDataCanned {
  public static OngoingMatchData.OngoingMatchDataBuilder ongoingMatchDataCanned() {
    return OngoingMatchData
      .builder().matchId(FixtureCanned.DEFAULT_ID.toString()).matchTime(15).matchPeriod(MatchPeriod.FIRST_HALF)
      .timestamp(Instant.now().toEpochMilli()).fixtureStatus(MatchDataFeed.FeedFixtureStatus.LIVE);
  }

  public static OngoingMatchData.PlayerMatchEventDTO.PlayerMatchEventDTOBuilder createPlayerMatchEvent() {
    return OngoingMatchData.PlayerMatchEventDTO
      .builder()
      .eventId("eventId")
      .entityId(PlayerCanned.DEFAULT_ID.toString())
      .teamId(TeamCanned.TEAM1_ID.toString())
      .event(SHOT)
      .periodId(1)
      .timeMin(34);
  }
}
