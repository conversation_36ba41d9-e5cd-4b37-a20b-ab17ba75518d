package com.wsf.dataingestor.shared.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@TestConfiguration
@SuppressWarnings("unused")
public class IntegrationTestCacheConfig {
  @Bean
  @Primary
  public CacheManager layeringCacheManager(@Qualifier("caffeineCacheManager") CacheManager caffeineCacheManager) {
    return caffeineCacheManager;
  }
}
