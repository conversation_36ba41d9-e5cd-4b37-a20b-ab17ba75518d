package com.wsf.dataingestor.shared.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import com.wsf.dataingestor.metrics.MetricsManager;

@TestConfiguration
public class JavaMailSenderConfig {

  @MockBean
  private JavaMailSenderImpl mockJavaMailSender;

  @Bean
  @Primary
  public JavaMailSender javaMailSender(MetricsManager metricsManager) {
    return mockJavaMailSender;
  }
}