package com.wsf.dataingestor.shared.config;

import java.util.Set;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import com.wsf.dataingestor.services.stats.AllStatistic0Validator;
import com.wsf.dataingestor.services.stats.StatsValidator;

@TestConfiguration
public class ValidatorTestConfig {
  @Bean("playerStatsValidator")
  @Primary
  public StatsValidator playerStatsValidator() {
    return new AllStatistic0Validator(Set.of());
  }
}