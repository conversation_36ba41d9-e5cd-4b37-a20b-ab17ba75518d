package com.wsf.dataingestor.shared.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.services.ThreadPoolService;

import static com.google.common.util.concurrent.MoreExecutors.directExecutor;

@TestConfiguration
public class ThreadPoolConfig {
  @Bean
  public ThreadPoolService threadPoolService(MetricsManager metricsManager) {
    return new ThreadPoolService(directExecutor(), directExecutor(), metricsManager);
  }
}
