package com.wsf.dataingestor.shared;

import java.time.Instant;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;
import com.wsf.dataingestor.cache.models.OngoingMatchData;
import com.wsf.dataingestor.cache.models.OngoingMatchData.PlayerMatchEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;

import static com.google.common.collect.Lists.newLinkedList;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_AWAY_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_HOME_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.defaultFixture;
import static com.wsf.dataingestor.sports.soccer.Constants.AWAY_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.HOME_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.OPPONENT_TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOAL_KICKS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOAL_KICKS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES_KICK_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SCORE;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS_REGULAR_TIMES;
import static com.wsf.domain.common.MatchPeriod.FIRST_HALF;
import static com.wsf.domain.common.MatchPeriod.SECOND_HALF;
import static com.wsf.domain.customer.soccer.Constants.AWAY_TEAM_SENT_OFF_STAT;
import static com.wsf.domain.customer.soccer.Constants.HOME_TEAM_SENT_OFF_STAT;
import static java.time.Instant.now;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toList;

public class TestUtils {

  public static final Integer MATCH_TIME = 45;
  public static final String FEED_ID = "feedId";
  public static final String BETSTOP_EVENT_ID = "betStopEventId";


  public static PlayerDataDTO buildHomePlayerFeedData(Map<String, Number> stats) {
    return buildPlayerFeedData(stats, DEFAULT_HOME_TEAM);
  }

  public static PlayerDataDTO buildAwayPlayerFeedData(Map<String, Number> stats) {
    return buildPlayerFeedData(stats, DEFAULT_AWAY_TEAM);
  }

  public static PlayerDataDTO buildPlayerFeedData(Map<String, Number> stats) {
    return buildPlayerFeedData(stats, DEFAULT_HOME_TEAM);
  }

  public static PlayerDataDTO buildPlayerFeedData(Map<String, Number> stats, Team team) {
    return buildPlayerFeedData(PlayerCanned.defaultPlayer(team), stats);
  }

  public static PlayerDataDTO buildPlayerFeedData(Player player, Map<String, Number> stats) {
    return PlayerDataDTO
      .builder().player(player).isPlaying(true).hasPlayed(true).stats(stats)
      .timestamp(now()).build();
  }

  public static OngoingMatchData buildCachedData() {
    return buildCachedData(0, 0);
  }

  public static OngoingMatchData buildCachedData(Map<String, Set<PlayerMatchEventDTO>> playerMatchEvents) {
    return buildCachedData(FIRST_HALF, playerMatchEvents);
  }

  public static OngoingMatchData buildCachedData(MatchPeriod matchPeriod,
                                                 Map<String, Set<PlayerMatchEventDTO>> playerMatchEvents) {
    return buildCachedData(0, 0, matchPeriod, playerMatchEvents, Map.of(), new HashSet<>());
  }

  public static OngoingMatchData buildCachedDataWithBetStopEvents(Set<String> processedBetStopEventIds) {
    return buildCachedData(0, 0, FIRST_HALF, new HashMap<>(), Map.of(), processedBetStopEventIds);
  }

  public static OngoingMatchData buildCachedDataWithLineUpPlayerIds(Map<String, Set<String>> teamIdToLineUp) {
    return buildCachedData(0, 0, FIRST_HALF, new HashMap<>(), teamIdToLineUp, emptySet());
  }

  public static OngoingMatchData buildCachedDataWithMatchStats() {
    return buildCachedData(0, 0);
  }

  public static OngoingMatchData buildCachedData(int homeScore, int awayScore) {
    return buildCachedData(homeScore, awayScore, FIRST_HALF, new HashMap<>(), Map.of(), new HashSet<>());
  }

  private static OngoingMatchData buildCachedData(int homeScore, int awayScore, MatchPeriod matchPeriod,
                                                  Map<String, Set<PlayerMatchEventDTO>> playerMatchEvents,
                                                  Map<String, Set<String>> lineupPlayerIds,
                                                  Set<String> processedBetStopEventIds) {
    return OngoingMatchData
      .builder()
      .matchId("fixtureId")
      .matchTime(MATCH_TIME)
      .matchPeriod(matchPeriod)
      .homeScore(homeScore)
      .awayScore(awayScore)
      .teamIdToLineUp(lineupPlayerIds)
      .timestamp(now().toEpochMilli())
      .eventIdToPlayerMatchEvents(playerMatchEvents)
      .processedBetStopEventIds(processedBetStopEventIds)
      .build();
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeed() {
    return buildFeed(emptyList());
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeed(Fixture fixture, FeedFixtureStatus status) {
    return buildFeed(fixture, emptyList(), null, null, null, null, FIRST_HALF, null, FeedFixtureStatus.FIXTURE, false,
      false, MatchDataFeed.FeedProvider.OPTA).fixtureStatus(status);
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeed(Fixture fixture, Instant matchDate) {
    return buildFeed(fixture, emptyList(), null, null, null, null, FIRST_HALF, null, FeedFixtureStatus.FIXTURE, false,
      false, MatchDataFeed.FeedProvider.OPTA).date(matchDate);
  }

  public static MatchDataFeed buildOptaFeed(Fixture fixture) {
    return buildFeed(fixture, emptyList(), null, null, null, null, FIRST_HALF, null, null, false, false,
      MatchDataFeed.FeedProvider.OPTA).build();
  }

  public static MatchDataFeed buildRunningballFeed(Fixture fixture) {
    return buildFeed(fixture, emptyList(), null, null, null, null, FIRST_HALF, null, null, false, false,
      MatchDataFeed.FeedProvider.RUNNINGBALL).build();
  }

  public static MatchDataFeed buildFeed(TeamDataDTO firstTeamData, TeamDataDTO secondTeamData) {
    return buildFeed(defaultFixture(), firstTeamData, secondTeamData).build();
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeed(Fixture fixture, TeamDataDTO firstTeamData,
                                                             TeamDataDTO secondTeamData) {
    return buildOptaFeed(fixture, emptyList(), newLinkedList(), null, firstTeamData, secondTeamData, FIRST_HALF, 5,
      FeedFixtureStatus.PLAYED, false);
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeed(Fixture fixture, TeamDataDTO firstTeamData,
                                                             TeamDataDTO secondTeamData,
                                                             List<EntityEventDTO> playerMatchEvents) {
    return buildOptaFeed(fixture, emptyList(), playerMatchEvents, null, firstTeamData, secondTeamData, FIRST_HALF, 5,
      FeedFixtureStatus.PLAYED, false);
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildFeedWithGoalEvents(List<PlayerDataDTO> playerData,
                                                                           List<EntityEventDTO> goalScorers) {
    return buildOptaFeed(defaultFixture(), playerData, goalScorers, null, null, null, SECOND_HALF, null, null, false);
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildLiveFeed(TeamDataDTO firstTeamData,
                                                                 TeamDataDTO secondTeamData) {
    return buildOptaFeed(defaultFixture(), emptyList(), newLinkedList(), null, firstTeamData, secondTeamData,
      FIRST_HALF, 5, FeedFixtureStatus.LIVE, false);
  }


  public static MatchDataFeed.MatchDataFeedBuilder buildFeed(List<PlayerDataDTO> playerData) {
    return buildOptaFeed(defaultFixture(), playerData, null, null, null, null, FIRST_HALF, null, null, false);
  }

  public static MatchDataFeed buildFeed(List<PlayerDataDTO> playerData, MatchPeriod half) {
    return buildOptaFeed(defaultFixture(), playerData, null, null, null, null, half, null, null, false).build();
  }

  public static MatchDataFeed buildFeed(List<PlayerDataDTO> playerData, List<TeamDataDTO> teamsData) {
    return buildOptaFeed(defaultFixture(), playerData, null, null, teamsData.get(0), teamsData.get(1), FIRST_HALF, null,
      null, false).build();
  }

  public static MatchDataFeed buildFeed(List<PlayerDataDTO> playerData, List<EntityEventDTO> goalScorers,
                                        MatchPeriod matchPeriod) {
    return buildOptaFeed(defaultFixture(), playerData, goalScorers, null, null, null, matchPeriod, null, null,
      false).build();
  }

  public static MatchDataFeed buildMA18DPFeed(Fixture fixture, Integer matchTime, FeedFixtureStatus matchStatus,
                                              List<PlayerDataDTO> playerDTOs, boolean isSnapshot) {
    return buildOptaFeed(fixture, playerDTOs, null, null, null, null, FIRST_HALF, matchTime, matchStatus, isSnapshot,
      true).build();
  }

  public static MatchDataFeed buildMA2Feed(Fixture fixture, Integer matchTime, FeedFixtureStatus matchStatus,
                                           List<PlayerDataDTO> playerDTOs, List<EntityEventDTO> playerMatchEvents) {
    return buildOptaFeed(fixture, playerDTOs, playerMatchEvents, null, null, null, FIRST_HALF, matchTime, matchStatus,
      false).build();
  }

  public static MatchDataFeed buildFeed(Fixture fixture, Integer matchTime, FeedFixtureStatus matchStatus,
                                        TeamDataDTO firstTeamData, TeamDataDTO secondTeamData, boolean isSnapshot) {
    return buildOptaFeed(fixture, emptyList(), emptyList(), null, firstTeamData, secondTeamData, null, matchTime,
      matchStatus, isSnapshot).build();
  }

  public static MatchDataFeed.MatchDataFeedBuilder buildOptaFeed(Fixture fixture, List<PlayerDataDTO> playerData,
                                                                 List<EntityEventDTO> playerMatchEvents,
                                                                 List<MatchDataFeed.MatchEventDTO> matchEvents,
                                                                 TeamDataDTO firstTeamData, TeamDataDTO secondTeamData,
                                                                 MatchPeriod matchPeriod, Integer matchTime,
                                                                 FeedFixtureStatus matchStatus, boolean isSnapshot) {
    return buildFeed(fixture, playerData, playerMatchEvents, matchEvents, firstTeamData, secondTeamData, matchPeriod,
      matchTime, matchStatus, isSnapshot, false, MatchDataFeed.FeedProvider.OPTA);
  }

  private static MatchDataFeed.MatchDataFeedBuilder buildOptaFeed(Fixture fixture, List<PlayerDataDTO> playerData,
                                                                  List<EntityEventDTO> playerMatchEvents,
                                                                  List<MatchDataFeed.MatchEventDTO> matchEvents,
                                                                  TeamDataDTO firstTeamData, TeamDataDTO secondTeamData,
                                                                  MatchPeriod matchPeriod, Integer matchTime,
                                                                  FeedFixtureStatus matchStatus, boolean isSnapshot,
                                                                  boolean isSingleEventFeed) {
    return buildFeed(fixture, playerData, playerMatchEvents, matchEvents, firstTeamData, secondTeamData, matchPeriod,
      matchTime, matchStatus, isSnapshot, isSingleEventFeed, MatchDataFeed.FeedProvider.OPTA);
  }

  private static MatchDataFeed.MatchDataFeedBuilder buildFeed(Fixture fixture, List<PlayerDataDTO> playerData,
                                                              List<EntityEventDTO> playerMatchEvents,
                                                              List<MatchDataFeed.MatchEventDTO> matchEvents,
                                                              TeamDataDTO firstTeamData, TeamDataDTO secondTeamData,
                                                              MatchPeriod matchPeriod, Integer matchTime,
                                                              FeedFixtureStatus matchStatus, boolean isSnapshot,
                                                              boolean isSingleEventFeed,
                                                              MatchDataFeed.FeedProvider feedProvider) {
    return MatchDataFeed
      .builder()
      .receivedTs(now())
      .feedId(FEED_ID)
      .fixture(fixture)
      .date(now())
      .matchPeriod(matchPeriod)
      .matchTimeMin(matchTime)
      .teamsData(Stream
        .of(firstTeamData, secondTeamData)
        .filter(Objects::nonNull)
        .collect(toList()))
      .playersData(playerData)
      .aggregatedPlayerMatchEvents(playerMatchEvents)
      .matchEvents(matchEvents)
      .isSnapshot(isSnapshot)
      .isSingleEventFeed(isSingleEventFeed)
      .provider(feedProvider)
      .fixtureStatus(matchStatus);
  }

  public static void addDefaultTeamStats(Map<String, Number> expectedStats) {
    expectedStats.put(TEAM_SCORE, 0);
    expectedStats.put(OPPONENT_TEAM_SCORE, 0);
    expectedStats.put(HOME_TEAM_SENT_OFF_STAT, 0);
    expectedStats.put(AWAY_TEAM_SENT_OFF_STAT, 0);
    expectedStats.put(HOME_SCORE, 0);
    expectedStats.put(AWAY_SCORE, 0);
    expectedStats.put(TEAM_SHOTS, 0);
    expectedStats.put(TEAM_SHOTS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_SHOTS_ON_GOAL, 0);
    expectedStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_GOAL_KICKS, 0);
    expectedStats.put(TEAM_GOAL_KICKS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_OFFSIDES, 0);
    expectedStats.put(TEAM_OFFSIDES_KICK_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_CORNERS, 0);
    expectedStats.put(TEAM_CORNERS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_YELLOW_CARDS, 0);
    expectedStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_GOALS, 0);
    expectedStats.put(TEAM_GOALS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_FOULS, 0);
    expectedStats.put(TEAM_FOULS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_RED_CARDS, 0);
    expectedStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    expectedStats.put(TEAM_TACKLES_WON, 0);
    expectedStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 0);
  }

  public static <K, V> Map<K, V> newMutableMap(K key, V value) {
    var map = new HashMap<K, V>();
    map.put(key, value);
    return map;
  }
}
