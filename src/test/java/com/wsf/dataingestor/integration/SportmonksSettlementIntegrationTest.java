package com.wsf.dataingestor.integration;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.ContextConfiguration;
import com.wsf.dataingestor.crons.FixtureRatingsUpdater;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.config.ValidatorTestConfig;
import com.wsf.dataingestor.sportmonks.clients.http.GetFixtureStats;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.CompetitionConfig.FeedsConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.domain.common.LivePlayerRating;
import com.wsf.domain.common.LiveTeamRating;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;
import com.wsf.kafka.domain.FixtureSummaryData;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.integration.EntitiesMockUtils.mockJobExecutionContext;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.readFixtureSummaryDataFromKafka;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.verifyJobsAreStopped;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.verifySettlementJobIsTriggered;
import static com.wsf.dataingestor.shared.canned.CompetitionConfigCanned.competitionConfigCanned;
import static com.wsf.dataingestor.shared.canned.CompetitionMarketsConfigCanned.createCompetitionMarketConfig;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.GOALS_MADE_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.domain.common.CompetitionConfig.Provider.SPORTMONKS;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.COMPLETED_PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static com.wsf.kafka.domain.FixtureStatus.PLAYED;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static java.lang.String.format;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {ValidatorTestConfig.class})
public class SportmonksSettlementIntegrationTest extends AbstractMongoTest {

  private static final String PLAYER1_ID = ObjectId.get().toString();
  private static final String PLAYER2_ID = ObjectId.get().toString();
  private static final String PLAYER3_ID = ObjectId.get().toString();

  @Autowired
  private FixtureRatingsUpdater fixtureRatingsUpdater;
  @Autowired
  private FixtureService fixtureService;
  @SpyBean
  private Scheduler scheduler;

  @MockBean
  private GetFixtureStats getFixtureStatsMock;
  @MockBean
  private KafkaService kafkaServiceMock;

  public void initEnv() {
    mockCompetition(allLeaguesTemplate);
    mockCompetitionsConfig(allLeaguesTemplate);
    jobExecutionContext = mockJobExecutionContext();
  }

  @After
  public void cleanup() {
    serieATemplate.dropCollection(Player.class);
    serieATemplate.dropCollection(Team.class);
    serieATemplate.dropCollection(LivePlayerRating.class);
    serieATemplate.dropCollection(LiveTeamRating.class);
    serieATemplate.dropCollection(PlayerRating.class);
    serieATemplate.dropCollection(TeamRating.class);
  }

  @Test
  public void whenAFixtureIsProcessed_andItIsOver_thenTheSettlementEventsAreSent() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockSpeRomPlayers(serieATemplate);
    mockSpeRomTeams(serieATemplate);
    mockFixture("18546333");
    mockStatsFeed("stats");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifySettlementJobIsTriggered(scheduler);
    await()
      .with()
      .atMost(5, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(SETTLED));

    verifyFinishedMatchPlayerRatingEvents();
    verifyFinishedMatchTeamRatingEvents();

    FixtureSummaryData fixtureSummaryData = readFixtureSummaryDataFromKafka(kafkaServiceMock);
    assertEquals(PLAYED, fixtureSummaryData.getFixtureStatus());
  }

  @Test
  public void whenAFixtureIsProcessed_andItIsOver_butThereAreNotMinutesPlayedForAPlayerWhoPlayed_thenTheSettlementEventsForThePlayerIsSent() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockHebLokPlayers(serieATemplate);
    mockHebLokTeams(serieATemplate);
    mockFixture("18557569");
    mockStatsFeed("stats_lokomotiv_sofia");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifySettlementJobIsTriggered(scheduler);
    await()
      .with()
      .atMost(5, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(SETTLED));

    verifyFinishedMatchRatingEventsWhenMissingMinutesPlayed();

    FixtureSummaryData fixtureSummaryData = readFixtureSummaryDataFromKafka(kafkaServiceMock);
    assertEquals(PLAYED, fixtureSummaryData.getFixtureStatus());
  }

  @Test
  public void whenAFixtureIsProcessed_andItIsOver_andThereIsNoPeriodForAGoalEvent_thenTheSettlementEventsForThePlayerIsSent() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockHebLokPlayers(serieATemplate);
    mockHebLokTeams(serieATemplate);
    mockFixture("18557569");
    mockStatsFeed("stats_lokomotiv_sofia_goal_no_period");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifySettlementJobIsTriggered(scheduler);
    await()
      .with()
      .atMost(5, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(SETTLED));

    verifyFinishedMatchRatingEventsWhenMissingPeriod();

    FixtureSummaryData fixtureSummaryData = readFixtureSummaryDataFromKafka(kafkaServiceMock);
    assertEquals(PLAYED, fixtureSummaryData.getFixtureStatus());
  }

  @Test
  public void whenAFixtureIsProcessed_andItWasPostponed_thenNotSettleTheFixture() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockFixture("18763123");
    mockStatsFeed("stats_postponed");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifyJobsAreStopped(scheduler);
    await()
      .with()
      .atMost(5, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(null));
  }

  private Map<String, PlayerRating> captureKafkaPlayerRatings(int numberOfCalls) {
    ArgumentCaptor<PlayerRating> ratingArgumentCaptor = ArgumentCaptor.forClass(PlayerRating.class);
    verify(kafkaServiceMock, times(numberOfCalls)).sendFinalPlayerRating(ratingArgumentCaptor.capture(),
      eq(FixtureStatus.PLAYED), eq(true), any(KafkaMetadata.class));

    return ratingArgumentCaptor.getAllValues()
      .stream()
      .collect(toMap(p -> p.getPlayer().getSportmonksPlayerId(), identity()));
  }

  private Map<String, TeamRating> captureKafkaTeamRatings(int numberOfCalls) {
    ArgumentCaptor<TeamRating> ratingArgumentCaptor = ArgumentCaptor.forClass(TeamRating.class);
    verify(kafkaServiceMock, times(numberOfCalls)).sendFinalTeamRating(ratingArgumentCaptor.capture(),
      eq(FixtureStatus.PLAYED), eq(true), eq(EMPTY_METADATA));

    return ratingArgumentCaptor.getAllValues()
      .stream()
      .collect(toMap(t -> t.getTeam().getSportmonksId(), identity()));
  }

  private void verifyFinishedMatchPlayerRatingEvents() {
    Map<String, PlayerRating> ratingsBySMPlayerId = captureKafkaPlayerRatings(3);

    verifyAbrahamRatings(ratingsBySMPlayerId);
    verifyVerdeRatings(ratingsBySMPlayerId);
    verifyPellegriniRatings(ratingsBySMPlayerId);
  }

  private void verifyFinishedMatchTeamRatingEvents() {
    Map<String, TeamRating> ratingsBySMTeamId = captureKafkaTeamRatings(2);
    verifySpeziaRatings(ratingsBySMTeamId);
    verifyRomaRatings(ratingsBySMTeamId);
  }

  private static void verifyRomaRatings(Map<String, TeamRating> ratingsBySMTeamId) {
    TeamRating romaRating = ratingsBySMTeamId.get("37");
    assertThat(romaRating.getStats().get(TEAM_GOALS), is(2));
    assertThat(romaRating.getStats().get(TEAM_SHOTS), is(13));
    assertThat(romaRating.getStats().get(TEAM_SHOTS_ON_GOAL), is(3));
    assertThat(romaRating.getStats().get(TEAM_CORNERS), is(0));
    assertThat(romaRating.getStats().get(TEAM_FOULS), is(10));
    assertThat(romaRating.getStats().get(TEAM_OFFSIDES), is(1));
    assertThat(romaRating.getStats().get(TEAM_YELLOW_CARDS), is(1));
  }

  private static void verifySpeziaRatings(Map<String, TeamRating> ratingsBySMTeamId) {
    TeamRating speziaRating = ratingsBySMTeamId.get("345");
    assertThat(speziaRating.getStats().get(TEAM_GOALS), is(0));
    assertThat(speziaRating.getStats().get(TEAM_SHOTS), is(7));
    assertThat(speziaRating.getStats().get(TEAM_SHOTS_ON_GOAL), is(0));
    assertThat(speziaRating.getStats().get(TEAM_CORNERS), is(4));
    assertThat(speziaRating.getStats().get(TEAM_FOULS), is(17));
    assertThat(speziaRating.getStats().get(TEAM_OFFSIDES), is(0));
    assertThat(speziaRating.getStats().get(TEAM_YELLOW_CARDS), is(3));
  }

  private void verifyFinishedMatchRatingEventsWhenMissingMinutesPlayed() {
    Map<String, PlayerRating> ratingsBySMPlayerId = captureKafkaPlayerRatings(2);

    PlayerRating zivkovicRating = ratingsBySMPlayerId.get("73753");
    assertThat(zivkovicRating.getStats().get(GOAL.getStatisticName()), is(1));
    assertThat(zivkovicRating.getStats().get(GOALS_MADE_REGULAR_TIMES), is(1));
  }

  private void verifyFinishedMatchRatingEventsWhenMissingPeriod() {
    Map<String, PlayerRating> ratingsBySMPlayerId = captureKafkaPlayerRatings(2);

    PlayerRating zivkovicRating = ratingsBySMPlayerId.get("73753");
    assertThat(zivkovicRating.getStats().get(GOAL.getStatisticName()), is(1));
    assertThat(zivkovicRating.getStats().get(GOALS_MADE_REGULAR_TIMES), is(1));
  }

  private static void verifyPellegriniRatings(Map<String, PlayerRating> ratingsBySMPlayerId) {
    PlayerRating pellegriniRating = ratingsBySMPlayerId.get("129946");
    assertTrue(pellegriniRating.getStats()
      .isEmpty());
  }

  private static void verifyVerdeRatings(Map<String, PlayerRating> ratingsBySMPlayerId) {
    PlayerRating verdeRating = ratingsBySMPlayerId.get("129778");
    assertThat(verdeRating.getStats().get(GOAL.getStatisticName()), is(0));
    assertThat(verdeRating.getStats().get(ASSIST_GOAL.getStatisticName()), is(0));
    assertThat(verdeRating.getStats().get(SHOT.getStatisticName()), is(1));
    assertThat(verdeRating.getStats().get(SHOT_ON_GOAL.getStatisticName()), is(0));
    assertThat(verdeRating.getStats().get(FOUL.getStatisticName()), is(1));
    assertThat(verdeRating.getStats().get(YELLOW_CARD.getStatisticName()), is(0));
    assertThat(verdeRating.getStats().get(PASS.getStatisticName()), is(10));
    assertThat(verdeRating.getStats().get(COMPLETED_PASS.getStatisticName()), is(7));
  }

  private static void verifyAbrahamRatings(Map<String, PlayerRating> ratingsBySMPlayerId) {
    PlayerRating abrahamRating = ratingsBySMPlayerId.get("3139");
    assertThat(abrahamRating.getStats().get(GOAL.getStatisticName()), is(1));
    assertThat(abrahamRating.getStats().get(GOALS_MADE_REGULAR_TIMES), is(1));
    assertThat(abrahamRating.getStats().get(ASSIST_GOAL.getStatisticName()), is(0));
    assertThat(abrahamRating.getStats().get(SHOT.getStatisticName()), is(2));
    assertThat(abrahamRating.getStats().get(SHOT_ON_GOAL.getStatisticName()), is(1));
    assertThat(abrahamRating.getStats().get(FOUL.getStatisticName()), is(0));
    assertThat(abrahamRating.getStats().get(YELLOW_CARD.getStatisticName()), is(0));
    assertThat(abrahamRating.getStats().get(PASS.getStatisticName()), is(24));
    assertThat(abrahamRating.getStats().get(COMPLETED_PASS.getStatisticName()), is(12));
  }

  private void mockStatsFeed(String file) throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] result = IOUtils.resourceToByteArray(format("feeds/sportmonks/%s.json", file), classLoader);
    when(getFixtureStatsMock.getFeed(anyString())).thenReturn(result);
  }

  static void mockCompetition(MongoTemplate template) {
    Competition competition = CompetitionCanned.competitionCanned().build();
    EntitiesMockUtils.mockCompetition(template, competition);
  }

  static void mockFixture(String smFixtureid) {
    Fixture fixture = FixtureCanned
      .defaultFixtureBuilder()
      .sportmonksFixtureId(smFixtureid)
      .active(true)
      .status(FixtureStatus.FIXTURE)
      .build();
    EntitiesMockUtils.mockFixture(allLeaguesTemplate, fixture);
  }

  static void mockSpeRomPlayers(MongoTemplate template) {
    mockPlayer(template, new ObjectId(PLAYER1_ID), "3139", TEAM1_ID); // Abraham
    mockPlayer(template, new ObjectId(PLAYER2_ID), "129778", TEAM2_ID); // Verde
    mockPlayer(template, new ObjectId(PLAYER3_ID), "129946", TEAM1_ID); // Pellegrini
  }

  static void mockHebLokPlayers(MongoTemplate template) {
    mockPlayer(template, new ObjectId(PLAYER1_ID), "69907", TEAM1_ID); // Vutov
    mockPlayer(template, new ObjectId(PLAYER2_ID), "73753", TEAM2_ID); // Zivkovic
  }

  static void mockSpeRomTeams(MongoTemplate template) {
    mockTeam(template, TEAM1_ID, "37"); // Roma
    mockTeam(template, TEAM2_ID, "345"); // Spezia
  }

  static void mockHebLokTeams(MongoTemplate template) {
    mockTeam(template, TEAM1_ID, "27859"); // Hebar
    mockTeam(template, TEAM2_ID, "7556"); // Lokomotiv
  }

  static void mockPlayer(MongoTemplate template, ObjectId playerId, String smPlayerId, ObjectId teamId) {
    Player player = PlayerCanned
      .playerCanned()
      .id(playerId)
      .sportmonksPlayerId(smPlayerId)
      .team(TeamCanned.defaultTeam(teamId))
      .build();
    EntitiesMockUtils.mockPlayer(template, player);
  }

  static void mockTeam(MongoTemplate template, ObjectId teamId, String smTeamId) {
    Team team = teamCanned().id(teamId).sportmonksId(smTeamId).build();
    EntitiesMockUtils.mockTeam(template, team);
  }

  static void mockCompetitionsConfig(MongoTemplate template) {
    CompetitionConfig competitionConfig = competitionConfigCanned()
      .feedsConfig(FeedsConfig
        .builder()
        .settlementConfig(CompetitionConfig.SettlementConfig
          .builder().provider(SPORTMONKS).delaySecs(1).build())
        .build())
      .markets(List.of(createCompetitionMarketConfig().propName(GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(ASSIST_GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(SHOT.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(SHOT_ON_GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(FOUL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(YELLOW_CARD.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(PASS.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(COMPLETED_PASS.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(TEAM_GOALS).build(),
        createCompetitionMarketConfig().propName(TEAM_SHOTS).build(),
        createCompetitionMarketConfig().propName(TEAM_SHOTS_ON_GOAL).build(),
        createCompetitionMarketConfig().propName(TEAM_FOULS).build(),
        createCompetitionMarketConfig().propName(TEAM_YELLOW_CARDS).build(),
        createCompetitionMarketConfig().propName(TEAM_OFFSIDES).build(),
        createCompetitionMarketConfig().propName(TEAM_CORNERS).build()))
      .build();

    EntitiesMockUtils.mockCompetitionsConfig(template, competitionConfig);
  }
}
