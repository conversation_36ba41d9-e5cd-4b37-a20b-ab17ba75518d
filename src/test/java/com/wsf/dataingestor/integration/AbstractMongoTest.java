package com.wsf.dataingestor.integration;

import de.flapdoodle.embed.mongo.MongodExecutable;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.config.IntegrationTestCacheConfig;
import com.wsf.dataingestor.shared.config.JavaMailSenderConfig;
import com.wsf.dataingestor.shared.config.ThreadPoolConfig;
import com.wsf.dataingestor.shared.db.DBUtils;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;

import static java.lang.String.format;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.MOCK;
import static org.springframework.data.mongodb.core.query.Query.query;

@ContextConfiguration(classes = {IntegrationTestCacheConfig.class, ThreadPoolConfig.class, JavaMailSenderConfig.class})
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = MOCK, properties = {"spring.main.allow-bean-definition-overriding=true"})
@AutoConfigureMockMvc
@TestPropertySource(properties = "spring.mongodb.embedded.version=3.5.5")
@ActiveProfiles("integration-test")
@Ignore
public abstract class AbstractMongoTest extends TestWithMocks {

  public static final String FIXTURE_COLLECTION = "fixtures";
  public static final String COMPETITIONS_COLLECTION = "competitions";
  public static final String PLAYERS_COLLECTION = "players";
  public static final String TEAMS_COLLECTION = "teams";
  public static final String COMPETITION_CONFIGS_COLLECTION = "competition_configs";

  public static final String PLAYER_RATINGS_COLLECTION = "ratings";

  public static final String CONTESTANT_UNAVAILABILITIES_COLLECTION = "contestant_unavailabilities";

  private static MongodExecutable mongodExecutable;
  protected static MongoTemplate mainTemplate, allLeaguesTemplate, serieATemplate, bulgariaTemplate;
  protected static JobExecutionContext jobExecutionContext;

  @Autowired
  protected MockMvc mvc;
  @Autowired
  protected ObjectMapper objectMapper;

  @BeforeClass
  public static void setupMongo() throws IOException {
    String dbUri = startMongo();

    mainTemplate = buildMongoTemplate(dbUri, "main");
    allLeaguesTemplate = buildMongoTemplate(dbUri, "all_leagues");
    serieATemplate = buildMongoTemplate(dbUri, "seriea");
    bulgariaTemplate = buildMongoTemplate(dbUri, "bulgarianfirstleague");
  }

  @AfterClass
  public static void shutdown() {
    mongodExecutable.stop();
  }

  @Before
  public void before() {
    initEnv();
  }

  /**
   * Init mocks
   */
  protected abstract void initEnv();

  protected String asJsonString(final Object obj) {
    try {
      return objectMapper.writeValueAsString(obj);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }


  protected static void insertFixturesIntoDb(List<Fixture> mockedFixtures) {
    for (var fixture : mockedFixtures) {
      allLeaguesTemplate.findAndReplace(query(Criteria.where("_id")
        .is(fixture.getId())), fixture, FindAndReplaceOptions.options().upsert(), FIXTURE_COLLECTION);
    }
  }

  protected static void insertCompetitionsIntoDb(List<Competition> competitions) {
    for (var competition : competitions) {
      allLeaguesTemplate.findAndReplace(query(Criteria.where("_id")
        .is(competition.getId())), competition, FindAndReplaceOptions.options().upsert(), COMPETITIONS_COLLECTION);
    }
  }

  protected static void insertCompetitionConfigsIntoDb(List<CompetitionConfig> competitionConfigs) {
    for (var competitionConfig : competitionConfigs) {
      allLeaguesTemplate.findAndReplace(query(Criteria.where("competition._id")
          .is(competitionConfig.getCompetition().getId())), competitionConfig, FindAndReplaceOptions.options().upsert(),
        COMPETITION_CONFIGS_COLLECTION);
    }
  }

  protected static void insertPlayersIntoDb(List<Player> players) {
    for (var player : players) {
      serieATemplate.findAndReplace(query(Criteria.where("_id")
        .is(player.getId())), player, FindAndReplaceOptions.options().upsert(), PLAYERS_COLLECTION);
    }
  }

  protected static void insertTeamsIntoDb(List<Team> teams) {
    for (var team : teams) {
      serieATemplate.findAndReplace(query(Criteria.where("_id")
        .is(team.getId())), team, FindAndReplaceOptions.options().upsert(), TEAMS_COLLECTION);
    }
  }

  private static MongoTemplate buildMongoTemplate(String dbUri, String dbName) {
    MongoClient mongoClient = MongoClients.create(dbUri);
    MongoTemplate mongoTemplate = new MongoTemplate(mongoClient, dbName);
    ((MappingMongoConverter) mongoTemplate.getConverter()).afterPropertiesSet();
    return mongoTemplate;
  }

  private static String startMongo() throws IOException {
    String ip = "localhost";
    int port = 28017;
    mongodExecutable = DBUtils.startEmbeddedMongo(ip, port);
    return format("mongodb://%s:%s", ip, port);
  }
}
