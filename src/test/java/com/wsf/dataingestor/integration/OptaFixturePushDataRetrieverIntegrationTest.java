package com.wsf.dataingestor.integration;

import java.util.List;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed.MatchEventDTO;
import com.wsf.dataingestor.opta.services.OptaFixturePushDataRetriever;
import com.wsf.dataingestor.services.ratings.FixtureStartDataProcessor;
import com.wsf.dataingestor.shared.canned.CompetitionConfigCanned;
import com.wsf.dataingestor.shared.canned.MatchDataFeedCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Tournament;
import com.wsf.domain.soccer.SoccerMatchEvent;
import com.wsf.kafka.domain.metadata.KafkaMetadata;

import static com.wsf.dataingestor.services.events.Utils.PREMIER_LEAGUE_COMPETITION_ID;
import static com.wsf.dataingestor.shared.canned.CompetitionCanned.competitionCanned;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.entityEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.goalMadePlayerEvent;
import static com.wsf.dataingestor.shared.canned.EntityEventDTOCanned.offsidePlayerEvent;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.MatchDataFeedCanned.matchDataFeedCanned;
import static com.wsf.dataingestor.shared.canned.MatchEventDTOCanned.matchEventDTOCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.dataingestor.shared.canned.TeamDataDTOCanned.teamDataDTOCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.BET_START;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.MATCH_FINISHED;
import static java.time.Instant.now;
import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class OptaFixturePushDataRetrieverIntegrationTest extends AbstractMongoTest {

  Fixture fixture = livePremierLeagueFixture();
  @Autowired
  private OptaFixturePushDataRetriever optaFixturePushDataRetriever;
  @Autowired
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;
  @Autowired
  private FixtureStartDataProcessor fixtureStartDataProcessor;
  @MockBean
  private KafkaService kafkaServiceMock;

  public void initEnv() {
    mockCompetitionsConfig(allLeaguesTemplate);
    mockCompetition(allLeaguesTemplate);
    insertFixturesIntoDb(List.of(fixture));
    var emptyOngoingMatchData = ongoingMatchDataCanned().build();
    ongoingMatchDataCacheService.set(fixture.getIdAsString(), emptyOngoingMatchData);
  }

  @After
  public void tearDown() {
    ongoingMatchDataCacheService.remove(fixture.getIdAsString());
  }

  @Test
  public void whenAFoulIsProcessed_thenBetStopIsSent() {
    // Arrange
    MatchEventDTO foulEvent = matchEventDTOCanned().event(FOUL).eventId("foulId")
      .timestamp(now()).build();
    var foulFeed = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(foulEvent)).build();
    mockMatchStarted();

    // Act
    Mockito.reset(kafkaServiceMock);
    optaFixturePushDataRetriever.processMA18DPFeed(foulFeed);

    //Assert
    await()
      .with()
      .atMost(10, SECONDS)
      .untilAsserted(() -> verify(kafkaServiceMock, times(2)).sendTeamEventRating(
        argThat(teamEvent -> teamEvent.getTeamBetStop().getReason().equals(FOUL.toString()))));
  }

  @Test
  public void whenAnOffsideIsProcessed_thenATeamLiveRatingIsSent() {
    // Arrange
    EntityEventDTO offsideEvent = offsidePlayerEvent()
      .timestamp(now()).build();
    var foulFeed = matchDataFeedCanned()
      .fixture(fixture)
      .teamsData(List.of(teamDataDTOCanned().build()))
      .feedPlayerMatchEvents(List.of(offsideEvent))
      .build();
    mockMatchStarted();

    // Act
    optaFixturePushDataRetriever.processMA18DPFeed(foulFeed);

    //Assert
    await()
      .with()
      .atMost(10, SECONDS)
      .untilAsserted(() -> verify(kafkaServiceMock, times(1)).sendLiveTeamRating(any(), any(KafkaMetadata.class)));
  }

  @Test
  public void whenBetsAreStoppedAndFirstHalfFinishes_thenBetsGetRestarted() {
    // Arrange
    MatchEventDTO foulEvent = matchEventDTOCanned().event(FOUL).eventId("foulId")
      .timestamp(now().minusSeconds(1)).build();
    MatchEventDTO firstHalfEndEvent = matchEventDTOCanned().event(BET_START).eventId("endOfFirstHalfId")
      .timestamp(now()).build();
    var foulMa18dp = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(foulEvent)).build();
    var firstHalfMa18dp = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(firstHalfEndEvent)).build();

    mockMatchStarted();

    // Act
    optaFixturePushDataRetriever.processMA18DPFeed(foulMa18dp);
    Mockito.reset(kafkaServiceMock);
    optaFixturePushDataRetriever.processMA18DPFeed(firstHalfMa18dp);

    //Assert
    await()
      .with()
      .atMost(10, SECONDS)
      .untilAsserted(() -> verify(kafkaServiceMock, times(2)).sendTeamEventRating(
        argThat(teamEvent -> teamEvent.getTeamBetStart().getMatch().getId().equals(fixture.getIdAsString()))));
  }

  @Test
  public void whenBetsAreNotStoppedAndFirstHalfFinishes_thenBetsDoNotGetRestarted() {
    // Arrange
    MatchEventDTO firstHalfEndEvent = matchEventDTOCanned().event(BET_START).eventId("endOfFirstHalfId")
      .timestamp(now().minusSeconds(60 * 45)).build();
    MatchEventDTO endOfMatchEvent = matchEventDTOCanned().event(MATCH_FINISHED).eventId("endOfMatchId")
      .timestamp(now()).build();
    var firstHalfMa18dp = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(firstHalfEndEvent)).build();
    var endOfMatchMa18Dp = matchDataFeedCanned().fixture(fixture).matchEvents(List.of(endOfMatchEvent)).build();

    mockMatchStarted();

    // Act
    optaFixturePushDataRetriever.processMA18DPFeed(firstHalfMa18dp);
    Mockito.reset(kafkaServiceMock);
    optaFixturePushDataRetriever.processMA18DPFeed(endOfMatchMa18Dp);

    //Assert
    await().with().atMost(10, SECONDS).untilAsserted(() -> {
      verify(kafkaServiceMock, times(2)).sendTeamEventRating(
        argThat(teamEvent -> teamEvent.getTeamBetStop().getMatch().getId().equals(fixture.getIdAsString())));
    });
  }

  @Test
  public void whenAGoalEventIsDeleted_thenLiveFixtureSummaryDataIsSent() {
    // Arrange
    var goalMadeEventId = "goalMade1";
    EntityEventDTO goalMadeEvent = goalMadePlayerEvent().eventId(goalMadeEventId).eventType(GOAL).build();
    var goalMadeFeed = matchDataFeedCanned().fixture(fixture).feedPlayerMatchEvents(List.of(goalMadeEvent)).build();

    EntityEventDTO deletedGoalEvent = entityEventDTOCanned().eventId(goalMadeEventId).eventType(
      SoccerMatchEvent.DELETED_EVENT).build();
    var deletedGoalFeed = matchDataFeedCanned()
      .fixture(fixture)
      .feedPlayerMatchEvents(List.of(deletedGoalEvent))
      .build();

    mockMatchStarted();

    // Act
    optaFixturePushDataRetriever.processMA18DPFeed(goalMadeFeed);
    optaFixturePushDataRetriever.processMA18DPFeed(deletedGoalFeed);

    //Assert
    await()
      .with()
      .atMost(10, SECONDS)
      .untilAsserted(() -> verify(kafkaServiceMock, times(2)).sendTemporaryFixtureSummary(any()));
  }

  private static Fixture livePremierLeagueFixture() {
    Competition premierLeagueCompetition = competitionCanned().id(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID)).build();
    Tournament premierLeagueTournament = TournamentCanned
      .tournamentCanned()
      .competition(premierLeagueCompetition)
      .build();
    return fixtureCanned().status(LIVE).isLiveEnabled(true).tournament(premierLeagueTournament).build();
  }

  private void mockMatchStarted() {
    var ma2 = MatchDataFeedCanned.matchDataFeedCanned().build();
    var emptyOngoingMatchData = ongoingMatchDataCanned().build();
    fixtureStartDataProcessor.processFeed(fixture, ma2, emptyOngoingMatchData);
  }

  private static void mockCompetitionsConfig(MongoTemplate template) {
    CompetitionConfig competitionConfig = CompetitionConfigCanned.premierCompetitionConfigCanned().build();

    EntitiesMockUtils.mockCompetitionsConfig(template, competitionConfig);
  }

  private static void mockCompetition(MongoTemplate template) {
    Competition competition = competitionCanned().id(new ObjectId(PREMIER_LEAGUE_COMPETITION_ID)).build();
    EntitiesMockUtils.mockCompetition(template, competition);
  }

}
