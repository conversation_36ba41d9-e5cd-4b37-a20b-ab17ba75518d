package com.wsf.dataingestor.integration;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.util.List;
import java.util.Optional;
import org.junit.Test;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import com.wsf.dataingestor.crons.PlayerAvailabilityUpdater;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.email.OptaSupportEmailClient;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.ContestantUnavailabilityUseCase;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.canned.FixtureCanned;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PlayerAvailabilityUpdaterTest extends AbstractMongoTest {

  @MockBean
  private JobExecutionContext jobExecutionContext;

  @MockBean
  private FixtureService fixtureService;

  @MockBean
  private ContestantUnavailabilityUseCase contestantUnavailabilityUseCase;

  @MockBean
  private OptaSupportEmailClient optaSupportEmailClient;

  @MockBean
  private FeedsConfigService feedsConfigService;

  @Autowired
  private MetricsManager metricsManager;

  private PlayerAvailabilityUpdater playerAvailabilityUpdater;

  @Override
  protected void initEnv() {
    MeterRegistry simpleRegistry = new SimpleMeterRegistry();
    metricsManager = new MetricsManager(simpleRegistry);

    playerAvailabilityUpdater = new PlayerAvailabilityUpdater(fixtureService, metricsManager,
      contestantUnavailabilityUseCase, feedsConfigService, optaSupportEmailClient);
  }

  @Test
  public void whenNotifyOptaSupportForMissingDataIsInvoked_andThereAreMissingFeeds_thenInvokeMailClientSuccessfully() throws Exception {
    //Arrange
    Trigger trigger = mock(Trigger.class);
    JobDataMap jobDataMap = mock(JobDataMap.class);
    when(jobExecutionContext.getTrigger()).thenReturn(trigger);
    when(trigger.getJobDataMap()).thenReturn(jobDataMap);
    when(jobDataMap.getInt(any())).thenReturn(1);

    when(contestantUnavailabilityUseCase.process(any())).thenReturn(Optional.of("id1"));

    var fixture = FixtureCanned.defaultFixture();
    when(fixtureService.findByDayWindow(any(), anyInt())).thenReturn(List.of(fixture));

    when(feedsConfigService.isProvisionalLineupFeedAvailable(any())).thenReturn(true);

    //Act
    playerAvailabilityUpdater.execute(jobExecutionContext);

    // Assert
    assertEquals(0, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
    verify(optaSupportEmailClient, times(1)).sendMissingMA46FeedMail(any());
  }

  @Test
  public void whenNotifyOptaSupportForMissingDataIsInvoked_andThereAreMissingFeeds_butInvokeMailClientFails_thenIncrementMetric() throws Exception {
    //Arrange
    Trigger trigger = mock(Trigger.class);
    JobDataMap jobDataMap = mock(JobDataMap.class);
    when(jobExecutionContext.getTrigger()).thenReturn(trigger);
    when(trigger.getJobDataMap()).thenReturn(jobDataMap);
    when(jobDataMap.getInt(any())).thenReturn(1);

    when(contestantUnavailabilityUseCase.process(any())).thenReturn(Optional.of("id1"));

    var fixture = FixtureCanned.defaultFixture();
    when(fixtureService.findByDayWindow(any(), anyInt())).thenReturn(List.of(fixture));

    when(feedsConfigService.isProvisionalLineupFeedAvailable(any())).thenReturn(true);

    doThrow(new RuntimeException("Error sending mail"))
      .when(optaSupportEmailClient).sendMissingMA46FeedMail(anySet());

    //Act
    playerAvailabilityUpdater.execute(jobExecutionContext);

    // Assert
    assertEquals(1, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
    verify(optaSupportEmailClient, times(1)).sendMissingMA46FeedMail(any());
  }
}