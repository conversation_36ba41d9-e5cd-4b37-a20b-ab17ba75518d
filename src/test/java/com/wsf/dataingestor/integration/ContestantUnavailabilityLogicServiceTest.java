package com.wsf.dataingestor.integration;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.repository.ContestantUnavailabilityRepository;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.service.ContestantUnavailabilityLogicService;
import com.wsf.dataingestor.shared.canned.ContestantUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FeedContestantUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FeedUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.ContestantUnavailability;

import static com.wsf.dataingestor.shared.canned.UnavailabilityCanned.unavailabilityCanned;
import static com.wsf.domain.common.ContestantUnavailability.UnavailabilityReason.INJURY;
import static com.wsf.domain.common.ContestantUnavailability.UnavailabilityReason.SUSPENSION;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.AVAILABLE;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.UNAVAILABLE;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class ContestantUnavailabilityLogicServiceTest extends AbstractMongoTest {

  private ContestantUnavailabilityLogicService contestantUnavailabilityLogicService;

  @Override
  protected void initEnv() {
    mainTemplate.dropCollection(CONTESTANT_UNAVAILABILITIES_COLLECTION);
  }

  @Before
  public void init() {
    ContestantUnavailabilityRepository contestantUnavailabilityRepository = new ContestantUnavailabilityRepository(
      mainTemplate);
    contestantUnavailabilityLogicService = new ContestantUnavailabilityLogicService(contestantUnavailabilityRepository);
  }

  @After
  public void cleanUp() {
    mainTemplate.dropCollection(CONTESTANT_UNAVAILABILITIES_COLLECTION);
  }

  @Test
  public void whenInTheFeedThereIsANewUnavailabilityForAContestant_thenTheContestantIsAlsoUpdatedInDb_thenReturnDto() {
    // Arrange
    var fixture = FixtureCanned.defaultFixture();
    var player = PlayerCanned.defaultPlayer();
    var startDate = Instant.now();
    var endDate = Instant.now().plus(10, ChronoUnit.DAYS);
    var description = "He broke the opponent's shin-bone like there was not tomorrow";

    var databaseContestantUnavailability = ContestantUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .contestantId(player.getIdAsString())
      .fixtureId(fixture.getIdAsString())
      .unavailabilities(Set.of(unavailabilityCanned()
        .reason(SUSPENSION)
        .startDate(startDate)
        .endDate(endDate)
        .description(description)
        .build()))
      .build();

    mainTemplate.save(databaseContestantUnavailability);

    var firstFeedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var secondFeedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke his shin-bone while breaking the opponent's shin-bone")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(firstFeedUnavailability, secondFeedUnavailability))
      .build();

    // Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(player.getIdAsString());
        assertThat(dto.action()).isEqualTo(UNAVAILABLE);
        assertThat(dto.isSuspended()).isTrue();
        assertThat(dto.isInjured()).isTrue();
      });

    List<ContestantUnavailability> contestantUnavailabilities = mainTemplate.find(new Query(Criteria.where("fixtureId")
      .is(fixture.getIdAsString())
      .and("contestantId")
      .is(player.getIdAsString())), ContestantUnavailability.class);

    assertThat(contestantUnavailabilities)
      .hasSize(1)
      .first()
      .satisfies(contestantUnavailability -> {
        assertThat(contestantUnavailability.getContestantId()).isEqualTo(player.getIdAsString());
        assertThat(contestantUnavailability.getFixtureId()).isEqualTo(fixture.getIdAsString());
        assertThat(contestantUnavailability.isContestantInjured()).isTrue();
        assertThat(contestantUnavailability.isContestantSuspended()).isTrue();
        assertThat(contestantUnavailability.getUnavailabilities())
          .hasSize(2)
          .anyMatch(unavailability -> unavailability.getReason() == SUSPENSION)
          .anyMatch(unavailability -> unavailability.getReason() == INJURY);
      });
  }

  @Test
  public void whenThereAreNewContestantUnavailabilities_thenAddToDatabase_thanReturnDto() {
    //Arrange
    var fixture = FixtureCanned.defaultFixture();
    var player = PlayerCanned.playerCanned().build();

    var feedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(feedUnavailability))
      .build();

    //Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of(feedContestantUnavailability));

    // Assert
    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(player.getIdAsString());
        assertThat(dto.action()).isEqualTo(UNAVAILABLE);
        assertThat(dto.isSuspended()).isTrue();
        assertThat(dto.isInjured()).isFalse();
      });

    List<ContestantUnavailability> contestantUnavailabilities = mainTemplate.find(new Query(Criteria.where("fixtureId")
      .is(fixture.getIdAsString())
      .and("contestantId")
      .is(player.getIdAsString())), ContestantUnavailability.class);

    assertThat(contestantUnavailabilities)
      .hasSize(1)
      .first()
      .satisfies(contestantUnavailability -> {
        assertThat(contestantUnavailability.getContestantId()).isEqualTo(player.getIdAsString());
        assertThat(contestantUnavailability.getFixtureId()).isEqualTo(fixture.getIdAsString());
        assertThat(contestantUnavailability.isContestantInjured()).isFalse();
        assertThat(contestantUnavailability.isContestantSuspended()).isTrue();
        assertThat(contestantUnavailability.getUnavailabilities())
          .hasSize(1)
          .anyMatch(unavailability -> unavailability.getReason() == SUSPENSION);
      });
  }

  @Test
  public void whenInTheFeedThereIsNotAContestantPresentInDb_thenRemoveTheOneContainedInDb_thenReturnDto() {
    //Arrange
    var fixture = FixtureCanned.defaultFixture();
    var feedPlayer = PlayerCanned.defaultPlayer();

    var databaseContestantUnavailability = ContestantUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .contestantId(feedPlayer.getIdAsString())
      .fixtureId(fixture.getIdAsString())
      .unavailabilities(Set.of(unavailabilityCanned()
        .reason(SUSPENSION)
        .startDate(Instant.now())
        .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
        .description("He broke the opponent's shin-bone like there was not tomorrow")
        .build()))
      .build();

    mainTemplate.save(databaseContestantUnavailability);

    // Act
    var contestantUnavailabilityDtos = contestantUnavailabilityLogicService.processUnavailabilitiesAndGetVariations(
      fixture, Set.of());

    // Assert
    assertThat(contestantUnavailabilityDtos)
      .hasSize(1)
      .first()
      .satisfies(dto -> {
        assertThat(dto.contestantId()).isEqualTo(feedPlayer.getIdAsString());
        assertThat(dto.action()).isEqualTo(AVAILABLE);
        assertThat(dto.isSuspended()).isFalse();
        assertThat(dto.isInjured()).isFalse();
      });

    List<ContestantUnavailability> contestantUnavailabilities = mainTemplate.find(new Query(Criteria.where("fixtureId")
      .is(fixture.getIdAsString())
      .and("contestantId")
      .is(feedPlayer.getIdAsString())), ContestantUnavailability.class);

    assertThat(contestantUnavailabilities).hasSize(0);
  }
}
