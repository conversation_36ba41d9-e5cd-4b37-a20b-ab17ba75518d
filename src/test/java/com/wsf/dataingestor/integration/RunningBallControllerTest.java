package com.wsf.dataingestor.integration;

import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.awaitility.Awaitility;
import org.awaitility.Duration;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import com.wsf.dataingestor.cache.OngoingMatchDataCacheService;
import com.wsf.dataingestor.config.cache.CacheConstants;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.kafka.domain.Match;
import com.wsf.kafka.domain.TeamEvent;

import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.OngoingMatchDataCanned.ongoingMatchDataCanned;
import static com.wsf.domain.common.Fixture.FixtureStatus.FIXTURE;
import static com.wsf.domain.common.Provider.RUNNINGBALL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.data.mongodb.core.query.Query.query;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class RunningBallControllerTest extends AbstractMongoTest {
  public static final String OPTA_FIXTURE_ID = "27t7tx8os50zrun731wfu2vis";

  @Autowired
  CacheManager cacheManager;
  @MockBean
  KafkaService kafkaService;
  @Captor
  ArgumentCaptor<TeamEvent> teamEvent;
  @Autowired
  private OngoingMatchDataCacheService ongoingMatchDataCacheService;

  @Override
  protected void initEnv() {
    Fixture fixture = fixtureCanned().optaFixtureId(OPTA_FIXTURE_ID).isLiveEnabled(true).status(FIXTURE).build();
    insertFixturesIntoDb(List.of(fixture));
    insertCompetitionsIntoDb(List.of(CompetitionCanned.competitionCanned().build()));
    var emptyOngoingMatchData = ongoingMatchDataCanned().build();
    ongoingMatchDataCacheService.set(fixture.getIdAsString(), emptyOngoingMatchData);
  }

  @Test
  public void whenRunningBallCancelCoverage_thenTheFixtureIsUpdatedInCacheAndDb() throws Exception {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/runningball/runningball_match_not_supported.xml", classLoader);
    String runningBallFeedContent = new String(bytes);

    Map<String, List<String>> paramMap = Map.of("rb_data", List.of(runningBallFeedContent));
    MultiValueMap<String, String> paramMultiMap = new LinkedMultiValueMap<>(paramMap);

    var httpRequest = post("/v1/runningball")
      .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
      .params(paramMultiMap);

    // Act
    ResultActions result = this.mvc.perform(httpRequest);

    // Assert
    result.andExpect(status().isOk());

    Awaitility.await().with().atMost(Duration.FIVE_SECONDS).untilAsserted(() -> {
      Fixture fixture = allLeaguesTemplate.findOne(query(Criteria.where("optaFixtureId")
        .is(OPTA_FIXTURE_ID)), Fixture.class);
      assertThat(fixture)
        .isNotNull()
        .satisfies(f -> {
          assertThat(f.getIsLiveEnabled()).isFalse();
          assertThat(f.getIsLiveEnabledBy()).contains(Map.entry(RUNNINGBALL, false));
          assertThat(f.getIsRunningBallConnected()).isTrue();
          assertThat(f.getStatus()).isEqualTo(FIXTURE);
        });

      Cache cache = cacheManager.getCache(CacheConstants.OPTA_FIXTURES_CACHE_NAME);
      Fixture cachedFixture = cache.get(OPTA_FIXTURE_ID, Fixture.class);
      assertThat(cachedFixture)
        .isNotNull()
        .satisfies(f -> {
          assertThat(f.getIsLiveEnabled()).isFalse();
          assertThat(f.getIsLiveEnabledBy()).contains(Map.entry(RUNNINGBALL, false));
          assertThat(f.getIsRunningBallConnected()).isTrue();
          assertThat(f.getStatus()).isEqualTo(FIXTURE);
        });

      verify(kafkaService, times(2)).sendTeamEventRating(teamEvent.capture());
      List<Match> betStopMatches = teamEvent.getAllValues()
        .stream()
        .map(teamEvent -> teamEvent.getTeamBetStop().getMatch())
        .toList();
      assertThat(betStopMatches).as("Matches should be live enabled, so engines do not discard the message.")
        .isNotEmpty()
        .allSatisfy(match -> {
          assertThat(match.getIsLiveEnabled()).isTrue();
        });
    });
  }
}