package com.wsf.dataingestor.integration;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.MediaType;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.services.ratings.MatchDataFeedRequestMapper.MatchStatsDto;
import com.wsf.dataingestor.services.ratings.MatchDataFeedRequestMapper.MatchStatsDto.ContestantStatsDto;
import com.wsf.dataingestor.sports.soccer.index.SoccerIndexCalculator;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.IndexPerformance;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.models.ContestantType.SOCCER_PLAYER;
import static com.wsf.dataingestor.models.ContestantType.SOCCER_TEAM;
import static com.wsf.dataingestor.shared.canned.CompetitionCanned.competitionCanned;
import static com.wsf.dataingestor.shared.canned.CompetitionConfigCanned.competitionConfigCanned;
import static com.wsf.dataingestor.shared.canned.CompetitionConfigCanned.marketCanned;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_3_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_4_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.createPlayer;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.UNINITIALIZED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class RatingControllerPostManualSettlementTest extends AbstractMongoTest {
  @MockBean
  private SoccerIndexCalculator soccerIndexCalculator;

  @MockBean
  private KafkaService kafkaServiceMock;

  @Override
  protected void initEnv() {
    serieATemplate.dropCollection(PLAYERS_COLLECTION);
    allLeaguesTemplate.dropCollection(FIXTURE_COLLECTION);
    serieATemplate.dropCollection(PLAYER_RATINGS_COLLECTION);
    when(soccerIndexCalculator.calculate(any(), anyMap())).thenReturn(IndexPerformance
      .builder()
      .index(6.5).isValid(true).build());
  }

  @Test
  public void whenTryToManuallySettleFixture_thenItShouldCreatePlayerRatingsForEachPlayer() throws Exception {
    Instant fixtureDate = LocalDate.parse("2016-04-17").atTime(18, 0, 0).toInstant(ZoneOffset.UTC);
    Competition competition = competitionCanned().dbName("seriea").build();
    CompetitionConfig competitionConfig = competitionConfigCanned()
      .markets(List.of(marketCanned().propName("yellowCards").build()))
      .build();
    Tournament tournament = tournamentCanned().competition(competition).build();
    Team homeTeam = teamCanned().id(TEAM1_ID).build();
    Team awayTeam = teamCanned().id(TEAM2_ID).build();
    Player player = createPlayer().team(homeTeam).tournament(tournament).build();
    Fixture fixture = fixtureCanned()
      .date(fixtureDate)
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .tournament(tournament)
      .build();

    insertCompetitionsIntoDb(List.of(competition));
    insertCompetitionConfigsIntoDb(List.of(competitionConfig));
    insertPlayersIntoDb(List.of(player));
    insertFixturesIntoDb(List.of(fixture));

    var playerStats = ContestantStatsDto
      .builder()
      .id(player.getIdAsString())
      .type(SOCCER_PLAYER)
      .hasPlayed(true)
      .stats(Map.of("goals", 1, "passes", 4, "yellowCards", 1))
      .build();

    var teamStats = ContestantStatsDto
      .builder()
      .id(homeTeam.getIdAsString())
      .type(SOCCER_TEAM)
      .hasPlayed(true)
      .stats(Map.of("teamShotsOnGoal", 3, "teamShots", 5))
      .build();

    var contestantStats = List.of(playerStats, teamStats);

    MatchStatsDto matchStatsDto = new MatchStatsDto(contestantStats, List.of(), Set.of());

    String url = String.format("/v1/fixtures/%s/manual-settle", fixture.getId());
    var httpRequest = post(url)
      .content(asJsonString(matchStatsDto))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    this.mvc.perform(httpRequest).andExpect(status().isOk()).andReturn();

    var playerRatings = serieATemplate.find(new Query(), PlayerRating.class);
    Fixture dbFixture = allLeaguesTemplate.findById(fixture.getId(), Fixture.class);

    assertThat(playerRatings.size(), is(1));
    assertThat(dbFixture.getProcessStatus(), is(Fixture.FixtureProcessStatus.SETTLED));
  }

  @Test
  public void whenThereAreFourPlayersAndHomeTeamWithStats_thenItShouldCreateRatings_andCallKafka_forAllPlayersAndForHomeTeam() throws Exception {
    Instant fixtureDate = LocalDate.parse("2018-04-17").atTime(18, 0, 0).toInstant(ZoneOffset.UTC);
    Competition competition = competitionCanned().dbName("seriea").build();
    CompetitionConfig competitionConfig = competitionConfigCanned()
      .markets(List.of(marketCanned().propName("shots").build()))
      .build();
    Tournament tournament = tournamentCanned().competition(competition).build();
    Team homeTeam = teamCanned().id(TEAM1_ID).build();
    Team awayTeam = teamCanned().id(TEAM2_ID).build();
    Player player1HomeTeam = createPlayer().id(PLAYER_1_ID).team(homeTeam).tournament(tournament).build();
    Player player2HomeTeam = createPlayer().id(PLAYER_2_ID).team(homeTeam).tournament(tournament).build();
    Player player1AwayTeam = createPlayer().id(PLAYER_3_ID).team(awayTeam).tournament(tournament).build();
    Player player2AwayTeam = createPlayer().id(PLAYER_4_ID).team(awayTeam).tournament(tournament).build();
    Fixture fixture = fixtureCanned()
      .date(fixtureDate)
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .tournament(tournament)
      .build();
    insertCompetitionsIntoDb(List.of(competition));
    insertCompetitionConfigsIntoDb(List.of(competitionConfig));
    insertPlayersIntoDb(List.of(player1HomeTeam, player2HomeTeam, player1AwayTeam, player2AwayTeam));
    insertFixturesIntoDb(List.of(fixture));

    var player1HomeTeamStats = ContestantStatsDto
      .builder()
      .id(player1HomeTeam.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("goals", 1, "shots", 2, "firstGoalscorer", 1))
      .build();
    var player2HomeTeamStats = ContestantStatsDto
      .builder()
      .id(player2HomeTeam.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("goals", 0, "passes", 20))
      .build();
    var player1AwayTeamStats = ContestantStatsDto
      .builder()
      .id(player1AwayTeam.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("passes", 15, "assists", 1))
      .build();
    var player2AwayTeamStats = ContestantStatsDto
      .builder()
      .id(player2AwayTeam.getIdAsString())
      .type(SOCCER_PLAYER)
      .hasPlayed(true)
      .stats(Map.of("goals", 3, "passes", 5))
      .build();

    var teamStats = ContestantStatsDto
      .builder()
      .id(homeTeam.getIdAsString())
      .type(SOCCER_TEAM)
      .hasPlayed(true)
      .stats(Map.of("teamShotsOnGoal", 3, "teamShots", 5))
      .build();

    var playersStats = List.of(player1HomeTeamStats, player2HomeTeamStats, player1AwayTeamStats, player2AwayTeamStats,
      teamStats);
    var matchStats = new MatchStatsDto(playersStats, List.of(), Set.of());

    String url = String.format("/v1/fixtures/%s/manual-settle", fixture.getId());
    var httpRequest = post(url)
      .content(asJsonString(matchStats))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    this.mvc.perform(httpRequest).andExpect(status().isOk()).andReturn();

    var playerRatings = serieATemplate.find(new Query(), PlayerRating.class);
    assertThat(playerRatings.size(), is(4));

    var teamRatings = serieATemplate.find(new Query(), TeamRating.class);
    assertThat(teamRatings.size(), is(1));

    var result = captureKafkaRatings(4, 2);
    Map<String, Number> homeTeamStats = result.getRight().get(0).getStats();
    assertThat(homeTeamStats.size(), is(2));
    assertThat(homeTeamStats.get("teamShotsOnGoal"), is(3));

    Map<String, Number> awayTeamStats = result.getRight().get(1).getStats();
    assertThat(awayTeamStats.size(), is(0));
  }

  @Test
  public void whenTwoPlayersAreReceived_andOnePlayerHasTheFlagIsPlayedToFalse_thenItShouldNotSaveAnyRatings_butItShouldPublishRatingsForBothPlayers() throws Exception {
    Instant fixtureDate = LocalDate.parse("2016-04-17").atTime(18, 0, 0).toInstant(ZoneOffset.UTC);
    Competition competition = competitionCanned().dbName("seriea").build();
    CompetitionConfig competitionConfig = competitionConfigCanned()
      .markets(List.of(marketCanned().propName("yellowCards").build()))
      .build();
    Tournament tournament = tournamentCanned().competition(competition).build();
    Team homeTeam = teamCanned().id(TEAM1_ID).build();
    Team awayTeam = teamCanned().id(TEAM2_ID).build();
    Player player1 = playerCanned().id(PLAYER_1_ID).team(homeTeam).tournament(tournament).build();
    Player player2 = playerCanned().id(PLAYER_2_ID).team(homeTeam).tournament(tournament).build();
    Fixture fixture = fixtureCanned()
      .date(fixtureDate)
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .tournament(tournament)
      .build();

    insertCompetitionsIntoDb(List.of(competition));
    insertCompetitionConfigsIntoDb(List.of(competitionConfig));
    insertPlayersIntoDb(List.of(player1, player2));
    insertFixturesIntoDb(List.of(fixture));

    var player1Stats = ContestantStatsDto
      .builder().id(player1.getIdAsString()).hasPlayed(false).type(SOCCER_PLAYER).stats(Map.of()).build();

    var player2Stats = ContestantStatsDto
      .builder()
      .id(player2.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("goals", 4, "passes", 10, "yellowCards", 1))
      .build();

    var teamStats = ContestantStatsDto
      .builder()
      .id(homeTeam.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_TEAM)
      .stats(Map.of("teamShotsOnGoal", 3, "teamShots", 5))
      .build();

    List<ContestantStatsDto> contestantStats = List.of(player1Stats, player2Stats, teamStats);

    var matchStats = new MatchStatsDto(contestantStats, List.of(), Set.of());

    String url = String.format("/v1/fixtures/%s/manual-settle", fixture.getId());
    var httpRequest = post(url)
      .content(asJsonString(matchStats))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    this.mvc.perform(httpRequest).andExpect(status().isOk()).andReturn();

    var playerRatings = serieATemplate.find(new Query(), PlayerRating.class);

    captureKafkaRatings(2, 2);
    assertThat(playerRatings.size(), is(1));
  }

  @Test
  public void whenPlayerHasNegativeStatsValues_thenItShouldDoNothing() throws Exception {
    Instant fixtureDate = LocalDate.parse("2016-04-17").atTime(18, 0, 0).toInstant(ZoneOffset.UTC);
    Competition competition = competitionCanned().dbName("seriea").build();
    Tournament tournament = tournamentCanned().competition(competition).build();
    Team homeTeam = teamCanned().build();
    Team awayTeam = teamCanned().build();
    Player player1 = createPlayer().team(homeTeam).tournament(tournament).build();
    Player player2 = createPlayer().team(awayTeam).tournament(tournament).build();
    Fixture fixture = fixtureCanned()
      .date(fixtureDate)
      .homeTeam(homeTeam)
      .awayTeam(awayTeam)
      .tournament(tournament)
      .build();

    insertCompetitionsIntoDb(List.of(competition));
    insertPlayersIntoDb(List.of(player1, player2));
    insertFixturesIntoDb(List.of(fixture));

    var player1Stats = ContestantStatsDto
      .builder()
      .id(player1.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("goals", 2, "passes", 30))
      .build();
    var player2Stats = ContestantStatsDto
      .builder()
      .id(player2.getIdAsString())
      .hasPlayed(true)
      .type(SOCCER_PLAYER)
      .stats(Map.of("goals", -3, "passes", 10))
      .build();

    List<ContestantStatsDto> playersStats = List.of(player1Stats, player2Stats);
    var matchStats = new MatchStatsDto(playersStats, List.of(), Set.of());

    String url = String.format("/v1/fixtures/%s/manual-settle", fixture.getId());
    var httpRequest = post(url)
      .content(asJsonString(matchStats))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    this.mvc.perform(httpRequest).andExpect(status().is5xxServerError()).andReturn();

    var playerRatings = serieATemplate.find(new Query(), PlayerRating.class);
    var dbFixture = allLeaguesTemplate.findById(fixture.getId(), Fixture.class);

    captureKafkaRatings(0);
    assertThat(playerRatings.size(), is(0));
    assertThat(dbFixture.getProcessStatus(), is(nullValue()));
  }

  private Pair<List<PlayerRating>, List<TeamRating>> captureKafkaRatings(int numberOfCalls) {
    return captureKafkaRatings(numberOfCalls, 0);
  }

  private Pair<List<PlayerRating>, List<TeamRating>> captureKafkaRatings(int playerRatings, int teamRatings) {
    var playerRatingArgumentCaptor = ArgumentCaptor.forClass(PlayerRating.class);
    verify(kafkaServiceMock, times(playerRatings)).sendFinalPlayerRating(playerRatingArgumentCaptor.capture(),
      eq(Fixture.FixtureStatus.PLAYED), eq(true), eq(UNINITIALIZED));

    var teamRatingArgumentCaptor = ArgumentCaptor.forClass(TeamRating.class);
    verify(kafkaServiceMock, times(teamRatings)).sendFinalTeamRating(teamRatingArgumentCaptor.capture(),
      eq(Fixture.FixtureStatus.PLAYED), eq(true), eq(UNINITIALIZED));

    return Pair.of(playerRatingArgumentCaptor.getAllValues(), teamRatingArgumentCaptor.getAllValues());
  }

}
