package com.wsf.dataingestor.integration;

import java.util.List;
import org.mockito.ArgumentCaptor;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import com.wsf.dataingestor.crons.FixtureStartUpdater;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.kafka.domain.ContestantUnavailabilityKafka;
import com.wsf.kafka.domain.FixtureSummaryData;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.data.mongodb.core.query.Query.query;

public class EntitiesMockUtils {

  static final JobKey FIXTURE_RATINGS_UPDATER_JOB_KEY = new JobKey("FixtureRatingsUpdaterTest");

  static final JobKey PRE_MATCH_JOB_KEY = new JobKey(FixtureCanned.DEFAULT_ID.toString(),
    FixtureStartUpdater.FIXTURE_START_JOB_GROUP);


  static void mockCompetition(MongoTemplate template, Competition competition) {
    template.findAndReplace(query(Criteria.where("_id")
      .is(competition.getId())), competition, FindAndReplaceOptions.options().upsert());
  }

  static void mockFixture(MongoTemplate template, Fixture fixture) {
    template.findAndReplace(query(Criteria.where("_id")
      .is(fixture.getId())), fixture, FindAndReplaceOptions.options().upsert());
  }

  static void mockPlayer(MongoTemplate template, Player player) {
    template.findAndReplace(query(Criteria.where("_id")
      .is(player.getId())), player, FindAndReplaceOptions.options().upsert());
  }

  static void mockTeam(MongoTemplate template, Team team) {
    template.findAndReplace(query(Criteria.where("_id")
      .is(team.getId())), team, FindAndReplaceOptions.options().upsert());
  }

  static void mockCompetitionsConfig(MongoTemplate template, CompetitionConfig competitionConfig) {
    template.findAndReplace(query(Criteria.where("competition._id")
      .is(competitionConfig.getCompetition().getId())), competitionConfig, FindAndReplaceOptions.options().upsert());
  }

  static JobExecutionContext mockJobExecutionContext() {
    JobExecutionContext executionContext = mock(JobExecutionContext.class);
    JobDetail jobDetail = mock(JobDetail.class);
    JobDataMap jobDataMap = mock(JobDataMap.class);
    when(jobDetail.getKey()).thenReturn(FIXTURE_RATINGS_UPDATER_JOB_KEY);
    when(jobDataMap.getString("fixtureId")).thenReturn(FixtureCanned.DEFAULT_ID.toString());
    when(executionContext.getJobDetail()).thenReturn(jobDetail);
    when(executionContext.getMergedJobDataMap()).thenReturn(jobDataMap);
    return executionContext;
  }

  static void verifySettlementJobIsTriggered(Scheduler scheduler) throws SchedulerException {
    verifyJobsAreStopped(scheduler);
    verify(scheduler).scheduleJob(any(), any());
  }

  static void verifyJobsAreStopped(Scheduler scheduler) throws SchedulerException {
    verify(scheduler).deleteJob(FIXTURE_RATINGS_UPDATER_JOB_KEY);
    verify(scheduler).deleteJob(PRE_MATCH_JOB_KEY);
  }

  public static FixtureSummaryData readFixtureSummaryDataFromKafka(KafkaService kafkaService) {
    ArgumentCaptor<FixtureSummaryData> fixtureSummaryDataArgumentCaptorCaptor = ArgumentCaptor.forClass(
      FixtureSummaryData.class);

    introduceLatency();

    verify(kafkaService).sendFinalFixtureSummary(fixtureSummaryDataArgumentCaptorCaptor.capture());

    List<FixtureSummaryData> fixtureSummaryDataList = fixtureSummaryDataArgumentCaptorCaptor.getAllValues();
    assertEquals(1, fixtureSummaryDataList.size());

    return fixtureSummaryDataList.get(0);
  }

  public static ContestantUnavailabilityKafka readContestantUnavailabilitiesFromKafka(KafkaService kafkaService) {
    ArgumentCaptor<ContestantUnavailabilityKafka> contestantUnavailabilityKafkaArgumentCaptor = ArgumentCaptor.forClass(
      ContestantUnavailabilityKafka.class);

    introduceLatency();

    verify(kafkaService).sendContestantUnavailabilities(contestantUnavailabilityKafkaArgumentCaptor.capture());

    List<ContestantUnavailabilityKafka> contestantUnavailabilityKafkaList = contestantUnavailabilityKafkaArgumentCaptor.getAllValues();
    assertEquals(1, contestantUnavailabilityKafkaList.size());

    return contestantUnavailabilityKafkaList.get(0);
  }

  public static void introduceLatency() {
    try {
      Thread.sleep(3000);
      //      Awaitility.waitAtMost(2, TimeUnit.SECONDS); //..... until the mock has ben resetted? db is written?
    } catch (Exception e) {
      //SHH
    }
  }
}
