package com.wsf.dataingestor.integration;

import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.web.model.MatchStateDto;
import com.wsf.domain.common.Fixture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class FixtureControllerTest extends AbstractMongoTest {

  @Override
  protected void initEnv() {
    allLeaguesTemplate.dropCollection("fixtures");
  }

  @Test
  public void whenAFixtureIsNotLive_thenAnEmptyObjectIsExpected() throws Exception {
    // Arrange
    String fixtureId = FixtureCanned.DEFAULT_ID.toString();
    MatchStateDto.MatchDataDto emptyState = new MatchStateDto.MatchDataDto(0, 1, 0, 0, 0, 0);
    Fixture fixture = FixtureCanned.defaultFixtureBuilder().status(Fixture.FixtureStatus.FIXTURE).build();
    allLeaguesTemplate.save(fixture);

    var httpRequest = get("/v1/fixtures/%s/match-state".formatted(fixtureId))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);

    // Act
    ResultActions result = this.mvc.perform(httpRequest);

    //Assert
    result.andExpect(status().isOk()).andExpect(r -> {
      var response = objectMapper.readValue(r.getResponse().getContentAsString(), MatchStateDto.class);
      assertThat(response.data()).isEqualTo(emptyState);
      assertThat(response.status()).isEqualTo(fixture.getStatus());
    });
  }

  @Test
  public void whenAFixtureDoesNotExists_then404IsExpected() throws Exception {
    // Arrange
    String fixtureId = FixtureCanned.DEFAULT_ID.toString();

    var httpRequest = get("/v1/fixtures/%s/match-state".formatted(fixtureId))
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);

    // Act
    ResultActions result = this.mvc.perform(httpRequest);

    // Assert
    result.andExpect(status().isGone());
  }

}
