package com.wsf.dataingestor.integration;

import java.util.List;
import java.util.Optional;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.shared.canned.TournamentCanned;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.Tournament;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class SportmonksFeedParserUtilsIntegrationTest extends AbstractMongoTest {
  private static Team team;
  private static Tournament tournament;
  private static Competition competition;

  @Autowired
  protected SportmonksFeedParserUtils sportmonksFeedParserUtils;

  @Override
  protected void initEnv() {}

  @BeforeClass
  public static void insertDataInDb() {
    team = TeamCanned.teamCanned().sportmonksId("100").build();
    tournament = TournamentCanned.tournamentCanned().build();
    competition = tournament.getCompetition();

    insertTeamsIntoDb(List.of(team));
    insertCompetitionsIntoDb(List.of(competition));
  }

  @Test
  public void whenPlayerSmIdMatches_thenWeFindHim() {
    Fixture fixture = FixtureCanned.fixtureCanned().tournament(tournament).build();
    //@formatter:off
    Player player = PlayerCanned.createPlayer()
      .sportmonksPlayerId("smId")
      .team(team)
      .tournament(tournament)
      .build();
    //@formatter:on
    insertPlayersIntoDb(List.of(player));

    Player playerReturned = sportmonksFeedParserUtils.getPlayerBySmPlayerId("smId", fixture);

    assertEquals(player.getId(), playerReturned.getId());
  }

  @Test
  public void whenPlayerFullNameAndSmTeamIdMatches_thenWeFindHim() {
    //@formatter:off
    Player player = PlayerCanned.createPlayer()
      .sportmonksPlayerId("smId")
      .firstName("Filippo")
      .name("Melegoni")
      .matchName("F. Melegoni")
      .team(team)
      .tournament(tournament)
      .build();
    //@formatter:on
    insertPlayersIntoDb(List.of(player));

    Optional<Player> playerReturned = sportmonksFeedParserUtils.getPlayerBySportMonksPlayerNameAndTeamId(
      competition.getIdAsString(), team.getSportmonksId(), "Filippo Melegoni");

    assertTrue(playerReturned.isPresent());
    assertEquals(player.getId(), playerReturned.get().getId());
  }

  @Test
  public void whenPlayerMatchNameAndSmTeamIdMatches_thenWeFindHim() {
    //@formatter:off
    Player player = PlayerCanned.createPlayer()
      .sportmonksPlayerId("smId")
      .firstName("Paulo")
      .name("Dybala")
      .matchName("P. Dybala")
      .team(team)
      .tournament(tournament)
      .build();
    //@formatter:on
    insertPlayersIntoDb(List.of(player));

    Optional<Player> playerReturned = sportmonksFeedParserUtils.getPlayerBySportMonksPlayerNameAndTeamId(
      competition.getIdAsString(), team.getSportmonksId(), "P. Dybala");

    assertTrue(playerReturned.isPresent());
    assertEquals(player.getId(), playerReturned.get().getId());
  }
}
