package com.wsf.dataingestor.integration;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.bson.types.ObjectId;
import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.wsf.dataingestor.crons.FixtureRatingsUpdater;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.http.MA2HttpClient;
import com.wsf.dataingestor.opta.clients.http.MA3HttpClient;
import com.wsf.dataingestor.services.FixtureService;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Competition;
import com.wsf.domain.common.CompetitionConfig;
import com.wsf.domain.common.CompetitionConfig.FeedsConfig;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Fixture.FixtureStatus;
import com.wsf.domain.common.LivePlayerRating;
import com.wsf.domain.common.LiveTeamRating;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.PlayerRating;
import com.wsf.domain.common.Team;
import com.wsf.domain.common.TeamRating;
import com.wsf.kafka.domain.FixtureSummaryData;

import static com.wsf.dataingestor.integration.EntitiesMockUtils.mockJobExecutionContext;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.readFixtureSummaryDataFromKafka;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.verifyJobsAreStopped;
import static com.wsf.dataingestor.integration.EntitiesMockUtils.verifySettlementJobIsTriggered;
import static com.wsf.dataingestor.shared.canned.CompetitionMarketsConfigCanned.createCompetitionMarketConfig;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID_STR;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_3_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_4_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_5_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_6_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_7_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_8_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOALS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_GOAL_KICKS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_RED_CARDS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_TACKLES_WON_REGULAR_TIMES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS_REGULAR_TIMES;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.CONNECTED;
import static com.wsf.domain.common.Fixture.FixtureProcessStatus.SETTLED;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.COMPLETED_PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static com.wsf.kafka.domain.FixtureStatus.PLAYED;
import static com.wsf.kafka.domain.metadata.KafkaMetadata.EMPTY_METADATA;
import static java.lang.String.format;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class OptaSettlementIntegrationTest extends AbstractMongoTest {

  @Autowired
  private FixtureRatingsUpdater fixtureRatingsUpdater;
  @Autowired
  private FixtureService fixtureService;
  @Autowired
  private MetricsManager metricsManager;
  @SpyBean
  private Scheduler scheduler;

  @MockBean
  private MA2HttpClient ma2HttpClient;
  @MockBean
  private MA3HttpClient ma3HttpClient;
  @MockBean
  private KafkaService kafkaServiceMock;

  public void initEnv() {
    mockCompetition(allLeaguesTemplate);
    mockCompetitionsConfig(allLeaguesTemplate);
    jobExecutionContext = mockJobExecutionContext();
  }

  @After
  public void cleanup() {
    serieATemplate.dropCollection(Player.class);
    serieATemplate.dropCollection(Team.class);
    serieATemplate.dropCollection(LivePlayerRating.class);
    serieATemplate.dropCollection(LiveTeamRating.class);
    serieATemplate.dropCollection(PlayerRating.class);
    serieATemplate.dropCollection(TeamRating.class);
  }

  @Test
  public void whenAFixtureIsProcessed_andItIsOver_thenTheSettlementEventsAreSent() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockPsgStrPlayers(serieATemplate);
    mockPsgStrTeams(serieATemplate);
    mockFixture("bukekpxmpmbs5qmcsrmgx2b6c", "2b3mar72yy8d6uvat1ka6tn3r", "71ajatxnuhc5cnm3xvgdky49w");
    mockStatsFeed("ma2_psg_stra");
    mockEventsFeed("ma3_psg_stra");

    int expectedMatchTotalShots = 19;
    int expectedMatchTotalShotsOnGoal = 7;
    int expectedMatchTotalCorners = 8;
    int expectedMatchTotalOffsides = 3;
    int expectedMatchTotalFouls = 24;
    int expectedMatchTotalYellowCards = 4;
    int expectedMatchTotalTacklesWon = 26;

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifySettlementJobIsTriggered(scheduler);
    await()
      .with()
      .atMost(10, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(SETTLED));

    //@formatter:off
    var homeStats = new HashMap<String, Number>();
    homeStats.put(TEAM_GOALS, 4);
    homeStats.put(TEAM_FOULS, 6);
    homeStats.put(TEAM_FOULS_REGULAR_TIMES, 6);
    homeStats.put(TEAM_SHOTS, 11);
    homeStats.put(TEAM_SHOTS_REGULAR_TIMES, 11);
    homeStats.put(TEAM_GOAL_KICKS, 8);
    homeStats.put(TEAM_OFFSIDES, 1);
    homeStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 1);
    homeStats.put(TEAM_CORNERS, 4);
    homeStats.put(TEAM_SHOTS_ON_GOAL, 5);
    homeStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 5);
    homeStats.put(TEAM_YELLOW_CARDS, 1);
    homeStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 1);
    homeStats.put(TEAM_RED_CARDS, 0);
    homeStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 0);
    homeStats.put(TEAM_TACKLES_WON, 10);
    homeStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 10);
    var awayStats = new HashMap<String, Number>();
    awayStats.put(TEAM_GOALS, 2);
    awayStats.put(TEAM_FOULS, 18);
    awayStats.put(TEAM_FOULS_REGULAR_TIMES, 18);
    awayStats.put(TEAM_SHOTS, 8);
    awayStats.put(TEAM_SHOTS_REGULAR_TIMES, 8);
    awayStats.put(TEAM_GOAL_KICKS, 4);
    awayStats.put(TEAM_OFFSIDES, 2);
    awayStats.put(TEAM_OFFSIDES_REGULAR_TIMES, 2);
    awayStats.put(TEAM_CORNERS, 4);
    awayStats.put(TEAM_SHOTS_ON_GOAL, 2);
    awayStats.put(TEAM_SHOTS_ON_GOAL_REGULAR_TIMES, 2);
    awayStats.put(TEAM_YELLOW_CARDS, 3);
    awayStats.put(TEAM_YELLOW_CARDS_REGULAR_TIMES, 3);
    awayStats.put(TEAM_RED_CARDS, 1);
    awayStats.put(TEAM_RED_CARDS_REGULAR_TIMES, 1);
    awayStats.put(TEAM_TACKLES_WON, 16);
    awayStats.put(TEAM_TACKLES_WON_REGULAR_TIMES, 16);
    Map<String, Map<String, Number>> expectedLiveRatings = Map.of(TEAM1_ID.toString(), homeStats, TEAM2_ID.toString(), awayStats);
    //@formatter:on

    // verifying what's in the db
    verifyLivePlayerRatingsNotEmptyForPlayer(PLAYER_1_ID_STR);
    verifyLivePlayerRatingsNotEmptyForPlayer(PLAYER_2_ID_STR);
    verifyLiveTeamRatingsNotEmptyForTeam(TEAM1_ID.toString(), expectedLiveRatings.get(TEAM1_ID.toString()));
    verifyLiveTeamRatingsNotEmptyForTeam(TEAM2_ID.toString(), expectedLiveRatings.get(TEAM2_ID.toString()));

    // verifying what's been sent to kafka
    verifyFinishedMatchRatingEvents();

    FixtureSummaryData fixtureSummaryData = readFixtureSummaryDataFromKafka(kafkaServiceMock);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, SHOT.getStatisticName(), expectedMatchTotalShots);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, SHOT_ON_GOAL.getStatisticName(),
      expectedMatchTotalShotsOnGoal);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, CORNER.getStatisticName(), expectedMatchTotalCorners);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, OFFSIDE.getStatisticName(), expectedMatchTotalOffsides);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, FOUL.getStatisticName(), expectedMatchTotalFouls);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, YELLOW_CARD.getStatisticName(),
      expectedMatchTotalYellowCards);
    checkCurrentValueOnFixtureSummaryData(fixtureSummaryData, TACKLE_WON.getStatisticName(),
      expectedMatchTotalTacklesWon);
    assertEquals(PLAYED, fixtureSummaryData.getFixtureStatus());
  }

  @Test
  public void whenAFixtureIsProcessed_andItIsPostponed_thenFixtureIsNotSettled() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockPsgStrPlayers(serieATemplate);
    mockPsgStrTeams(serieATemplate);
    mockFixture("arjpqu92xd4tcn13abppo7ims", "2b3mar72yy8d6uvat1ka6tn3r", "71ajatxnuhc5cnm3xvgdky49w");
    mockStatsFeed("ma2_postponed");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifyJobsAreStopped(scheduler);
    await()
      .with()
      .atMost(10, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(null));

    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_1_ID_STR);
    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_2_ID_STR);
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM1_ID.toString());
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM2_ID.toString());
  }

  @Test
  public void whenAFixtureIsSuspended_thenFixtureEventsAreNotSent() throws IOException, SchedulerException {
    Mockito.reset(scheduler);

    mockPsgStrPlayers(serieATemplate);
    mockPsgStrTeams(serieATemplate);
    mockFixture("bukekpxmpmbs5qmcsrmgx2b6c", "2b3mar72yy8d6uvat1ka6tn3r", "71ajatxnuhc5cnm3xvgdky49w");
    mockStatsFeed("ma2_suspended");
    mockEventsFeed("ma3_psg_stra");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    verifyJobsAreStopped(scheduler);
    await()
      .with()
      .atMost(10, TimeUnit.SECONDS)
      .until(() -> fixtureService.getFixture(FixtureCanned.DEFAULT_ID.toString()).getProcessStatus(), equalTo(null));

    // verifying what's in the db
    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_1_ID_STR);
    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_2_ID_STR);
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM1_ID.toString());
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM2_ID.toString());
  }

  @Test
  public void whenStatsForAllPlayersAre0_thenSettlementIsNotProcessed() throws IOException {
    Mockito.reset(scheduler);

    mockSamSivPlayers(serieATemplate);
    mockSamSivTeams(serieATemplate);
    mockFixture("25uc7c4deb7do7uc2u26f88wk", "dpsnqu7pd2b0shfzjyn5j1znf", "f432akygffyamal3h6poig65t");
    mockStatsFeed("ma2_empty_stats");
    mockEventsFeed("ma3_empty_stats");

    fixtureRatingsUpdater.execute(jobExecutionContext);

    await()
      .with()
      .atMost(10, TimeUnit.SECONDS)
      .untilAsserted(() -> assertThat(metricsManager.SETTLEMENT_UPDATE_ERROR.count(), is(1.0)));

    // verifying what's in the db
    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_1_ID_STR);
    verifyLivePlayerRatingsAreEmptyForPlayer(PLAYER_2_ID_STR);
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM1_ID.toString());
    verifyLiveTeamRatingsAreEmptyForTeam(TEAM2_ID.toString());
  }

  private static void checkCurrentValueOnFixtureSummaryData(FixtureSummaryData fixtureSummaryData, String marketName,
                                                            int expectedMarketCurrentValue) {
    List<FixtureSummaryData.Event> marketEvents = fixtureSummaryData.getEvents()
      .stream()
      .filter(event -> event.getEventType().equals(marketName))
      .toList();

    assertEquals(expectedMarketCurrentValue, marketEvents.size());
  }

  private static void verifyLivePlayerRatingsNotEmptyForPlayer(String playerId) {
    List<LivePlayerRating> livePlayerRatings = serieATemplate.find(Query.query(Criteria.where("playerId")
      .is(playerId)), LivePlayerRating.class);

    assert !livePlayerRatings.isEmpty();

    livePlayerRatings.forEach(livePlayerRating -> {
      assert livePlayerRating.getEventId() != null;
    });
  }

  private static void verifyLivePlayerRatingsAreEmptyForPlayer(String playerId) {
    List<LivePlayerRating> livePlayerRatings = serieATemplate.find(Query.query(Criteria.where("playerId")
      .is(playerId)), LivePlayerRating.class);

    assert livePlayerRatings.isEmpty();
  }

  private static void verifyLiveTeamRatingsNotEmptyForTeam(String teamId, Map<String, Number> expectedTeamLiveRating) {
    List<LiveTeamRating> liveTeamRatings = serieATemplate.find(Query.query(Criteria.where("teamId")
      .is(teamId)), LiveTeamRating.class);

    assert !liveTeamRatings.isEmpty();
    assert liveTeamRatings.size() == 1;
    LiveTeamRating liveTeamRating = liveTeamRatings.get(0);
    assert liveTeamRating.getEventId() != null;
    Map<String, Number> liveTeamRatingStats = liveTeamRating.getStats();
    liveTeamRatingStats.keySet()
      .forEach(
        stat -> assertEquals(format("stat %s should match expectation %s", stat, expectedTeamLiveRating.get(stat)),
          expectedTeamLiveRating.get(stat), liveTeamRatingStats.get(stat)));
  }

  private static void verifyLiveTeamRatingsAreEmptyForTeam(String teamId) {
    List<LiveTeamRating> liveTeamRatings = serieATemplate.find(Query.query(Criteria.where("teamId")
      .is(teamId)), LiveTeamRating.class);

    assert liveTeamRatings.isEmpty();
  }

  private Map<String, PlayerRating> captureKafkaPlayerRatings() {
    ArgumentCaptor<PlayerRating> ratingArgumentCaptor = ArgumentCaptor.forClass(PlayerRating.class);
    verify(kafkaServiceMock, times(7)).sendFinalPlayerRating(ratingArgumentCaptor.capture(), eq(FixtureStatus.PLAYED),
      eq(true), eq(EMPTY_METADATA));

    return ratingArgumentCaptor.getAllValues()
      .stream()
      .collect(toMap(p -> p.getPlayer().getIdAsString(), identity()));
  }

  private Map<String, TeamRating> captureKafkaTeamRatings() {
    ArgumentCaptor<TeamRating> ratingArgumentCaptor = ArgumentCaptor.forClass(TeamRating.class);
    verify(kafkaServiceMock, times(2)).sendFinalTeamRating(ratingArgumentCaptor.capture(), eq(FixtureStatus.PLAYED),
      eq(true), eq(EMPTY_METADATA));

    return ratingArgumentCaptor.getAllValues()
      .stream()
      .collect(toMap(t -> t.getTeam().getIdAsString(), identity()));
  }

  private void verifyFinishedMatchRatingEvents() {
    Map<String, PlayerRating> playerRatingsById = captureKafkaPlayerRatings();
    Map<String, TeamRating> teamRatingsById = captureKafkaTeamRatings();

    verifyDialloRatings(playerRatingsById);
    verifyIcardiRatings(playerRatingsById);
    verifyPSGRatings(teamRatingsById);
    verifyStrasbourgRatings(teamRatingsById);
    Assertions
      .assertThat(playerRatingsById.keySet())
      .doesNotContain(PLAYER_8_ID.toString());
  }

  private static void verifyPSGRatings(Map<String, TeamRating> ratingsById) {
    TeamRating ratings = ratingsById.get(TEAM1_ID.toString());
    assertThat(ratings.getStats().get(TEAM_OFFSIDES), is(1));
    assertThat(ratings.getStats().get(TEAM_GOALS), is(4));
  }

  private static void verifyStrasbourgRatings(Map<String, TeamRating> ratingsById) {
    TeamRating ratings = ratingsById.get(TEAM2_ID.toString());
    assertThat(ratings.getStats().get(TEAM_OFFSIDES), is(2));
    assertThat(ratings.getStats().get(TEAM_GOALS), is(2));
  }

  private static void verifyDialloRatings(Map<String, PlayerRating> ratingsById) {
    PlayerRating dialloRatings = ratingsById.get(PLAYER_1_ID_STR);
    assertThat(dialloRatings.getStats().get(GOAL.getStatisticName()), is(0));
    assertThat(dialloRatings.getStats().get(ASSIST_GOAL.getStatisticName()), is(1));
    assertThat(dialloRatings.getStats().get(SHOT.getStatisticName()), is(0));
    assertThat(dialloRatings.getStats().get(SHOT_ON_GOAL.getStatisticName()), is(0));
    assertThat(dialloRatings.getStats().get(FOUL.getStatisticName()), is(0));
    assertThat(dialloRatings.getStats().get(YELLOW_CARD.getStatisticName()), is(0));
    assertThat(dialloRatings.getStats().get(PASS.getStatisticName()), is(71));
    assertThat(dialloRatings.getStats().get(COMPLETED_PASS.getStatisticName()), is(63));
  }

  private static void verifyIcardiRatings(Map<String, PlayerRating> ratingsById) {
    PlayerRating icardiRatings = ratingsById.get(PLAYER_2_ID_STR);
    assertThat(icardiRatings.getStats().get(GOAL.getStatisticName()), is(1));
    assertThat(icardiRatings.getStats().get(ASSIST_GOAL.getStatisticName()), is(0));
    assertThat(icardiRatings.getStats().get(SHOT.getStatisticName()), is(3));
    assertThat(icardiRatings.getStats().get(SHOT_ON_GOAL.getStatisticName()), is(1));
    assertThat(icardiRatings.getStats().get(FOUL.getStatisticName()), is(1));
    assertThat(icardiRatings.getStats().get(YELLOW_CARD.getStatisticName()), is(0));
    assertThat(icardiRatings.getStats().get(PASS.getStatisticName()), is(13));
    assertThat(icardiRatings.getStats().get(COMPLETED_PASS.getStatisticName()), is(10));
  }

  private void mockStatsFeed(String file) throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] result = IOUtils.resourceToByteArray(format("feeds/opta/%s.json", file), classLoader);
    when(ma2HttpClient.getFeed(anyString())).thenReturn(result);
  }

  private void mockEventsFeed(String file) throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] result = IOUtils.resourceToByteArray(format("feeds/opta/%s.json", file), classLoader);
    when(ma3HttpClient.getFeed(anyString())).thenReturn(result);
  }

  private static void mockCompetition(MongoTemplate template) {
    Competition competition = CompetitionCanned.competitionCanned().build();
    EntitiesMockUtils.mockCompetition(template, competition);
  }

  private static void mockFixture(String optaFixtureId, String optaHomeTeamId, String optaAwayTeamId) {
    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .id(FixtureCanned.DEFAULT_ID)
      .homeTeam(teamCanned().id(TEAM1_ID).optaId(optaHomeTeamId).build())
      .awayTeam(teamCanned().id(TEAM2_ID).optaId(optaAwayTeamId).build())
      .optaFixtureId(optaFixtureId)
      .status(FixtureStatus.FIXTURE)
      .processStatus(CONNECTED)
      .build();
    EntitiesMockUtils.mockFixture(allLeaguesTemplate, fixture);
  }

  private static void mockPsgStrPlayers(MongoTemplate template) {
    mockActivePlayer(template, PLAYER_1_ID, "dyigvzjpa74cw4urhh7fwzs2d", TEAM1_ID); // A. Diallo
    mockActivePlayer(template, PLAYER_2_ID, "drkx7msmzruq5myxk9ani6z11", TEAM1_ID); // Icardi
    mockActivePlayer(template, PLAYER_3_ID, "9ysk6656hj0na1hrizgt83k5x", TEAM2_ID); // Lienard
    mockActivePlayer(template, PLAYER_4_ID, "f5lfo1hz7u5bk494w53arcacq", TEAM1_ID); // Ebimbe
    mockActivePlayer(template, PLAYER_5_ID, "4ju2soyxqa6hu0oo007ej8wdl", TEAM2_ID); // Fila
    mockActivePlayer(template, PLAYER_6_ID, "d5at3cowukpxiptq5ozcbe6s5", TEAM2_ID); // Djiku
    mockActivePlayer(template, PLAYER_7_ID, "wg9qybhs3qeq9awhux331uqx", TEAM2_ID); // Bellegarde
    mockInactivePlayer(template, PLAYER_8_ID, "wg9qybhs3qeq9awhux331uqz", TEAM2_ID); // Bellegarde
  }

  private static void mockSamSivPlayers(MongoTemplate template) {
    mockActivePlayer(template, PLAYER_1_ID, "dlgueejo95xoqfugtpgc3i2nd", TEAM1_ID); // Justesen
    mockActivePlayer(template, PLAYER_2_ID, "dzv5l0y2ad05k7fiwzah07mcp", TEAM2_ID); // Bekiroglu
  }

  private static void mockActivePlayer(MongoTemplate template, ObjectId playerId, String optaPlayerId,
                                       ObjectId teamId) {
    mockPlayer(template, playerId, optaPlayerId, teamId, true);
  }

  private static void mockInactivePlayer(MongoTemplate template, ObjectId playerId, String optaPlayerId,
                                         ObjectId teamId) {
    mockPlayer(template, playerId, optaPlayerId, teamId, false);
  }

  private static void mockPlayer(MongoTemplate template, ObjectId playerId, String optaPlayerId, ObjectId teamId,
                                 boolean isActive) {
    Player player = PlayerCanned
      .playerCanned()
      .id(playerId)
      .optaPlayerId(optaPlayerId)
      .team(TeamCanned.defaultTeam(teamId))
      .isActive(isActive)
      .build();
    EntitiesMockUtils.mockPlayer(template, player);
  }

  private static void mockPsgStrTeams(MongoTemplate template) {
    mockTeam(template, TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r");
    mockTeam(template, TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w");
  }

  private static void mockSamSivTeams(MongoTemplate template) {
    mockTeam(template, TEAM1_ID, "dpsnqu7pd2b0shfzjyn5j1znf");
    mockTeam(template, TEAM2_ID, "f432akygffyamal3h6poig65t");
  }

  private static void mockTeam(MongoTemplate template, ObjectId teamId, String optaTeamId) {
    Team team = TeamCanned.teamCanned().id(teamId).optaId(optaTeamId).build();
    EntitiesMockUtils.mockTeam(template, team);
  }

  private static void mockCompetitionsConfig(MongoTemplate template) {
    CompetitionConfig competitionConfig = CompetitionConfig
      .builder()
      .competition(CompetitionConfig.Competition
        .builder().id(CompetitionCanned.DEFAULT_ID).build())
      .feedsConfig(FeedsConfig
        .builder()
        .settlementConfig(CompetitionConfig.SettlementConfig
          .builder().provider(CompetitionConfig.Provider.OPTA).delaySecs(1).build())
        .build())
      .markets(List.of(createCompetitionMarketConfig().propName(GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(ASSIST_GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(SHOT.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(SHOT_ON_GOAL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(FOUL.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(YELLOW_CARD.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(PASS.getStatisticName()).build(),
        createCompetitionMarketConfig().propName(COMPLETED_PASS.getStatisticName()).build()))
      .build();

    EntitiesMockUtils.mockCompetitionsConfig(template, competitionConfig);
  }
}
