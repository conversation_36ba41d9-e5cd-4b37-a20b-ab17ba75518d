package com.wsf.dataingestor.integration.fixturesummaryretrieval;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import com.wsf.dataingestor.integration.AbstractMongoTest;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryDto;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;
import com.wsf.dataingestor.shared.canned.FixtureSummaryDataDtoCanned;

import static java.util.Optional.ofNullable;
import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class GetFixtureSummaryIntegrationTest extends AbstractMongoTest {

  @Autowired
  FixtureSummaryRetriever fixtureSummaryRetriever;
  @Autowired
  CacheManager cacheManager;

  @Override
  protected void initEnv() {
    cacheManager.getCacheNames()
      .forEach(name -> ofNullable(cacheManager.getCache(name)).ifPresent(Cache::clear));
  }

  @Test
  public void whenTheCacheIsEmpty_thenItReturns404() throws Exception {
    String fixtureId = "test-fixture-id";

    var httpRequest = get("/v1/fixture-summaries/{fixtureId}", fixtureId)
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    ResultActions result = this.mvc.perform(httpRequest);

    result.andExpect(status().isNotFound());
  }

  @Test
  public void whenTheCacheIsPopulated_thenItReturnsTheCorrectDto() throws Exception {
    // Arrange
    String fixtureId = "test-fixture-id";
    FixtureSummaryDto expectedDto = FixtureSummaryDataDtoCanned.fixtureSummaryDataCanned().build();

    fixtureSummaryRetriever.put(fixtureId, expectedDto);

    // Act
    var httpRequest = get("/v1/fixture-summaries/{fixtureId}", fixtureId)
      .contentType(MediaType.APPLICATION_JSON)
      .accept(MediaType.APPLICATION_JSON);
    ResultActions result = this.mvc.perform(httpRequest);

    // Assert
    MvcResult mvcResult = result.andExpect(status().isOk()).andReturn();
    FixtureSummaryDto actualDto = objectMapper.readValue(mvcResult.getResponse().getContentAsString(),
      FixtureSummaryDto.class);
    assertThat(actualDto).isEqualTo(expectedDto);
  }
}
