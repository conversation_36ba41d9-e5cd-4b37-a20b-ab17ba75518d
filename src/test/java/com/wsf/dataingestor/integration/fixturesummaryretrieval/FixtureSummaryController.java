package com.wsf.dataingestor.integration.fixturesummaryretrieval;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryDto;
import com.wsf.dataingestor.modules.fixturesummary.FixtureSummaryRetriever;

@Slf4j
@RestController
@RequestMapping("/v1/fixture-summaries")
@RequiredArgsConstructor
public class FixtureSummaryController {

  private final FixtureSummaryRetriever fixtureSummaryRetriever;

  @RequestMapping(value = "/{fixtureId}", method = RequestMethod.GET)
  public ResponseEntity<FixtureSummaryDto> getFixtureSummary(@PathVariable String fixtureId) {
    FixtureSummaryDto fixtureSummaryDto = fixtureSummaryRetriever.get(fixtureId);
    return ResponseEntity.ok(fixtureSummaryDto);
  }

}
