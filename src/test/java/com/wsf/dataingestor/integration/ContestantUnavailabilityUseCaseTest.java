package com.wsf.dataingestor.integration;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.wsf.dataingestor.config.PlayerAvailabilityUpdaterConfig;
import com.wsf.dataingestor.exceptions.FeedNotFoundException;
import com.wsf.dataingestor.kafka.KafkaService;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.MA46Client;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.ContestantUnavailabilityUseCase;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.service.ContestantUnavailabilityLogicService;
import com.wsf.dataingestor.shared.canned.ContestantUnavailabilitiesDataFeedCanned;
import com.wsf.dataingestor.shared.canned.FeedContestantUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FeedUnavailabilityCanned;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.ContestantUnavailability;
import com.wsf.kafka.domain.ContestantUnavailabilityKafka;

import static com.wsf.dataingestor.integration.EntitiesMockUtils.readContestantUnavailabilitiesFromKafka;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.OPTA_FIXTURE_ID;
import static com.wsf.domain.common.ContestantUnavailability.UnavailabilityReason.SUSPENSION;
import static com.wsf.kafka.domain.ContestantUnavailabilityKafka.Action.UNAVAILABLE;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

public class ContestantUnavailabilityUseCaseTest extends AbstractMongoTest {

  @Value("${crons.player-availability-updater.alert-fixtures-within-hours}")
  private int alertFixturesWithinHours;
  @Value("${crons.player-availability-updater.not-alert-fixtures-within-hours}")
  private int notAlertFixturesWithinHours;

  @MockBean
  private MA46Client ma46Client;
  @MockBean
  private KafkaService kafkaServiceMock;

  @Autowired
  private PlayerAvailabilityUpdaterConfig playerAvailabilityUpdaterConfig;
  @Autowired
  private ContestantUnavailabilityLogicService contestantUnavailabilityLogicService;
  @Autowired
  private MetricsManager metricsManager;

  @Autowired
  private ContestantUnavailabilityUseCase contestantUnavailabilityUseCase;

  @Override
  public void initEnv() {
    mainTemplate.dropCollection(CONTESTANT_UNAVAILABILITIES_COLLECTION);
  }

  @Before
  public void init() {
    MeterRegistry simpleRegistry = new SimpleMeterRegistry();
    metricsManager = new MetricsManager(simpleRegistry);

    contestantUnavailabilityUseCase = new ContestantUnavailabilityUseCase(playerAvailabilityUpdaterConfig, ma46Client,
      contestantUnavailabilityLogicService, metricsManager, kafkaServiceMock);
  }

  @After
  public void cleanUp() {
    mainTemplate.dropCollection(CONTESTANT_UNAVAILABILITIES_COLLECTION);
  }

  @Test
  public void whenContestantUnavailabilityErrorIsBeyondConfiguredNextHoursAlert_thanNotIncrementErrorMetric() {
    //Arrange
    Instant now = Instant.now();

    var fixture = FixtureCanned.defaultFixture();
    fixture.setDate(now.plus(Duration.ofHours(alertFixturesWithinHours + 10)));

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenThrow(new RuntimeException());

    //Act
    contestantUnavailabilityUseCase.process(fixture);

    // Assert
    assertEquals(0, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
  }

  @Test
  public void whenContestantUnavailabilityErrorIsWithinConfiguredNextHoursAlert_thanIncrementErrorMetric() {
    //Arrange
    Instant now = Instant.now();

    var fixture = FixtureCanned.defaultFixture();
    fixture.setDate(now.plus(Duration.ofHours(alertFixturesWithinHours - 10)));

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenThrow(new RuntimeException());

    //Act
    contestantUnavailabilityUseCase.process(fixture);

    // Assert
    assertEquals(1, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
  }

  @Test
  public void whenThereAreNewContestantUnavailabilities_thenAddToDatabase_thanSendEventToKafka() {
    //Arrange
    var fixture = FixtureCanned.defaultFixture();
    var player = PlayerCanned.playerCanned().build();

    var feedUnavailability = FeedUnavailabilityCanned
      .contestantUnavailabilityCanned()
      .description("He broke the opponent's shin-bone like there was not tomorrow")
      .reason(ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION)
      .startDate(Instant.now())
      .endDate(Instant.now().plus(10, ChronoUnit.DAYS))
      .build();

    var feedContestantUnavailability = FeedContestantUnavailabilityCanned
      .contestantUnavailabilitiesCanned()
      .player(player)
      .unavailabilities(Set.of(feedUnavailability))
      .build();

    var contestantUnavailabilitiesDataFeed = ContestantUnavailabilitiesDataFeedCanned
      .contestantUnavailabilitiesDataFeedBuilder()
      .feedContestantUnavailabilities(Set.of(feedContestantUnavailability))
      .build();

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenReturn(contestantUnavailabilitiesDataFeed);

    //Act
    contestantUnavailabilityUseCase.process(fixture);

    // Arrange
    ContestantUnavailabilityKafka contestantUnavailabilityKafka = readContestantUnavailabilitiesFromKafka(
      kafkaServiceMock);

    assertThat(contestantUnavailabilityKafka).satisfies(event -> {
      assertThat(event.getContestantId()).isEqualTo(player.getIdAsString());
      assertThat(event.getFixtureId()).isEqualTo(fixture.getIdAsString());
      assertThat(event.getAction()).isEqualTo(UNAVAILABLE);
      assertThat(event.getIsInjured()).isFalse();
      assertThat(event.getIsSuspended()).isTrue();
    });

    List<ContestantUnavailability> contestantUnavailabilities = mainTemplate.find(new Query(Criteria.where("fixtureId")
      .is(fixture.getIdAsString())
      .and("contestantId")
      .is(player.getIdAsString())), ContestantUnavailability.class);

    assertThat(contestantUnavailabilities)
      .hasSize(1)
      .first()
      .satisfies(contestantUnavailability -> {
        assertThat(contestantUnavailability.getContestantId()).isEqualTo(player.getIdAsString());
        assertThat(contestantUnavailability.getFixtureId()).isEqualTo(fixture.getIdAsString());
        assertThat(contestantUnavailability.isContestantInjured()).isFalse();
        assertThat(contestantUnavailability.isContestantSuspended()).isTrue();
        assertThat(contestantUnavailability.getUnavailabilities())
          .hasSize(1)
          .anyMatch(unavailability -> unavailability.getReason() == SUSPENSION);
      });
  }

  @Test
  public void whenContestantUnavailabilityErrorIsFeedNotFoundException_andTheFixtureIsInTheNextHours_thenSaveOptaFixtureId() {
    //Arrange
    Instant now = Instant.now();

    var fixture = FixtureCanned.defaultFixture();
    fixture.setDate(now.plus(Duration.ofHours(alertFixturesWithinHours - 2)));

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenThrow(
      new FeedNotFoundException("Missing feed", new IllegalStateException()));

    //Act
    Optional<String> feedsNotFound = contestantUnavailabilityUseCase.process(fixture);

    // Assert
    assertEquals(0, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
    assertThat(feedsNotFound)
      .isPresent().hasValue(OPTA_FIXTURE_ID);
  }

  @Test
  public void whenContestantUnavailabilityErrorIsFeedNotFoundException_andTheFixtureIsInTheNextHours_butIsInTheNotAlertRange_thenDiscardOptaFixtureId() {
    //Arrange
    Instant now = Instant.now();

    var fixture = FixtureCanned.defaultFixture();
    fixture.setDate(now.plus(Duration.ofHours(notAlertFixturesWithinHours - 2)));

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenThrow(
      new FeedNotFoundException("Missing feed", new IllegalStateException()));

    //Act
    Optional<String> feedsNotFound = contestantUnavailabilityUseCase.process(fixture);

    // Assert
    assertEquals(0, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
    assertThat(feedsNotFound).isEmpty();
  }

  @Test
  public void whenContestantUnavailabilityErrorIsFeedNotFoundException_andTheFixtureIsInNotInTheNextHours_thenNotSaveOptaFixtureId() {
    //Arrange
    Instant now = Instant.now();

    var fixture = FixtureCanned.defaultFixture();
    fixture.setDate(now.plus(Duration.ofHours(alertFixturesWithinHours + 10)));

    when(ma46Client.retrieveParsedFeed(fixture.getOptaFixtureId())).thenThrow(
      new FeedNotFoundException("Missing feed", new IllegalStateException()));

    //Act
    Optional<String> feedsNotFound = contestantUnavailabilityUseCase.process(fixture);

    // Assert
    assertEquals(0, metricsManager.PLAYER_AVAILABILITY_FEED_ERROR.count());
    assertThat(feedsNotFound).isEmpty();
  }
}