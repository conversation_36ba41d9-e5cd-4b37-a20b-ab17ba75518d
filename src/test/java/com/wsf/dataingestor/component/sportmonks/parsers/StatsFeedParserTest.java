package com.wsf.dataingestor.component.sportmonks.parsers;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.io.IOException;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.hamcrest.MatcherAssert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.services.FeedsConfigService;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.dataingestor.sportmonks.parsers.StatsFeedParser;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.services.ratings.ScoresComputer.computeScoreFromFeed;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.DEFAULT_AWAY_TEAM;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_FOULS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_OFFSIDES;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_SHOTS_ON_GOAL;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_YELLOW_CARDS;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.util.Objects.nonNull;
import static java.util.stream.Collectors.groupingBy;
import static org.apache.commons.io.IOUtils.resourceToString;
import static org.assertj.core.api.Assertions.assertThat;
import static org.bson.types.ObjectId.getSmallestWithDate;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class StatsFeedParserTest extends TestWithMocks {

  private static final ObjectId CELIK_ID = new ObjectId("111111111111111111111111");
  private final SimpleMeterRegistry meterRegistry = new SimpleMeterRegistry();

  @Mock
  private FeedsConfigService feedsConfigServiceMock;
  @Mock
  private SportmonksFeedParserUtils smFeedParserUtilsMock;
  private StatsFeedParser parser;

  @Before
  public void setUp() throws Exception {
    when(feedsConfigServiceMock.getSupportedStatsForCompetitionId(anyString())).thenReturn(
      Set.of(GOAL.getStatisticName(), ASSIST_GOAL.getStatisticName(), SHOT.getStatisticName(), SHOT_ON_GOAL.getStatisticName(), TEAM_SHOTS, TEAM_SHOTS_ON_GOAL, TEAM_CORNERS,
        TEAM_OFFSIDES, TEAM_FOULS));

    MetricsManager metricsManager = new MetricsManager(meterRegistry);
    parser = new StatsFeedParser(new JsonConfig().jsonObjectMapper(), smFeedParserUtilsMock, feedsConfigServiceMock,
      metricsManager);
  }

  @Test
  public void parseFinalFeed() throws IOException {
    // Arrange
    Fixture fixture = fixtureCanned().build();
    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    Player federicoInsua = PlayerCanned.playerCanned().team(DEFAULT_AWAY_TEAM).sportmonksPlayerId("96514").build();
    Player tammyAbraham = PlayerCanned.playerCanned().team(DEFAULT_AWAY_TEAM).sportmonksPlayerId("3139").build();
    Player celik = PlayerCanned.playerCanned().id(CELIK_ID).build();

    // trick for always assigning the same ObjectId to the same player from the feed
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(anyString(), any())).thenAnswer(
      (Answer<Player>) StatsFeedParserTest::getPlayerWithFixedObjectId);

    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("96514"), any())).thenReturn(federicoInsua);
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("3139"), any())).thenReturn(tammyAbraham);
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("450031"), any())).thenReturn(celik);

    var homeTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM1_ID)
      .name("Spezia").build();
    var awayTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM2_ID)
      .name("Roma").build();

    when(smFeedParserUtilsMock.getTeam(anyString(), eq("345"))).thenReturn(homeTeam);
    when(smFeedParserUtilsMock.getTeam(anyString(), eq("37"))).thenReturn(awayTeam);

    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_final.json", Charset.defaultCharset(), classLoader);

    // Act
    var statsFeed = parser.parseFeed(result);

    // Assert
    assertThat(statsFeed.getDate()).isEqualTo(Instant.parse("2023-01-22T17:00:00Z"));
    assertThat(statsFeed.getFixture()).isEqualTo(fixture);
    assertThat(statsFeed.getMatchPeriod()).isEqualTo(MatchPeriod.END_MATCH);
    assertThat(statsFeed.getFixtureStatus()).isEqualTo(FeedFixtureStatus.PLAYED);
    assertThat(statsFeed.getProvider()).isEqualTo(MatchDataFeed.FeedProvider.SPORTMONKS);
    assertThat(statsFeed.getMatchTimeMin()).isEqualTo(90);
    assertTrue(statsFeed.isFinalData());
    assertFalse(statsFeed.isSnapshot());
    assertThat(statsFeed.isExtraTimeHappened()).isFalse();

    int expectedHomeScore = 0;
    int expectedAwayScore = 2;
    verifyScoresFromEvents(statsFeed, expectedHomeScore, expectedAwayScore);

    assertEquals(Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, RED_CARD, SUB_ON, SUB_OFF),
      statsFeed.getSupportedEventTypes());

    List<EntityEventDTO> events = statsFeed.getAggregatedPlayerMatchEvents();
    MatcherAssert.assertThat(events, hasSize(28));
    Map<SoccerMatchEvent, List<EntityEventDTO>> eventTypeToEvents = events
      .stream()
      .collect(groupingBy(EntityEventDTO::getEventType));

    List<EntityEventDTO> goalEvents = eventTypeToEvents.get(GOAL);
    assertThat(goalEvents)
      .hasSize(2)
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80275407_goalsMade"))
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276037_goalsMade"));

    List<EntityEventDTO> assistEvents = eventTypeToEvents.get(ASSIST_GOAL);
    assertThat(assistEvents)
      .hasSize(2)
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80275407_assistsGoal"))
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276037_assistsGoal"));

    List<EntityEventDTO> yellowCardEvents = eventTypeToEvents.get(YELLOW_CARD);
    assertThat(yellowCardEvents)
      .hasSize(4)
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80275182_yellowCards"))
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276980_yellowCards"))
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276238_yellowCards"))
      .anySatisfy(event -> {
        assertThat(event.getEventId()).isEqualTo("80275299_yellowCards");
        assertThat(event.isOnBench()).isTrue();
      });

    assertThat(eventTypeToEvents.get(RED_CARD))
      .hasSize(2)
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276239_redCards"))
      .anySatisfy(event -> assertThat(event.getEventId()).isEqualTo("80276981_redCards"));

    List<PlayerDataDTO> playersData = statsFeed.getPlayersData();
    assertThat(playersData)
      .hasSize(31)
      .allSatisfy(playerData -> {
        assertNotNull(playerData.getPlayer());
        assertTrue(playerData.isHasPlayed());
        assertFalse(playerData.getStats()
          .isEmpty());
      });
    assertThat(playersData)
      .filteredOn(playerData -> playerData.getPlayerId().equals(CELIK_ID.toString()))
      .allSatisfy(playerData -> assertNull(playerData.getStats().get(YELLOW_CARD.getStatisticName())));

    assertThat(playersData)
      .filteredOn(player -> nonNull(player.getMatchPosition()))
      .hasSize(22);

    var teamsData = statsFeed.getTeamsData();
    assertThat(teamsData)
      .filteredOn(teamData -> teamData.getTeamId().equals(homeTeam.getIdAsString()))
      .allSatisfy(teamData -> {
        Map<String, Number> stats = teamData.getStats();
        assertEquals(7, stats.get(TEAM_SHOTS));
        assertEquals(0, stats.get(TEAM_SHOTS_ON_GOAL));
        assertEquals(4, stats.get(TEAM_CORNERS));
        assertEquals(17, stats.get(TEAM_FOULS));
        assertEquals(0, stats.get(TEAM_OFFSIDES));
        assertNull(stats.get(TEAM_YELLOW_CARDS));
      });

    assertThat(teamsData)
      .filteredOn(teamData -> teamData.getTeamId().equals(awayTeam.getIdAsString()))
      .allSatisfy(teamData -> {
        Map<String, Number> stats = teamData.getStats();
        assertEquals(13, stats.get(TEAM_SHOTS));
        assertEquals(3, stats.get(TEAM_SHOTS_ON_GOAL));
        assertEquals(0, stats.get(TEAM_CORNERS));
        assertEquals(10, stats.get(TEAM_FOULS));
        assertEquals(1, stats.get(TEAM_OFFSIDES));
        assertNull(stats.get(TEAM_YELLOW_CARDS));
      });
  }

  @Test
  public void parseFinalFeedWithExtraTimes() throws IOException {
    // Arrange
    Fixture fixture = fixtureCanned().build();
    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    var homeTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM1_ID)
      .name("Spezia").build();
    var awayTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM2_ID)
      .name("Roma").build();

    when(smFeedParserUtilsMock.getTeam(anyString(), eq("345"))).thenReturn(homeTeam);
    when(smFeedParserUtilsMock.getTeam(anyString(), eq("37"))).thenReturn(awayTeam);
    
    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_final_with_extra_times.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var statsFeed = parser.parseFeed(result);

    // Assert
    assertThat(statsFeed.getDate()).isEqualTo(Instant.parse("2023-01-22T17:00:00Z"));
    assertThat(statsFeed.getFixture()).isEqualTo(fixture);
    assertThat(statsFeed.getMatchPeriod()).isEqualTo(MatchPeriod.END_MATCH);
    assertThat(statsFeed.getFixtureStatus()).isEqualTo(FeedFixtureStatus.PLAYED);
    assertThat(statsFeed.getProvider()).isEqualTo(MatchDataFeed.FeedProvider.SPORTMONKS);
    assertThat(statsFeed.isExtraTimeHappened()).isTrue();
  }


  @Test
  public void parsePreMatchFeed() throws IOException {
    Fixture fixture = fixtureCanned().build();
    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    Player player = PlayerCanned.defaultPlayer();
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(anyString(), any())).thenReturn(player);

    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_prematch.json", Charset.defaultCharset(), classLoader);

    var statsFeed = parser.parseFeed(result);

    MatcherAssert.assertThat(statsFeed.getDate(), is(Instant.parse("2023-02-15T19:30:00Z")));
    MatcherAssert.assertThat(statsFeed.getFixture(), is(fixture));
    MatcherAssert.assertThat(statsFeed.getMatchPeriod(), is(MatchPeriod.FIRST_HALF));
    MatcherAssert.assertThat(statsFeed.getFixtureStatus(), is(FeedFixtureStatus.FIXTURE));
    MatcherAssert.assertThat(statsFeed.getMatchTimeMin(), is(0));
    assertFalse(statsFeed.isFinalData());
    assertFalse(statsFeed.isSnapshot());
    MatcherAssert.assertThat(statsFeed.getProvider(), is(MatchDataFeed.FeedProvider.SPORTMONKS));
    verifyScoresFromEvents(statsFeed, 0, 0);
    MatcherAssert.assertThat(statsFeed.getPlayersData()
      .size(), is(22));
  }

  @Test
  public void whenAnEventDoesNotHavePeriod_thenWeProcessTheEventWithNullMatchPeriod() throws IOException {
    Fixture fixture = fixtureCanned().build();
    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    Player player = PlayerCanned.defaultPlayer();
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(anyString(), any())).thenReturn(player);

    Player playerOhio = PlayerCanned.playerCanned().id(new ObjectId("64eefd758918d00a519aeabc")).build();
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("28912766"), any())).thenReturn(playerOhio);

    Player playerAlzate = PlayerCanned.playerCanned().id(new ObjectId("64eefd758918d00a519aeabd")).build();
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("14069"), any())).thenReturn(playerAlzate);

    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_event_no_period.json", Charset.defaultCharset(),
      classLoader);
    MatchDataFeed matchDataFeed = parser.parseFeed(result);

    List<EntityEventDTO> aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    MatcherAssert.assertThat(aggregatedPlayerMatchEvents, hasSize(14));
    EntityEventDTO goalEvent = aggregatedPlayerMatchEvents.get(0);
    assertNull(goalEvent.getPeriod());
    MatcherAssert.assertThat(goalEvent.getEventType(), is(GOAL));
    MatcherAssert.assertThat(goalEvent.getEntityId(), is(playerOhio.getIdAsString()));
    EntityEventDTO assistEvent = aggregatedPlayerMatchEvents.get(1);
    assertNull(assistEvent.getPeriod());
    MatcherAssert.assertThat(assistEvent.getEventType(), is(ASSIST_GOAL));
    MatcherAssert.assertThat(assistEvent.getEntityId(), is(playerAlzate.getIdAsString()));
  }

  @Test
  public void parseFeedWithMissingMinutesPlayed() throws IOException {
    Fixture fixture = fixtureCanned().build();
    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    Player player = PlayerCanned.defaultPlayer();
    Player playerZivkovic = PlayerCanned.playerCanned().sportmonksPlayerId("73753").build();

    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(anyString(), any())).thenReturn(player);
    when(smFeedParserUtilsMock.getPlayerBySmPlayerId(eq("73753"), any())).thenReturn(playerZivkovic);

    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_final_without_minutes_played.json",
      Charset.defaultCharset(), classLoader);

    var statsFeed = parser.parseFeed(result);

    List<PlayerDataDTO> playersData = statsFeed.getPlayersData();
    Optional<PlayerDataDTO> zivkovicData = playersData
      .stream()
      .filter(playerData -> playerData.getPlayer().equals(playerZivkovic))
      .findAny();
    assertTrue(zivkovicData.isPresent());
  }

  @Test
  public void whenEventsAreReceived_thenWeParseSubstitutes() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_with_subs.json", Charset.defaultCharset(), classLoader);

    Fixture fixture = fixtureCanned().build();

    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    var homeTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM1_ID)
      .name("Always Ready").build();
    var awayTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM2_ID)
      .name("LDU Quito").build();

    when(smFeedParserUtilsMock.getTeam(anyString(), eq("10779"))).thenReturn(homeTeam);
    when(smFeedParserUtilsMock.getTeam(anyString(), eq("234259"))).thenReturn(awayTeam);

    // Act
    var statsFeed = parser.parseFeed(result);

    // Assert
    var aggregatedPlayerMatchEvents = statsFeed.getAggregatedPlayerMatchEvents();

    assertThat(aggregatedPlayerMatchEvents)
      .filteredOn(event -> event.getEventType() == SUB_OFF || event.getEventType() == SUB_ON)
      .hasSize(18)
      .anySatisfy(substitutionEvent -> {
        assertEquals(SUB_ON, substitutionEvent.getEventType());
        assertEquals("37667163", substitutionEvent.getExternalEntityId());
      });
  }

  @Test
  public void whenSubstitutionsAreReceived_thenWeMatchSubOnAndSubOffEvents() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = resourceToString("feeds/sportmonks/stats_with_subs.json", Charset.defaultCharset(), classLoader);

    Fixture fixture = fixtureCanned().build();

    when(smFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    var homeTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM1_ID)
      .name("Always Ready").build();
    var awayTeam = TeamCanned.teamCanned().id(TeamCanned.TEAM2_ID)
      .name("LDU Quito").build();

    when(smFeedParserUtilsMock.getTeam(anyString(), eq("10779"))).thenReturn(homeTeam);
    when(smFeedParserUtilsMock.getTeam(anyString(), eq("234259"))).thenReturn(awayTeam);

    // Act
    var statsFeed = parser.parseFeed(result);

    // Assert
    var aggregatedPlayerMatchEvents = statsFeed.getAggregatedPlayerMatchEvents();

    assertThat(aggregatedPlayerMatchEvents)
      .filteredOn(event -> event.getEventType() == SUB_OFF || event.getEventType() == SoccerMatchEvent.SUB_ON)
      .filteredOn(event -> event.getEventId().equals("117284590_substitution"))
      .hasSize(2)
      .anySatisfy(substitutionEvent -> assertEquals(SUB_OFF, substitutionEvent.getEventType()))
      .anySatisfy(substitutionEvent -> assertEquals(SUB_OFF, substitutionEvent.getEventType()));
  }

  private static Player getPlayerWithFixedObjectId(InvocationOnMock invocation) {
    int smPlayerId = Integer.parseInt((String) invocation.getArguments()[0]);
    Instant instantFromPlayerId = Instant.EPOCH.plus(smPlayerId, ChronoUnit.SECONDS);
    return PlayerCanned.defaultPlayer(getSmallestWithDate(new Date(instantFromPlayerId.toEpochMilli())));
  }

  private static void verifyScoresFromEvents(MatchDataFeed statsFeed, int expectedHomeScore, int expectedAwayScore) {
    var score = computeScoreFromFeed(statsFeed);
    int homeScore = score.getHomeScore();
    int awayScore = score.getAwayScore();

    MatcherAssert.assertThat(homeScore, is(expectedHomeScore));
    MatcherAssert.assertThat(awayScore, is(expectedAwayScore));
  }
}
