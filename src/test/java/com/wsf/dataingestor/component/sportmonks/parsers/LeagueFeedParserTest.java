package com.wsf.dataingestor.component.sportmonks.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Calendar;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.CompetitionCanned;
import com.wsf.dataingestor.sportmonks.parsers.LeagueFeedParser;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;

import static com.wsf.dataingestor.shared.canned.CompetitionCanned.DEFAULT_COMPETITION;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.util.TimeZone.getTimeZone;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class LeagueFeedParserTest extends TestWithMocks {

  @Mock
  SportmonksFeedParserUtils smFeedParserUtilsMock;

  private LeagueFeedParser parser;

  @Before
  public void setUp() throws Exception {
    when(smFeedParserUtilsMock.getCompetition(anyString())).thenReturn(CompetitionCanned.competitionCanned().build());
    when(smFeedParserUtilsMock.getTournament(anyString())).thenReturn(tournamentCanned().build());
    parser = new LeagueFeedParser(new JsonConfig().jsonObjectMapper(), smFeedParserUtilsMock);
  }

  @Test
  public void testParseLeagueFeed() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/league.json", Charset.defaultCharset(), classLoader);

    // Act
    CurrentTournamentFeed currentTournamentFeed = parser.parseFeed(result);

    // Assert
    var expectedStartDate = Calendar.getInstance(getTimeZone("UTC"));
    expectedStartDate.set(2024, Calendar.JULY, 9, 0, 0, 0);

    LocalDate date = LocalDate.parse("2024-07-09");
    Instant expectedInstant = date.atStartOfDay().toInstant(ZoneOffset.UTC);

    assertThat(currentTournamentFeed.getCompetition(), is(DEFAULT_COMPETITION));
    assertThat(currentTournamentFeed.getYear(), is("2024-2025"));
    assertThat(currentTournamentFeed.getExternalSeasonId(), is("23619"));
    assertThat(currentTournamentFeed.getStartDate(), is(expectedInstant));
    assertThat(currentTournamentFeed.getProvider(), is(CurrentTournamentFeed.Provider.SPORTMONKS));
  }

  @Test
  public void testParseLeagueFeed_noCurrentSeason() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/league_no_current_season.json", Charset.defaultCharset(), classLoader);

    // Act
    CurrentTournamentFeed currentTournamentFeed = parser.parseFeed(result);

    // Assert
    assertThat(currentTournamentFeed.getCompetition(), is(DEFAULT_COMPETITION));
    assertNull(currentTournamentFeed.getYear());
    assertNull(currentTournamentFeed.getExternalSeasonId());
    assertNull(currentTournamentFeed.getStartDate());
    assertThat(currentTournamentFeed.getProvider(), is(CurrentTournamentFeed.Provider.SPORTMONKS));
  }
}
