package com.wsf.dataingestor.component.sportmonks.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixtureDTO.Relation;
import com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.sportmonks.parsers.SeasonFeedParser;
import com.wsf.dataingestor.sportmonks.parsers.SportmonksFeedParserUtils;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.models.FixtureDTO.Relation.FIRST_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SECOND_LEG;
import static com.wsf.dataingestor.models.FixtureDTO.Relation.SINGLE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.FIXTURE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.SUSPENDED;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.util.Objects.nonNull;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class SeasonFeedParserTest extends TestWithMocks {

  private static final Tournament DEFAULT_TOURNAMENT = tournamentCanned().build();
  @Mock
  SportmonksFeedParserUtils smFeedParserUtilsMock;
  private SeasonFeedParser parser;

  @Before
  public void setUp() throws Exception {
    when(smFeedParserUtilsMock.getTournamentByExternalSportmonksId(anyString())).thenReturn(DEFAULT_TOURNAMENT);
    parser = new SeasonFeedParser(new JsonConfig().jsonObjectMapper(), smFeedParserUtilsMock);
  }

  @Test
  public void testParseChampions() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_champions_2122.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    assertNotNull(fixturesFeed.getTournament());

    var fixtures = fixturesFeed.getFixtures();

    Assertions
      .assertThat(fixtures)
      .filteredOn(fixtureDTO -> nonNull(fixtureDTO.getExternalHomeTeamAbbreviation()))
      .isNotEmpty();

    Assertions
      .assertThat(fixtures)
      .filteredOn(fixtureDTO -> nonNull(fixtureDTO.getExternalAwayTeamAbbreviation()))
      .isNotEmpty();

    var fixturesMap = fixtures
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    testFixture(fixturesMap.get("18395624"), SINGLE, PLAYED, false);

    testFixture(fixturesMap.get("18453055"), FIRST_LEG, PLAYED, false);
    testFixture(fixturesMap.get("18509787"), SECOND_LEG, PLAYED, true);

    testFixture(fixturesMap.get("18509785"), "18509784", SECOND_LEG, PLAYED, true);
    testFixture(fixturesMap.get("18509784"), "18509785", FIRST_LEG, PLAYED, false);

    testFixture(fixturesMap.get("18509781"), SINGLE, PLAYED, true);

  }

  @Test
  public void testParseSeriea() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_seriea.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    testFixture(fixturesMap.get("18548159"), FIXTURE);

    testFixture(fixturesMap.get("18546223"), PLAYED);
  }

  @Test
  public void testParseMx() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_mx.json", Charset.defaultCharset(), classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    //Apertura
    testFixture(fixturesMap.get("18528208"), SINGLE, PLAYED, false);
    testFixture(fixturesMap.get("18528340"), SINGLE, PLAYED, false);

    //Reclasification
    testFixture(fixturesMap.get("18709636"), SINGLE, PLAYED, true);

    //Torneo
    testFixture(fixturesMap.get("18709628"), "18709629", FIRST_LEG, PLAYED, false);
    testFixture(fixturesMap.get("18709629"), "18709628", SECOND_LEG, PLAYED, true);

    //Apertura-final
    testFixture(fixturesMap.get("18709782"), "18709783", FIRST_LEG, PLAYED, false);
    testFixture(fixturesMap.get("18709783"), "18709782", SECOND_LEG, PLAYED, true);

    //clausura
    testFixture(fixturesMap.get("18727870"), SINGLE, PLAYED, false);
    testFixture(fixturesMap.get("18729067"), SINGLE, FIXTURE, false);
  }

  @Test
  public void testParseBrasil() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_brasil.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    testFixture(fixturesMap.get("18489393"), PLAYED);
    testFixture(fixturesMap.get("18489540"), PLAYED);
    testFixture(fixturesMap.get("18489772"), PLAYED);
  }

  @Test
  public void testParseChile() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_chile.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    testFixture(fixturesMap.get("18762210"), PLAYED);
    testFixture(fixturesMap.get("18762349"), FIXTURE);
    testFixture(fixturesMap.get("18762447"), FIXTURE);
  }

  //  The Bulgarian Parva Liga has a regular season and a playoff phase. The regular season consists of 14 teams playing
  //  each other twice for a total of 26 matches per team. The top six teams qualify for the championship playoff, while
  //  the bottom eight teams enter the relegation playoff1
  @Test
  public void testParseBulgarianParvaLigaTournamentStructure() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_bulgaria.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    for (var f : fixturesFeed.getFixtures()) {
      assertThat(f.getLeg(), is(SINGLE));
      assertThat(f.getCanGoExtraTime(), is(false));
    }

    testFixture(fixturesMap.get("18533880"), PLAYED);
    testFixture(fixturesMap.get("18557539"), FIXTURE);
  }

  @Test
  public void testParseOldBulgarianParvaLigaTournamentStructure() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_old_bulgaria.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    for (var f : fixturesFeed.getFixtures()) {
      assertThat(f.getLeg(), is(SINGLE));
      assertThat(f.getFixtureStatus(), is(PLAYED));
    }
    testFixture(fixturesMap.get("18149483"), PLAYED);
    testFixture(fixturesMap.get("18525668"), SINGLE, PLAYED, true);
  }

  //The Peru first division is also known as Liga 11. It is the top tier of Peruvian football. It has two stages:
  // Apertura and Clausura2. Each stage has 18 teams playing a single round-robin. The winners of each stage qualify for
  // the Playoffs2.
  @Test
  public void testParsePeru() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_peru.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    for (var f : fixturesFeed.getFixtures()) {
      assertThat(f.getLeg(), is(SINGLE));
      assertThat(f.getCanGoExtraTime(), is(false));
    }

    testFixture(fixturesMap.get("18763092"), PLAYED);
    testFixture(fixturesMap.get("18763226"), FIXTURE);
  }

  @Test
  public void testParseOldPeru() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_old_peru.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    //random
    testFixture(fixturesMap.get("18545058"), PLAYED);

    //    "name": "Championship - Semi-finals",
    testFixture(fixturesMap.get("18720693"), "18720694", FIRST_LEG, SUSPENDED, false);
    testFixture(fixturesMap.get("18720694"), "18720693", SECOND_LEG, PLAYED, true);

    //    "name": "Championship - Final",
    testFixture(fixturesMap.get("18723532"), "18723533", FIRST_LEG, PLAYED, false);
    testFixture(fixturesMap.get("18723533"), "18723532", SECOND_LEG, PLAYED, true);
  }

  //  The tournament takes place over 33 days and there are no playoffs after the regular season. The tournament usually starts at
  //  February and ends in December.
  @Test
  public void testParseEcuador() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_ecuador.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    for (var f : fixturesFeed.getFixtures()) {
      assertThat(f.getLeg(), is(SINGLE));
      assertThat(f.getCanGoExtraTime(), is(false));
    }
  }

  @Test
  public void testParseOldEcuador() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/season_old_ecuador.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    var fixturesMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    //random
    testFixture(fixturesMap.get("18472026"), PLAYED);

    //    "name": "Championship - Final",
    testFixture(fixturesMap.get("18718654"), "18718655", FIRST_LEG, PLAYED, false);
    testFixture(fixturesMap.get("18718655"), "18718654", SECOND_LEG, PLAYED, true);
  }

  @Test
  public void ignorePlaceholderTest() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/sportmonks/placeholder_test.json", Charset.defaultCharset(),
      classLoader);

    // Act
    var fixturesFeed = parser.parseFeed(result);

    // Assert
    assert fixturesFeed.getFixtures()
      .stream()
      .map(FixtureDTO::getExternalFixtureId)
      .noneMatch("18807086"::equals);
  }

  private static void testFixture(FixtureDTO fixture, FeedFixtureStatus fixtureStatus) {
    testFixture(fixture, SINGLE, fixtureStatus, false);
  }

  private static void testFixture(FixtureDTO fixture, String relatedFixtureId, Relation fixtureRelation,
                                  FeedFixtureStatus fixtureStatus, boolean canGoExtraTime) {
    assertThat(fixture.getExternalRelatedFixtureId(), is(relatedFixtureId));
    testFixture(fixture, fixtureRelation, fixtureStatus, canGoExtraTime);
  }

  private static void testFixture(FixtureDTO fixture, Relation fixtureRelation, FeedFixtureStatus fixtureStatus,
                                  boolean canGoExtraTime) {
    assertThat(fixture.getLeg(), is(fixtureRelation));
    assertThat(fixture.getCanGoExtraTime(), is(canGoExtraTime));
    assertThat(fixture.getFixtureStatus(), is(fixtureStatus));
    assertFalse(fixture.getIsNeutralVenue());
  }
}
