package com.wsf.dataingestor.component.sportmonks.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.sportmonks.parsers.TeamsFeedParser;

import static com.wsf.dataingestor.models.SquadsFeed.FeedProvider.SPORTMONKS;
import static java.util.Objects.nonNull;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertNotNull;

public class TeamsFeedParserTest extends TestWithMocks {

  TeamsFeedParser parser;

  @Before
  public void setUp() throws Exception {
    parser = new TeamsFeedParser(new JsonConfig().jsonObjectMapper());
  }

  @Test
  public void parseFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/sportmonks/teams.json", Charset.defaultCharset(), classLoader);

    SquadsFeed squadsFeed = parser.parseFeed(feed);

    assertThat(squadsFeed.getProvider(), is(SPORTMONKS));
    assertThat(squadsFeed.getSquadPlayers()
      .size(), is(605));

    var squadsPlayers = squadsFeed.getSquadPlayers();
    Assertions
      .assertThat(squadsPlayers)
      .filteredOn(squadPlayerDTO -> nonNull(squadPlayerDTO.getTeam().getExternalTeamAbbreviation()))
      .isNotEmpty();

    for (SquadPlayerDTO squadPlayer : squadsPlayers) {
      assertNotNull(squadPlayer.getTeam());
    }
  }

  @Test
  public void parseFeed_onePlayerNoBirthDate() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/sportmonks/teams_nobirthdate.json", Charset.defaultCharset(),
      classLoader);

    SquadsFeed squadsFeed = parser.parseFeed(feed);

    assertThat(squadsFeed.getProvider(), is(SPORTMONKS));
    assertThat(squadsFeed.getSquadPlayers()
      .size(), is(604));
    for (SquadPlayerDTO squadPlayer : squadsFeed.getSquadPlayers()) {
      assertNotNull(squadPlayer.getTeam());
    }
  }

  @Test
  public void parseFeed_onePlayerHasDifferentTeam() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/sportmonks/teams_wrongteam.json", Charset.defaultCharset(),
      classLoader);

    SquadsFeed squadsFeed = parser.parseFeed(feed);

    assertThat(squadsFeed.getProvider(), is(SPORTMONKS));
    assertThat(squadsFeed.getSquadPlayers()
      .size(), is(604));
    for (SquadPlayerDTO squadPlayer : squadsFeed.getSquadPlayers()) {
      assertNotNull(squadPlayer.getTeam());
    }
  }
}
