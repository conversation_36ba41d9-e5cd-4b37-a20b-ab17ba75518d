package com.wsf.dataingestor.component.sportmonks.parsers;

import junitparams.JUnitParamsRunner;
import junitparams.Parameters;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import com.wsf.dataingestor.sportmonks.parsers.StatsMapper;

import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.SM_SHOTS;
import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.SM_SHOTS_BLOCKED;
import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.SM_SHOTS_OFF_GOAL;
import static com.wsf.dataingestor.sportmonks.parsers.StatsMapper.SM_SHOTS_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static java.util.Map.of;
import static java.util.Optional.ofNullable;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

@RunWith(JUnitParamsRunner.class)
public class StatsMapperTest {

  private static final Set<String> SHOTS_SUPPORTED_STATS = Set.of(SHOT.getStatisticName(), SHOT_ON_GOAL.getStatisticName());

  @Test
  public void whenStatsAreTranslated_andNoStatsCanBeMapped_thenTheSupportedStatsAreReturnedWith0() {
    Map<String, String> stats = of("51", "0", "83", "1", "14", "0", "19", "1");
    Map<String, Number> translatedStats = StatsMapper.translatePlayerStats(stats, SHOTS_SUPPORTED_STATS);
    assertThat(translatedStats, is(of(SHOT.getStatisticName(), 0, SHOT_ON_GOAL.getStatisticName(), 0)));
  }

  @Test
  @Parameters(method = "translatedStatsParam")
  public void shotsStats(Map<String, String> smStats, Map<String, Number> expectedTranslatedStats) {
    Map<String, Number> translatedStats = StatsMapper.translatePlayerStats(smStats, SHOTS_SUPPORTED_STATS);
    assertThat(translatedStats, is(expectedTranslatedStats));
  }

  private static Map<String, String> buildSMShotsMap(Integer shots, Integer shotsOnGoal, Integer shotsOffGoal,
                                                     Integer shotsBlocked) {
    Map<String, String> smStats = new HashMap<>();
    ofNullable(shots).ifPresent(s -> smStats.put(SM_SHOTS, String.valueOf(s)));
    ofNullable(shotsOnGoal).ifPresent(s -> smStats.put(SM_SHOTS_ON_GOAL, String.valueOf(s)));
    ofNullable(shotsOffGoal).ifPresent(s -> smStats.put(SM_SHOTS_OFF_GOAL, String.valueOf(s)));
    ofNullable(shotsBlocked).ifPresent(s -> smStats.put(SM_SHOTS_BLOCKED, String.valueOf(s)));
    return smStats;
  }

  private Object[] translatedStatsParam() {
    return new Object[] {new Object[] {buildSMShotsMap(null, 1, null, null), buildShotsMap(1, 1)},
                         new Object[] {buildSMShotsMap(null, null, null, 1), buildShotsMap(0, 0)},
                         new Object[] {buildSMShotsMap(null, 2, null, 1), buildShotsMap(2, 2)},
                         new Object[] {buildSMShotsMap(1, 1, 1, 1), buildShotsMap(2, 1)},
                         new Object[] {buildSMShotsMap(1, null, 1, 2), buildShotsMap(1, 0)},
                         new Object[] {buildSMShotsMap(1, null, 1, null), buildShotsMap(1, 0)},
                         new Object[] {buildSMShotsMap(null, null, 2, 2), buildShotsMap(0, 0)}};
  }

  private static Map<String, Number> buildShotsMap(Integer shots, Integer shotsOnGoal) {
    return of(SHOT.getStatisticName(), shots, SHOT_ON_GOAL.getStatisticName(), shotsOnGoal);
  }
}
