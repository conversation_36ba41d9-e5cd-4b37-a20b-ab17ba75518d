package com.wsf.dataingestor.component.opta.parsers;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.cache.OptaCachedEventService;
import com.wsf.dataingestor.cache.OptaCachedEventService.EventEntity;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.models.TeamDataDTO;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPEventsMapper;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18DPFeedParser;
import com.wsf.dataingestor.opta.parsers.ma18dp.MA18ValidEvents;
import com.wsf.dataingestor.opta.parsers.ma18dp.Ma18DPSubstitutionsHandler;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.DEFAULT_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_3_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.playerCanned;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.teamCanned;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.DELETED_EVENT;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.PASS;
import static com.wsf.domain.soccer.SoccerMatchEvent.POSSIBLE_CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.time.temporal.ChronoUnit.MILLIS;
import static java.util.concurrent.TimeUnit.MILLISECONDS;
import static java.util.stream.Collectors.toSet;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.data.Offset.offset;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class MA18DPFeedParserTest extends TestWithMocks {

  public static final ObjectMapper objectMapper = new JsonConfig().jsonObjectMapper();
  private static final String OPTA_TEAM_ID_1 = "2b3mar72yy8d6uvat1ka6tn3r";
  private static final String OPTA_TEAM_ID_2 = "75qj99fhg5c0ztj2tva5u4uii";
  private static final Team TEAM_1 = teamCanned().id(TEAM1_ID).optaId(OPTA_TEAM_ID_1).build();
  private static final Team TEAM_2 = teamCanned().id(TEAM2_ID).optaId(OPTA_TEAM_ID_2).build();
  private static final Fixture FIXTURE = fixtureCanned().homeTeam(TEAM_1).awayTeam(TEAM_2).build();
  private static final Player PLAYER = playerCanned().build();
  @Mock
  OptaCachedEventService optaCachedEventService;
  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;
  MetricsManager manager;
  private MA18DPEventsMapper eventsMapper;

  @Before
  public void setup() {
    manager = new MetricsManager(new SimpleMeterRegistry());
    eventsMapper = MA18DPEventsMapper
      .builder()
      .playerEventTypeIdToQualifiersId(MA18ValidEvents.playerEventTypeIdToFilters)
      .matchEventTypeIdToQualifiersId(MA18ValidEvents.matchEventTypeIdToFilters)
      .metricsManager(manager)
      .optaCachedEventService(optaCachedEventService)
      .ma18DPSubstitutionsHandler(new Ma18DPSubstitutionsHandler(optaCachedEventService, manager))
      .build();
  }

  @Test
  public void whenAFeedIsParsed_thenTheCorrectSupportedEventsAreReturned() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_goalkick.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    // Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    assertEquals(
      Set.of(GOAL, ASSIST_GOAL, FOUL, GOALKICK, YELLOW_CARD, RED_CARD, SHOT, SHOT_ON_GOAL, CORNER, OFFSIDE, SUB_OFF,
        SUB_ON, DELETED_EVENT, TACKLE_WON), matchDataFeed.getSupportedEventTypes());
  }

  @Test
  public void whenFirstHalfEnds_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    String ma18dp = """
      {
        "content": {
          "liveData": {
            "matchDetails": {
              "id": "bukekpxmpmbs5qmcsrmgx2b6c",
              "event": [
                {
                  "id": 2318853337,
                  "typeId": 30,
                  "periodId": 1,
                  "timeMin": 46,
                  "timeSec": 58,
                  "contestantId": "75qj99fhg5c0ztj2tva5u4uii",
                  "outcome": 0
                }
              ]
            }
          }
        }
      }
      """;

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    // Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(ma18dp.getBytes());

    //Assert
    assertThat(matchDataFeed.getMatchEvents()).hasSize(1);
    MatchDataFeed.MatchEventDTO firstHalfEndEvent = matchDataFeed.getMatchEvents().get(0);
    assertThat(firstHalfEndEvent.getEvent()).isEqualTo(SoccerMatchEvent.BET_START);
  }

  @Test
  public void whenAGoalKickEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_goalkick.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> feedEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(feedEvents.size()).isEqualTo(1);
    var entityEvent = feedEvents.get(0);
    assertThat(entityEvent.getEventType()).isEqualTo(GOALKICK);
    assertThat(entityEvent.getEventId()).isEqualTo("666");
    assertThat(entityEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
  }

  @Test
  public void whenAnOffsideEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_offside.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> feedEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(feedEvents.size()).isEqualTo(1);
    var entityEvent = feedEvents.get(0);
    assertThat(entityEvent.getEventType()).isEqualTo(OFFSIDE);
    assertThat(entityEvent.getEventId()).isEqualTo("2318843537");
    assertThat(entityEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
  }

  @Test
  public void whenATackleWonEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_tackle_won.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> feedEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(feedEvents.size()).isEqualTo(1);
    var entityEvent = feedEvents.get(0);
    assertThat(entityEvent.getEventType()).isEqualTo(TACKLE_WON);
    assertThat(entityEvent.getEventId()).isEqualTo("2318813653");
    assertThat(entityEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
  }

  @Test
  public void whenACornerEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_corner_taken.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> feedEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(feedEvents.size()).isEqualTo(1);
    var entityEvent = feedEvents.get(0);
    assertThat(entityEvent.getEventType()).isEqualTo(CORNER);
    assertThat(entityEvent.getEventId()).isEqualTo("2306661843");
    assertThat(entityEvent.getTeamId()).isEqualTo(PLAYER.getTeam().getIdAsString());
  }

  @Test
  public void whenAYellowCardEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_yellowcard.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(1);
    assertThat(playerEvents.size()).isEqualTo(1);
    EntityEventDTO playerEvent = playerEvents.get(0);
    assertThat(playerEvent.getEventType()).isEqualTo(YELLOW_CARD);
    assertThat(playerEvent.getEventId()).isEqualTo("2532895939");
    assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
    assertThat(playerEvent.getTimeMin()).isEqualTo(17);
  }

  @Test
  public void whenASecondYellowCardEventIsReceived_thenTheEventIsParsedAndReturnedInTheParsedFeed() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_second_yellowcard.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(1);
    assertThat(playerEvents.size()).isEqualTo(1);
    EntityEventDTO redCard = playerEvents.get(0);
    assertThat(redCard.getEventType()).isEqualTo(RED_CARD);
    assertThat(redCard.getEventId()).isEqualTo("2706428321");
    assertThat(redCard.getPeriod()).isEqualTo(MatchPeriod.EXTRA_FIRST_HALF);
    assertThat(redCard.getTimeMin()).isEqualTo(91);
  }

  @Test
  public void whenAnEventIsReceived_andThePlayerCouldNotBeFound_thenAnUnknownPlayerIsReturned() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_yellowcard.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(null);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData)
      .hasSize(1)
      .allMatch(PlayerDataDTO::isUnknown);
    assertThat(playerEvents.size()).isEqualTo(1);
    EntityEventDTO playerEvent = playerEvents.get(0);
    assertThat(playerEvent.getEntityId()).isEqualTo("UNKNOWN_PLAYER_OPTA_672cwl9zj2d6lce6jda350vvu");
    assertThat(playerEvent.getTeamId()).isEqualTo(TEAM_1.getIdAsString());
    assertThat(playerEvent.getEventType()).isEqualTo(YELLOW_CARD);
    assertThat(playerEvent.getEventId()).isEqualTo("2532895939");
    assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
    assertThat(playerEvent.getTimeMin()).isEqualTo(17);
  }

  @Test
  public void whenAGoalEventIsReceived_andThereIsAnAssistRelatedEvent_thenBothGoalAndAssistAreReturnedInTheParsedFeed() throws IOException {
    //Arrange
    String externalAssistPlayer = "ejzxqtdo5xxvbg6gvtju3lk9h";
    Player wsfAssistPlayer = playerCanned().id(PLAYER_3_ID).build();
    String externalAssistEventId = "723037560";
    var cachedEntity = EventEntity
      .builder()
      .externalEventId(externalAssistEventId)
      .externalPlayerId(externalAssistPlayer)
      .playerId(wsfAssistPlayer.getIdAsString())
      .build();

    String assistTeamId = "6123c226c1575f85c7053150";
    String relatedAssistEvent = "391";
    when(optaCachedEventService.get(assistTeamId, relatedAssistEvent)).thenReturn(cachedEntity);

    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_assist.json", classLoader);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);

    String externalGoalScorerPlayer = "4gbk8ka3fv401f8zyslo38fai";
    when(optaFeedParserUtilsMock.getPlayer(eq(externalGoalScorerPlayer), any())).thenReturn(PLAYER);
    when(optaFeedParserUtilsMock.getPlayer(eq(externalAssistPlayer), any())).thenReturn(wsfAssistPlayer);
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    String externalGoalEventId = "2633189847";

    //Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    //Assert
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(2);
    assertThat(playerEvents.size()).isEqualTo(4);

    assertThat(playerEvents).hasSize(4);
    assertThat(playerEvents)
      .filteredOn(playerEvent -> ASSIST_GOAL.equals(playerEvent.getEventType()))
      .hasSize(1)
      .first()
      .satisfies(playerEvent -> {
        assertThat(playerEvent.getEventId()).isEqualTo(externalGoalEventId);
        assertThat(playerEvent.getRelatedEventId()).isEqualTo(externalGoalEventId);
        assertThat(playerEvent.getEventType()).isEqualTo(ASSIST_GOAL);
        assertThat(playerEvent.getExternalEntityId()).isEqualTo(externalAssistPlayer);
        assertThat(playerEvent.getEntityId()).isEqualTo(wsfAssistPlayer.getIdAsString());
        assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
        assertThat(playerEvent.getTimeMin()).isEqualTo(51);
      });

    assertThat(playerEvents)
      .filteredOn(playerEvent -> GOAL.equals(playerEvent.getEventType()))
      .hasSize(1)
      .first()
      .satisfies(playerEvent -> {
        assertThat(playerEvent.getEventId()).isEqualTo(externalGoalEventId);
        assertThat(playerEvent.getRelatedEventId()).isNull();
        assertThat(playerEvent.getEventType()).isEqualTo(GOAL);
        assertThat(playerEvent.getExternalEntityId()).isEqualTo(externalGoalScorerPlayer);
        assertThat(playerEvent.getEntityId()).isEqualTo(PLAYER.getIdAsString());
        assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
        assertThat(playerEvent.getTimeMin()).isEqualTo(51);
      });
  }

  @Test
  public void whenASubstitutionEventIsReceived_andIsWithoutARelatedEventQualifier_thenNoEventIsCreated_andSubstitutionMetricsIsIncremented() throws IOException {
    //Arrange
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_suboff.json", ClassLoader.getSystemClassLoader());
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);
    double substitutions = manager.SUBSTITUTIONS_RECEIVED.count();

    //Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    //Assert
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(playerEvents).hasSize(0);
    assertThat(manager.SUBSTITUTIONS_RECEIVED.count()).isEqualTo(substitutions + 1);
  }

  @Test
  public void whenASubstitutionEventWithARelatedEventIsReceived_thenWeIncrementRelatedSubstitutionMetric() throws IOException {
    //Arrange
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_subon.json", ClassLoader.getSystemClassLoader());
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);
    double substitutions = manager.SUBSTITUTIONS_RECEIVED.count();

    //Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    //Assert
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    assertThat(playerEvents).hasSize(1);
    assertThat(manager.SUBSTITUTIONS_RECEIVED.count()).isEqualTo(substitutions + 1);
  }

  @Test
  public void whenASubstitutionEventWithARelatedEventIsReceived_thenWeIncrementRelationLatencyMetric() throws IOException {
    //Arrange
    byte[] feed = IOUtils.resourceToByteArray("feeds/opta/ma18dp_subon.json", ClassLoader.getSystemClassLoader());
    String subOffEventId = "907";
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);

    int subOffMillisecondsFromNow = 50;
    Instant fiftyMsAgo = Instant.now().minus(subOffMillisecondsFromNow, MILLIS);
    when(optaCachedEventService.get(anyString(), eq(subOffEventId))).thenReturn(
      new EventEntity("externalEventId", "externalPlayerId", "playerId", fiftyMsAgo));

    //Act
    ma18FeedParser.parseFeed(feed);

    // Assert
    assertThat(manager.SUBSTITUTIONS_RECEIVED.count()).isEqualTo(1);
    assertThat(manager.RELATED_SUBSTITUTIONS_RECEIVED.count()).isEqualTo(1);
    assertThat(manager.RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY.count()).isEqualTo(1);
    assertThat(manager.RELATED_SUBSTITUTIONS_RECEIVAL_LATENCY.totalTime(MILLISECONDS)).isEqualTo(
      subOffMillisecondsFromNow, offset(50.));
  }

  @Test
  public void whenAGoalEventIsReceived_andThereIsAnAssistRelatedEventWithoutValue_weExpectAnError() throws IOException {
    //Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_assist_missing_value.json", classLoader);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    String expectedEventId = "2633189847";

    //Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    //Assert
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(1);
    assertThat(playerEvents.size()).isEqualTo(3);

    EntityEventDTO playerEvent = playerEvents.get(2);
    assertThat(playerEvent.getEventType()).isEqualTo(GOAL);
    assertThat(playerEvent.getEventId()).isEqualTo(expectedEventId);
    assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
    assertThat(playerEvent.getTimeMin()).isEqualTo(51);

  }

  @Test
  public void whenAGoalEventIsReceived_andThereIsNotAnAssistRelatedEvent_thenNothingHappens() throws IOException {
    //Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_goal_no_assists.json", classLoader);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    String expectedEventId = "2633189847";

    //Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    //Assert
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(1);
    assertThat(playerEvents.size()).isEqualTo(3);

    EntityEventDTO playerEvent = playerEvents.get(2);
    assertThat(playerEvent.getEventType()).isEqualTo(GOAL);
    assertThat(playerEvent.getEventId()).isEqualTo(expectedEventId);
    assertThat(playerEvent.getPeriod()).isEqualTo(MatchPeriod.FIRST_HALF);
    assertThat(playerEvent.getTimeMin()).isEqualTo(51);

  }

  @Test
  public void whenAnAssistEventIsReceived_thenTheEventIsParsed_butTheCacheDoesNotHaveIt_thenAnErrorIsReturned() throws IOException {
    //Arrange
    String relatedAssistEvent = "391";
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_assist.json", classLoader);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);
    when(optaCachedEventService.get("6123c226c1575f85c7053150", relatedAssistEvent)).thenReturn(null);
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    //Act
    ma18FeedParser.parseFeed(bytes);

    //Assert
    assertThat(manager.NO_EVENT_INFO_ERROR.count()).isOne();
  }

  @Test
  public void whenTheFeedContainsAnEventAndStats_thenItIsParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_event_stats.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // for matchtime we rely on ma2
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size()).isEqualTo(1);
    assertThat(playerIndexes
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count()).isEqualTo((long) 1);
    PlayerDataDTO playerDataDTO = playerIndexes
      .stream()
      .filter(p -> p.getPlayerId().equals(DEFAULT_ID.toString()))
      .findFirst().get();
    assertThat(playerDataDTO.getStats()
      .size()).isEqualTo(1);
    assertThat(playerDataDTO.getStats().get(PASS.getStatisticName())).isEqualTo(4);
    assertThat(playerEvents.size()).isEqualTo(2);
    EntityEventDTO shotEvent = playerEvents.get(0);
    assertThat(shotEvent.getEventType()).isEqualTo(SHOT);
    assertThat(shotEvent.getEventId()).isEqualTo("2341975927");

    EntityEventDTO shotOnGoalEvent = playerEvents.get(1);
    assertThat(shotOnGoalEvent.getEventType()).isEqualTo(SHOT_ON_GOAL);
    assertThat(shotOnGoalEvent.getEventId()).isEqualTo("2341975927");

    List<TeamDataDTO> teamsData = matchDataFeed.getTeamsData();
    assertThat(teamsData.size()).isEqualTo(2);
    TeamDataDTO teamDataDTO = teamsData
      .stream()
      .filter(t -> t.getTeamId().equals(TEAM2_ID.toString()))
      .findFirst().get();
    assertThat(teamDataDTO.getStats()
      .size()).isEqualTo(0);
  }

  @Test
  public void whenTheFeedContainsAnEvent_thenItIsParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_event.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size()).isEqualTo(1);
    assertThat(playerIndexes
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count()).isEqualTo((long) 1);
    PlayerDataDTO playerDataDTO = playerIndexes
      .stream()
      .filter(p -> p.getPlayerId().equals(DEFAULT_ID.toString()))
      .findFirst().get();
    assertNotNull(playerDataDTO);
    assertThat(playerEvents.size()).isEqualTo(2);
    EntityEventDTO shotEvent = playerEvents.get(0);
    assertThat(shotEvent.getEventType()).isEqualTo(SHOT);
    assertThat(shotEvent.getEventId()).isEqualTo("2341975927");

    EntityEventDTO shotOnGoalEvent = playerEvents.get(1);
    assertThat(shotOnGoalEvent.getEventType()).isEqualTo(SHOT_ON_GOAL);
    assertThat(shotOnGoalEvent.getEventId()).isEqualTo("2341975927");

    List<TeamDataDTO> teamsData = matchDataFeed.getTeamsData();
    assertThat(teamsData.size()).isEqualTo(2);
    TeamDataDTO teamDataDTO = teamsData
      .stream()
      .filter(t -> t.getTeamId().equals(TEAM2_ID.toString()))
      .findFirst().get();
    assertNotNull(teamDataDTO);
  }

  @Test
  public void whenTheFeedContainsAnEventThatWeDoNotWantToProcess_thenItIsParsedAsAnUnknownEvent() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_keypass.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    String optaPlayerId1 = "c9yprnk5bqte686eqdm42oic5ahh";
    Player player1 = PlayerCanned.createPlayer().id(PlayerCanned.PLAYER_1_ID).optaPlayerId(optaPlayerId1).build();
    when(optaFeedParserUtilsMock.getPlayer(eq(optaPlayerId1), any())).thenReturn(player1);

    String optaPlayerId2 = "c9yprnk5bqte686eqdm42oic5";
    Player player2 = PlayerCanned.createPlayer().id(PlayerCanned.PLAYER_2_ID).optaPlayerId(optaPlayerId2).build();
    when(optaFeedParserUtilsMock.getPlayer(eq(optaPlayerId2), any())).thenReturn(player2);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(1);
    assertThat(playersData
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count()).isEqualTo((long) 1);
    assertThat(playerEvents.size()).isEqualTo(1);
    EntityEventDTO unknownEvent = playerEvents.get(0);
    assertThat(unknownEvent.getEventType()).isEqualTo(SoccerMatchEvent.UNKNOWN_EVENT);
    assertThat(unknownEvent.getEventId()).isEqualTo("19850636");

    List<TeamDataDTO> teamsData = matchDataFeed.getTeamsData();
    assertThat(teamsData.size()).isEqualTo(2);
    TeamDataDTO teamDataDTO = teamsData
      .stream()
      .filter(t -> t.getTeamId().equals(TEAM2_ID.toString()))
      .findFirst().get();
    assertNotNull(teamDataDTO);
  }

  @Test
  public void whenTheFeedContainsOnlyStats_thenItIsParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_stats.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    List<EntityEventDTO> playerEvents = matchDataFeed.getFeedPlayerMatchEvents();
    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size()).isEqualTo(1);
    assertThat(playerIndexes
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count()).isEqualTo((long) 1);
    PlayerDataDTO playerDataDTO = playerIndexes
      .stream()
      .filter(p -> p.getPlayerId().equals(DEFAULT_ID.toString()))
      .findFirst().get();
    assertThat(playerDataDTO.getStats()
      .size()).isEqualTo(6);
    for (Map.Entry<String, Number> entry : playerDataDTO.getStats().entrySet()) {
      Number val = entry.getKey().equals(PASS.getStatisticName()) ? 4 : 0;
      assertThat(entry.getValue()).isEqualTo(val);
    }
    assertThat(playerEvents.size()).isEqualTo(0);

    List<TeamDataDTO> teamsData = matchDataFeed.getTeamsData();
    assertThat(teamsData.size()).isEqualTo(2);
    TeamDataDTO teamDataDTO = teamsData
      .stream()
      .filter(t -> t.getTeamId().equals(TEAM2_ID.toString()))
      .findFirst().get();
    assertTrue(teamDataDTO.getStats()
      .isEmpty());
  }

  @Test
  public void whenTheFeedContainsAMatchEvent_thenItIsParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_matchevent.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_1))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(OPTA_TEAM_ID_2))).thenReturn(TEAM_2);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(PLAYER);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

    assertThat(matchDataFeed.getMatchEvents()
      .size()).isEqualTo(1);
  }

  @Test
  public void whenAnEmptyFeedIsSent_thenItIsParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_subscribed.json", classLoader);

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);
    assertNull(matchDataFeed.getFixtureStatus());

    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size()).isEqualTo(0);
  }

  @Test
  public void whenALineUpFeedIsSent_thenThePlayersInTheLineUpAreParsedCorrectly() throws IOException {
    //Arrage
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_lineup.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq("50x1m4u58lffhq6v6ga1hbxmy"))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    String otherPlayersId = "000000000000000000000012";
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(
      playerCanned().id(new ObjectId(otherPlayersId)).build());
    String player1Id = "000000000000000000000001";
    when(optaFeedParserUtilsMock.getPlayer(eq("6ekdnbnk56xlxforb5owt3dn9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player1Id)).build());
    String player2Id = "000000000000000000000002";
    when(optaFeedParserUtilsMock.getPlayer(eq("22dchchxwrsty67ultrs61qz9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player2Id)).build());
    String player3Id = "000000000000000000000003";
    when(optaFeedParserUtilsMock.getPlayer(eq("9ej7t1srxjcxe5p627eumn6d"), any())).thenReturn(
      playerCanned().id(new ObjectId(player3Id)).build());
    String player4Id = "000000000000000000000004";
    when(optaFeedParserUtilsMock.getPlayer(eq("3n66mcws9belxbv3pu5uwhfu2"), any())).thenReturn(
      playerCanned().id(new ObjectId(player4Id)).build());
    String player5Id = "000000000000000000000005";
    when(optaFeedParserUtilsMock.getPlayer(eq("9dewnn65ylruq567thg7ebqhh"), any())).thenReturn(
      playerCanned().id(new ObjectId(player5Id)).build());
    String player6Id = "000000000000000000000006";
    when(optaFeedParserUtilsMock.getPlayer(eq("9t88fceutc2x108ga0qkzkafp"), any())).thenReturn(
      playerCanned().id(new ObjectId(player6Id)).build());
    String player7Id = "000000000000000000000007";
    when(optaFeedParserUtilsMock.getPlayer(eq("9e7b5cm66oxf26fmp33yuppsl"), any())).thenReturn(
      playerCanned().id(new ObjectId(player7Id)).build());
    String player8Id = "000000000000000000000008";
    when(optaFeedParserUtilsMock.getPlayer(eq("bkvvapojagxvj17nzk3oy8ct1"), any())).thenReturn(
      playerCanned().id(new ObjectId(player8Id)).build());
    String player9Id = "000000000000000000000009";
    when(optaFeedParserUtilsMock.getPlayer(eq("1tztnrv51q99ewl193qk526z9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player9Id)).build());
    String player10Id = "000000000000000000000010";
    when(optaFeedParserUtilsMock.getPlayer(eq("3pwr7lqvo4o08c4nvzqpuue39"), any())).thenReturn(
      playerCanned().id(new ObjectId(player10Id)).build());
    String player11Id = "000000000000000000000011";
    when(optaFeedParserUtilsMock.getPlayer(eq("1km9nvqz03yoj9cxpv127id7u"), any())).thenReturn(
      playerCanned().id(new ObjectId(player11Id)).build());

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);

    // Act
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(11);
    Set<String> playerIds = playersData
      .stream()
      .map(PlayerDataDTO::getPlayerId)
      .collect(toSet());

    Set<String> expectedPlayerIds = Set.of(player1Id, player2Id, player3Id, player4Id, player5Id, player6Id, player7Id,
      player8Id, player9Id, player10Id, player11Id);

    assertThat(playerIds).containsAll(expectedPlayerIds);
  }

  @Test
  public void whenATemporaryCardIsReceived_itIsMappedAsABetStop() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_temporary_card.json", classLoader);

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);

    // Act
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getMatchEvents())
      .hasSize(1)
      .anySatisfy(matchEventDTO -> {
        assertThat(matchEventDTO.getEvent()).isEqualTo(CARD);
      });
  }

  @Test
  public void whenATemporaryCornerIsReceived_itIsMappedAsABetStop() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_temporary_corner.json", classLoader);

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);

    // Act
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getMatchEvents())
      .hasSize(1)
      .anySatisfy(matchEventDTO -> {
        assertThat(matchEventDTO.getEvent()).isEqualTo(POSSIBLE_CORNER);
      });
  }

  @Test
  public void whenATemporaryFreekickIsReceived_itIsMappedAsABetStop() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_temporary_freekick.json", classLoader);

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);

    // Act
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getMatchEvents())
      .hasSize(1)
      .anySatisfy(matchEventDTO -> {
        assertThat(matchEventDTO.getEvent()).isEqualTo(FOUL);
      });
  }

  @Test
  public void whenATemporaryOffsideIsReceived_itIsMappedAsABetStop() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_temporary_offside.json", classLoader);

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtilsMock.getTeam(anyString(), anyString())).thenReturn(TEAM_1);

    // Act
    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getMatchEvents())
      .hasSize(1)
      .anySatisfy(matchEventDTO -> {
        assertThat(matchEventDTO.getEvent()).isEqualTo(OFFSIDE);
      });
  }

  @Test
  public void whenALineUpFeedIsSent_andThereAreTwoLineUpEventsInTheMessage_thenAllPlayersInTheLineUpAreParsedCorrectly() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_two_lineups.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq("50x1m4u58lffhq6v6ga1hbxmy"))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(FIXTURE);
    String otherPlayersId = "000000000000000000000012";
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(
      playerCanned().id(new ObjectId(otherPlayersId)).build());
    String player1Id = "000000000000000000000001";
    when(optaFeedParserUtilsMock.getPlayer(eq("6ekdnbnk56xlxforb5owt3dn9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player1Id)).build());
    String player2Id = "000000000000000000000002";
    when(optaFeedParserUtilsMock.getPlayer(eq("22dchchxwrsty67ultrs61qz9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player2Id)).build());
    String player3Id = "000000000000000000000003";
    when(optaFeedParserUtilsMock.getPlayer(eq("9ej7t1srxjcxe5p627eumn6d"), any())).thenReturn(
      playerCanned().id(new ObjectId(player3Id)).build());
    String player4Id = "000000000000000000000004";
    when(optaFeedParserUtilsMock.getPlayer(eq("3n66mcws9belxbv3pu5uwhfu2"), any())).thenReturn(
      playerCanned().id(new ObjectId(player4Id)).build());
    String player5Id = "000000000000000000000005";
    when(optaFeedParserUtilsMock.getPlayer(eq("9dewnn65ylruq567thg7ebqhh"), any())).thenReturn(
      playerCanned().id(new ObjectId(player5Id)).build());
    String player6Id = "000000000000000000000006";
    when(optaFeedParserUtilsMock.getPlayer(eq("9t88fceutc2x108ga0qkzkafp"), any())).thenReturn(
      playerCanned().id(new ObjectId(player6Id)).build());
    String player7Id = "000000000000000000000007";
    when(optaFeedParserUtilsMock.getPlayer(eq("9e7b5cm66oxf26fmp33yuppsl"), any())).thenReturn(
      playerCanned().id(new ObjectId(player7Id)).build());
    String player8Id = "000000000000000000000008";
    when(optaFeedParserUtilsMock.getPlayer(eq("bkvvapojagxvj17nzk3oy8ct1"), any())).thenReturn(
      playerCanned().id(new ObjectId(player8Id)).build());
    String player9Id = "000000000000000000000009";
    when(optaFeedParserUtilsMock.getPlayer(eq("1tztnrv51q99ewl193qk526z9"), any())).thenReturn(
      playerCanned().id(new ObjectId(player9Id)).build());
    String player10Id = "000000000000000000000010";
    when(optaFeedParserUtilsMock.getPlayer(eq("3pwr7lqvo4o08c4nvzqpuue39"), any())).thenReturn(
      playerCanned().id(new ObjectId(player10Id)).build());
    String player11Id = "000000000000000000000011";
    when(optaFeedParserUtilsMock.getPlayer(eq("1km9nvqz03yoj9cxpv127id7u"), any())).thenReturn(
      playerCanned().id(new ObjectId(player11Id)).build());

    String player12Id = "000000000000000000000012";
    when(optaFeedParserUtilsMock.getPlayer(eq("6ekdnbnk56xlxforb5owt3dn8"), any())).thenReturn(
      playerCanned().id(new ObjectId(player12Id)).build());
    String player13Id = "000000000000000000000013";
    when(optaFeedParserUtilsMock.getPlayer(eq("22dchchxwrsty67ultrs61qz8"), any())).thenReturn(
      playerCanned().id(new ObjectId(player13Id)).build());
    String player14Id = "000000000000000000000014";
    when(optaFeedParserUtilsMock.getPlayer(eq("9ej7t1srxjcxe5p627eumn6c"), any())).thenReturn(
      playerCanned().id(new ObjectId(player14Id)).build());
    String player15Id = "000000000000000000000015";
    when(optaFeedParserUtilsMock.getPlayer(eq("3n66mcws9belxbv3pu5uwhfu1"), any())).thenReturn(
      playerCanned().id(new ObjectId(player15Id)).build());
    String player16Id = "000000000000000000000016";
    when(optaFeedParserUtilsMock.getPlayer(eq("9dewnn65ylruq567thg7ebqhi"), any())).thenReturn(
      playerCanned().id(new ObjectId(player16Id)).build());
    String player17Id = "000000000000000000000017";
    when(optaFeedParserUtilsMock.getPlayer(eq("9t88fceutc2x108ga0qkzkafo"), any())).thenReturn(
      playerCanned().id(new ObjectId(player17Id)).build());
    String player18Id = "000000000000000000000018";
    when(optaFeedParserUtilsMock.getPlayer(eq("9e7b5cm66oxf26fmp33yuppsk"), any())).thenReturn(
      playerCanned().id(new ObjectId(player18Id)).build());
    String player19Id = "000000000000000000000019";
    when(optaFeedParserUtilsMock.getPlayer(eq("bkvvapojagxvj17nzk3oy8ct0"), any())).thenReturn(
      playerCanned().id(new ObjectId(player19Id)).build());
    String player20Id = "000000000000000000000020";
    when(optaFeedParserUtilsMock.getPlayer(eq("1tztnrv51q99ewl193qk526z8"), any())).thenReturn(
      playerCanned().id(new ObjectId(player20Id)).build());
    String player21Id = "000000000000000000000021";
    when(optaFeedParserUtilsMock.getPlayer(eq("3pwr7lqvo4o08c4nvzqpuue38"), any())).thenReturn(
      playerCanned().id(new ObjectId(player21Id)).build());
    String player22Id = "000000000000000000000022";
    when(optaFeedParserUtilsMock.getPlayer(eq("1km9nvqz03yoj9cxpv127id7t"), any())).thenReturn(
      playerCanned().id(new ObjectId(player22Id)).build());

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(new JsonConfig().jsonObjectMapper(), optaFeedParserUtilsMock,
      eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData.size()).isEqualTo(22);
    Set<String> playerIds = playersData
      .stream()
      .map(PlayerDataDTO::getPlayerId)
      .collect(toSet());

    Set<String> expectedPlayerIds = Set.of(player1Id, player2Id, player3Id, player4Id, player5Id, player6Id, player7Id,
      player8Id, player9Id, player10Id, player11Id, player12Id, player13Id, player14Id, player15Id, player16Id,
      player17Id, player18Id, player19Id, player20Id, player21Id, player22Id);

    assertThat(playerIds).containsAll(expectedPlayerIds);
  }

  @Test
  public void whenAFeedWithMultipleEventsIsReceived_andALineUpEventIsPresent_thenAllEventsAreProcessed() throws IOException {
    // TODO Red test: Understand what to do if we have a player without any event. Team is not present what should we do?
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma18dp_multiple_events.json", classLoader);

    when(optaFeedParserUtilsMock.getTeam(anyString(), eq("a3nyxabgsqlnqfkeg41m6tnpp"))).thenReturn(TEAM_1);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq("b496gs285it6bheuikox6z9mj"))).thenReturn(TEAM_2);
    Fixture fixture = fixtureCanned()
      .homeTeam(teamCanned().optaId("a3nyxabgsqlnqfkeg41m6tnpp").build())
      .awayTeam(teamCanned().optaId("b496gs285it6bheuikox6z9mj").build())
      .build();
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);
    String player1Id = "000000000000000000000001";
    when(optaFeedParserUtilsMock.getPlayer(eq("eo4uva2gnx08xdazfaqzyz2dx"), any())).thenReturn(
      playerCanned().id(new ObjectId(player1Id)).build());
    String player2Id = "000000000000000000000002";
    when(optaFeedParserUtilsMock.getPlayer(eq("2t5s4f41jefzuzth0bhcixtcl"), any())).thenReturn(
      playerCanned().id(new ObjectId(player2Id)).build());

    MA18DPFeedParser ma18FeedParser = new MA18DPFeedParser(objectMapper, optaFeedParserUtilsMock, eventsMapper);
    MatchDataFeed matchDataFeed = ma18FeedParser.parseFeed(bytes);

    List<PlayerDataDTO> playersData = matchDataFeed.getPlayersData();
    assertThat(playersData).hasSize(16);
    assertThat(playersData)
      .filteredOn(playerDataDTO -> !playerDataDTO.isUnknown())
      .hasSize(2);
    assertThat(playersData)
      .filteredOn(PlayerDataDTO::isUnknown)
      .hasSize(14);

    // Assert
    assertThat(matchDataFeed.getFeedPlayerMatchEvents()).hasSize(68);
  }
}
