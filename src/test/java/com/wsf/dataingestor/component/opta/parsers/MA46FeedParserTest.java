package com.wsf.dataingestor.component.opta.parsers;

import java.nio.charset.Charset;
import java.util.function.Function;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.opta.parsers.MA46FeedParser;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.FeedContestantUnavailability;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;

import static com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason.INJURY;
import static com.wsf.dataingestor.opta.usecases.contestantunavailability.models.ContestantUnavailabilitiesDataFeed.UnavailabilityReason.SUSPENSION;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class MA46FeedParserTest extends TestWithMocks {

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  private MA46FeedParser ma46FeedParser;

  @Before
  public void setUp() throws Exception {
    ma46FeedParser = new MA46FeedParser(new JsonConfig().jsonObjectMapper(), optaFeedParserUtilsMock);
  }

  @Test
  public void whenAJsonFeedIsParsed_thenTheBuiltParsedObjectIsTheSame() throws Exception {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/opta/ma46/ma46_provisional.json", Charset.defaultCharset(),
      classLoader);

    var tournamentCalendarOptaId = "b25u56idqlgo8s1rahhltqd5g";

    var cerriOptaPlayerId = "209wrczpg1xivjis7rmntw0r9";
    when(
      optaFeedParserUtilsMock.getPlayerFromTournament(eq(cerriOptaPlayerId), eq(tournamentCalendarOptaId))).thenReturn(
      PlayerCanned.defaultPlayer());

    var koneOptaPlayerId = "a8dm3433l77bkgla902imcr56";
    when(
      optaFeedParserUtilsMock.getPlayerFromTournament(eq(koneOptaPlayerId), eq(tournamentCalendarOptaId))).thenReturn(
      PlayerCanned.defaultPlayer());

    var alvarezOptaPlayerId = "1to07m18m7wqroewvyxzrz0lx";
    when(optaFeedParserUtilsMock.getPlayerFromTournament(eq(alvarezOptaPlayerId),
      eq(tournamentCalendarOptaId))).thenReturn(PlayerCanned.defaultPlayer());

    var circatiOptaPlayerId = "c1sj6aogyfpm5wqvhd2bli7e";
    when(optaFeedParserUtilsMock.getPlayerFromTournament(eq(circatiOptaPlayerId),
      eq(tournamentCalendarOptaId))).thenReturn(PlayerCanned.defaultPlayer());

    var kowalskiOptaPlayerId = "8535b50trr70zrcs7lnmg1b10";
    when(optaFeedParserUtilsMock.getPlayerFromTournament(eq(kowalskiOptaPlayerId),
      eq(tournamentCalendarOptaId))).thenReturn(PlayerCanned.defaultPlayer());

    var coulibalyOptaPlayerId = "68iplmlkdp5wkgnlnnanj9be1";
    when(optaFeedParserUtilsMock.getPlayerFromTournament(eq(coulibalyOptaPlayerId),
      eq(tournamentCalendarOptaId))).thenReturn(PlayerCanned.defaultPlayer());

    // Act
    var parsedFeed = ma46FeedParser.parseFeed(feed);

    // Assert
    assertThat(parsedFeed.feedContestantUnavailabilities())
      .hasSize(6)
      .map(FeedContestantUnavailability::unavailabilities)
      .flatMap(Function.identity())
      .filteredOn(problem -> problem.reason() == INJURY)
      .hasSize(5);

    assertThat(parsedFeed.feedContestantUnavailabilities())
      .hasSize(6)
      .map(FeedContestantUnavailability::unavailabilities)
      .flatMap(Function.identity())
      .filteredOn(problem -> problem.reason() == SUSPENSION)
      .hasSize(1);
  }
}