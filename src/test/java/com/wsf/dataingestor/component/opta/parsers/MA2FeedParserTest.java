package com.wsf.dataingestor.component.opta.parsers;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.assertj.core.api.Assertions;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.ma2.MA2FeedParser;
import com.wsf.dataingestor.opta.parsers.ma2.StatsMapper;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.dataingestor.shared.canned.TeamCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.MatchPeriod;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.PLAYED;
import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_PLAYER_STATS_FINAL;
import static com.wsf.dataingestor.opta.parsers.ma2.StatsConstants.OPTA_TO_WSF_TEAM_STATS_FINAL;
import static com.wsf.dataingestor.shared.canned.FixtureCanned.fixtureCanned;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_10_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_1_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_2_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_3_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_4_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_5_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_6_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_7_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_8_ID;
import static com.wsf.dataingestor.shared.canned.PlayerCanned.PLAYER_9_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.dataingestor.sports.soccer.Constants.MINS_PLAYED;
import static com.wsf.dataingestor.sports.soccer.Constants.TEAM_CORNERS;
import static com.wsf.domain.soccer.SoccerMatchEvent.ASSIST_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.RED_CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_OFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.SUB_ON;
import static com.wsf.domain.soccer.SoccerMatchEvent.YELLOW_CARD;
import static java.lang.String.format;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class MA2FeedParserTest extends TestWithMocks {

  private static final Player PLAYER_1 = PlayerCanned.defaultPlayer(PLAYER_1_ID);
  private static final Player PLAYER_2 = PlayerCanned.defaultPlayer(PLAYER_2_ID);
  private static final Player PLAYER_3 = PlayerCanned.defaultPlayer(PLAYER_3_ID);
  private static final Player PLAYER_4 = PlayerCanned.defaultPlayer(PLAYER_4_ID);
  private static final Player PLAYER_5 = PlayerCanned.defaultPlayer(PLAYER_5_ID);
  private static final Player PLAYER_6 = PlayerCanned.defaultPlayer(PLAYER_6_ID);
  private static final Player PLAYER_7 = PlayerCanned.defaultPlayer(PLAYER_7_ID);
  private static final Player PLAYER_8 = PlayerCanned.defaultPlayer(PLAYER_8_ID);
  private static final Player PLAYER_9 = PlayerCanned.defaultPlayer(PLAYER_9_ID);
  private static final Player PLAYER_10 = PlayerCanned.defaultPlayer(PLAYER_10_ID);

  private static final Team TEAM_1 = TeamCanned.defaultTeam(TEAM1_ID);

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  private MA2FeedParser ma2FeedParser;

  @Before
  public void setup() {
    mockTeams();
    mockPlayers();

    ma2FeedParser = new MA2FeedParser(new JsonConfig().jsonObjectMapper(),
      new StatsMapper(OPTA_TO_WSF_PLAYER_STATS_FINAL, OPTA_TO_WSF_TEAM_STATS_FINAL), optaFeedParserUtilsMock);
  }

  @Test
  public void parseFeed() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_final.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r"))
      .awayTeam(defaultTeam(TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getDate(), is(Instant.parse("2021-08-14T19:00:00Z")));
    assertThat(matchDataFeed.getMatchTimeMin(), is(95));
    assertThat(matchDataFeed.getFixtureStatus(), is(PLAYED));
    assertThat(matchDataFeed.isExtraTimeHappened(), is(false));

    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size(), is(31));

    assertThat(playerIndexes
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count(), is((long) 31));

    for (PlayerDataDTO playerDataDTO : playerIndexes) {
      assertNotNull(playerDataDTO.getPlayer());
      assertNotNull(playerDataDTO.getStats());
    }

    assertEquals(Set.of(GOAL, ASSIST_GOAL, YELLOW_CARD, RED_CARD, SUB_OFF, SUB_ON),
      matchDataFeed.getSupportedEventTypes());

    List<EntityEventDTO> playerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    List<EntityEventDTO> scorers = playerMatchEvents
      .stream()
      .filter(e -> e.getEventType() == GOAL)
      .toList();
    assertThat(scorers.size(), is(6));

    List<EntityEventDTO> assistPlayers = playerMatchEvents
      .stream()
      .filter(e -> e.getEventType() == ASSIST_GOAL)
      .toList();
    assertThat(assistPlayers.size(), is(5));

    List<EntityEventDTO> sentOff = playerMatchEvents
      .stream()
      .filter(e -> e.getEventType() == RED_CARD)
      .toList();
    assertThat(sentOff.size(), is(1));

    Map<String, Integer> expectedEventIdToPeriodId = Map.of("2318814599_2b3mar72yy8d6uvat1ka6tn3r", 1,
      "2318835129_2b3mar72yy8d6uvat1ka6tn3r", 1, "2318836127_2b3mar72yy8d6uvat1ka6tn3r", 1,
      "2318875995_71ajatxnuhc5cnm3xvgdky49w", 3, "2318887465_71ajatxnuhc5cnm3xvgdky49w", 3,
      "2318905909_2b3mar72yy8d6uvat1ka6tn3r", 3);

    scorers.forEach(scorer -> {
      String eventId = scorer.getEventId();
      int expectedPeriodId = scorer.getPeriod().getPeriodId();
      assertThat(format("eventId=%s should have periodId=%s", eventId, expectedPeriodId), expectedPeriodId,
        is(expectedEventIdToPeriodId.get(eventId)));
      assertFalse(scorer.isOnBench());
    });

    Map<String, Number> firstTeamStats = matchDataFeed.getTeamsData().get(0).getStats();
    Map<String, Number> secondTeamStats = matchDataFeed.getTeamsData().get(1).getStats();

    assertThat(firstTeamStats.get(TEAM_CORNERS), is(4));
    assertThat(secondTeamStats.get(TEAM_CORNERS), is(4));
  }

  @Test
  public void parseFeedWithExtraTimes() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_final_with_extra_times.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r"))
      .awayTeam(defaultTeam(TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    assertThat(matchDataFeed.getDate(), is(Instant.parse("2021-08-14T19:00:00Z")));
    assertThat(matchDataFeed.getMatchTimeMin(), is(95));
    assertThat(matchDataFeed.getFixtureStatus(), is(PLAYED));
    assertThat(matchDataFeed.isExtraTimeHappened(), is(true));
  }

  @Test
  public void whenAFeedIsReceived_andAPlayerCannotBeFound_thenTheCorrectTeamIsAssignedToEventsOfThatPlayer() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_final.json");

    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(null);

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r"))
      .awayTeam(defaultTeam(TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    List<EntityEventDTO> playerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    Assertions
      .assertThat(playerMatchEvents)
      .allMatch(event -> Set
        .of(TEAM1_ID.toString(), TEAM2_ID.toString())
        .contains(event.getTeamId()));
  }

  @Test
  public void parseFeed_halfTime() throws IOException {
    byte[] bytes = loadFile("feeds/opta/ma2_halftime.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r"))
      .awayTeam(defaultTeam(TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    assertThat(matchDataFeed.getDate(), is(Instant.parse("2021-08-14T19:00:00Z")));
    assertNull(matchDataFeed.getMatchTimeMin());
    assertThat(matchDataFeed.getFixtureStatus(), is(LIVE));
    assertThat(matchDataFeed.getMatchPeriod(), is(MatchPeriod.HALF_TIME));

    List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
    assertThat(playerIndexes.size(), is(23));

    assertThat(playerIndexes
      .stream()
      .filter(PlayerDataDTO::isHasPlayed)
      .count(), is((long) 23));

    for (PlayerDataDTO playerDataDTO : playerIndexes) {
      assertNotNull(playerDataDTO.getPlayer());
      assertNotNull(playerDataDTO.getStats());
      assertNull(playerDataDTO.getStats().get(MINS_PLAYED));
    }
  }

  @Test
  public void whenEventsAreReceived_thenTheExpectedEventsAreParsed() throws IOException {
    byte[] bytes = loadFile("feeds/opta/ma2_second_yellow_no_yellow_card.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2b3mar72yy8d6uvat1ka6tn3r"))
      .awayTeam(defaultTeam(TEAM2_ID, "71ajatxnuhc5cnm3xvgdky49w"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    List<EntityEventDTO> aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    List<EntityEventDTO> montielYCEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_3_ID.toString()))
      .filter(e -> e.getEventType() == YELLOW_CARD)
      .toList();

    assertThat(montielYCEvents.size(), is(1));

    EntityEventDTO firstYellowCardEvent = montielYCEvents.get(0);
    assertThat(firstYellowCardEvent.getPeriod(), is(MatchPeriod.SECOND_HALF));
    assertThat(firstYellowCardEvent.getTimeMin(), is(99));
    assertFalse(firstYellowCardEvent.isOnBench());

    List<EntityEventDTO> montielRedCardEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_3_ID.toString()))
      .filter(e -> e.getEventType() == RED_CARD)
      .toList();

    assertThat(montielRedCardEvents.size(), is(1));

    List<EntityEventDTO> xavierGoalEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_7_ID.toString()))
      .filter(e -> e.getEventType() == GOAL)
      .toList();

    EntityEventDTO goalEvent = xavierGoalEvents.get(0);
    assertThat(xavierGoalEvents.size(), is(1));
    assertThat(goalEvent.getPeriod(), is(MatchPeriod.SECOND_HALF));
    assertThat(goalEvent.getTimeMin(), is(86));
    assertFalse(goalEvent.isOnBench());

    List<EntityEventDTO> kennedyAssistEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_8_ID.toString()))
      .filter(e -> e.getEventType() == ASSIST_GOAL)
      .toList();

    EntityEventDTO assistEvent = kennedyAssistEvents.get(0);
    assertThat(xavierGoalEvents.size(), is(1));
    assertThat(assistEvent.getPeriod(), is(MatchPeriod.SECOND_HALF));
    assertThat(assistEvent.getTimeMin(), is(86));
    assertFalse(assistEvent.isOnBench());
  }

  @Test
  public void parseFeedWhenMatchIsPostponedBeforeStarting() throws IOException {
    byte[] bytes = loadFile("feeds/opta/ma2_postponed.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "4gi4qw4gt6nfq0c6r8y574qgp"))
      .awayTeam(defaultTeam(TEAM2_ID, "cvrl5bces32x6fup1ks1hvets"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    assertTrue(matchDataFeed.isPostponed());
    assertTrue(matchDataFeed.getPlayersData()
      .isEmpty());
    assertTrue(matchDataFeed.getTeamsData()
      .isEmpty());
  }

  @Test
  public void whenEventsAreReceived_thenWeParserSubstitutes() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_substitution.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2tk2l9sgktwc9jhzqdd4mpdtb"))
      .awayTeam(defaultTeam(TEAM2_ID, "6tuibxq39fdryu8ou06wcm0q3"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    var aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    var substituteEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(event -> event.getEventType() == SUB_OFF || event.getEventType() == SUB_ON)
      .collect(Collectors.groupingBy(EntityEventDTO::getEventId));

    assert !substituteEvents.isEmpty();
    substituteEvents.values()
      .forEach(eventsList -> {
        assertEquals(2, eventsList.size());
        String firstEventType = eventsList.get(0).getEventType().getStatisticName();
        String secondEventType = eventsList.get(1).getEventType().getStatisticName();
        assertNotEquals(firstEventType, secondEventType);
      });
  }

  @Test
  public void whenEventsAreReceived_andPlayerGotYCOnTheBenchAfterHisSubOff_thenYellowCardIsIncludedWithOnBenchFieldTrue() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_yc_after_substitution.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2tk2l9sgktwc9jhzqdd4mpdtb"))
      .awayTeam(defaultTeam(TEAM2_ID, "6tuibxq39fdryu8ou06wcm0q3"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    List<EntityEventDTO> aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    List<EntityEventDTO> elShaarawyYCEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_9_ID.toString()))
      .filter(e -> e.getEventType() == YELLOW_CARD)
      .toList();

    assertThat(elShaarawyYCEvents.size(), is(1));
    assertTrue(elShaarawyYCEvents.get(0).isOnBench());
  }

  @Test
  public void whenEventsAreReceived_andPlayerGotYCOnTheBenchBeforeHisSubIn_thenYellowCardIsIncludedWithOnBenchFieldTrue() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_yc_before_substitution.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2tk2l9sgktwc9jhzqdd4mpdtb"))
      .awayTeam(defaultTeam(TEAM2_ID, "6tuibxq39fdryu8ou06wcm0q3"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    List<EntityEventDTO> aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    List<EntityEventDTO> elShaarawyYCEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_9_ID.toString()))
      .filter(e -> e.getEventType() == YELLOW_CARD)
      .toList();

    assertThat(elShaarawyYCEvents.size(), is(1));
    assertTrue(elShaarawyYCEvents.get(0).isOnBench());
  }

  @Test
  public void whenEventsAreReceived_andPlayerGotYCOnTheBench_andHeDidNotEnterThePitch_thenYellowCardIsIncludedWithOnBenchFieldTrue() throws IOException {
    // Arrange
    byte[] bytes = loadFile("feeds/opta/ma2_yc_on_bench.json");

    Fixture fixture = fixtureCanned()
      .homeTeam(defaultTeam(TEAM1_ID, "2tk2l9sgktwc9jhzqdd4mpdtb"))
      .awayTeam(defaultTeam(TEAM2_ID, "6tuibxq39fdryu8ou06wcm0q3"))
      .build();

    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);

    // Act
    MatchDataFeed matchDataFeed = ma2FeedParser.parseFeed(bytes);

    // Assert
    List<EntityEventDTO> aggregatedPlayerMatchEvents = matchDataFeed.getAggregatedPlayerMatchEvents();
    List<EntityEventDTO> elShaarawyYCEvents = aggregatedPlayerMatchEvents
      .stream()
      .filter(e -> e.getEntityId().equals(PLAYER_10_ID.toString()))
      .filter(e -> e.getEventType() == YELLOW_CARD)
      .toList();

    assertThat(elShaarawyYCEvents.size(), is(1));
    assertTrue(elShaarawyYCEvents.get(0).isOnBench());
  }

  private void mockTeams() {
    when(optaFeedParserUtilsMock.getTeam(anyString(), any())).thenReturn(TEAM_1);
  }

  private void mockPlayers() {
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenAnswer(invocation -> {
      String optaPlayerId = invocation.getArgument(0, String.class);
      return PlayerCanned.createPlayer().id(ObjectId.get()).optaPlayerId(optaPlayerId).build();
    });
    when(optaFeedParserUtilsMock.getPlayer(eq("drkx7msmzruq5myxk9ani6z11"), any())).thenReturn(PLAYER_1);
    when(optaFeedParserUtilsMock.getPlayer(eq("4pyehox7p3z64c4l3k8yoyfv9"), any())).thenReturn(PLAYER_2);
    when(optaFeedParserUtilsMock.getPlayer(eq("b5lbifwyixas23f0orh10xvmy"), any())).thenReturn(PLAYER_3); // Montiel
    when(optaFeedParserUtilsMock.getPlayer(eq("b8yk8yve4u39s9gjjvedzf6l0"), any())).thenReturn(PLAYER_4); // Redondo
    when(optaFeedParserUtilsMock.getPlayer(eq("37bl9v0zpux1tjoutqz121u51"), any())).thenReturn(PLAYER_5); // Cabrera
    when(optaFeedParserUtilsMock.getPlayer(eq("6mgrw5ld3fdg7vubcfuzravpw"), any())).thenReturn(PLAYER_6); // Minissale
    when(optaFeedParserUtilsMock.getPlayer(eq("51xii47wyc6cefmrwoijpdvbp"), any())).thenReturn(PLAYER_7); // Xavier
    when(optaFeedParserUtilsMock.getPlayer(eq("83jg484gnx01poxy7w94toges"), any())).thenReturn(PLAYER_8); // Kennedy
    when(optaFeedParserUtilsMock.getPlayer(eq("a1iz91ed6hus61yhhk2r0vkd1"), any())).thenReturn(PLAYER_9); // El Shaarawy
    when(optaFeedParserUtilsMock.getPlayer(eq("cfmha2icj5q0mp8ecajlbxb84"), any())).thenReturn(PLAYER_10); // Joao Costa
  }

  private byte[] loadFile(String name) throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    return IOUtils.resourceToByteArray(name, classLoader);
  }
}
