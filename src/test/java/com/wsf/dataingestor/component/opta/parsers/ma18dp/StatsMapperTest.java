package com.wsf.dataingestor.component.opta.parsers.ma18dp;

import java.util.Map;
import org.junit.Test;


import com.wsf.dataingestor.opta.parsers.ma18dp.StatsMapper;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.is;

public class StatsMapperTest {

  @Test
  public void testTranslateTeamStatsNoSnapshot() {
    Map<String, String> feedStats = Map.of("fkFoulLost", "1");

    Map<String, Number> stats = StatsMapper.translateTeamStats(feedStats, true);

    assertThat(stats.keySet(), is(empty()));
  }

  @Test
  public void testTranslateTeamStatsSnapshot() {
    Map<String, String> feedStats = Map.of();

    Map<String, Number> stats = StatsMapper.translateTeamStats(feedStats, true);

    assertThat(stats.keySet(), is(empty()));
  }
}
