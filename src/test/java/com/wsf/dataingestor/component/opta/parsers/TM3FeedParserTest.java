package com.wsf.dataingestor.component.opta.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.SquadPlayerDTO;
import com.wsf.dataingestor.models.SquadsFeed;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.TM3FeedParser;
import com.wsf.dataingestor.shared.TestWithMocks;

import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.util.Optional.of;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class TM3FeedParserTest extends TestWithMocks {

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  private TM3FeedParser tm3FeedParser;

  @Before
  public void setup() {
    tm3FeedParser = new TM3FeedParser(new JsonConfig().jsonObjectMapper(), optaFeedParserUtilsMock);
  }

  @Test
  public void parseSquadsData() throws IOException {
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/tm3.json", Charset.defaultCharset(), classLoader);

    when(optaFeedParserUtilsMock.getTournament(anyString())).thenReturn(of(tournamentCanned().build()));

    SquadsFeed squadsFeed = tm3FeedParser.parseFeed(result);

    assertEquals(696, squadsFeed.getSquadPlayers()
      .size());

    for (SquadPlayerDTO squadPlayerDTO : squadsFeed.getSquadPlayers()) {
      assertNotNull(squadPlayerDTO.getLastName());
      assertNotNull(squadPlayerDTO.getTeam().getExternalTeamAbbreviation());
    }
  }
}
