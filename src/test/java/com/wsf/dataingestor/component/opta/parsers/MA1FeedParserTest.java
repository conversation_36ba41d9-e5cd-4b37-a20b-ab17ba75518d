package com.wsf.dataingestor.component.opta.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.FixtureDTO;
import com.wsf.dataingestor.models.FixturesFeed;
import com.wsf.dataingestor.opta.parsers.MA1FeedParser;
import com.wsf.dataingestor.services.TournamentService;
import com.wsf.dataingestor.shared.TestWithMocks;

import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static java.util.Optional.of;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class MA1FeedParserTest extends TestWithMocks {
  @Mock
  TournamentService tournamentServiceMock;

  private MA1FeedParser ma1FeedParser;

  @Before
  public void setup() {
    ma1FeedParser = new MA1FeedParser(new JsonConfig().jsonObjectMapper(), tournamentServiceMock);
    when(tournamentServiceMock.findByOptaExternalId(anyString())).thenReturn(of(tournamentCanned().build()));
  }

  @Test
  public void parseMatchesData() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/ma1.json", Charset.defaultCharset(), classLoader);
    MA1FeedParser ma1FeedParser = new MA1FeedParser(new JsonConfig().jsonObjectMapper(), tournamentServiceMock);

    // Act
    FixturesFeed fixturesFeed = ma1FeedParser.parseFeed(result);

    // Assert
    assertEquals(552, fixturesFeed.getFixtures()
      .size());
    List<FixtureDTO> fixtures = fixturesFeed.getFixtures();
    Set<String> notNeutralVenueFixturesIds = Set.of("5aq4s6ggrbj5qfz6x7a39xgd0", "5avsoxt1ppnip5jn2i6wiwf10");
    for (FixtureDTO fixtureDTO : fixtures) {
      assertNotNull(fixtureDTO.getExternalHomeTeamId());
      assertNotNull(fixtureDTO.getExternalAwayTeamId());
      assertNotNull(fixtureDTO.getLastUpdated());
      assertNotNull(fixtureDTO.getTime());
      assertNotNull(fixtureDTO.getIsNeutralVenue());
      if (notNeutralVenueFixturesIds.contains(fixtureDTO.getExternalFixtureId())) {
        assertFalse(fixtureDTO.getIsNeutralVenue());
        assertTrue(fixtureDTO.getIsLiveSupported());
      } else {
        assertTrue(fixtureDTO.getIsNeutralVenue());
      }
    }
  }

  @Test
  public void parseWOC() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/ma1_woc.json", Charset.defaultCharset(), classLoader);

    // Act
    FixturesFeed fixturesFeed = ma1FeedParser.parseFeed(result);

    // Assert
    assertEquals(10, fixturesFeed.getFixtures()
      .size());
    for (FixtureDTO fixtureDTO : fixturesFeed.getFixtures()) {
      assertNotNull(fixtureDTO.getExternalHomeTeamId());
      assertNotNull(fixtureDTO.getExternalAwayTeamId());
      assertNotNull(fixtureDTO.getLastUpdated());
      assertNotNull(fixtureDTO.getTime());
    }
    assertEquals(6, fixturesFeed.getFixtures()
      .stream()
      .filter(FixtureDTO::getCanGoExtraTime)
      .count());
  }

  @Test
  public void parseEuropa() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/ma1_europa.json", Charset.defaultCharset(), classLoader);

    // Act
    FixturesFeed fixturesFeed = ma1FeedParser.parseFeed(result);

    // Assert
    var fixtureMap = fixturesFeed.getFixtures()
      .stream()
      .collect(Collectors.toMap(FixtureDTO::getExternalFixtureId, Function.identity()));

    {
      var fixture = fixtureMap.get("4xe59xif5vtju3334zh9g77kk");
      assertThat(fixture.getExternalRelatedFixtureId(), is("dzkuoybttnobgpkkysazggduc"));
      assertThat(fixture.getLeg(), is(FixtureDTO.Relation.FIRST_LEG));
    }
    {
      var fixture = fixtureMap.get("dzkuoybttnobgpkkysazggduc");
      assertThat(fixture.getExternalRelatedFixtureId(), is("4xe59xif5vtju3334zh9g77kk"));
      assertThat(fixture.getLeg(), is(FixtureDTO.Relation.SECOND_LEG));
    }
    {
      var fixture = fixtureMap.get("2tbj0w9qef5aeqjicojh9jpqs");
      assertNull(fixture.getExternalRelatedFixtureId());
      assertNull(fixture.getLeg());
    }
  }

  @Test
  public void parseFixtureWithNoVenue() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/ma1_no_venue.json", Charset.defaultCharset(), classLoader);
    MA1FeedParser ma1FeedParser = new MA1FeedParser(new JsonConfig().jsonObjectMapper(), tournamentServiceMock);

    // Act
    FixturesFeed fixturesFeed = ma1FeedParser.parseFeed(result);

    // Assert
    assertEquals(552, fixturesFeed.getFixtures()
      .size());
    List<FixtureDTO> fixtures = fixturesFeed.getFixtures();
    for (FixtureDTO fixtureDTO : fixtures) {
      assertNotNull(fixtureDTO.getExternalHomeTeamId());
      assertNotNull(fixtureDTO.getExternalAwayTeamId());
      assertNotNull(fixtureDTO.getLastUpdated());
      assertNotNull(fixtureDTO.getTime());
      if (fixtureDTO.getExternalFixtureId().equals("5aq4s6ggrbj5qfz6x7a39xgd0")) {
        assertFalse(fixtureDTO.getIsNeutralVenue());
      } else {
        assertNotNull(fixtureDTO.getIsNeutralVenue());
      }
    }
  }

  @Test
  public void parseLigue1() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String result = IOUtils.resourceToString("feeds/opta/ma1_ligue1.json", Charset.defaultCharset(), classLoader);
    MA1FeedParser ma1FeedParser = new MA1FeedParser(new JsonConfig().jsonObjectMapper(), tournamentServiceMock);

    // Act
    FixturesFeed fixturesFeed = ma1FeedParser.parseFeed(result);

    // Assert
    List<FixtureDTO> fixtures = fixturesFeed.getFixtures();
    for (FixtureDTO fixtureDTO : fixtures) {
      assertNotNull(fixtureDTO.getExternalHomeTeamAbbreviation());
      assertNotNull(fixtureDTO.getExternalAwayTeamAbbreviation());
    }
  }
}

