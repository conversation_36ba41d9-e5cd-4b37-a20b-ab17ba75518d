package com.wsf.dataingestor.component.opta.clients.handlers;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import javax.naming.AuthenticationException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketError;
import com.neovisionaries.ws.client.WebSocketException;
import com.neovisionaries.ws.client.WebSocketFrame;
import com.wsf.dataingestor.clients.ws.WebSocketManager;
import com.wsf.dataingestor.clients.ws.handlers.ReconnectHandler;
import com.wsf.dataingestor.clients.ws.handlers.WSHandler;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.opta.clients.handlers.SDDPHandler;
import com.wsf.dataingestor.shared.TestWithMocks;

import static com.wsf.dataingestor.clients.ws.handlers.ReconnectHandler.MaxNrRetriesExceeded;
import static java.util.Collections.emptyMap;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Slf4j
public class SDDPHandlerTest extends TestWithMocks {

  private static final String fixtureId = "fixture1";
  private static final String outlet = "outletJson";
  private static final String feedName = "stats";

  @Mock
  public ObjectMapper objectMapperMock;

  @Mock
  public ReconnectHandler reconnectHandlerMock;

  @Mock
  public FunctionMock feedHandlerMock;

  @Mock
  public ConsumerMock subscribeMock;

  @Mock
  public RunnableMock unsubscribeMock;

  @Mock
  public BiConsumerMock errorMock;

  @Mock
  public WebSocket webSocketMock;

  private WSHandler handler;

  @Before
  public void setUp() {
    handler = buildHandler();
  }

  @Test
  public void whenWebSocketIsConnected_thenTheOutletIsSentAsTextMessage() throws Exception {
    handler.onConnected(webSocketMock, emptyMap());

    verify(webSocketMock).sendText(eq(outlet));
  }

  @Test
  public void whenWebSocketIsDisconnected_andTheConnectionIsClosedByTheServer_thenTheReconnectHandlerIsCalled() throws Exception {
    handler.onDisconnected(webSocketMock, new WebSocketFrame(), new WebSocketFrame(), true);

    verify(reconnectHandlerMock).reconnect(eq(webSocketMock));
  }

  @Test
  public void whenWebSocketIsDisconnected_andTheConnectionIsClosedByTheServer_andMaxTriesAreReached_thenTheErrorHandlerIsCalled() throws Exception {
    MaxNrRetriesExceeded maxNrRetriesExceeded = new MaxNrRetriesExceeded();
    doThrow(maxNrRetriesExceeded)
      .when(reconnectHandlerMock).reconnect(any(WebSocket.class));

    handler.onDisconnected(webSocketMock, new WebSocketFrame(), new WebSocketFrame(), true);

    verify(errorMock).apply(anyString(), eq(maxNrRetriesExceeded));
  }

  @Test
  public void whenWebSocketIsDisconnected_andTheConnectionIsClosedByTheServer_andIOExceptionIsThrown_thenTheErrorHandlerIsCalled() throws Exception {
    IOException exception = new IOException();
    doThrow(exception)
      .when(reconnectHandlerMock).reconnect(any(WebSocket.class));

    handler.onDisconnected(webSocketMock, new WebSocketFrame(), new WebSocketFrame(), true);

    verify(errorMock).apply(anyString(), eq(exception));
  }

  @Test
  public void whenAnErrorIsReturned_thenTheOnErrorHandlerIsCalled() throws Exception {
    WebSocketException exceptionMock = new WebSocketException(WebSocketError.IO_ERROR_IN_READING, "error");
    handler.onError(webSocketMock, exceptionMock);

    verify(errorMock).apply(eq("error"), eq(exceptionMock));
  }

  @Test
  public void whenATextMessageIsReceived_andClientIsNotSubscribed_andAnErrorIsThrownParsingTheResponse_thenTheOnErrorHandlerIsCalled() throws Exception {
    IOException exception = new IOException();
    doThrow(exception)
      .when(objectMapperMock).readTree(any(byte[].class));

    handler.onTextMessage(webSocketMock, "message".getBytes());

    verify(errorMock).apply(anyString(), eq(exception));
  }

  @Test
  public void whenASeqIdIsPresent_andATextMessageIsReceived_andClientIsNotSubscribed_andItGetsAuthorized_thenTheClientSubscribesToTheFixture() throws Exception {
    String respJson = setupAuthorized(true);
    int seqId = 10;

    handler.updateSeqId(seqId);
    handler.onTextMessage(webSocketMock, respJson.getBytes());

    ArgumentCaptor<String> subscribeReqCapt = ArgumentCaptor.forClass(String.class);

    verify(webSocketMock).sendText(subscribeReqCapt.capture());

    JsonNode jsonNode = new ObjectMapper().readTree(subscribeReqCapt.getValue());

    jsonNode = jsonNode.get("content");

    JsonNode feed = jsonNode.get("feed").get(0);

    assertThat(jsonNode.get("name").asText(), is("subscribe"));
    assertThat(feed.asText(), is("stats"));
    assertThat(jsonNode.get("fixtureUuid").asText(), is(fixtureId));

    JsonNode snapshot = jsonNode.get("snapShot").get(0);
    assertThat(snapshot.get("feed").asText(), is("matchEvent"));
    assertThat(snapshot.get("seqId").asInt(), is(seqId));
  }

  @Test
  public void whenATextMessageIsReceived_andClientIsNotSubscribed_andItDoesNotGetAuthorized_thenTheErrorHandlerIsCalled() throws Exception {
    String respJson = setupAuthorized(false);

    handler.onTextMessage(webSocketMock, respJson.getBytes());

    verify(errorMock).apply(anyString(), any(AuthenticationException.class));
    verify(webSocketMock).disconnect();
  }

  @Test
  public void whenATextMessageIsReceived_andThereIsAParsingError_thenTheErrorHandlerIsCalled() throws Exception {
    doThrow(IOException.class)
      .when(objectMapperMock).readTree(any(byte[].class));

    handler.onTextMessage(webSocketMock, "test".getBytes());

    verify(errorMock).apply(anyString(), any(IOException.class));
    verify(webSocketMock).disconnect();
  }

  @Test
  public void whenASubscriptionPositiveResponseIsReceived_andClientIsNotSubscribed_thenTheOnSubscribeHandlerIsCalled() throws Exception {
    String respJson = setupSubscribed(true);

    handler.onTextMessage(webSocketMock, respJson.getBytes());

    verify(subscribeMock).apply(any());
  }

  @Test
  public void whenASubscriptionNegativeResponseIsReceived_andClientIsNotSubscribed_thenTheOnErrorHandlerIsCalled() throws Exception {
    String respJson = setupSubscribed(false);

    handler.onTextMessage(webSocketMock, respJson.getBytes());

    verify(errorMock).apply(anyString(), any(IllegalStateException.class));
  }

  @Test
  public void whenAFeedResponseIsReceived_andClientIsSubscribed_thenTheFeedHandlerIsCalled() throws Exception {
    String respJson = setupSubscribed(true);

    handler.onTextMessage(webSocketMock, respJson.getBytes());

    byte[] feed = "feedData".getBytes();

    handler.onTextMessage(webSocketMock, feed);

    verify(feedHandlerMock).apply(eq(feed));
  }

  private String setupAuthorized(boolean isAuthorized) throws IOException {
    String respJson = String.format("{\"outlet\": { \"msg\": \"%s\" }}",
      isAuthorized ? "is_authorised" : "not_authorised");

    JsonNode authRespNode = new ObjectMapper().readTree(respJson);

    when(objectMapperMock.readTree(any(byte[].class))).thenReturn(authRespNode);
    return respJson;
  }

  private String setupSubscribed(boolean isSubscribed) throws IOException {
    String respJson = String.format("{\"content\": { \"msg\": \"%s\" }}",
      isSubscribed ? "is_subscribed" : "is_unsubscribed");

    JsonNode authRespNode = new ObjectMapper().readTree(respJson);

    when(objectMapperMock.readTree(any(byte[].class))).thenReturn(authRespNode);
    return respJson;
  }

  private SDDPHandler buildHandler() {
    return SDDPHandler
      .builder()
      .optaFixtureId(fixtureId)
      .feedName(feedName)
      .outlet(outlet)
      .jsonObjectMapper(objectMapperMock)
      .reconnectHandler(reconnectHandlerMock)
      .feedHandler(feedHandlerMock::apply)
      .onSubscribeHandler(subscribeMock::apply)
      .onUnsubscribeHandler(unsubscribeMock::apply)
      .onErrorHandler(errorMock::apply)
      .metricsManager(new MetricsManager(new SimpleMeterRegistry()))
      .build();
  }

  @FunctionalInterface
  public interface FunctionMock {
    Boolean apply(byte[] in);
  }

  @FunctionalInterface
  public interface ConsumerMock {
    void apply(WebSocketManager.WSData in);
  }

  @FunctionalInterface
  public interface RunnableMock {
    void apply();
  }

  @FunctionalInterface
  public interface BiConsumerMock {
    void apply(String in, Exception ex);
  }

}
