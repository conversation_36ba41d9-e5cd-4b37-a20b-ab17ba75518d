package com.wsf.dataingestor.component.opta.parsers;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Optional;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.CurrentTournamentFeed;
import com.wsf.dataingestor.opta.parsers.OT2FeedParser;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.domain.common.Tournament;

import static com.wsf.dataingestor.shared.canned.CompetitionCanned.DEFAULT_COMPETITION;
import static com.wsf.dataingestor.shared.canned.TournamentCanned.tournamentCanned;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class OT2FeedParserTest extends TestWithMocks {

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  private OT2FeedParser feedParser;

  @Before
  public void setUp() throws Exception {
    feedParser = new OT2FeedParser(optaFeedParserUtilsMock, new JsonConfig().jsonObjectMapper());
  }

  @Test
  public void parseFeed() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/opta/ot2.json", Charset.defaultCharset(), classLoader);
    Tournament tournament = tournamentCanned().build();

    when(optaFeedParserUtilsMock.getCompetition(anyString())).thenReturn(DEFAULT_COMPETITION);
    when(optaFeedParserUtilsMock.getTournament(anyString())).thenReturn(Optional.of(tournament));

    // Act
    CurrentTournamentFeed currentTournamentFeed = feedParser.parseFeed(feed);

    // Assert
    assertThat(currentTournamentFeed.getExternalSeasonId(), is("c0ne08fv3r8wi4c08x3bvw0lw"));
    assertThat(currentTournamentFeed.getYear(), is("2022-2023"));
    assertThat(currentTournamentFeed.getCompetition(), is(DEFAULT_COMPETITION));
    assertThat(currentTournamentFeed.getExistingTournament(), is(tournament));
    assertThat(currentTournamentFeed.getProvider(), is(CurrentTournamentFeed.Provider.OPTA));
    assertNotNull(currentTournamentFeed.getFeedId());
  }

  @Test
  public void parseYearWithSpecialCharacters() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/opta/ot2_african_cup.json", Charset.defaultCharset(), classLoader);
    Tournament tournament = tournamentCanned().build();

    when(optaFeedParserUtilsMock.getCompetition(anyString())).thenReturn(DEFAULT_COMPETITION);
    when(optaFeedParserUtilsMock.getTournament(anyString())).thenReturn(Optional.of(tournament));

    // Act
    CurrentTournamentFeed currentTournamentFeed = feedParser.parseFeed(feed);

    // Assert
    assertThat(currentTournamentFeed.getYear(), is("2023"));
    assertNotNull(currentTournamentFeed.getFeedId());
  }

  @Test
  public void parseFeed_noExistingTournamentFound() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/opta/ot2.json", Charset.defaultCharset(), classLoader);

    when(optaFeedParserUtilsMock.getCompetition(anyString())).thenReturn(DEFAULT_COMPETITION);
    when(optaFeedParserUtilsMock.getTournament(anyString())).thenReturn(Optional.empty());

    // Act
    CurrentTournamentFeed currentTournamentFeed = feedParser.parseFeed(feed);

    // Assert
    assertThat(currentTournamentFeed.getExternalSeasonId(), is("c0ne08fv3r8wi4c08x3bvw0lw"));
    assertThat(currentTournamentFeed.getYear(), is("2022-2023"));
    assertThat(currentTournamentFeed.getCompetition(), is(DEFAULT_COMPETITION));
    assertNull(currentTournamentFeed.getExistingTournament());
    assertThat(currentTournamentFeed.getProvider(), is(CurrentTournamentFeed.Provider.OPTA));
    assertNotNull(currentTournamentFeed.getFeedId());
  }

  @Test
  public void parseFeed_noActiveTournamentCalendar() throws IOException {
    // Arrange
    ClassLoader classLoader = getClass().getClassLoader();
    String feed = IOUtils.resourceToString("feeds/opta/ot2_no_active_calendar.json", Charset.defaultCharset(),
      classLoader);

    when(optaFeedParserUtilsMock.getCompetition(anyString())).thenReturn(DEFAULT_COMPETITION);

    // Act
    CurrentTournamentFeed currentTournamentFeed = feedParser.parseFeed(feed);

    // Assert
    assertThat(currentTournamentFeed.getCompetition(), is(DEFAULT_COMPETITION));
    assertNull(currentTournamentFeed.getExternalSeasonId());
    assertNull(currentTournamentFeed.getYear());
    assertNull(currentTournamentFeed.getExistingTournament());
    assertThat(currentTournamentFeed.getProvider(), is(CurrentTournamentFeed.Provider.OPTA));
    assertNotNull(currentTournamentFeed.getFeedId());
  }
}
