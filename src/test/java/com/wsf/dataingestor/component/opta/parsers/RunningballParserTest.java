package com.wsf.dataingestor.component.opta.parsers;

import java.io.IOException;
import java.util.List;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.models.MatchDataFeed;
import com.wsf.dataingestor.models.PlayerDataDTO;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.runningball.parsers.RunningballParser;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Fixture;

import static com.wsf.dataingestor.models.MatchDataFeed.FeedFixtureStatus.LIVE;
import static com.wsf.domain.soccer.SoccerMatchEvent.CARD;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.KICKOFF;
import static com.wsf.domain.soccer.SoccerMatchEvent.LIVE_COVERAGE_CANCELLED;
import static com.wsf.domain.soccer.SoccerMatchEvent.NO_VAR_CHECK;
import static com.wsf.domain.soccer.SoccerMatchEvent.POSSIBLE_CORNER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class RunningballParserTest extends TestWithMocks {

  private final static Fixture FIXTURE = FixtureCanned.fixtureCanned().build();

  @Mock
  private OptaFeedParserUtils optaFeedParserUtils;

  @Test
  public void whenAFeedContainsABetStop_thenItIsParsedCorrectly() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_betstop.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .first()
      .satisfies(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(1);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
        assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);

        assertThat(matchDataFeed.getMatchEvents()
          .size()).isEqualTo(2);
        MatchDataFeed.MatchEventDTO matchEventDTO = matchDataFeed.getMatchEvents().get(0);
        assertThat(matchEventDTO.getEvent()).isEqualTo(GOAL);
      });
  }

  @Test
  public void whenAFeedContainsACardEvent_thenItIsParsedCorrectly() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_card.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .first()
      .satisfies(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(1);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
        assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes).isEmpty();

        assertThat(matchDataFeed.getMatchEvents())
          .hasSize(2)
          .anySatisfy(matchEventDTO -> assertThat(matchEventDTO.getEvent()).isEqualTo(CARD));
      });
  }

  @Test
  public void whenAFeedContainsABetStart_thenItIsParsedCorrectly() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_betstart.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .first()
      .satisfies(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(88);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
        assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);

        assertThat(matchDataFeed.getMatchEvents()
          .size()).isEqualTo(1);
        MatchDataFeed.MatchEventDTO matchEventDTO = matchDataFeed.getMatchEvents().get(0);
        assertThat(matchEventDTO.getEvent()).isEqualTo(KICKOFF);
      });
  }

  @Test
  public void whenAFeedContainsADangerousShot_thenItIsParsedCorrectly() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_dangerous_shot.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .first()
      .satisfies(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(88);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
        assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);

        assertThat(matchDataFeed.getMatchEvents()
          .size()).isEqualTo(1);
        MatchDataFeed.MatchEventDTO matchEventDTO = matchDataFeed.getMatchEvents().get(0);
        assertThat(matchEventDTO.getEvent()).isEqualTo(GOALKICK);
      });
  }

  @Test
  public void whenAFeedContainsADangerousCorner_thenItIsParsedCorrectly() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_dangerous_corner.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .first()
      .satisfies(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(88);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);
        assertThat(matchDataFeed.getFixture()).isEqualTo(FIXTURE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);

        assertThat(matchDataFeed.getMatchEvents()
          .size()).isEqualTo(1);
        MatchDataFeed.MatchEventDTO matchEventDTO = matchDataFeed.getMatchEvents().get(0);
        assertThat(matchEventDTO.getEvent()).isEqualTo(POSSIBLE_CORNER);
      });
  }

  @Test
  public void whenAFeedForAFixtureWeDoNotSupportIsReceived_thenNullIsReturned() throws IOException {
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(null);

    String fileContent = getFileContent("feeds/runningball/runningball_betstop.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds).isEmpty();
  }

  @Test
  public void whenAFeedContainsMultipleMatches_thenParseMatchDataFeedForEach() throws IOException {
    String fileContent = getFileContent("feeds/runningball/runningball_multiple_matches.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);

    var optaFixtureIdForNoVarEvent = "a8aftv3cph1sc8bwzdd4k989g";
    var fixtureWithNoVarEvent = FixtureCanned.fixtureCanned().optaFixtureId(optaFixtureIdForNoVarEvent).build();
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(optaFixtureIdForNoVarEvent)).thenReturn(fixtureWithNoVarEvent);

    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    assertThat(matchDataFeeds)
      .isNotEmpty()
      .hasSize(4)
      .anySatisfy(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(75);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);
        assertThat(matchDataFeed.getMatchEvents()).isEmpty();
      })
      .anySatisfy(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(35);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);
        assertThat(matchDataFeed.getMatchEvents()).isEmpty();
      })
      .anySatisfy(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(49);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);

        assertThat(matchDataFeed.getFixture().getOptaFixtureId()).isEqualTo(optaFixtureIdForNoVarEvent);

        assertThat(matchDataFeed.getMatchEvents())
          .hasSize(1)
          .anySatisfy(matchEvent -> assertThat(matchEvent.getEvent()).isEqualTo(NO_VAR_CHECK));
      })
      .anySatisfy(matchDataFeed -> {
        assertThat(matchDataFeed.getMatchTimeMin()).isEqualTo(58);
        assertThat(matchDataFeed.getFixtureStatus()).isEqualTo(LIVE);

        List<PlayerDataDTO> playerIndexes = matchDataFeed.getPlayersData();
        assertThat(playerIndexes.size()).isEqualTo(0);
        assertThat(matchDataFeed.getMatchEvents()).isEmpty();
      });
  }

  @Test
  public void whenRunningBallDoesNotSupportAMatch_thenSuspendOdds() throws IOException {
    // Arrange
    String fileContent = getFileContent("feeds/runningball/runningball_match_not_supported.xml");
    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);

    var unsupportedMatchOptaId = "27t7tx8os50zrun731wfu2vis";
    var fixtureWithNoVarEvent = FixtureCanned.fixtureCanned().optaFixtureId(unsupportedMatchOptaId).build();
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(unsupportedMatchOptaId)).thenReturn(fixtureWithNoVarEvent);

    // Act
    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    // Assert
    assertThat(matchDataFeeds)
      .isNotEmpty()
      .hasSize(1)
      .first()
      .satisfies(matchDataFeed -> assertThat(matchDataFeed.getMatchEvents())
        .hasSize(1)
        .anySatisfy(matchEvent -> assertThat(matchEvent.getEvent()).isEqualTo(LIVE_COVERAGE_CANCELLED)));
  }

  @Test
  public void whenRunningBallSendsANotSupportedSystemMessage_thenItIsDiscarded() throws IOException {
    // Arrange
    String fileContent = getFileContent("feeds/runningball/runningball_not_supported_system_message.xml");

    RunningballParser runningballParser = new RunningballParser(new JsonConfig().xmlObjectMapper(),
      optaFeedParserUtils);

    var unsupportedMatchOptaId = "27t7tx8os50zrun731wfu2vis";
    var fixtureWithNoVarEvent = FixtureCanned.fixtureCanned().optaFixtureId(unsupportedMatchOptaId).build();
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(anyString())).thenReturn(FIXTURE);
    when(optaFeedParserUtils.getFixtureByOptaFixtureId(unsupportedMatchOptaId)).thenReturn(fixtureWithNoVarEvent);

    // Act
    List<MatchDataFeed> matchDataFeeds = runningballParser.parseFeed(fileContent);

    // Assert
    assertThat(matchDataFeeds)
      .isNotEmpty()
      .hasSize(1)
      .first()
      .satisfies(matchDataFeed -> assertThat(matchDataFeed.getMatchEvents()).isEmpty());
  }


  private static String getFileContent(String fileName) throws IOException {
    ClassLoader classLoader = RunningballParserTest.class.getClassLoader();
    return IOUtils.resourceToString(fileName, java.nio.charset.StandardCharsets.UTF_8, classLoader);
  }
}
