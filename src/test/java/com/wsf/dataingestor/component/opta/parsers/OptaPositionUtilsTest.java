package com.wsf.dataingestor.component.opta.parsers;

import java.util.List;
import java.util.Map;
import org.junit.Test;
import com.wsf.dataingestor.opta.parsers.OptaPositionUtils;
import com.wsf.domain.common.Player;

import static com.google.common.collect.ImmutableList.of;
import static com.google.common.collect.Maps.newHashMap;
import static com.wsf.dataingestor.opta.parsers.OptaPositionUtils.buildOptaPositionToPosition;
import static com.wsf.domain.common.Player.DetailedPosition.CENTRE_BACK;
import static com.wsf.domain.common.Player.DetailedPosition.CENTRE_MIDFIELDER;
import static com.wsf.domain.common.Player.DetailedPosition.GOALKEEPER;
import static com.wsf.domain.common.Player.DetailedPosition.STRIKER;
import static com.wsf.domain.common.Player.DetailedPosition.WING_BACK;
import static com.wsf.domain.common.Player.DetailedPosition.WING_FORWARD;
import static com.wsf.domain.common.Player.DetailedPosition.WING_MIDFIELDER;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

public class OptaPositionUtilsTest {

  @Test
  public void whenPlayerPosition5for442FormationIsRequested_thenCentreBackIsReturned() {
    Player.DetailedPosition position = OptaPositionUtils.parseDetailedPlayerPosition(2, 5);
    assertThat(position, is(CENTRE_BACK));
  }

  @Test
  public void whenPlayerPosition10for343FormationIsRequested_thenCentreBackIsReturned() {
    Player.DetailedPosition position = OptaPositionUtils.parseDetailedPlayerPosition(13, 10);
    assertThat(position, is(WING_FORWARD));
  }

  @Test
  public void testBuildOptaPositionToPosition() {
    Map<Player.DetailedPosition, List<Integer>> positionToOptaPositions = newHashMap();
    positionToOptaPositions.put(GOALKEEPER, of(1));
    positionToOptaPositions.put(CENTRE_BACK, of(5, 6));
    positionToOptaPositions.put(WING_BACK, of(2, 3));
    positionToOptaPositions.put(CENTRE_MIDFIELDER, of(4, 8));
    positionToOptaPositions.put(WING_MIDFIELDER, of(7, 11));
    positionToOptaPositions.put(STRIKER, of(9, 10));

    Map<Integer, Player.DetailedPosition> optaPositionToPosition = buildOptaPositionToPosition(positionToOptaPositions);

    assertThat(optaPositionToPosition.get(1), is(GOALKEEPER));
    assertThat(optaPositionToPosition.get(2), is(WING_BACK));
    assertThat(optaPositionToPosition.get(3), is(WING_BACK));
    assertThat(optaPositionToPosition.get(4), is(CENTRE_MIDFIELDER));
    assertThat(optaPositionToPosition.get(5), is(CENTRE_BACK));
    assertThat(optaPositionToPosition.get(6), is(CENTRE_BACK));
    assertThat(optaPositionToPosition.get(7), is(WING_MIDFIELDER));
    assertThat(optaPositionToPosition.get(8), is(CENTRE_MIDFIELDER));
    assertThat(optaPositionToPosition.get(9), is(STRIKER));
    assertThat(optaPositionToPosition.get(10), is(STRIKER));
    assertThat(optaPositionToPosition.get(11), is(WING_MIDFIELDER));
  }
}
