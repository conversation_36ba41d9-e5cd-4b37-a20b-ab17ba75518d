package com.wsf.dataingestor.component.opta.parsers;

import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.mockito.Mock;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.metrics.MetricsManager;
import com.wsf.dataingestor.models.EntityEventDTO;
import com.wsf.dataingestor.models.MatchEventsFeed;
import com.wsf.dataingestor.opta.parsers.OptaFeedParserUtils;
import com.wsf.dataingestor.opta.parsers.ma3.MA3EventsMapper;
import com.wsf.dataingestor.opta.parsers.ma3.MA3FeedParser;
import com.wsf.dataingestor.opta.parsers.ma3.MA3ValidEvents;
import com.wsf.dataingestor.shared.TestWithMocks;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.dataingestor.shared.canned.PlayerCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Player;
import com.wsf.domain.common.Team;
import com.wsf.domain.soccer.SoccerMatchEvent;

import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM1_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.TEAM2_ID;
import static com.wsf.dataingestor.shared.canned.TeamCanned.defaultTeam;
import static com.wsf.domain.soccer.SoccerMatchEvent.CORNER;
import static com.wsf.domain.soccer.SoccerMatchEvent.FOUL;
import static com.wsf.domain.soccer.SoccerMatchEvent.GOALKICK;
import static com.wsf.domain.soccer.SoccerMatchEvent.OFFSIDE;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT;
import static com.wsf.domain.soccer.SoccerMatchEvent.SHOT_ON_GOAL;
import static com.wsf.domain.soccer.SoccerMatchEvent.TACKLE_WON;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class MA3FeedParserTest extends TestWithMocks {

  MetricsManager manager = new MetricsManager(new SimpleMeterRegistry());

  @Mock
  OptaFeedParserUtils optaFeedParserUtilsMock;

  @Test
  public void whenThereArePenaltiesAtMatchEnd_thenWeDoNotCountThemAsMatchShots() throws IOException {
    // Arrange
    Team arsenal = defaultTeam(TEAM1_ID, "4dsgumo7d4zupm2ugsvm4zm4d");
    Team porto = defaultTeam(TEAM2_ID, "66bsnl0zjb7l5akwo00h0y5me");
    Fixture fixture = FixtureCanned.defaultFixture(arsenal, porto);
    Player player = PlayerCanned.createPlayer().build();
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(arsenal.getOptaId()))).thenReturn(arsenal);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(porto.getOptaId()))).thenReturn(porto);
    when(optaFeedParserUtilsMock.getPlayer(anyString(), any())).thenReturn(player);
    MA3FeedParser ma3FeedParser = setupMA3FeedParser();

    // Act
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma3_penalties.json", classLoader);
    MatchEventsFeed matchEventsFeed = ma3FeedParser.parseFeed(bytes);
    var shots = (long) getEventsPerType(matchEventsFeed, SHOT).size();
    var shotsOnGoal = (long) getEventsPerType(matchEventsFeed, SHOT_ON_GOAL).size();

    // Assert
    assertEquals(23, shots);
    assertEquals(7, shotsOnGoal);
  }

  @Test
  public void parseStandardMa3() throws IOException {
    // Arrange
    Team sevilla = defaultTeam(TEAM1_ID, "10eyb18v5puw4ez03ocaug09m");
    Team mallorca = defaultTeam(TEAM2_ID, "50x1m4u58lffhq6v6ga1hbxmy");
    Fixture fixture = FixtureCanned.defaultFixture(sevilla, mallorca);
    Player player1 = PlayerCanned.createPlayer().optaPlayerId("6ekdnbnk56xlxforb5owt3dn9").team(mallorca).build();
    Player player2 = PlayerCanned.createPlayer().optaPlayerId("3pwr7lqvo4o08c4nvzqpuue39").team(mallorca).build();
    Player player3 = PlayerCanned.createPlayer().optaPlayerId("45rxt159xjefvxlfgeykclw8a").team(sevilla).build();
    when(optaFeedParserUtilsMock.getFixtureOrThrow(anyString())).thenReturn(fixture);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(sevilla.getOptaId()))).thenReturn(sevilla);
    when(optaFeedParserUtilsMock.getTeam(anyString(), eq(mallorca.getOptaId()))).thenReturn(mallorca);
    when(optaFeedParserUtilsMock.getPlayer(eq(player1.getOptaPlayerId()), any())).thenReturn(player1);
    when(optaFeedParserUtilsMock.getPlayer(eq(player2.getOptaPlayerId()), any())).thenReturn(player2);
    when(optaFeedParserUtilsMock.getPlayer(eq(player3.getOptaPlayerId()), any())).thenReturn(player3);

    MA3FeedParser ma3FeedParser = setupMA3FeedParser();

    // Act
    ClassLoader classLoader = getClass().getClassLoader();
    byte[] bytes = IOUtils.resourceToByteArray("feeds/opta/ma3.json", classLoader);
    MatchEventsFeed matchEventsFeed = ma3FeedParser.parseFeed(bytes);

    assertEquals(Set.of(GOALKICK, SHOT, SHOT_ON_GOAL, CORNER, OFFSIDE, FOUL, TACKLE_WON),
      matchEventsFeed.getSupportedEventTypes());

    var goalKicks = getEventsPerType(matchEventsFeed, GOALKICK);
    var shots = getEventsPerType(matchEventsFeed, SHOT);
    long nrOfShotsForTeam1 = shots
      .stream()
      .filter(event -> event.getTeamId().equals(sevilla.getIdAsString()))
      .count();
    var shotsOnGoal = getEventsPerType(matchEventsFeed, SHOT_ON_GOAL);
    long nrOfShotsOnGoalForTeam1 = shotsOnGoal
      .stream()
      .filter(event -> event.getTeamId().equals(sevilla.getIdAsString()))
      .count();
    var offsides = getEventsPerType(matchEventsFeed, OFFSIDE);
    var fouls = getEventsPerType(matchEventsFeed, FOUL);
    var tacklesWon = getEventsPerType(matchEventsFeed, TACKLE_WON);

    // Assert
    assertEquals(98, matchEventsFeed.getEvents()
      .size());
    assertEquals(15, goalKicks.size());
    assertEquals(19, shots.size());
    assertEquals(11, nrOfShotsForTeam1);
    assertEquals(10, shotsOnGoal.size());
    assertEquals(4, nrOfShotsOnGoalForTeam1);
    assertEquals(6, offsides.size());
    assertEquals(18, fouls.size());
    assertEquals(22, tacklesWon.size());
  }

  private static List<EntityEventDTO> getEventsPerType(MatchEventsFeed matchEventsFeed, SoccerMatchEvent eventType) {
    return matchEventsFeed.getEvents()
      .stream()
      .filter(e -> eventType == e.getEventType())
      .collect(Collectors.toList());
  }

  private MA3FeedParser setupMA3FeedParser() {
    var ma3FeedEventsMapper = MA3EventsMapper
      .builder().playerEventTypeIdToQualifiersId(MA3ValidEvents.eventTypeIdToFilters).metricsManager(manager).build();
    return new MA3FeedParser(new JsonConfig().jsonObjectMapper(), optaFeedParserUtilsMock, ma3FeedEventsMapper);
  }
}
