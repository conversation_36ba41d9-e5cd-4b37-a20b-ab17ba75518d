package com.wsf.dataingestor.component.perfomance;

import java.text.DecimalFormat;
import java.util.Collection;
import org.junit.Ignore;
import org.junit.Test;
import org.openjdk.jmh.results.RunResult;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import com.wsf.dataingestor.unit.performance.cache.OngoingMatchesCacheTest;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class JmhPerformanceTest {

  private static DecimalFormat df = new DecimalFormat("0.000");
  private static final double CACHE_REFERENCE_SCORE = 83573.356;
  private static final double MA2_PARSE_REFERENCE_SCORE = 1522452.661;

  @Ignore
  @Test
  public void runHazelcastCacheBenchmark() throws RunnerException {
    Options opt = new OptionsBuilder().include(OngoingMatchesCacheTest.class.getSimpleName()).build();
    Collection<RunResult> runResults = new Runner(opt).run();
    assertFalse(runResults.isEmpty());
    for (RunResult runResult : runResults) {
      assertDeviationWithin(runResult, CACHE_REFERENCE_SCORE, 0.05);
    }
  }

  //  @Ignore
  //  @Test
  //  public void runMA2ParseFeedBenchmark() throws RunnerException {
  //    Options opt = new OptionsBuilder()
  //      .include(MA2FeedPerformanceTest.class.getSimpleName())
  //      .build();
  //    Collection<RunResult> runResults = new Runner(opt).run();
  //    assertFalse(runResults.isEmpty());
  //    for(RunResult runResult : runResults) {
  //      assertDeviationWithin(runResult, MA2_PARSE_REFERENCE_SCORE, 0.05);
  //    }
  //  }

  private static void assertDeviationWithin(RunResult result, double referenceScore, double maxDeviation) {
    double score = result.getPrimaryResult().getScore();
    double deviation = Math.abs(score / referenceScore - 1);
    String deviationString = df.format(deviation * 100) + "%";
    String maxDeviationString = df.format(maxDeviation * 100) + "%";
    String errorMessage = "Deviation " + deviationString + " exceeds maximum allowed deviation " + maxDeviationString;
    assertTrue(errorMessage, deviation < maxDeviation);
  }

}
