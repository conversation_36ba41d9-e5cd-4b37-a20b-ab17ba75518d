package com.wsf.dataingestor.component;

import java.io.IOException;
import org.junit.Test;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsf.dataingestor.config.JsonConfig;
import com.wsf.dataingestor.shared.canned.FixtureCanned;
import com.wsf.domain.common.Fixture;
import com.wsf.domain.common.Round;

import static org.junit.Assert.assertNotNull;

public class SerializationTest {

  @Test
  public void fixtureRoundSerializationWorks() throws IOException {
    Fixture fixture = FixtureCanned
      .fixtureCanned()
      .round(Round
        .builder().type("group").number(27).betradarId("232").build())
      .build();

    ObjectMapper objectMapper = new JsonConfig().jsonObjectMapper();

    String json = objectMapper.writeValueAsString(fixture);
    assertNotNull(json);
  }
}