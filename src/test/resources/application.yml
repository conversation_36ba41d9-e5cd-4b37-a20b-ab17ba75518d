server.port: 8080

spring:
  http:
    multipart:
      max-file-size: 100MB
  profiles:
    active: integration-test
  application:
    name: WSF Ingestor

service:
  data:
    mongodb:
      main:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
      competitions:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
      allleagues:
        uri: ${ENV_COMMON_MONGO_DB_URI_FULL}
        database: ${ENV_ALL_LEAGUES_MONGO_DB_DATABASE}
  kafka:
    servers: ${ENV_KAFKA_SERVERS}
    topic:
      player_events: ${ENV_KAFKA_TOPIC_PLAYER_EVENTS}
      player_odds: ${ENV_KAFKA_TOPIC_PLAYER_ODDS}
      team_events: ${ENV_KAFKA_TOPIC_TEAM_EVENTS}
      team_odds: ${ENV_KAFKA_TOPIC_TEAM_ODDS}
      live_ratings: ${ENV_KAFKA_TOPIC_LIVE_RATINGS}
      final_fixture_summaries: ${ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES}
      fixture_summary_updates: ${ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES}
      contestant_unavailabilities: ${ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES}
      fixture_changes: ${ENV_KAFKA_TOPIC_FIXTURE_CHANGES}
  aws:
    access-key: ********************
    secret-key: pMez7cC5qsb63lsAqtYbU5HJ4C/+3wgXzT5fLJjJ
  feeds:
    store:
      enabled: ${ENV_STORE_FEEDS}
      type: ${ENV_STORE_TYPE} # "file" or "s3"
      path: ${ENV_STORE_FEEDS_PATH}
  entity-mapper:
    host: ${ENV_ENTITY_MAPPER_HOST}
    port: ${ENV_ENTITY_MAPPER_PORT}

email:
  username: test

crons:
  current-tournament-updater:
    enabled: ${ENV_TOURNAMENT_CRON_ENABLED}
    schedule: ${ENV_TOURNAMENT_CRON_SCHEDULE}
  squads-updater:
    enabled: ${ENV_SQUADS_CRON_ENABLED}
    schedule: ${ENV_SQUADS_CRON_SCHEDULE}
  matches-updater:
    enabled: ${ENV_MATCHES_CRON_ENABLED}
    schedule: ${ENV_MATCHES_CRON_SCHEDULE}
  matches-watcher-updater:
    enabled: ${ENV_MATCHES_WATCHER_CRON_ENABLED}
    schedule: ${ENV_MATCHES_WATCHER_CRON_SCHEDULE}
  matches-stats-updater:
    enabled: ${ENV_STATS_CRON_ENABLED}
    interval-secs: ${ENV_STATS_CRON_INTERVAL_SEC}
    max-repetitions: ${ENV_STATS_CRON_REPS}
  settlement-updater:
    enabled: ${ENV_SETTLEMENT_CRON_ENABLED}
    interval-secs: ${ENV_SETTLEMENT_CRON_INTERVAL_SEC}
    max-repetitions: ${ENV_SETTLEMENT_CRON_REPS}
  player-availability-updater:
    enabled: ${ENV_AVAILABILITY_CRON_ENABLED}
    schedule: ${ENV_AVAILABILITY_CRON_SCHEDULE}
    days-window: 5
    not-alert-fixtures-within-hours: 61
    alert-fixtures-within-hours: 72

opta:
  sdapi:
    host: ${ENV_OPTA_SD_URL}
    port: ${ENV_OPTA_SD_PORT}
    outlet-auth-key: ${ENV_OPTA_OUTLET_KEY}
  sddp:
    url: ${ENV_OPTA_SP_URL}
    outlet-auth-key: ${ENV_OPTA_OUTLET_KEY}

sportmonks:
  api:
    v2:
      host: soccer.sportmonks.com
    v3:
      host: api.sportmonks.com
    token: ${ENV_SPORTMONKS_API_TOKEN}

management:
  info.git.mode: full
  endpoints.web:
    exposure:
      include: health, prometheus, info
    base-path: /
  endpoint:
    health.show-details: always

2_HOURS: 7200
3_HOURS: 10800
6_HOURS: 21600
25_HOURS: 90000

cache:
  redis:
    primaryHost: ${ENV_REDIS_PRIMARY_HOST}
    readerHost: ${ENV_REDIS_READER_HOST}
    timeoutSecs: 60
    db: ${ENV_REDIS_DB}
  caches:
    ingestor_team_active_players:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 100
    ingestor_opta_players:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 2500
    ingestor_opta_fixtures:
      redis:
        ttlSecs: ${25_HOURS}
      local:
        ttlSecs: ${25_HOURS}
        maxSize: 50
    ingestor_team_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 100
    ingestor_player_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 2500
    ingestor_fixture_data:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 50
    ingestor_opta_players_events:
      redis:
        ttlSecs: ${6_HOURS}
      local:
        ttlSecs: ${6_HOURS}
        maxSize: 100000
    ingestor_match_locks:
      redis:
        ttlSecs: ${2_HOURS}
      local:
        ttlSecs: ${2_HOURS}
        maxSize: 50
    ingestor_fixture_summaries:
      redis:
        ttlSecs: ${3_HOURS}
      local:
        ttlSecs: ${3_HOURS}
        maxSize: 200

ENV_ALL_LEAGUES_MONGO_DB_DATABASE: all_leagues

ENV_KAFKA_TOPIC_PLAYER_ODDS: "player_odds_triggers"
ENV_KAFKA_TOPIC_TEAM_ODDS: "team_odds_triggers"
ENV_KAFKA_TOPIC_PLAYER_EVENTS: "player_events"
ENV_KAFKA_TOPIC_TEAM_EVENTS: "team_events"
ENV_KAFKA_TOPIC_LIVE_RATINGS: "live_ratings"
ENV_KAFKA_TOPIC_FINAL_FIXTURE_SUMMARIES: "final_fixture_summaries"
ENV_KAFKA_TOPIC_FIXTURE_SUMMARY_UPDATES: "fixture_summary_updates"
ENV_KAFKA_TOPIC_CONTESTANT_UNAVAILABILITIES: "contestant_unavailabilities"
ENV_KAFKA_TOPIC_FIXTURE_CHANGES: "fixture_changes"

ENV_STORE_FEEDS: false
ENV_STORE_TYPE: file
ENV_STORE_FEEDS_PATH: /tmp/opta_feeds/


ENV_TOURNAMENT_CRON_ENABLED: false
ENV_TOURNAMENT_CRON_SCHEDULE: ""

ENV_SQUADS_CRON_ENABLED: false
ENV_SQUADS_CRON_SCHEDULE: ""

ENV_MATCHES_CRON_ENABLED: false
ENV_MATCHES_CRON_SCHEDULE: ""

ENV_MATCHES_WATCHER_CRON_ENABLED: false
ENV_MATCHES_WATCHER_CRON_SCHEDULE: ""

ENV_STATS_CRON_ENABLED: false
ENV_STATS_CRON_INTERVAL_SEC: 1
ENV_STATS_CRON_REPS: 1

ENV_SETTLEMENT_CRON_ENABLED: false
ENV_SETTLEMENT_CRON_INTERVAL_SEC: 120
ENV_SETTLEMENT_CRON_REPS: 1

ENV_AVAILABILITY_CRON_ENABLED: false
ENV_AVAILABILITY_CRON_SCHEDULE: ""

ENV_REDIS_PRIMARY_HOST:
ENV_REDIS_READER_HOST:
ENV_REDIS_DB: 0

---
spring:
  config:
    activate:
      on-profile: integration-test

ENV_COMMON_MONGO_DB_URI_FULL: mongodb://127.0.0.1:28017

ENV_KAFKA_SERVERS: http://localhost:9092

ENV_OPTA_SD_URL: localhost
ENV_OPTA_SD_PORT: 1080
ENV_OPTA_SP_URL: localhost:1080
ENV_OPTA_OUTLET_KEY: "test"

ENV_SPORTMONKS_API_TOKEN: "q7J4t5OMrfmsuU0WsgMRI9hUJ7o3wFgH72xiMgQFdVeHGdvkzpSlrmQ85zzU"

ENV_ENTITY_MAPPER_HOST: localhost
ENV_ENTITY_MAPPER_PORT: 5000
