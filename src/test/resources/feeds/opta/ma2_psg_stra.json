{"matchInfo": {"id": "bukekpxmpmbs5qmcsrmgx2b6c", "coverageLevel": "15", "date": "2021-08-14Z", "time": "19:00:00Z", "localDate": "2021-08-14", "localTime": "21:00:00", "week": "2", "postMatch": "1", "numberOfPeriods": 2, "periodLength": 45, "lastUpdated": "2024-01-10T08:18:38Z", "description": "PSG vs Strasbourg", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "dm5ka0os1e3dxcp3vh05kmp33", "name": "Ligue 1", "knownName": "French Ligue 1", "sponsorName": "LIGUE 1 Uber Eats", "competitionCode": "LI1", "competitionFormat": "Domestic league", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}, "tournamentCalendar": {"id": "buq1p8cf83xr4f0fygagjxn2s", "startDate": "2021-08-06Z", "endDate": "2022-05-21Z", "name": "2021/2022"}, "stage": {"id": "bux99e61orz4fnrkc6jzqr4lw", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2021-08-06Z", "endDate": "2022-05-21Z", "name": "Regular Season"}, "contestant": [{"id": "2b3mar72yy8d6uvat1ka6tn3r", "name": "PSG", "shortName": "PSG", "officialName": "Paris Saint-Germain FC", "code": "PSG", "position": "home", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}, {"id": "71ajatxnuhc5cnm3xvgdky49w", "name": "Strasbourg", "shortName": "Strasbourg", "officialName": "RC Strasbourg Alsace", "code": "STR", "position": "away", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}], "venue": {"id": "efcoydloex7y04q29pac9s67c", "neutral": "no", "longName": "Parc des Princes", "shortName": "Parc des Princes"}}, "liveData": {"matchDetails": {"periodId": 14, "matchStatus": "Played", "winner": "home", "matchLengthMin": 95, "matchLengthSec": 57, "period": [{"id": 1, "start": "2021-08-14T19:00:08Z", "end": "2021-08-14T19:47:03Z", "lengthMin": 46, "lengthSec": 55}, {"id": 2, "start": "2021-08-14T20:03:19Z", "end": "2021-08-14T20:52:21Z", "lengthMin": 49, "lengthSec": 2}], "scores": {"ht": {"home": 3, "away": 0}, "ft": {"home": 4, "away": 2}, "total": {"home": 4, "away": 2}}}, "goal": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 1, "timeMin": 3, "timeMinSec": "2:18", "lastUpdated": "2023-12-27T13:17:52Z", "timestamp": "2021-08-14T19:02:27Z", "type": "G", "scorerId": "drkx7msmzruq5myxk9ani6z11", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "dyigvzjpa74cw4urhh7fwzs2d", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318814599", "homeScore": 1, "awayScore": 0}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 1, "timeMin": 25, "timeMinSec": "24:46", "lastUpdated": "2023-04-28T15:25:47Z", "timestamp": "2021-08-14T19:24:55Z", "type": "OG", "scorerId": "am0pcyvtaws1i69jt09dgnsnp", "scorerName": "<PERSON><PERSON>", "optaEventId": "2318835129", "homeScore": 2, "awayScore": 0}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 1, "timeMin": 27, "timeMinSec": "26:12", "lastUpdated": "2021-08-14T19:27:11Z", "timestamp": "2021-08-14T19:26:20Z", "type": "G", "scorerId": "36fznb9hzqvn6lnlla7f3ycwl", "scorerName": "<PERSON><PERSON>", "optaEventId": "2318836127", "homeScore": 3, "awayScore": 0}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 53, "timeMinSec": "52:43", "lastUpdated": "2022-09-19T13:32:56Z", "timestamp": "2021-08-14T20:11:02Z", "type": "G", "scorerId": "950o8rjqvnl5bnidq2dc3fglx", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "c8u45gxriyq1rup5fpr30xrdh", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318875995", "homeScore": 3, "awayScore": 1}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 64, "timeMinSec": "63:09", "lastUpdated": "2023-04-28T15:25:47Z", "timestamp": "2021-08-14T20:21:29Z", "type": "G", "scorerId": "am0pcyvtaws1i69jt09dgnsnp", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "9ysk6656hj0na1hrizgt83k5x", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318887465", "homeScore": 3, "awayScore": 2}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 86, "timeMinSec": "85:04", "lastUpdated": "2022-03-29T12:51:21Z", "timestamp": "2021-08-14T20:43:24Z", "type": "G", "scorerId": "4pyehox7p3z64c4l3k8yoyfv9", "scorerName": "<PERSON>", "assistPlayerId": "5e9ilgrz3tzg9kd1gk3yvrahh", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318905909", "homeScore": 4, "awayScore": 2}], "card": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 51, "timeMinSec": "50:02", "lastUpdated": "2023-08-15T12:30:25Z", "timestamp": "2021-08-14T20:08:22Z", "type": "YC", "playerId": "f5lfo1hz7u5bk494w53arcacq", "playerName": "<PERSON><PERSON>", "optaEventId": "2318873205", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 60, "timeMinSec": "59:01", "lastUpdated": "2023-02-26T13:39:35Z", "timestamp": "2021-08-14T20:17:20Z", "type": "YC", "playerId": "4ju2soyxqa6hu0oo007ej8wdl", "playerName": "<PERSON><PERSON>", "optaEventId": "2318883251", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 79, "timeMinSec": "78:26", "lastUpdated": "2023-12-22T22:02:32Z", "timestamp": "2021-08-14T20:36:46Z", "type": "YC", "playerId": "d5at3cowukpxiptq5ozcbe6s5", "playerName": "<PERSON><PERSON>", "optaEventId": "2318901263", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 81, "timeMinSec": "80:48", "lastUpdated": "2023-12-22T22:02:32Z", "timestamp": "2021-08-14T20:39:08Z", "type": "Y2C", "playerId": "d5at3cowukpxiptq5ozcbe6s5", "playerName": "<PERSON><PERSON>", "optaEventId": "2318902737", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 92, "timeMinSec": "92:00", "lastUpdated": "2024-01-02T15:34:43Z", "timestamp": "2021-08-14T20:50:20Z", "type": "YC", "playerId": "wg9qybhs3qeq9awhux331uqx", "playerName": "<PERSON><PERSON>", "optaEventId": "2318910843", "cardReason": "Dissent"}], "substitute": [{"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 1, "timeMin": 42, "timeMinSec": "41:52", "lastUpdated": "2024-01-04T17:26:24Z", "timestamp": "2021-08-14T19:42:01Z", "playerOnId": "c8u45gxriyq1rup5fpr30xrdh", "playerOnName": "<PERSON><PERSON>", "playerOffId": "88nw24pkg2ikj9c91tccvqol", "playerOffName": "<PERSON><PERSON>", "subReason": "Injury"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 60, "timeMinSec": "59:25", "lastUpdated": "2023-10-11T14:13:02Z", "timestamp": "2021-08-14T20:17:44Z", "playerOnId": "5qg0coudjinnt978myh4n4n2t", "playerOnName": "<PERSON><PERSON>", "playerOffId": "f5lfo1hz7u5bk494w53arcacq", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 63, "timeMinSec": "62:32", "lastUpdated": "2023-08-15T11:17:35Z", "timestamp": "2021-08-14T20:20:52Z", "playerOnId": "6u47fsriz3ejlf72hxtsew5bd", "playerOnName": "<PERSON><PERSON>", "playerOffId": "4ju2soyxqa6hu0oo007ej8wdl", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 70, "timeMinSec": "70:00", "lastUpdated": "2022-03-29T12:51:21Z", "timestamp": "2021-08-14T20:28:20Z", "playerOnId": "4pyehox7p3z64c4l3k8yoyfv9", "playerOnName": "<PERSON>", "playerOffId": "36fznb9hzqvn6lnlla7f3ycwl", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 76, "timeMinSec": "75:33", "lastUpdated": "2024-01-06T11:40:20Z", "timestamp": "2021-08-14T20:33:52Z", "playerOnId": "bndkgzdolm2c3iteenkv8s3it", "playerOnName": "<PERSON><PERSON>", "playerOffId": "am0pcyvtaws1i69jt09dgnsnp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 76, "timeMinSec": "75:50", "lastUpdated": "2021-08-14T20:34:18Z", "timestamp": "2021-08-14T20:34:10Z", "playerOnId": "20n6djablebcoizw6b219rp3p", "playerOnName": "<PERSON><PERSON>", "playerOffId": "950o8rjqvnl5bnidq2dc3fglx", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 76, "timeMinSec": "75:58", "lastUpdated": "2021-08-14T20:38:24Z", "timestamp": "2021-08-14T20:34:18Z", "playerOnId": "4gucmltvhuvakf8mrheveqdt6", "playerOnName": "<PERSON><PERSON>", "playerOffId": "42vsnv2j6emkbynforgon4n2t", "playerOffName": "<PERSON><PERSON>", "subReason": "Injury"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 88, "timeMinSec": "87:35", "lastUpdated": "2023-03-07T01:00:19Z", "timestamp": "2021-08-14T20:45:55Z", "playerOnId": "3775rpg889ojktofzx1y6jpat", "playerOnName": "<PERSON><PERSON><PERSON>", "playerOffId": "2lyib1m8bhrkcc3ukk12fbefp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 88, "timeMinSec": "87:49", "lastUpdated": "2023-10-11T12:25:40Z", "timestamp": "2021-08-14T20:46:08Z", "playerOnId": "dw9fzixumboknmucqgqffurmy", "playerOnName": "<PERSON><PERSON>", "playerOffId": "drkx7msmzruq5myxk9ani6z11", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "formationUsed": "433", "player": [{"playerId": "9sme7jykdv8msgfu0nxx6h39x", "firstName": "<PERSON><PERSON>", "lastName": "Navas Gamboa", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "rightsidePass", "value": "11"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "8"}, {"type": "goalsConceded", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateLongBalls", "value": "6"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "accurateGoalKicks", "value": "8"}, {"type": "possLostAll", "value": "2"}, {"type": "totalHighClaim", "value": "1"}, {"type": "fwdPass", "value": "14"}, {"type": "minsPlayed", "value": "90"}, {"type": "totalChippedPass", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "29"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "42"}, {"type": "accurateBackZonePass", "value": "36"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "totalPass", "value": "38"}, {"type": "leftside<PERSON><PERSON>", "value": "13"}, {"type": "accurateKeeperThrows", "value": "3"}, {"type": "totalLaunches", "value": "1"}, {"type": "goalKicks", "value": "8"}, {"type": "totalBackZonePass", "value": "37"}, {"type": "accuratePass", "value": "36"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "dyigvzjpa74cw4urhh7fwzs2d", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 22, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "goalAssist", "value": "1"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "totalAttAssist", "value": "2"}, {"type": "dispossessed", "value": "2"}, {"type": "possLostCtrl", "value": "14"}, {"type": "interceptionWon", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "58"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "goalsConceded", "value": "2"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"type": "fwdPass", "value": "22"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "possLostAll", "value": "14"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "28"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "20"}, {"type": "penAreaEntries", "value": "2"}, {"type": "rightsidePass", "value": "36"}, {"type": "accurateThrows", "value": "10"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "accurateCross", "value": "2"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "totalPass", "value": "71"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "interception", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "aerialWon", "value": "2"}, {"type": "duel<PERSON>on", "value": "9"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "accuratePass", "value": "63"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalCross", "value": "3"}, {"type": "backwardPass", "value": "9"}, {"type": "longPassOwnToOppSuccess", "value": "6"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "lostCorners", "value": "1"}, {"type": "effectiveBlockedCross", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "accurateFwdZonePass", "value": "43"}, {"type": "totalThrows", "value": "10"}, {"type": "totalFwdZonePass", "value": "50"}, {"type": "ballRecovery", "value": "6"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "totalTackle", "value": "3"}, {"type": "totalContest", "value": "5"}, {"type": "totalChippedPass", "value": "1"}, {"type": "errorLeadToShot", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "touches", "value": "104"}, {"type": "effectiveClearance", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "4"}, {"type": "crosses18yardplus", "value": "2"}, {"type": "openPlayPass", "value": "66"}, {"type": "crosses18yard", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "1qywnx1icmb4x7dbhhsck0vrp", "firstName": "Presnel", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Presnel", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "captain": "yes", "stat": [{"type": "passesRight", "value": "1"}, {"type": "rightsidePass", "value": "23"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "fiftyFifty", "value": "1"}, {"type": "passesLeft", "value": "23"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "aerialLost", "value": "1"}, {"type": "fwdPass", "value": "32"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "90"}, {"type": "headPass", "value": "2"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConceded", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "77"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "totalAttAssist", "value": "1"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "openPlayPass", "value": "81"}, {"type": "duelLost", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "93"}, {"type": "effectiveClearance", "value": "4"}, {"type": "totalClearance", "value": "4"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "longPassOwnToOpp", "value": "16"}, {"type": "totalFwdZonePass", "value": "37"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateFwdZonePass", "value": "34"}, {"type": "offsideProvoked", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "lostCorners", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "14"}, {"type": "backwardPass", "value": "6"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalBackZonePass", "value": "47"}, {"type": "accuratePass", "value": "80"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "aerialWon", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "accurateBackZonePass", "value": "46"}, {"type": "turnover", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalPass", "value": "84"}, {"type": "leftside<PERSON><PERSON>", "value": "23"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "cm0gwoyh5rdb216fx685a7l5h", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "possWonMid3rd", "value": "1"}, {"type": "passesRight", "value": "16"}, {"type": "rightsidePass", "value": "25"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "fiftyFifty", "value": "2"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "possLostAll", "value": "7"}, {"type": "aerialLost", "value": "2"}, {"type": "fwdPass", "value": "23"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "minsPlayed", "value": "90"}, {"type": "headPass", "value": "4"}, {"type": "totalLongBalls", "value": "5"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "80"}, {"type": "gameStarted", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "interceptionWon", "value": "3"}, {"type": "dispossessed", "value": "2"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "openPlayPass", "value": "84"}, {"type": "duelLost", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "touches", "value": "96"}, {"type": "effectiveClearance", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalClearance", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "ballRecovery", "value": "2"}, {"type": "totalFwdZonePass", "value": "24"}, {"type": "offsideProvoked", "value": "1"}, {"type": "accurateFwdZonePass", "value": "21"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "9"}, {"type": "backwardPass", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalBackZonePass", "value": "60"}, {"type": "accuratePass", "value": "80"}, {"type": "aerialWon", "value": "4"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "accurateBackZonePass", "value": "59"}, {"type": "successfulFiftyFifty", "value": "2"}, {"type": "turnover", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "interception", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "34"}, {"type": "totalPass", "value": "84"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "1vr896w03yky9kmu8x5tjkicp", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "attCmissRight", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"type": "minsPlayed", "value": "90"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "fwdPass", "value": "25"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "10"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "possLostCtrl", "value": "10"}, {"type": "attRfTotal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "74"}, {"type": "goalsConceded", "value": "2"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "attBxCentre", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesRight", "value": "34"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "fiftyFifty", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "18"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "accurateThrows", "value": "11"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "totalBackZonePass", "value": "41"}, {"type": "accuratePass", "value": "77"}, {"type": "totalLaunches", "value": "1"}, {"type": "backwardPass", "value": "24"}, {"type": "totalCross", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "31"}, {"type": "totalPass", "value": "82"}, {"type": "attOpenplay", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "totalOffside", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "38"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "107"}, {"type": "effectiveClearance", "value": "2"}, {"type": "accurateLaunches", "value": "1"}, {"type": "duelLost", "value": "3"}, {"type": "openPlayPass", "value": "79"}, {"type": "crosses18yard", "value": "2"}, {"type": "attMissRight", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "wasFouled", "value": "3"}, {"type": "accurateFwdZonePass", "value": "39"}, {"type": "totalThrows", "value": "11"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "43"}, {"type": "ballRecovery", "value": "5"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "totalChippedPass", "value": "4"}, {"type": "totalContest", "value": "3"}, {"type": "attOboxMiss", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "2lyib1m8bhrkcc3ukk12fbefp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "38"}, {"type": "duelLost", "value": "1"}, {"type": "openPlayPass", "value": "29"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "20"}, {"type": "wasFouled", "value": "1"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalContest", "value": "3"}, {"type": "totalTackle", "value": "1"}, {"type": "totalBackZonePass", "value": "10"}, {"type": "accuratePass", "value": "29"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "backwardPass", "value": "7"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "totalPass", "value": "31"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "passesRight", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "passesLeft", "value": "12"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fiftyFifty", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "9"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "rightsidePass", "value": "8"}, {"type": "penAreaEntries", "value": "2"}, {"type": "fwdPass", "value": "9"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "88"}, {"type": "possLostAll", "value": "4"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "possWonAtt3rd", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "e40eqhxzzid2chfyxph37tuxh", "firstName": "Ander", "lastName": "<PERSON>", "shortFirstName": "Ander", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "110"}, {"type": "duelLost", "value": "2"}, {"type": "openPlayPass", "value": "99"}, {"type": "blocked<PERSON><PERSON>", "value": "5"}, {"type": "wasFouled", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "accurateFwdZonePass", "value": "43"}, {"type": "ballRecovery", "value": "1"}, {"type": "totalFwdZonePass", "value": "49"}, {"type": "longPassOwnToOpp", "value": "13"}, {"type": "totalTackle", "value": "2"}, {"type": "totalChippedPass", "value": "5"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalBackZonePass", "value": "51"}, {"type": "accuratePass", "value": "94"}, {"type": "backwardPass", "value": "18"}, {"type": "longPassOwnToOppSuccess", "value": "10"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "22"}, {"type": "totalPass", "value": "100"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "successfulFiftyFifty", "value": "4"}, {"type": "accurateBackZonePass", "value": "51"}, {"type": "passesRight", "value": "16"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "passesLeft", "value": "11"}, {"type": "fiftyFifty", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "16"}, {"type": "challengeLost", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "rightsidePass", "value": "32"}, {"type": "headPass", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "13"}, {"type": "fwdPass", "value": "28"}, {"type": "possLostAll", "value": "8"}, {"type": "finalThirdEntries", "value": "10"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "possLostCtrl", "value": "8"}, {"type": "successfulOpenPlayPass", "value": "93"}, {"type": "gameStarted", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalLongBalls", "value": "6"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "f5lfo1hz7u5bk494w53arcacq", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "Junior", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 36, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "wasFouled", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "accurateFwdZonePass", "value": "31"}, {"type": "totalFwdZonePass", "value": "37"}, {"type": "ballRecovery", "value": "3"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "totalContest", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "77"}, {"type": "duelLost", "value": "3"}, {"type": "openPlayPass", "value": "66"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "totalPass", "value": "67"}, {"type": "leftside<PERSON><PERSON>", "value": "17"}, {"type": "fouls", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "accurateBackZonePass", "value": "30"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "totalBackZonePass", "value": "30"}, {"type": "accuratePass", "value": "61"}, {"type": "backwardPass", "value": "18"}, {"type": "longPassOwnToOppSuccess", "value": "8"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "totalSubOff", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "18"}, {"type": "penAreaEntries", "value": "2"}, {"type": "rightsidePass", "value": "18"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "passesRight", "value": "18"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "finalThirdEntries", "value": "12"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "possLostCtrl", "value": "8"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "60"}, {"type": "goalsConceded", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "totalLongBalls", "value": "6"}, {"type": "headPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "minsPlayed", "value": "60"}, {"type": "fwdPass", "value": "14"}, {"type": "possLostAll", "value": "8"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "5e9ilgrz3tzg9kd1gk3yvrahh", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Mbappé", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "successfulFiftyFifty", "value": "1"}, {"type": "turnover", "value": "3"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "totalFlickOn", "value": "1"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "attemptsObox", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "totalPass", "value": "37"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attemptsIbox", "value": "3"}, {"type": "lostCorners", "value": "1"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "attLfTotal", "value": "2"}, {"type": "accuratePass", "value": "29"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "duel<PERSON>on", "value": "10"}, {"type": "totalCross", "value": "6"}, {"type": "backwardPass", "value": "13"}, {"type": "totalContest", "value": "10"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attOboxMiss", "value": "1"}, {"type": "attSvLowLeft", "value": "1"}, {"type": "accurateFwdZonePass", "value": "25"}, {"type": "wonCorners", "value": "1"}, {"type": "wasFouled", "value": "3"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "totalFwdZonePass", "value": "36"}, {"type": "attObxCentre", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "shotFastbreak", "value": "1"}, {"type": "openPlayPass", "value": "35"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "crosses18yard", "value": "4"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "attSvLowRight", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalCornersIntobox", "value": "2"}, {"type": "touches", "value": "67"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "duelLost", "value": "8"}, {"type": "assistOwnGoal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "attRfTotal", "value": "2"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "totalPullBack", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonAtt3rd", "value": "2"}, {"type": "attBxCentre", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "attFreekickMiss", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "goalAssist", "value": "1"}, {"type": "touchesInOppBox", "value": "11"}, {"type": "possLostCtrl", "value": "20"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalAttAssist", "value": "2"}, {"type": "possLostAll", "value": "20"}, {"type": "attFastbreak", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "totalLayoffs", "value": "3"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "cornerTaken", "value": "4"}, {"type": "attBxLeft", "value": "2"}, {"type": "attFreekickTotal", "value": "1"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "fwdPass", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "totalScoringAtt", "value": "4"}, {"type": "attLfTarget", "value": "2"}, {"type": "minsPlayed", "value": "90"}, {"type": "attIboxTarget", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"type": "accuratePullBack", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "23"}, {"type": "rightsidePass", "value": "13"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "penAreaEntries", "value": "9"}, {"type": "attCmissLeft", "value": "1"}, {"type": "passesLeft", "value": "12"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fiftyFifty", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "totalCrossNocorner", "value": "4"}, {"type": "attMissLeft", "value": "1"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "accurateCross", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "totalFastbreak", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "4"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "drkx7msmzruq5myxk9ani6z11", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "attemptsObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "totalFlickOn", "value": "2"}, {"type": "bigChanceScored", "value": "1"}, {"type": "attMissHigh", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "totalPass", "value": "13"}, {"type": "attOpenplay", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "aerialWon", "value": "2"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "accuratePass", "value": "10"}, {"type": "backwardPass", "value": "7"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "attHdTotal", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "wasFouled", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "ballRecovery", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "openPlayPass", "value": "10"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "touches", "value": "23"}, {"type": "effectiveClearance", "value": "2"}, {"type": "goals", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "attRfTotal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "goalsConceded", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "attBxCentre", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "possLostAll", "value": "5"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "totalLayoffs", "value": "2"}, {"type": "attIboxMiss", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "minsPlayed", "value": "88"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "penAreaEntries", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "totalSubOff", "value": "1"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "overrun", "value": "1"}, {"type": "attHdGoal", "value": "1"}, {"type": "attHdMiss", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "36fznb9hzqvn6lnlla7f3ycwl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "crosses18yard", "value": "1"}, {"type": "openPlayPass", "value": "34"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "touches", "value": "42"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalFwdZonePass", "value": "23"}, {"type": "ballRecovery", "value": "3"}, {"type": "accurateFwdZonePass", "value": "20"}, {"type": "wasFouled", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "backwardPass", "value": "13"}, {"type": "accuratePass", "value": "32"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "bigChanceScored", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "successfulFiftyFifty", "value": "2"}, {"type": "attRfGoal", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attemptsIbox", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "totalPass", "value": "34"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "passesRight", "value": "9"}, {"type": "rightsidePass", "value": "7"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "fiftyFifty", "value": "3"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "fwdPass", "value": "4"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "minsPlayed", "value": "70"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "32"}, {"type": "attRfTotal", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalAttAssist", "value": "2"}, {"type": "dispossessed", "value": "2"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "3775rpg889ojktofzx1y6jpat", "firstName": "<PERSON>", "lastName": "Alcântara do Nascimento", "shortFirstName": "<PERSON>", "shortLastName": "Alcântara do Nascimento", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "accurateLongBalls", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "7"}, {"type": "totalLongBalls", "value": "1"}, {"type": "minsPlayed", "value": "2"}, {"type": "fwdPass", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "totalPass", "value": "7"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "accuratePass", "value": "7"}, {"type": "backwardPass", "value": "4"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "touches", "value": "9"}, {"type": "openPlayPass", "value": "7"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "atlhc604ri3rr4w012otutyj8", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "77fldfqlxps1xbdg29anv4vai", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "34z1r2585ifo6zdcfuu6xumfo", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "G<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "G<PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 35, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "dw9fzixumboknmucqgqffurmy", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Kalimuendo-Muinga", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 29, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "passesLeft", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "minsPlayed", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "3"}, {"type": "touches", "value": "5"}, {"type": "openPlayPass", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "backwardPass", "value": "3"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "accuratePass", "value": "4"}, {"type": "totalPass", "value": "4"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "eyzcfyn1crjcgmm98uk9u8oyy", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 38, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "5qg0coudjinnt978myh4n4n2t", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "rightsidePass", "value": "12"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "accurateChippedPass", "value": "4"}, {"type": "passesLeft", "value": "2"}, {"type": "passesRight", "value": "4"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConceded", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "26"}, {"type": "interceptionWon", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "fwdPass", "value": "7"}, {"type": "minsPlayed", "value": "30"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalChippedPass", "value": "5"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "totalFwdZonePass", "value": "15"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "openPlayPass", "value": "27"}, {"type": "duelLost", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "touches", "value": "29"}, {"type": "accurateBackZonePass", "value": "13"}, {"type": "interception", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "totalPass", "value": "28"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "backwardPass", "value": "3"}, {"type": "accuratePass", "value": "27"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "ckjcm9q26zd6q8tkq0go6oxcl", "firstName": "Sergio", "lastName": "<PERSON>", "shortFirstName": "Sergio", "shortLastName": "Rico", "matchName": "Sergio Rico", "shirtNumber": 16, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4pyehox7p3z64c4l3k8yoyfv9", "firstName": "<PERSON>", "lastName": "Sarabia García", "shortFirstName": "<PERSON>", "shortLastName": "Sarabia", "matchName": "<PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalContest", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "wasFouled", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalFwdZonePass", "value": "14"}, {"type": "openPlayPass", "value": "17"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "touches", "value": "23"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "freekick<PERSON><PERSON>", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "bigChanceScored", "value": "1"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "attOpenplay", "value": "1"}, {"type": "totalPass", "value": "17"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "attemptsIbox", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "attGoalLowCentre", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "accuratePass", "value": "13"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalCross", "value": "2"}, {"type": "backwardPass", "value": "9"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesRight", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "totalLongBalls", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "possLostAll", "value": "7"}, {"type": "totalSubOn", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "attLfGoal", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "minsPlayed", "value": "20"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "1yt2lhoij0ozjfg1othrlqsph", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "0", "sh": "4", "type": "subsMade", "value": "4"}, {"fh": "2", "sh": "2", "type": "wonCorners", "value": "4"}, {"fh": "0", "sh": "1", "type": "attSvLowLeft", "value": "1"}, {"fh": "165", "sh": "145", "type": "accurateFwdZonePass", "value": "310"}, {"fh": "194", "sh": "170", "type": "totalFwdZonePass", "value": "364"}, {"fh": "14", "sh": "7", "type": "totalThrows", "value": "21"}, {"fh": "1", "sh": "2", "type": "attObxCentre", "value": "3"}, {"fh": "14", "sh": "19", "type": "ballRecovery", "value": "33"}, {"fh": "0", "sh": "1", "type": "shotFastbreak", "value": "1"}, {"fh": "38", "sh": "31", "type": "longPassOwnToOpp", "value": "69"}, {"fh": "2", "sh": "0", "type": "attIboxBlocked", "value": "2"}, {"fh": "1", "sh": "0", "type": "offtargetAttAssist", "value": "1"}, {"fh": "2", "sh": "0", "type": "attHdTotal", "value": "2"}, {"fh": "1", "sh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "4", "sh": "6", "type": "totalTackle", "value": "10"}, {"fh": "0", "sh": "1", "type": "errorLeadToShot", "value": "1"}, {"fh": "11", "sh": "13", "type": "totalContest", "value": "24"}, {"fh": "16", "sh": "12", "type": "totalChippedPass", "value": "28"}, {"fh": "6", "sh": "5", "type": "totalClearance", "value": "11"}, {"fh": "0", "sh": "2", "type": "attOboxMiss", "value": "2"}, {"fh": "6", "sh": "5", "type": "effectiveClearance", "value": "11"}, {"fh": "1", "sh": "1", "type": "totalCornersIntobox", "value": "2"}, {"fh": "468", "sh": "397", "type": "touches", "value": "865"}, {"fh": "2", "sh": "3", "type": "attemptsConcededIbox", "value": "5"}, {"fh": "0", "sh": "1", "type": "totalYellowCard", "value": "1"}, {"fh": "1", "sh": "1", "type": "accurateLaunches", "value": "2"}, {"fh": "1", "sh": "0", "type": "ownGoalAccrued", "value": "1"}, {"fh": "0", "sh": "1", "type": "attSvLowRight", "value": "1"}, {"fh": "1", "sh": "2", "type": "won<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "0", "sh": "1", "type": "freekick<PERSON><PERSON>", "value": "1"}, {"fh": "3", "sh": "1", "type": "goals", "value": "4"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "17", "sh": "16", "type": "duelLost", "value": "33"}, {"fh": "2", "sh": "1", "type": "goalsOpenplay", "value": "3"}, {"fh": "2", "sh": "0", "type": "crosses18yardplus", "value": "2"}, {"fh": "4", "sh": "1", "type": "accurateLayoffs", "value": "5"}, {"fh": "364", "sh": "302", "type": "openPlayPass", "value": "666"}, {"fh": "2", "sh": "2", "type": "attemptedTackleFoul", "value": "4"}, {"fh": "4", "sh": "5", "type": "crosses18yard", "value": "9"}, {"fh": "0", "sh": "1", "type": "attMissRight", "value": "1"}, {"fh": "5", "sh": "8", "type": "blocked<PERSON><PERSON>", "value": "13"}, {"fh": "4", "sh": "3", "type": "attOpenplay", "value": "7"}, {"fh": "381", "sh": "316", "type": "totalPass", "value": "697"}, {"fh": "103", "sh": "77", "type": "leftside<PERSON><PERSON>", "value": "180"}, {"fh": "6", "sh": "3", "type": "interception", "value": "9"}, {"fh": "1", "sh": "4", "type": "successfulPutThrough", "value": "5"}, {"fh": "5", "sh": "3", "type": "attemptsIbox", "value": "8"}, {"fh": "1", "sh": "0", "type": "attRfGoal", "value": "1"}, {"fh": "3", "sh": "0", "type": "attemptsConcededObox", "value": "3"}, {"fh": "2", "sh": "1", "type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "1", "sh": "0", "type": "totalOffside", "value": "1"}, {"fh": "6", "sh": "5", "type": "successfulFiftyFifty", "value": "11"}, {"fh": "187", "sh": "148", "type": "accurateBackZonePass", "value": "335"}, {"fh": "1", "sh": "2", "type": "attemptsObox", "value": "3"}, {"fh": "1", "sh": "0", "type": "attMissHigh", "value": "1"}, {"fh": "4", "sh": "2", "type": "ontargetAttAssist", "value": "6"}, {"fh": "2", "sh": "1", "type": "totalFlickOn", "value": "3"}, {"fh": "2", "sh": "1", "type": "bigChanceScored", "value": "3"}, {"fh": "10", "sh": "0", "type": "aerialWon", "value": "10"}, {"fh": "30", "sh": "24", "type": "duel<PERSON>on", "value": "54"}, {"fh": "349", "sh": "293", "type": "accuratePass", "value": "642"}, {"fh": "194", "sh": "153", "type": "totalBackZonePass", "value": "347"}, {"fh": "5", "sh": "3", "type": "goalKicks", "value": "8"}, {"fh": "1", "sh": "4", "type": "totalLaunches", "value": "5"}, {"fh": "7", "sh": "7", "type": "totalCross", "value": "14"}, {"fh": "71", "sh": "65", "type": "backwardPass", "value": "136"}, {"fh": "0", "sh": "1", "type": "attGoalLowCentre", "value": "1"}, {"fh": "30", "sh": "27", "type": "longPassOwnToOppSuccess", "value": "57"}, {"fh": "0", "sh": "1", "type": "effectiveBlockedCross", "value": "1"}, {"fh": "3", "sh": "1", "type": "lostCorners", "value": "4"}, {"fh": "4", "sh": "9", "type": "unsuccessfulTouch", "value": "13"}, {"fh": "2", "sh": "1", "type": "accurateKeeperThrows", "value": "3"}, {"fh": "8", "sh": "9", "type": "fkFoulWon", "value": "17"}, {"fh": "0", "sh": "4", "type": "attLfTotal", "value": "4"}, {"fh": "4", "sh": "4", "type": "effectiveHeadClearance", "value": "8"}, {"fh": "5", "sh": "8", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "13"}, {"fh": "0", "sh": "1", "type": "attCmissLeft", "value": "1"}, {"fh": "59", "sh": "44", "type": "passesLeft", "value": "103"}, {"fh": "9", "sh": "9", "type": "accurateChippedPass", "value": "18"}, {"fh": "0", "sh": "1", "type": "overrun", "value": "1"}, {"fh": "2", "sh": "0", "type": "attGoalLowRight", "value": "2"}, {"fh": "8", "sh": "11", "type": "fiftyFifty", "value": "19"}, {"fh": "2", "sh": "0", "type": "forwardGoals", "value": "2"}, {"fh": "1", "sh": "0", "type": "attHdGoal", "value": "1"}, {"fh": "0", "sh": "1", "type": "accuratePullBack", "value": "1"}, {"type": "firstHalfGoals", "value": "3"}, {"fh": "75", "sh": "68", "type": "totalFinalThirdPasses", "value": "143"}, {"fh": "2", "sh": "3", "type": "fouledFinalThird", "value": "5"}, {"fh": "2", "sh": "0", "type": "challengeLost", "value": "2"}, {"fh": "12", "sh": "10", "type": "penAreaEntries", "value": "22"}, {"fh": "102", "sh": "87", "type": "rightsidePass", "value": "189"}, {"fh": "2", "sh": "4", "type": "fkFoulLost", "value": "6"}, {"fh": "14", "sh": "7", "type": "accurateThrows", "value": "21"}, {"fh": "0", "sh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "1", "sh": "1", "type": "goalAssistIntentional", "value": "2"}, {"fh": "8", "sh": "9", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"fh": "68.1", "sh": "61.7", "type": "possessionPercentage", "value": "65"}, {"fh": "0", "sh": "1", "type": "attMissLeft", "value": "1"}, {"fh": "1", "sh": "0", "type": "attHdMiss", "value": "1"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "sh": "0", "type": "accurateCross", "value": "3"}, {"fh": "3", "sh": "0", "type": "blockedScoringAtt", "value": "3"}, {"fh": "51", "sh": "59", "type": "passesRight", "value": "110"}, {"fh": "0", "sh": "1", "type": "totalFastbreak", "value": "1"}, {"fh": "6", "sh": "6", "type": "possWonMid3rd", "value": "12"}, {"fh": "0", "sh": "1", "type": "blocked<PERSON><PERSON>", "value": "1"}, {"fh": "6", "sh": "6", "type": "totalCrossNocorner", "value": "12"}, {"fh": "1", "sh": "1", "type": "goalAssist", "value": "2"}, {"fh": "18", "sh": "11", "type": "touchesInOppBox", "value": "29"}, {"fh": "0", "sh": "2", "type": "goalsConcededIbox", "value": "2"}, {"fh": "15", "sh": "19", "type": "accurateLongBalls", "value": "34"}, {"fh": "0", "sh": "1", "type": "attFreekickMiss", "value": "1"}, {"fh": "33", "sh": "25", "type": "finalThirdEntries", "value": "58"}, {"fh": "5", "sh": "2", "type": "totalAttAssist", "value": "7"}, {"fh": "6", "sh": "3", "type": "dispossessed", "value": "9"}, {"fh": "50", "sh": "47", "type": "possLostCtrl", "value": "97"}, {"fh": "6", "sh": "3", "type": "interceptionWon", "value": "9"}, {"fh": "4", "sh": "1", "type": "attRfTotal", "value": "5"}, {"fh": "4", "sh": "1", "type": "attAssistOpenplay", "value": "5"}, {"fh": "332", "sh": "279", "type": "successfulOpenPlayPass", "value": "611"}, {"fh": "3", "sh": "9", "type": "possWonDef3rd", "value": "12"}, {"fh": "3", "sh": "3", "type": "possWonAtt3rd", "value": "6"}, {"fh": "3", "sh": "1", "type": "attBxCentre", "value": "4"}, {"fh": "0", "sh": "2", "type": "goalsConceded", "value": "2"}, {"fh": "3", "sh": "1", "type": "attIboxGoal", "value": "4"}, {"fh": "1", "sh": "1", "type": "goalAssistOpenplay", "value": "2"}, {"fh": "0", "sh": "1", "type": "totalPullBack", "value": "1"}, {"fh": "21", "sh": "25", "type": "totalLongBalls", "value": "46"}, {"fh": "0", "sh": "1", "type": "attCmissRight", "value": "1"}, {"fh": "2", "sh": "3", "type": "ontargetScoringAtt", "value": "5"}, {"fh": "0", "sh": "1", "type": "attLfGoal", "value": "1"}, {"fh": "58", "sh": "55", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "113"}, {"fh": "0", "sh": "2", "type": "attIboxTarget", "value": "2"}, {"fh": "105", "sh": "87", "type": "fwdPass", "value": "192"}, {"fh": "1", "sh": "0", "type": "totalHighClaim", "value": "1"}, {"fh": "0", "sh": "2", "type": "attLfTarget", "value": "2"}, {"fh": "6", "sh": "5", "type": "totalScoringAtt", "value": "11"}, {"fh": "1", "sh": "0", "type": "attOboxBlocked", "value": "1"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "2"}, {"fh": "0", "sh": "1", "type": "attFastbreak", "value": "1"}, {"fh": "2", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "4", "sh": "5", "type": "aerialLost", "value": "9"}, {"fh": "50", "sh": "47", "type": "possLostAll", "value": "97"}, {"fh": "5", "sh": "2", "type": "totalLayoffs", "value": "7"}, {"fh": "1", "sh": "0", "type": "accurateCornersIntobox", "value": "1"}, {"fh": "2", "sh": "2", "type": "cornerTaken", "value": "4"}, {"fh": "1", "sh": "1", "type": "attBxLeft", "value": "2"}, {"fh": "4", "sh": "4", "type": "headClea<PERSON>", "value": "8"}, {"fh": "0", "sh": "1", "type": "attFreekickTotal", "value": "1"}, {"fh": "5", "sh": "3", "type": "accurateGoalKicks", "value": "8"}, {"fh": "1", "sh": "0", "type": "attIboxMiss", "value": "1"}, {"type": "formationUsed", "value": "433"}], "kit": {"id": "659", "colour1": "#000066", "colour2": "#FF0000", "type": "home"}}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "formationUsed": "532", "player": [{"playerId": "2vgr8c97x4b2e51tyvvixusid", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "successfulOpenPlayPass", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLongBalls", "value": "11"}, {"type": "goalsConceded", "value": "4"}, {"type": "goalsConcededIbox", "value": "4"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "possLostCtrl", "value": "9"}, {"type": "possLostAll", "value": "9"}, {"type": "accurateGoalKicks", "value": "4"}, {"type": "savedIbox", "value": "2"}, {"type": "fwdPass", "value": "8"}, {"type": "totalHighClaim", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "rightsidePass", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "totalPass", "value": "16"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateKeeperThrows", "value": "2"}, {"type": "accuratePass", "value": "7"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "aerialWon", "value": "1"}, {"type": "saves", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "goalKicks", "value": "4"}, {"type": "totalLaunches", "value": "6"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalClearance", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "openPlayPass", "value": "9"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "touches", "value": "22"}, {"type": "attemptsConcededIbox", "value": "9"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "d5at3cowukpxiptq5ozcbe6s5", "firstName": "<PERSON>", "lastName": "Baidoo Djiku", "shortFirstName": "<PERSON>", "shortLastName": "Djiku", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "longPassOwnToOppSuccess", "value": "5"}, {"type": "lostCorners", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "accuratePass", "value": "27"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateBackZonePass", "value": "20"}, {"type": "fouls", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "totalPass", "value": "32"}, {"type": "interception", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "openPlayPass", "value": "32"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "touches", "value": "38"}, {"type": "effectiveClearance", "value": "2"}, {"type": "attemptsConcededIbox", "value": "8"}, {"type": "redCard", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "totalTackle", "value": "1"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalClearance", "value": "2"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "ballRecovery", "value": "4"}, {"type": "totalThrows", "value": "1"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "longPassOwnToOpp", "value": "6"}, {"type": "aerialLost", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "headPass", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "minsPlayed", "value": "81"}, {"type": "fwdPass", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "gameStarted", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "interceptionWon", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "passesRight", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "accurateThrows", "value": "1"}, {"type": "rightsidePass", "value": "18"}, {"type": "passesLeft", "value": "3"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "fiftyFifty", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "cb6w7n7n1eh9huwzyzvgmdxih", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "longPassOwnToOppSuccess", "value": "5"}, {"type": "totalBackZonePass", "value": "18"}, {"type": "accuratePass", "value": "28"}, {"type": "backwardPass", "value": "1"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "accurateBackZonePass", "value": "17"}, {"type": "fouls", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "totalPass", "value": "33"}, {"type": "interception", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "openPlayPass", "value": "33"}, {"type": "touches", "value": "38"}, {"type": "effectiveClearance", "value": "2"}, {"type": "attemptsConcededIbox", "value": "9"}, {"type": "duelLost", "value": "1"}, {"type": "totalChippedPass", "value": "6"}, {"type": "totalClearance", "value": "2"}, {"type": "accurateFwdZonePass", "value": "11"}, {"type": "offsideProvoked", "value": "1"}, {"type": "totalFwdZonePass", "value": "15"}, {"type": "ballRecovery", "value": "3"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "possLostAll", "value": "5"}, {"type": "minsPlayed", "value": "90"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "fwdPass", "value": "12"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "28"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "goalsConceded", "value": "4"}, {"type": "totalLongBalls", "value": "5"}, {"type": "goalsConcededIbox", "value": "4"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "possLostCtrl", "value": "5"}, {"type": "interceptionWon", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "penAreaEntries", "value": "1"}, {"type": "rightsidePass", "value": "10"}, {"type": "passesLeft", "value": "3"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "fiftyFifty", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "eo47h2ekymiisj3kry3og7tgp", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "accurateFwdZonePass", "value": "8"}, {"type": "wasFouled", "value": "2"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "ballRecovery", "value": "7"}, {"type": "totalClearance", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "accurateLaunches", "value": "2"}, {"type": "attemptsConcededIbox", "value": "9"}, {"type": "touches", "value": "44"}, {"type": "effectiveClearance", "value": "2"}, {"type": "duelLost", "value": "6"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "29"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "interception", "value": "3"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalPass", "value": "31"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "fouls", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateBackZonePass", "value": "20"}, {"type": "totalBackZonePass", "value": "21"}, {"type": "accuratePass", "value": "28"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "aerialWon", "value": "2"}, {"type": "backwardPass", "value": "4"}, {"type": "totalLaunches", "value": "4"}, {"type": "lostCorners", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "challengeLost", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "passesRight", "value": "4"}, {"type": "possWonMid3rd", "value": "4"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "4"}, {"type": "interceptionWon", "value": "3"}, {"type": "possLostCtrl", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "26"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalsConceded", "value": "4"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "fwdPass", "value": "13"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "minsPlayed", "value": "90"}, {"type": "possLostAll", "value": "3"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ysk6656hj0na1hrizgt83k5x", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Wing Back", "positionSide": "Left", "formationPlace": "3", "captain": "yes", "stat": [{"type": "successfulFiftyFifty", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "accurateBackZonePass", "value": "15"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "fouls", "value": "3"}, {"type": "totalPass", "value": "37"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "accuratePass", "value": "30"}, {"type": "totalBackZonePass", "value": "18"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "totalCross", "value": "8"}, {"type": "backwardPass", "value": "10"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalChippedPass", "value": "4"}, {"type": "totalTackle", "value": "3"}, {"type": "accurateFwdZonePass", "value": "16"}, {"type": "wonCorners", "value": "2"}, {"type": "wasFouled", "value": "2"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "27"}, {"type": "totalThrows", "value": "7"}, {"type": "openPlayPass", "value": "36"}, {"type": "crosses18yardplus", "value": "5"}, {"type": "blocked<PERSON><PERSON>", "value": "4"}, {"type": "attemptedTackleFoul", "value": "3"}, {"type": "crosses18yard", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "touches", "value": "62"}, {"type": "attemptsConcededIbox", "value": "9"}, {"type": "duelLost", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "freekick<PERSON><PERSON>", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "29"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLongBalls", "value": "4"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "goalsConceded", "value": "4"}, {"type": "goalsConcededIbox", "value": "4"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "goalAssist", "value": "1"}, {"type": "possLostCtrl", "value": "15"}, {"type": "totalAttAssist", "value": "1"}, {"type": "possLostAll", "value": "15"}, {"type": "cornerTaken", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "9"}, {"type": "minsPlayed", "value": "90"}, {"type": "challengeLost", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "12"}, {"type": "accurateThrows", "value": "7"}, {"type": "rightsidePass", "value": "19"}, {"type": "penAreaEntries", "value": "8"}, {"type": "passesLeft", "value": "9"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "fiftyFifty", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalCrossNocorner", "value": "7"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "4ju2soyxqa6hu0oo007ej8wdl", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Wing Back", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "attemptsConcededObox", "value": "1"}, {"type": "totalPass", "value": "33"}, {"type": "leftside<PERSON><PERSON>", "value": "19"}, {"type": "fouls", "value": "1"}, {"type": "accurateBackZonePass", "value": "15"}, {"type": "turnover", "value": "1"}, {"type": "backwardPass", "value": "7"}, {"type": "totalCross", "value": "3"}, {"type": "accuratePass", "value": "30"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "totalThrows", "value": "1"}, {"type": "accurateFwdZonePass", "value": "15"}, {"type": "totalContest", "value": "1"}, {"type": "totalTackle", "value": "3"}, {"type": "duelLost", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "attemptsConcededIbox", "value": "8"}, {"type": "touches", "value": "47"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "crosses18yard", "value": "2"}, {"type": "openPlayPass", "value": "33"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "possLostCtrl", "value": "10"}, {"type": "dispossessed", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "yellowCard", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "30"}, {"type": "gameStarted", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"type": "minsPlayed", "value": "63"}, {"type": "possLostAll", "value": "10"}, {"type": "aerialLost", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateThrows", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "penAreaEntries", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "10"}, {"type": "passesRight", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "wg9qybhs3qeq9awhux331uqx", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "totalLaunches", "value": "2"}, {"type": "backwardPass", "value": "10"}, {"type": "totalCross", "value": "4"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalBackZonePass", "value": "18"}, {"type": "accuratePass", "value": "31"}, {"type": "attemptsObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "attMissHigh", "value": "1"}, {"type": "turnover", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "totalPass", "value": "36"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "crosses18yard", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "openPlayPass", "value": "35"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "attemptsConcededIbox", "value": "9"}, {"type": "touches", "value": "49"}, {"type": "totalCornersIntobox", "value": "3"}, {"type": "attOboxMiss", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "22"}, {"type": "ballRecovery", "value": "5"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "18"}, {"type": "cornerTaken", "value": "3"}, {"type": "totalLayoffs", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "14"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "90"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "fwdPass", "value": "9"}, {"type": "headPass", "value": "1"}, {"type": "goalsConceded", "value": "4"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "yellowCard", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "attRfTotal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "30"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "possLostCtrl", "value": "14"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "goalsConcededIbox", "value": "4"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "passesRight", "value": "6"}, {"type": "accurateCross", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "rightsidePass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "9"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "overrun", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "42vsnv2j6emkbynforgon4n2t", "firstName": "Sanjin", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Sanjin", "shortLastName": "Prcic", "matchName": "<PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "8", "stat": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "accurateChippedPass", "value": "7"}, {"type": "fiftyFifty", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "15"}, {"type": "challengeLost", "value": "1"}, {"type": "rightsidePass", "value": "16"}, {"type": "passesRight", "value": "11"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "accurateLongBalls", "value": "9"}, {"type": "totalAttAssist", "value": "1"}, {"type": "possLostCtrl", "value": "8"}, {"type": "interceptionWon", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "44"}, {"type": "gameStarted", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalLongBalls", "value": "11"}, {"type": "headPass", "value": "1"}, {"type": "minsPlayed", "value": "76"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "13"}, {"type": "fwdPass", "value": "13"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "8"}, {"type": "totalLayoffs", "value": "2"}, {"type": "accurateFwdZonePass", "value": "24"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "28"}, {"type": "attObxCentre", "value": "1"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalChippedPass", "value": "8"}, {"type": "attOboxMiss", "value": "1"}, {"type": "touches", "value": "55"}, {"type": "attemptsConcededIbox", "value": "8"}, {"type": "duelLost", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "openPlayPass", "value": "51"}, {"type": "attMissRight", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "3"}, {"type": "totalPass", "value": "51"}, {"type": "leftside<PERSON><PERSON>", "value": "19"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "accurateBackZonePass", "value": "20"}, {"type": "attemptsObox", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "accuratePass", "value": "44"}, {"type": "totalBackZonePass", "value": "23"}, {"type": "backwardPass", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "6"}, {"type": "attLfTotal", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "88nw24pkg2ikj9c91tccvqol", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "duelLost", "value": "5"}, {"type": "effectiveClearance", "value": "1"}, {"type": "touches", "value": "13"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "openPlayPass", "value": "6"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "accuratePass", "value": "5"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "totalPass", "value": "6"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "fiftyFifty", "value": "2"}, {"type": "overrun", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "handBall", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "challengeLost", "value": "3"}, {"type": "minsPlayed", "value": "42"}, {"type": "headPass", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "possLostCtrl", "value": "5"}, {"type": "interceptionWon", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "950o8rjqvnl5bnidq2dc3fglx", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "Gameiro", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "9", "stat": [{"type": "passesRight", "value": "4"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attHdGoal", "value": "1"}, {"type": "fiftyFifty", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "passesLeft", "value": "8"}, {"type": "totalSubOff", "value": "1"}, {"type": "rightsidePass", "value": "6"}, {"type": "totalFinalThirdPasses", "value": "14"}, {"type": "minsPlayed", "value": "76"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "11"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "fwdPass", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalLayoffs", "value": "2"}, {"type": "attIboxMiss", "value": "1"}, {"type": "attGoalLowLeft", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "possLostAll", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "touchesInOppBox", "value": "7"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "attIboxGoal", "value": "1"}, {"type": "attBxCentre", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "23"}, {"type": "gameStarted", "value": "1"}, {"type": "duelLost", "value": "3"}, {"type": "goals", "value": "1"}, {"type": "attemptsConcededIbox", "value": "8"}, {"type": "touches", "value": "36"}, {"type": "blocked<PERSON><PERSON>", "value": "3"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "openPlayPass", "value": "26"}, {"type": "ballRecovery", "value": "1"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "accurateFwdZonePass", "value": "18"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "attHdTotal", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "backwardPass", "value": "12"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "aerialWon", "value": "1"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "accuratePass", "value": "26"}, {"type": "attLfTotal", "value": "1"}, {"type": "attemptsIbox", "value": "3"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalPass", "value": "29"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "attOpenplay", "value": "3"}, {"type": "attCmissHigh", "value": "1"}, {"type": "accurateBackZonePass", "value": "8"}, {"type": "attMissHigh", "value": "1"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "totalOffside", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "am0pcyvtaws1i69jt09dgnsnp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Ajorque", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Ajorque", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "challengeLost", "value": "1"}, {"type": "accurateFlickOn", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "rightsidePass", "value": "6"}, {"type": "penAreaEntries", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "totalSubOff", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attHdGoal", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "16"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "goalsConceded", "value": "3"}, {"type": "attIboxGoal", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "aerialLost", "value": "6"}, {"type": "headPass", "value": "2"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "fwdPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "minsPlayed", "value": "76"}, {"type": "totalContest", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "attHdTotal", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "ownGoals", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "openPlayPass", "value": "17"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attemptsConcededIbox", "value": "8"}, {"type": "touches", "value": "30"}, {"type": "duelLost", "value": "9"}, {"type": "goals", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalFlickOn", "value": "2"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "totalPass", "value": "18"}, {"type": "fouls", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalBackZonePass", "value": "10"}, {"type": "accuratePass", "value": "17"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "aerialWon", "value": "2"}, {"type": "backwardPass", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "6u47fsriz3ejlf72hxtsew5bd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "attemptsConcededIbox", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "touches", "value": "15"}, {"type": "duelLost", "value": "6"}, {"type": "openPlayPass", "value": "8"}, {"type": "crosses18yard", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "totalThrows", "value": "2"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "accuratePass", "value": "8"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "backwardPass", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "totalPass", "value": "8"}, {"type": "fouls", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "passesRight", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "challengeLost", "value": "3"}, {"type": "accurateThrows", "value": "2"}, {"type": "minsPlayed", "value": "27"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "fwdPass", "value": "2"}, {"type": "possLostAll", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "d9y9v3rvj0dkiuaohfu46w7o5", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "bndkgzdolm2c3iteenkv8s3it", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "duelLost", "value": "1"}, {"type": "touches", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "accuratePass", "value": "3"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "minsPlayed", "value": "14"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "egtyl4noukbjkm1va1i11e87o", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cgeh7qj32flqu9p3a4wnn3s9h", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 16, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "1ho9nox6j8s5cxd1kxjspsrca", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4gucmltvhuvakf8mrheveqdt6", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "headClea<PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "minsPlayed", "value": "14"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "fiftyFifty", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "aerialWon", "value": "2"}, {"type": "accuratePass", "value": "1"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "successfulFiftyFifty", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "touches", "value": "5"}, {"type": "totalClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "c8u45gxriyq1rup5fpr30xrdh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "totalPass", "value": "28"}, {"type": "fouls", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "turnover", "value": "1"}, {"type": "successfulFiftyFifty", "value": "2"}, {"type": "totalOffside", "value": "1"}, {"type": "accurateBackZonePass", "value": "10"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "accuratePass", "value": "23"}, {"type": "totalBackZonePass", "value": "11"}, {"type": "backwardPass", "value": "6"}, {"type": "totalCross", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalFwdZonePass", "value": "19"}, {"type": "totalThrows", "value": "1"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "totalTackle", "value": "2"}, {"type": "totalContest", "value": "2"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "touches", "value": "38"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "openPlayPass", "value": "28"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "goalAssist", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "possLostCtrl", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "23"}, {"type": "goalsConceded", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "totalLongBalls", "value": "3"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "minsPlayed", "value": "48"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "fwdPass", "value": "9"}, {"type": "possLostAll", "value": "9"}, {"type": "totalSubOn", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "fiftyFifty", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "11"}, {"type": "challengeLost", "value": "2"}, {"type": "penAreaEntries", "value": "4"}, {"type": "accurateThrows", "value": "1"}, {"type": "rightsidePass", "value": "5"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "passesRight", "value": "7"}, {"type": "accurateCross", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "20n6djablebcoizw6b219rp3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "attemptsConcededObox", "value": "2"}, {"type": "attemptsIbox", "value": "1"}, {"type": "totalPass", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "accuratePass", "value": "1"}, {"type": "attOneOnOne", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "attMissRight", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "minsPlayed", "value": "14"}, {"type": "attCmissRight", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "as310m2g7v386tnuzf1n0a19l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "2", "sh": "2", "type": "lostCorners", "value": "4"}, {"fh": "4", "sh": "5", "type": "unsuccessfulTouch", "value": "9"}, {"fh": "10", "sh": "19", "type": "longPassOwnToOppSuccess", "value": "29"}, {"fh": "0", "sh": "1", "type": "attOneOnOne", "value": "1"}, {"fh": "1", "sh": "3", "type": "effectiveHeadClearance", "value": "4"}, {"fh": "2", "sh": "0", "type": "attLfTotal", "value": "2"}, {"fh": "1", "sh": "1", "type": "accurateKeeperThrows", "value": "2"}, {"fh": "2", "sh": "4", "type": "fkFoulWon", "value": "6"}, {"fh": "110", "sh": "81", "type": "totalBackZonePass", "value": "191"}, {"fh": "146", "sh": "163", "type": "accuratePass", "value": "309"}, {"fh": "17", "sh": "16", "type": "duel<PERSON>on", "value": "33"}, {"fh": "0", "sh": "2", "type": "saves", "value": "2"}, {"fh": "4", "sh": "5", "type": "aerialWon", "value": "9"}, {"fh": "30", "sh": "34", "type": "backwardPass", "value": "64"}, {"fh": "8", "sh": "10", "type": "totalCross", "value": "18"}, {"fh": "8", "sh": "6", "type": "totalLaunches", "value": "14"}, {"fh": "0", "sh": "1", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "2", "sh": "2", "type": "goalKicks", "value": "4"}, {"fh": "2", "sh": "6", "type": "successfulFiftyFifty", "value": "8"}, {"fh": "1", "sh": "1", "type": "totalOffside", "value": "2"}, {"fh": "1", "sh": "1", "type": "totalFlickOn", "value": "2"}, {"fh": "2", "sh": "2", "type": "ontargetAttAssist", "value": "4"}, {"fh": "2", "sh": "0", "type": "attMissHigh", "value": "2"}, {"fh": "3", "sh": "0", "type": "attemptsObox", "value": "3"}, {"fh": "93", "sh": "72", "type": "accurateBackZonePass", "value": "165"}, {"fh": "6", "sh": "4", "type": "interception", "value": "10"}, {"fh": "4", "sh": "4", "type": "successfulPutThrough", "value": "8"}, {"fh": "1", "sh": "0", "type": "attCmissHigh", "value": "1"}, {"fh": "50", "sh": "55", "type": "leftside<PERSON><PERSON>", "value": "105"}, {"fh": "174", "sh": "190", "type": "totalPass", "value": "364"}, {"fh": "3", "sh": "3", "type": "attOpenplay", "value": "6"}, {"fh": "1", "sh": "2", "type": "attemptsConcededObox", "value": "3"}, {"fh": "1", "sh": "1", "type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "2", "sh": "3", "type": "attemptsIbox", "value": "5"}, {"fh": "166", "sh": "182", "type": "openPlayPass", "value": "348"}, {"fh": "3", "sh": "1", "type": "accurateLayoffs", "value": "4"}, {"fh": "0", "sh": "2", "type": "goalsOpenplay", "value": "2"}, {"fh": "3", "sh": "4", "type": "crosses18yardplus", "value": "7"}, {"fh": "6", "sh": "12", "type": "blocked<PERSON><PERSON>", "value": "18"}, {"fh": "2", "sh": "4", "type": "crosses18yard", "value": "6"}, {"fh": "5", "sh": "6", "type": "attemptedTackleFoul", "value": "11"}, {"fh": "1", "sh": "1", "type": "attMissRight", "value": "2"}, {"fh": "1", "sh": "1", "type": "accurateLaunches", "value": "2"}, {"fh": "0", "sh": "2", "type": "totalYellowCard", "value": "2"}, {"fh": "6", "sh": "3", "type": "attemptsConcededIbox", "value": "9"}, {"fh": "240", "sh": "259", "type": "touches", "value": "499"}, {"fh": "3", "sh": "1", "type": "totalCornersIntobox", "value": "4"}, {"fh": "6", "sh": "4", "type": "effectiveClearance", "value": "10"}, {"fh": "0", "sh": "3", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "0", "sh": "2", "type": "goals", "value": "2"}, {"fh": "30", "sh": "24", "type": "duelLost", "value": "54"}, {"fh": "0", "sh": "1", "type": "freekick<PERSON><PERSON>", "value": "1"}, {"fh": "6", "sh": "6", "type": "won<PERSON><PERSON><PERSON>", "value": "12"}, {"fh": "3", "sh": "0", "type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "6", "sh": "4", "type": "totalClearance", "value": "10"}, {"fh": "0", "sh": "1", "type": "interceptionsInBox", "value": "1"}, {"fh": "2", "sh": "4", "type": "totalContest", "value": "6"}, {"fh": "15", "sh": "13", "type": "totalChippedPass", "value": "28"}, {"fh": "9", "sh": "7", "type": "totalTackle", "value": "16"}, {"fh": "3", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "0", "sh": "2", "type": "attHdTotal", "value": "2"}, {"fh": "2", "sh": "0", "type": "attOboxMiss", "value": "2"}, {"fh": "53", "sh": "94", "type": "accurateFwdZonePass", "value": "147"}, {"fh": "1", "sh": "0", "type": "ownGoals", "value": "1"}, {"fh": "3", "sh": "1", "type": "wonCorners", "value": "4"}, {"fh": "1", "sh": "4", "type": "subsMade", "value": "5"}, {"fh": "1", "sh": "0", "type": "attIboxBlocked", "value": "1"}, {"fh": "1", "sh": "0", "type": "offtargetAttAssist", "value": "1"}, {"fh": "15", "sh": "26", "type": "longPassOwnToOpp", "value": "41"}, {"fh": "0", "sh": "1", "type": "totalRedCard", "value": "1"}, {"fh": "4", "sh": "8", "type": "totalThrows", "value": "12"}, {"fh": "3", "sh": "0", "type": "attObxCentre", "value": "3"}, {"fh": "72", "sh": "119", "type": "totalFwdZonePass", "value": "191"}, {"fh": "20", "sh": "22", "type": "ballRecovery", "value": "42"}, {"fh": "46", "sh": "47", "type": "possLostAll", "value": "93"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "10", "sh": "0", "type": "aerialLost", "value": "10"}, {"fh": "0", "sh": "1", "type": "attGoalLowLeft", "value": "1"}, {"fh": "1", "sh": "1", "type": "attIboxMiss", "value": "2"}, {"fh": "2", "sh": "2", "type": "accurateGoalKicks", "value": "4"}, {"fh": "1", "sh": "3", "type": "headClea<PERSON>", "value": "4"}, {"fh": "3", "sh": "1", "type": "cornerTaken", "value": "4"}, {"fh": "4", "sh": "1", "type": "totalLayoffs", "value": "5"}, {"fh": "0", "sh": "2", "type": "savedIbox", "value": "2"}, {"fh": "0", "sh": "1", "type": "attCmissRight", "value": "1"}, {"fh": "0", "sh": "2", "type": "ontargetScoringAtt", "value": "2"}, {"fh": "0", "sh": "1", "type": "totalHighClaim", "value": "1"}, {"fh": "5", "sh": "3", "type": "totalScoringAtt", "value": "8"}, {"fh": "1", "sh": "0", "type": "attOboxBlocked", "value": "1"}, {"fh": "43", "sh": "52", "type": "fwdPass", "value": "95"}, {"fh": "20", "sh": "56", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "76"}, {"fh": "3", "sh": "0", "type": "attAssistOpenplay", "value": "3"}, {"fh": "139", "sh": "157", "type": "successfulOpenPlayPass", "value": "296"}, {"fh": "3", "sh": "1", "type": "attRfTotal", "value": "4"}, {"fh": "0", "sh": "2", "type": "goalAssistOpenplay", "value": "2"}, {"fh": "24", "sh": "24", "type": "totalLongBalls", "value": "48"}, {"fh": "3", "sh": "1", "type": "goalsConceded", "value": "4"}, {"fh": "0", "sh": "2", "type": "attIboxGoal", "value": "2"}, {"fh": "0", "sh": "1", "type": "possWonAtt3rd", "value": "1"}, {"fh": "11", "sh": "10", "type": "possWonDef3rd", "value": "21"}, {"fh": "2", "sh": "3", "type": "attBxCentre", "value": "5"}, {"fh": "12", "sh": "13", "type": "accurateLongBalls", "value": "25"}, {"fh": "12", "sh": "17", "type": "finalThirdEntries", "value": "29"}, {"fh": "3", "sh": "1", "type": "goalsConcededIbox", "value": "4"}, {"fh": "3", "sh": "10", "type": "touchesInOppBox", "value": "13"}, {"fh": "0", "sh": "2", "type": "goalAssist", "value": "2"}, {"fh": "6", "sh": "4", "type": "interceptionWon", "value": "10"}, {"fh": "46", "sh": "47", "type": "possLostCtrl", "value": "93"}, {"fh": "3", "sh": "2", "type": "totalAttAssist", "value": "5"}, {"fh": "4", "sh": "2", "type": "dispossessed", "value": "6"}, {"fh": "8", "sh": "9", "type": "possWonMid3rd", "value": "17"}, {"fh": "1", "sh": "0", "type": "attIboxOwnGoal", "value": "1"}, {"fh": "5", "sh": "9", "type": "totalCrossNocorner", "value": "14"}, {"fh": "31.9", "sh": "38.3", "type": "possessionPercentage", "value": "35"}, {"fh": "2", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "0", "sh": "2", "type": "goalAssistIntentional", "value": "2"}, {"fh": "18", "sh": "32", "type": "passesRight", "value": "50"}, {"fh": "0", "sh": "3", "type": "accurateCross", "value": "3"}, {"fh": "2", "sh": "0", "type": "blockedScoringAtt", "value": "2"}, {"fh": "0", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "0", "sh": "1", "type": "accurateFlickOn", "value": "1"}, {"fh": "8", "sh": "9", "type": "challengeLost", "value": "17"}, {"fh": "26", "sh": "68", "type": "totalFinalThirdPasses", "value": "94"}, {"fh": "0", "sh": "1", "type": "fouledFinalThird", "value": "1"}, {"fh": "51", "sh": "49", "type": "rightsidePass", "value": "100"}, {"fh": "9", "sh": "9", "type": "fkFoulLost", "value": "18"}, {"fh": "4", "sh": "8", "type": "accurateThrows", "value": "12"}, {"fh": "0", "sh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "10", "sh": "12", "type": "penAreaEntries", "value": "22"}, {"fh": "1", "sh": "0", "type": "handBall", "value": "1"}, {"fh": "10", "sh": "8", "type": "accurateChippedPass", "value": "18"}, {"fh": "16", "sh": "28", "type": "passesLeft", "value": "44"}, {"fh": "6", "sh": "12", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "18"}, {"fh": "0", "sh": "2", "type": "forwardGoals", "value": "2"}, {"fh": "8", "sh": "11", "type": "fiftyFifty", "value": "19"}, {"fh": "0", "sh": "2", "type": "attHdGoal", "value": "2"}, {"fh": "0", "sh": "1", "type": "attGoalLowRight", "value": "1"}, {"fh": "1", "sh": "1", "type": "overrun", "value": "2"}, {"type": "formationUsed", "value": "532"}], "kit": {"id": "8124", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"attendance": "46962", "matchOfficial": [{"id": "maohjb0q36etyc8e32pm1n11", "type": "Main", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>"}, {"id": "237wf33tlj0dp8abnkmk4lc5x", "type": "Assistant referee 1", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>"}, {"id": "eky20xkl3yitd09g5lgeg7vth", "type": "Assistant referee 2", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>"}, {"id": "as0d8uqw2fqdu2elyjdqoop1x", "type": "Fourth official", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>"}]}}}