{"matchInfo": {"id": "bukekpxmpmbs5qmcsrmgx2b6c", "coverageLevel": "13", "date": "2023-08-08Z", "time": "22:00:00Z", "localDate": "2023-08-08", "localTime": "19:00:00", "postMatch": "1", "numberOfPeriods": 2, "periodLength": 45, "lastUpdated": "2023-08-09T00:09:03Z", "description": "Fluminense vs Argentinos Juniors", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "59tpnfrwnvhnhzmnvfyug68hj", "name": "CONMEBOL Libertadores", "knownName": "CONMEBOL Libertadores", "competitionCode": "COL", "competitionFormat": "International cup", "country": {"id": "7ygvdgl31hirp07yeye1tvsut", "name": "South America"}}, "tournamentCalendar": {"id": "dbsk2r2snitn1gtnz9t292hhw", "startDate": "2023-02-08Z", "endDate": "2023-11-11Z", "name": "2023"}, "stage": {"id": "dc5vzhtkmslth6kxeigezbndg", "formatId": "614sqb9421oaflw5mpvvm9uj9", "startDate": "2023-08-02Z", "endDate": "2023-08-11Z", "name": "8th Finals"}, "contestant": [{"id": "2b3mar72yy8d6uvat1ka6tn3r", "name": "Fluminense", "shortName": "Fluminense", "officialName": "Fluminense FC", "code": "FLU", "position": "home", "country": {"id": "2vufyvpoxd9lfl9f6vpp7tz6y", "name": "Brazil"}}, {"id": "71ajatxnuhc5cnm3xvgdky49w", "name": "Argentinos Juniors", "shortName": "Argentinos", "officialName": "Argentinos Juniors", "code": "ARG", "position": "away", "country": {"id": "2vovxa97k7v7ofa85dah2xktb", "name": "Argentina"}}], "venue": {"id": "dygqhhdie2q91cj9z8k4rb9h1", "neutral": "no", "longName": "Estadio Jornalista M<PERSON> (Maracanã)", "shortName": "Estadio Jornalista M<PERSON> (Maracanã)"}}, "liveData": {"matchDetails": {"periodId": 14, "matchStatus": "Played", "winner": "home", "relatedMatchId": "9kne7c2nt0bxn5wr6y1q9nqc4", "matchLengthMin": 101, "matchLengthSec": 19, "period": [{"id": 1, "start": "2023-08-08T22:00:14Z", "end": "2023-08-08T22:46:20Z", "lengthMin": 46, "lengthSec": 6, "announcedInjuryTime": 60}, {"id": 2, "start": "2023-08-08T23:01:45Z", "end": "2023-08-08T23:56:58Z", "lengthMin": 55, "lengthSec": 13, "announcedInjuryTime": 600}], "scores": {"ht": {"home": 0, "away": 0}, "ft": {"home": 2, "away": 0}, "total": {"home": 2, "away": 0}, "aggregate": {"home": 3, "away": 1}}}, "goal": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 86, "timeMinSec": "85:20", "lastUpdated": "2023-08-08T23:43:19Z", "timestamp": "2023-08-08T23:42:06Z", "type": "G", "scorerId": "51xii47wyc6cefmrwoijpdvbp", "scorerName": "<PERSON>", "assistPlayerId": "83jg484gnx01poxy7w94toges", "assistPlayerName": "<PERSON>", "optaEventId": "2574762481", "homeScore": 1, "awayScore": 0}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 97, "timeMinSec": "96:37", "lastUpdated": "2023-08-08T23:54:49Z", "timestamp": "2023-08-08T23:53:23Z", "type": "G", "scorerId": "83jg484gnx01poxy7w94toges", "scorerName": "<PERSON>", "optaEventId": "2574763401", "homeScore": 2, "awayScore": 0}], "card": [{"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 1, "timeMin": 43, "timeMinSec": "42:52", "lastUpdated": "2023-08-08T22:43:21Z", "timestamp": "2023-08-08T22:43:07Z", "type": "YC", "playerId": "6mgrw5ld3fdg7vubcfuzravpw", "playerName": "<PERSON><PERSON>", "optaEventId": "2574759473", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 49, "timeMinSec": "48:16", "lastUpdated": "2023-08-08T23:05:22Z", "timestamp": "2023-08-08T23:05:02Z", "type": "YC", "playerId": "5csw8o2v8b6vohlnpdje5e2qy", "playerName": "<PERSON><PERSON>", "optaEventId": "2574760045", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 54, "timeMinSec": "53:50", "lastUpdated": "2023-08-08T23:10:42Z", "timestamp": "2023-08-08T23:10:36Z", "type": "YC", "playerId": "ayb309hol7w8us3tn4yr2fl04", "playerName": "<PERSON><PERSON>", "optaEventId": "2574760355", "cardReason": "Dissent"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 71, "timeMinSec": "70:56", "lastUpdated": "2023-08-08T23:27:53Z", "timestamp": "2023-08-08T23:27:42Z", "type": "YC", "playerId": "9r6gsqdzcbx1226xfwj7jnz6x", "playerName": "<PERSON><PERSON>", "optaEventId": "2574761511", "cardReason": "F<PERSON>l"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 75, "timeMinSec": "74:58", "lastUpdated": "2023-08-08T23:31:58Z", "timestamp": "2023-08-08T23:31:44Z", "type": "YC", "playerId": "bgsz3d0fmlaw8003dn66i4m0a", "playerName": "<PERSON>", "optaEventId": "2574761763", "cardReason": "F<PERSON>l"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 76, "timeMinSec": "75:05", "lastUpdated": "2023-08-08T23:34:28Z", "timestamp": "2023-08-08T23:31:51Z", "type": "YC", "playerId": "b8yk8yve4u39s9gjjvedzf6l0", "playerName": "<PERSON>. <PERSON>", "optaEventId": "2574761849", "cardReason": "Dissent"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 83, "timeMinSec": "82:18", "lastUpdated": "2023-08-08T23:39:13Z", "timestamp": "2023-08-08T23:39:04Z", "type": "YC", "playerId": "83jg484gnx01poxy7w94toges", "playerName": "<PERSON>", "optaEventId": "2574762221", "cardReason": "F<PERSON>l"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 91, "timeMinSec": "90:24", "lastUpdated": "2023-08-08T23:47:23Z", "timestamp": "2023-08-08T23:47:10Z", "type": "YC", "playerId": "51xii47wyc6cefmrwoijpdvbp", "playerName": "<PERSON>", "optaEventId": "2574762851", "cardReason": "Excessive celebration"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 99, "timeMinSec": "98:17", "lastUpdated": "2023-08-08T23:55:23Z", "timestamp": "2023-08-08T23:55:03Z", "type": "YC", "playerId": "b5lbifwyixas23f0orh10xvmy", "playerName": "<PERSON><PERSON>", "optaEventId": "2574763485", "cardReason": "Dissent"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 99, "timeMinSec": "98:34", "lastUpdated": "2023-08-08T23:55:38Z", "timestamp": "2023-08-08T23:55:20Z", "type": "Y2C", "playerId": "b5lbifwyixas23f0orh10xvmy", "playerName": "<PERSON><PERSON>", "optaEventId": "2574763503", "cardReason": "Dissent"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 90, "timeMinSec": "89:36", "lastUpdated": "2023-08-08T23:46:39Z", "timestamp": "2023-08-08T23:46:22Z", "type": "RC", "teamOfficialId": "63auw9130usejz6f2dvr8lln9", "officialName": "<PERSON><PERSON>", "optaEventId": "2574762805", "cardReason": "Foul and Abusive Language"}], "substitute": [{"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 56, "timeMinSec": "55:07", "lastUpdated": "2023-08-08T23:11:57Z", "timestamp": "2023-08-08T23:11:53Z", "playerOnId": "2jvkdusyl6zxjo3y5elcmwmzu", "playerOnName": "<PERSON><PERSON>", "playerOffId": "5csw8o2v8b6vohlnpdje5e2qy", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 60, "timeMinSec": "59:52", "lastUpdated": "2023-08-08T23:16:48Z", "timestamp": "2023-08-08T23:16:38Z", "playerOnId": "axlwwwsz1usbt11ets2jftk44", "playerOnName": "<PERSON><PERSON>", "playerOffId": "8w6x0722s2rze7j4i7qe39cyd", "playerOffName": "<PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 72, "timeMinSec": "71:27", "lastUpdated": "2023-08-08T23:28:19Z", "timestamp": "2023-08-08T23:28:13Z", "playerOnId": "83jg484gnx01poxy7w94toges", "playerOnName": "<PERSON>", "playerOffId": "3vrd6fnkrjiv15adjrtow86vp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 72, "timeMinSec": "71:44", "lastUpdated": "2023-08-08T23:28:38Z", "timestamp": "2023-08-08T23:28:30Z", "playerOnId": "bllae5c0f10a5q3ody264uhnt", "playerOnName": "<PERSON>", "playerOffId": "euz15mg3zvaoxmibrw7npjdnt", "playerOffName": "Lima", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 84, "timeMinSec": "83:05", "lastUpdated": "2023-08-08T23:39:58Z", "timestamp": "2023-08-08T23:39:51Z", "playerOnId": "ekva81fqsa77x7dzu6ddrdz6y", "playerOnName": "<PERSON><PERSON>", "playerOffId": "8y23wogr9k3syubwaumwelsdl", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 92, "timeMinSec": "91:04", "lastUpdated": "2023-08-08T23:47:58Z", "timestamp": "2023-08-08T23:47:50Z", "playerOnId": "dph8wxqda1ns6t931enjahjv9", "playerOnName": "<PERSON><PERSON>", "playerOffId": "6mgrw5ld3fdg7vubcfuzravpw", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 92, "timeMinSec": "91:20", "lastUpdated": "2023-08-08T23:48:14Z", "timestamp": "2023-08-08T23:48:06Z", "playerOnId": "d31ev2jsivhgekixpi3d7qwkq", "playerOnName": "<PERSON><PERSON>", "playerOffId": "37bl9v0zpux1tjoutqz121u51", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 2, "timeMin": 92, "timeMinSec": "91:58", "lastUpdated": "2023-08-08T23:48:54Z", "timestamp": "2023-08-08T23:48:44Z", "playerOnId": "ejgtltbsu0djglqw98gywgu6t", "playerOnName": "Marlon", "playerOffId": "8buy9bx3obao6u4cptzplh6mt", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 2, "timeMin": 93, "timeMinSec": "92:02", "lastUpdated": "2023-08-08T23:48:54Z", "timestamp": "2023-08-08T23:48:48Z", "playerOnId": "8pc4sgpzp3i7k7u5e3s5vzn1m", "playerOnName": "<PERSON><PERSON>", "playerOffId": "c43ver9u780ukdbs31y69rtux", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "formationUsed": "433", "player": [{"playerId": "ar8bum4ujl4htu51e2y9wr7x1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Lo<PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Lo<PERSON>", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "effectiveClearance", "value": "1"}, {"type": "accurateGoalKicks", "value": "5"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "cleanSheet", "value": "1"}, {"type": "possLostAll", "value": "4"}, {"type": "possLostCtrl", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "22"}, {"type": "totalLongBalls", "value": "5"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "totalBackZonePass", "value": "23"}, {"type": "successfulOpenPlayPass", "value": "17"}, {"type": "rightsidePass", "value": "7"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "ballRecovery", "value": "6"}, {"type": "openPlayPass", "value": "19"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "gkSmother", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "fwdPass", "value": "13"}, {"type": "goalKicks", "value": "5"}, {"type": "touches", "value": "31"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "minsPlayed", "value": "90"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalPass", "value": "26"}, {"type": "accurateKeeperThrows", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "1y9i3ndruu5b2p0vwkf7s40r9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 40, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "totalChippedPass", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalPass", "value": "36"}, {"type": "headPass", "value": "4"}, {"type": "gameStarted", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "accurateBackZonePass", "value": "15"}, {"type": "lostCorners", "value": "1"}, {"type": "totalContest", "value": "3"}, {"type": "touches", "value": "54"}, {"type": "fouls", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "fwdPass", "value": "13"}, {"type": "accurateFwdZonePass", "value": "13"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "totalTackle", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "offsideProvoked", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "passesLeft", "value": "13"}, {"type": "turnover", "value": "1"}, {"type": "openPlayPass", "value": "35"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "ballRecovery", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "rightsidePass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "11"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalBackZonePass", "value": "19"}, {"type": "duelLost", "value": "6"}, {"type": "totalThrows", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "challengeLost", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "accuratePass", "value": "28"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "possLostAll", "value": "12"}, {"type": "penAreaEntries", "value": "2"}, {"type": "possLostCtrl", "value": "12"}, {"type": "accurateThrows", "value": "5"}, {"type": "backwardPass", "value": "9"}, {"type": "cleanSheet", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalFwdZonePass", "value": "18"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "8w6x0722s2rze7j4i7qe39cyd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 30, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "7"}, {"type": "totalPass", "value": "53"}, {"type": "totalChippedPass", "value": "3"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "accurateBackZonePass", "value": "34"}, {"type": "lostCorners", "value": "1"}, {"type": "touches", "value": "57"}, {"type": "minsPlayed", "value": "60"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "aerialLost", "value": "2"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "fwdPass", "value": "9"}, {"type": "accurateFwdZonePass", "value": "16"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "totalClearance", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "accurateLongBalls", "value": "6"}, {"type": "openPlayPass", "value": "52"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "duelLost", "value": "2"}, {"type": "totalBackZonePass", "value": "35"}, {"type": "successfulOpenPlayPass", "value": "49"}, {"type": "rightsidePass", "value": "30"}, {"type": "totalLongBalls", "value": "7"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "50"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "possLostAll", "value": "3"}, {"type": "possLostCtrl", "value": "3"}, {"type": "backwardPass", "value": "2"}, {"type": "totalFwdZonePass", "value": "18"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9r6gsqdzcbx1226xfwj7jnz6x", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "captain": "yes", "stat": [{"type": "passesLeft", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "10"}, {"type": "duel<PERSON>on", "value": "9"}, {"type": "totalTackle", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "38"}, {"type": "fwdPass", "value": "16"}, {"type": "wasFouled", "value": "4"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "aerialLost", "value": "4"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "47"}, {"type": "fouls", "value": "3"}, {"type": "attHdTotal", "value": "1"}, {"type": "touches", "value": "107"}, {"type": "attIboxMiss", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "dangerousPlay", "value": "1"}, {"type": "accurateBackZonePass", "value": "57"}, {"type": "yellowCard", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headPass", "value": "4"}, {"type": "totalPass", "value": "97"}, {"type": "attMissHigh", "value": "1"}, {"type": "totalChippedPass", "value": "4"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "passesRight", "value": "22"}, {"type": "gameStarted", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "attBxCentre", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "totalFwdZonePass", "value": "40"}, {"type": "attHdMiss", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "backwardPass", "value": "8"}, {"type": "possLostCtrl", "value": "3"}, {"type": "possLostAll", "value": "3"}, {"type": "aerialWon", "value": "4"}, {"type": "accuratePass", "value": "95"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "totalLongBalls", "value": "5"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "rightsidePass", "value": "26"}, {"type": "successfulOpenPlayPass", "value": "91"}, {"type": "attemptsIbox", "value": "1"}, {"type": "duelLost", "value": "7"}, {"type": "totalBackZonePass", "value": "57"}, {"type": "totalFinalThirdPasses", "value": "9"}, {"type": "longPassOwnToOpp", "value": "11"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "openPlayPass", "value": "93"}, {"type": "turnover", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "51xii47wyc6cefmrwoijpdvbp", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 2, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "totalFwdZonePass", "value": "30"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "accurateThrows", "value": "5"}, {"type": "possLostCtrl", "value": "9"}, {"type": "possLostAll", "value": "9"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalLaunches", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "backwardPass", "value": "9"}, {"type": "totalAttAssist", "value": "2"}, {"type": "accurateCross", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalLongBalls", "value": "4"}, {"type": "totalThrows", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "39"}, {"type": "ballRecovery", "value": "1"}, {"type": "openPlayPass", "value": "44"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "totalBackZonePass", "value": "19"}, {"type": "duelLost", "value": "4"}, {"type": "longPassOwnToOpp", "value": "6"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "rightsidePass", "value": "8"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "effectiveBlockedCross", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "37"}, {"type": "totalCross", "value": "3"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "crosses18yardplus", "value": "3"}, {"type": "attOpenplay", "value": "1"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "accurateFwdZonePass", "value": "24"}, {"type": "wasFouled", "value": "2"}, {"type": "fwdPass", "value": "12"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalTackle", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "17"}, {"type": "minsPlayed", "value": "90"}, {"type": "touches", "value": "62"}, {"type": "attGoalHighRight", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "passesRight", "value": "19"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalPass", "value": "46"}, {"type": "yellowCard", "value": "1"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "lostCorners", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "euz15mg3zvaoxmibrw7npjdnt", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Moreira de Lima", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Moreira de Lima", "knownName": "Lima", "matchName": "Lima", "shirtNumber": 45, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "crosses18yard", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "fwdPass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "15"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "touches", "value": "41"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "minsPlayed", "value": "72"}, {"type": "possWonMid3rd", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalPass", "value": "35"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "10"}, {"type": "accurateBackZonePass", "value": "16"}, {"type": "totalFwdZonePass", "value": "20"}, {"type": "dispossessed", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "penAreaEntries", "value": "2"}, {"type": "possLostCtrl", "value": "6"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "backwardPass", "value": "13"}, {"type": "totalAttAssist", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "31"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "34"}, {"type": "ballRecovery", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "30"}, {"type": "rightsidePass", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "totalBackZonePass", "value": "16"}, {"type": "duelLost", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "bgsz3d0fmlaw8003dn66i4m0a", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 7, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "passesRight", "value": "10"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalChippedPass", "value": "3"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "totalPass", "value": "57"}, {"type": "totalContest", "value": "2"}, {"type": "yellowCard", "value": "1"}, {"type": "accurateBackZonePass", "value": "28"}, {"type": "lostCorners", "value": "1"}, {"type": "fouls", "value": "3"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "touches", "value": "79"}, {"type": "totalFlickOn", "value": "1"}, {"type": "possWonMid3rd", "value": "5"}, {"type": "interception", "value": "1"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "accurateFwdZonePass", "value": "25"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "wasFouled", "value": "5"}, {"type": "fwdPass", "value": "8"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "totalTackle", "value": "5"}, {"type": "duel<PERSON>on", "value": "12"}, {"type": "longPassOwnToOppSuccess", "value": "7"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "passesLeft", "value": "7"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "ballRecovery", "value": "7"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "openPlayPass", "value": "54"}, {"type": "accurateLongBalls", "value": "5"}, {"type": "turnover", "value": "3"}, {"type": "totalBackZonePass", "value": "31"}, {"type": "duelLost", "value": "7"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "rightsidePass", "value": "33"}, {"type": "successfulOpenPlayPass", "value": "50"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalLongBalls", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "aerialWon", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "accuratePass", "value": "53"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "challengeLost", "value": "1"}, {"type": "possLostCtrl", "value": "10"}, {"type": "possLostAll", "value": "10"}, {"type": "backwardPass", "value": "4"}, {"type": "cleanSheet", "value": "1"}, {"type": "totalFwdZonePass", "value": "26"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "effectiveClearance", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "3ih14qvw1mcjz8z8o8e3h7lcl", "firstName": "<PERSON>", "lastName": "Chagas de Lima", "shortFirstName": "<PERSON>", "shortLastName": "Chagas de Lima", "knownName": "<PERSON>", "matchName": "PH Ganso", "shirtNumber": 10, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "totalCross", "value": "3"}, {"type": "cornerTaken", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "totalClearance", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "crosses18yardplus", "value": "2"}, {"type": "finalThirdEntries", "value": "9"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "accurateFwdZonePass", "value": "29"}, {"type": "fwdPass", "value": "11"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "19"}, {"type": "minsPlayed", "value": "90"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "touches", "value": "62"}, {"type": "attLfTotal", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "passesRight", "value": "11"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalPass", "value": "51"}, {"type": "totalChippedPass", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "accurateBackZonePass", "value": "14"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "totalFwdZonePass", "value": "40"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "accurateThrows", "value": "3"}, {"type": "possLostCtrl", "value": "12"}, {"type": "penAreaEntries", "value": "5"}, {"type": "possLostAll", "value": "12"}, {"type": "backwardPass", "value": "13"}, {"type": "cleanSheet", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "totalThrows", "value": "3"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "43"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "49"}, {"type": "duelLost", "value": "1"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "attFreekickTotal", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "19"}, {"type": "rightsidePass", "value": "8"}, {"type": "successfulOpenPlayPass", "value": "41"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "3vrd6fnkrjiv15adjrtow86vp", "firstName": "<PERSON>", "lastName": "<PERSON> <PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON> <PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "totalFinalThirdPasses", "value": "9"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "duelLost", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "rightsidePass", "value": "6"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "ballRecovery", "value": "5"}, {"type": "turnover", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "openPlayPass", "value": "15"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "6"}, {"type": "totalLongBalls", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "totalAttAssist", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "possLostAll", "value": "16"}, {"type": "penAreaEntries", "value": "5"}, {"type": "possLostCtrl", "value": "16"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalFwdZonePass", "value": "15"}, {"type": "totalContest", "value": "4"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "overrun", "value": "1"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalPass", "value": "15"}, {"type": "headPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "touches", "value": "28"}, {"type": "fouls", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "72"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "fwdPass", "value": "5"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "totalClearance", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "crosses18yardplus", "value": "3"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "totalCross", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "8buy9bx3obao6u4cptzplh6mt", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Cano", "matchName": "<PERSON><PERSON>", "shirtNumber": 14, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "wonCorners", "value": "1"}, {"type": "attSetpiece", "value": "1"}, {"type": "attHdTarget", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "touches", "value": "14"}, {"type": "attHdTotal", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "89"}, {"type": "fouls", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "attIboxMiss", "value": "1"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "attemptsObox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalPass", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attMissRight", "value": "1"}, {"type": "attIboxTarget", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "backwardPass", "value": "1"}, {"type": "attHdMiss", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "possLostCtrl", "value": "5"}, {"type": "challengeLost", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accuratePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "attemptsIbox", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "duelLost", "value": "3"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "ballRecovery", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "4x28fz3umkky3c6488mza2961", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Arias", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "possWonMid3rd", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "touches", "value": "62"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "fouls", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "totalContest", "value": "7"}, {"type": "totalPass", "value": "32"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateFreekickCross", "value": "1"}, {"type": "passesRight", "value": "13"}, {"type": "totalCrossNocorner", "value": "7"}, {"type": "passesLeft", "value": "6"}, {"type": "cornerTaken", "value": "1"}, {"type": "crosses18yard", "value": "6"}, {"type": "possWonDef3rd", "value": "4"}, {"type": "totalCross", "value": "8"}, {"type": "totalTackle", "value": "4"}, {"type": "duel<PERSON>on", "value": "11"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "fwdPass", "value": "9"}, {"type": "wasFouled", "value": "2"}, {"type": "accurateFwdZonePass", "value": "23"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "accuratePass", "value": "28"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "14"}, {"type": "totalThrows", "value": "5"}, {"type": "totalLayoffs", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "28"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "rightsidePass", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "16"}, {"type": "duelLost", "value": "8"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "turnover", "value": "1"}, {"type": "openPlayPass", "value": "32"}, {"type": "ballRecovery", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "freekick<PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalFwdZonePass", "value": "32"}, {"type": "attAssistSetplay", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "backwardPass", "value": "10"}, {"type": "penAreaEntries", "value": "5"}, {"type": "possLostAll", "value": "16"}, {"type": "possLostCtrl", "value": "16"}, {"type": "accurateThrows", "value": "5"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "4"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "83jg484gnx01poxy7w94toges", "firstName": "<PERSON>", "lastName": "Batista de Souza", "shortFirstName": "<PERSON>", "shortLastName": "Batista de Souza", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 9, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "touches", "value": "9"}, {"type": "attIboxGoal", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "minsPlayed", "value": "18"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "totalPass", "value": "4"}, {"type": "passesRight", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "totalContest", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "shotFastbreak", "value": "1"}, {"type": "fwdPass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "attOpenplay", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "accuratePass", "value": "2"}, {"type": "bigChanceScored", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "openPlayPass", "value": "4"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "goalAssist", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "attemptsIbox", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "1"}, {"type": "goalFastbreak", "value": "1"}, {"type": "totalFwdZonePass", "value": "4"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "attFastbreak", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "totalFastbreak", "value": "1"}, {"type": "attBxCentre", "value": "2"}, {"type": "possLostAll", "value": "5"}, {"type": "possLostCtrl", "value": "5"}, {"type": "backwardPass", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "d84h8iwgv3hut0ywacmxfghrd", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "Leonardo", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "7rfeo8qrjotskdx5e6bwk53h1", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 38, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "axlwwwsz1usbt11ets2jftk44", "firstName": "<PERSON><PERSON>", "lastName": "Lima", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Lima", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "attemptedTackleFoul", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "passesLeft", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "passesRight", "value": "1"}, {"type": "totalPass", "value": "18"}, {"type": "totalChippedPass", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "touches", "value": "19"}, {"type": "minsPlayed", "value": "30"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "fouls", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "errorLeadToShot", "value": "1"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "rightsidePass", "value": "7"}, {"type": "ballRecovery", "value": "1"}, {"type": "openPlayPass", "value": "16"}, {"type": "accuratePass", "value": "17"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "6m0pdqqontm1tfxayxku0kv3e", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 37, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "90lubz3zmksfos06g12wk2z56", "firstName": "Cláudio", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Cláudio", "shortLastName": "<PERSON><PERSON><PERSON>", "knownName": "Guga", "matchName": "Guga", "shirtNumber": 23, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "bllae5c0f10a5q3ody264uhnt", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "challengeLost", "value": "1"}, {"type": "accuratePass", "value": "10"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "duelLost", "value": "3"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "backwardPass", "value": "2"}, {"type": "possLostCtrl", "value": "4"}, {"type": "penAreaEntries", "value": "2"}, {"type": "possLostAll", "value": "4"}, {"type": "assistPassLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "minsPlayed", "value": "18"}, {"type": "fouls", "value": "2"}, {"type": "touches", "value": "14"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "totalPass", "value": "13"}, {"type": "totalChippedPass", "value": "3"}, {"type": "passesRight", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "fwdPass", "value": "6"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "ejgtltbsu0djglqw98gywgu6t", "firstName": "Marlon", "lastName": "<PERSON> da <PERSON>", "shortFirstName": "Marlon", "shortLastName": "<PERSON> da <PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "Marlon", "shirtNumber": 4, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "totalSubOn", "value": "1"}, {"type": "minsPlayed", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "7uso425g5dkg9kxwplfuug5jo", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON> <PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON> <PERSON>", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 99, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "bczsxri7n7bckp13a75p385qs", "firstName": "<PERSON>", "lastName": "<PERSON> And<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON> And<PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "2iv3xaumkhla44s6b23b4zuok", "firstName": "<PERSON>", "lastName": "de Faria Rangel", "shortFirstName": "<PERSON>", "shortLastName": "de Faria Rangel", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 22, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "ayzdo5a0ce5fc7jepjhc6hq6t", "firstName": "T<PERSON>go", "lastName": "dos <PERSON>", "shortFirstName": "T<PERSON>go", "shortLastName": "dos <PERSON>", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 29, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "adn56r4ll4xlkxe4wr4x1mndh", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "knownName": "<PERSON>", "type": "manager"}, "stat": [{"fh": "2", "sh": "4", "type": "totalAttAssist", "value": "6"}, {"fh": "2", "sh": "3", "type": "accurateCross", "value": "5"}, {"fh": "53", "sh": "24", "type": "backwardPass", "value": "77"}, {"type": "cleanSheet", "value": "1"}, {"fh": "1", "sh": "1", "type": "attHdMiss", "value": "2"}, {"fh": "2", "sh": "1", "type": "totalLaunches", "value": "3"}, {"fh": "12", "sh": "12", "type": "penAreaEntries", "value": "24"}, {"fh": "55", "sh": "51", "type": "possLostAll", "value": "106"}, {"fh": "55", "sh": "51", "type": "possLostCtrl", "value": "106"}, {"fh": "9", "sh": "9", "type": "accurateThrows", "value": "18"}, {"fh": "1", "sh": "3", "type": "attAssistOpenplay", "value": "4"}, {"fh": "8", "sh": "9", "type": "fkFoulWon", "value": "17"}, {"fh": "2", "sh": "3", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "2", "sh": "1", "type": "blockedScoringAtt", "value": "3"}, {"fh": "0", "sh": "1", "type": "attFastbreak", "value": "1"}, {"fh": "4", "sh": "1", "type": "dispossessed", "value": "5"}, {"fh": "2", "sh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "6", "sh": "6", "type": "effectiveClearance", "value": "12"}, {"fh": "1", "sh": "4", "type": "effectiveHeadClearance", "value": "5"}, {"fh": "1", "sh": "0", "type": "attMissRight", "value": "1"}, {"fh": "0", "sh": "4", "type": "attBxCentre", "value": "4"}, {"fh": "0", "sh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "0", "sh": "1", "type": "errorLeadToShot", "value": "1"}, {"fh": "0", "sh": "1", "type": "totalFastbreak", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalAssistOpenplay", "value": "1"}, {"fh": "4", "sh": "9", "type": "unsuccessfulTouch", "value": "13"}, {"fh": "1", "sh": "0", "type": "freekick<PERSON><PERSON>", "value": "1"}, {"fh": "2", "sh": "6", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"fh": "0", "sh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "132", "sh": "133", "type": "totalFwdZonePass", "value": "265"}, {"fh": "0", "sh": "1", "type": "goalFastbreak", "value": "1"}, {"fh": "1", "sh": "0", "type": "attAssistSetplay", "value": "1"}, {"fh": "2", "sh": "3", "type": "accurateGoalKicks", "value": "5"}, {"fh": "0", "sh": "1", "type": "subsGoals", "value": "1"}, {"fh": "1", "sh": "5", "type": "attemptsIbox", "value": "6"}, {"fh": "1", "sh": "1", "type": "effectiveBlockedCross", "value": "2"}, {"fh": "242", "sh": "161", "type": "successfulOpenPlayPass", "value": "403"}, {"fh": "1", "sh": "1", "type": "offtargetAttAssist", "value": "2"}, {"fh": "90", "sh": "67", "type": "rightsidePass", "value": "157"}, {"fh": "5", "sh": "11", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "16"}, {"fh": "26", "sh": "16", "type": "longPassOwnToOpp", "value": "42"}, {"fh": "42", "sh": "60", "type": "totalFinalThirdPasses", "value": "102"}, {"fh": "22", "sh": "26", "type": "duelLost", "value": "48"}, {"fh": "162", "sh": "82", "type": "totalBackZonePass", "value": "244"}, {"fh": "1", "sh": "0", "type": "attFreekickTotal", "value": "1"}, {"fh": "0", "sh": "3", "type": "ontargetScoringAtt", "value": "3"}, {"fh": "14", "sh": "8", "type": "accurateLongBalls", "value": "22"}, {"fh": "276", "sh": "187", "type": "openPlayPass", "value": "463"}, {"fh": "0", "sh": "1", "type": "goalAssist", "value": "1"}, {"fh": "0", "sh": "1", "type": "attGoalLowRight", "value": "1"}, {"fh": "3", "sh": "0", "type": "fouledFinalThird", "value": "3"}, {"fh": "22", "sh": "23", "type": "ballRecovery", "value": "45"}, {"fh": "0", "sh": "3", "type": "keeper<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "2", "sh": "3", "type": "blocked<PERSON><PERSON>", "value": "5"}, {"fh": "3", "sh": "5", "type": "challengeLost", "value": "8"}, {"fh": "2", "sh": "2", "type": "attemptsConcededObox", "value": "4"}, {"fh": "252", "sh": "175", "type": "accuratePass", "value": "427"}, {"fh": "1", "sh": "0", "type": "attemptsConcededIbox", "value": "1"}, {"fh": "0", "sh": "1", "type": "bigChanceScored", "value": "1"}, {"fh": "5", "sh": "4", "type": "aerialWon", "value": "9"}, {"fh": "28", "sh": "39", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "67"}, {"fh": "9", "sh": "9", "type": "totalThrows", "value": "18"}, {"fh": "2", "sh": "0", "type": "totalLayoffs", "value": "2"}, {"fh": "20", "sh": "16", "type": "totalLongBalls", "value": "36"}, {"fh": "0", "sh": "1", "type": "defender<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "sh": "5", "type": "totalScoringAtt", "value": "8"}, {"fh": "4", "sh": "6", "type": "won<PERSON><PERSON><PERSON>", "value": "10"}, {"fh": "72.1", "sh": "60.9", "type": "possessionPercentage", "value": "66.8"}, {"fh": "19", "sh": "9", "type": "longPassOwnToOppSuccess", "value": "28"}, {"fh": "1", "sh": "2", "type": "attRfTotal", "value": "3"}, {"fh": "6", "sh": "8", "type": "totalTackle", "value": "14"}, {"fh": "22", "sh": "27", "type": "duel<PERSON>on", "value": "49"}, {"fh": "0", "sh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "1", "sh": "4", "type": "headClea<PERSON>", "value": "5"}, {"fh": "2", "sh": "0", "type": "accurateLayoffs", "value": "2"}, {"fh": "1", "sh": "3", "type": "ontargetAttAssist", "value": "4"}, {"fh": "0", "sh": "2", "type": "attRfGoal", "value": "2"}, {"fh": "3", "sh": "4", "type": "attemptedTackleFoul", "value": "7"}, {"fh": "0", "sh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "60", "sh": "50", "type": "fwdPass", "value": "110"}, {"fh": "0", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"fh": "0", "sh": "1", "type": "shotFastbreak", "value": "1"}, {"fh": "2", "sh": "3", "type": "successfulPutThrough", "value": "5"}, {"fh": "104", "sh": "101", "type": "accurateFwdZonePass", "value": "205"}, {"fh": "19", "sh": "30", "type": "finalThirdEntries", "value": "49"}, {"fh": "2", "sh": "2", "type": "interception", "value": "4"}, {"fh": "1", "sh": "3", "type": "attOpenplay", "value": "4"}, {"fh": "4", "sh": "10", "type": "fkFoulLost", "value": "14"}, {"fh": "3", "sh": "5", "type": "crosses18yardplus", "value": "8"}, {"fh": "7", "sh": "11", "type": "totalCrossNocorner", "value": "18"}, {"fh": "1", "sh": "2", "type": "cornerTaken", "value": "3"}, {"fh": "1", "sh": "2", "type": "wonCorners", "value": "3"}, {"fh": "30", "sh": "22", "type": "passesLeft", "value": "52"}, {"fh": "6", "sh": "6", "type": "totalClearance", "value": "12"}, {"fh": "0", "sh": "2", "type": "goals", "value": "2"}, {"fh": "3", "sh": "6", "type": "crosses18yard", "value": "9"}, {"fh": "8", "sh": "8", "type": "possWonDef3rd", "value": "16"}, {"fh": "1", "sh": "0", "type": "attSetpiece", "value": "1"}, {"fh": "0", "sh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "0", "sh": "2", "type": "goalsOpenplay", "value": "2"}, {"fh": "8", "sh": "12", "type": "totalCross", "value": "20"}, {"fh": "1", "sh": "1", "type": "totalCornersIntobox", "value": "2"}, {"fh": "150", "sh": "77", "type": "accurateBackZonePass", "value": "227"}, {"fh": "4", "sh": "3", "type": "lostCorners", "value": "7"}, {"fh": "5", "sh": "12", "type": "touchesInOppBox", "value": "17"}, {"fh": "1", "sh": "1", "type": "attIboxMiss", "value": "2"}, {"fh": "1", "sh": "0", "type": "overrun", "value": "1"}, {"fh": "2", "sh": "0", "type": "attemptsObox", "value": "2"}, {"fh": "0", "sh": "4", "type": "subsMade", "value": "4"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "8", "sh": "10", "type": "totalContest", "value": "18"}, {"fh": "0", "sh": "4", "type": "totalYellowCard", "value": "4"}, {"fh": "0", "sh": "1", "type": "attMissHigh", "value": "1"}, {"fh": "286", "sh": "203", "type": "totalPass", "value": "489"}, {"fh": "16", "sh": "16", "type": "totalChippedPass", "value": "32"}, {"fh": "8", "sh": "6", "type": "accurateChippedPass", "value": "14"}, {"fh": "2", "sh": "2", "type": "interceptionWon", "value": "4"}, {"fh": "2", "sh": "0", "type": "attObxCentre", "value": "2"}, {"fh": "0", "sh": "3", "type": "accurateKeeperThrows", "value": "3"}, {"fh": "1", "sh": "0", "type": "accurateFreekickCross", "value": "1"}, {"fh": "50", "sh": "47", "type": "passesRight", "value": "97"}, {"fh": "1", "sh": "1", "type": "blocked<PERSON><PERSON>", "value": "2"}, {"fh": "2", "sh": "0", "type": "attOboxBlocked", "value": "2"}, {"fh": "0", "sh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "2", "sh": "3", "type": "goalKicks", "value": "5"}, {"fh": "5", "sh": "6", "type": "aerialLost", "value": "11"}, {"fh": "11", "sh": "8", "type": "possWonMid3rd", "value": "19"}, {"fh": "1", "sh": "0", "type": "totalFlickOn", "value": "1"}, {"fh": "1", "sh": "1", "type": "attLfTotal", "value": "2"}, {"fh": "0", "sh": "1", "type": "attGoalHighRight", "value": "1"}, {"fh": "1", "sh": "2", "type": "attHdTotal", "value": "3"}, {"fh": "356", "sh": "283", "type": "touches", "value": "639"}, {"fh": "83", "sh": "62", "type": "leftside<PERSON><PERSON>", "value": "145"}, {"fh": "3", "sh": "2", "type": "possWonAtt3rd", "value": "5"}, {"fh": "0", "sh": "2", "type": "attIboxGoal", "value": "2"}, {"type": "formationUsed", "value": "433"}], "kit": {"id": "1271", "colour1": "#CC0000", "colour2": "#FFFFFF", "type": "home"}}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "formationUsed": "541", "player": [{"playerId": "aolr3cmeor7vwcbtirfm3gpyi", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "totalLaunches", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "possLostAll", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "savedIbox", "value": "1"}, {"type": "accurateGoalKicks", "value": "5"}, {"type": "diveSave", "value": "1"}, {"type": "rightsidePass", "value": "5"}, {"type": "divingSave", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "punches", "value": "1"}, {"type": "openPlayPass", "value": "7"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "4"}, {"type": "ballRecovery", "value": "5"}, {"type": "aerialWon", "value": "1"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "accuratePass", "value": "12"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "totalLongBalls", "value": "5"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalKeeperSweeper", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "totalClearance", "value": "1"}, {"type": "saves", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "accurateKeeperThrows", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalPass", "value": "14"}, {"type": "gameStarted", "value": "1"}, {"type": "goalKicks", "value": "5"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "touches", "value": "20"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "cfrtvk0ymxtkytftfggnzbg0l", "firstName": "<PERSON>", "lastName": "Villalba", "shortFirstName": "<PERSON>", "shortLastName": "Villalba", "knownName": "<PERSON>", "matchName": "L. <PERSON>", "shirtNumber": 6, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "passesLeft", "value": "7"}, {"type": "totalClearance", "value": "3"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "totalCross", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "4"}, {"type": "totalTackle", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "finalThirdEntries", "value": "8"}, {"type": "fwdPass", "value": "15"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "successfulPutThrough", "value": "3"}, {"type": "touches", "value": "59"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "totalContest", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "lostCorners", "value": "1"}, {"type": "accurateBackZonePass", "value": "20"}, {"type": "gameStarted", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "passesRight", "value": "1"}, {"type": "totalPass", "value": "41"}, {"type": "accurateChippedPass", "value": "4"}, {"type": "totalChippedPass", "value": "11"}, {"type": "headPass", "value": "4"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "effectiveClearance", "value": "3"}, {"type": "goalsConceded", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFwdZonePass", "value": "19"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalLaunches", "value": "1"}, {"type": "backwardPass", "value": "7"}, {"type": "accurateThrows", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "possLostAll", "value": "17"}, {"type": "possLostCtrl", "value": "17"}, {"type": "accuratePass", "value": "29"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "aerialWon", "value": "2"}, {"type": "totalLongBalls", "value": "10"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalThrows", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "duelLost", "value": "1"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "effectiveBlockedCross", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "26"}, {"type": "rightsidePass", "value": "17"}, {"type": "ballRecovery", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "openPlayPass", "value": "37"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "ayb309hol7w8us3tn4yr2fl04", "firstName": "<PERSON>", "lastName": "<PERSON> Cesar<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON> Cesar<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 29, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "successfulOpenPlayPass", "value": "19"}, {"type": "rightsidePass", "value": "5"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "duelLost", "value": "1"}, {"type": "totalBackZonePass", "value": "23"}, {"type": "accurateLongBalls", "value": "7"}, {"type": "openPlayPass", "value": "25"}, {"type": "ballRecovery", "value": "6"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "24"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalThrows", "value": "4"}, {"type": "totalLongBalls", "value": "8"}, {"type": "backwardPass", "value": "3"}, {"type": "totalLaunches", "value": "3"}, {"type": "penAreaEntries", "value": "1"}, {"type": "possLostAll", "value": "7"}, {"type": "possLostCtrl", "value": "7"}, {"type": "accurateThrows", "value": "4"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "effectiveClearance", "value": "4"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "errorLeadToShot", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "yellowCard", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "totalPass", "value": "31"}, {"type": "totalChippedPass", "value": "5"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "touches", "value": "45"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "90"}, {"type": "longPassOwnToOppSuccess", "value": "7"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "fwdPass", "value": "15"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateLaunches", "value": "3"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "totalClearance", "value": "4"}, {"type": "possWonDef3rd", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "6mgrw5ld3fdg7vubcfuzravpw", "firstName": "<PERSON>", "lastName": "Minissale", "shortFirstName": "<PERSON>", "shortLastName": "Minissale", "matchName": "<PERSON><PERSON>", "shirtNumber": 30, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "longPassOwnToOpp", "value": "8"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "duelLost", "value": "1"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "rightsidePass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "openPlayPass", "value": "14"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "totalLongBalls", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalThrows", "value": "8"}, {"type": "totalLaunches", "value": "1"}, {"type": "accurateThrows", "value": "6"}, {"type": "penAreaEntries", "value": "4"}, {"type": "possLostAll", "value": "10"}, {"type": "possLostCtrl", "value": "10"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "effectiveClearance", "value": "4"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "yellowCard", "value": "1"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "totalPass", "value": "14"}, {"type": "totalChippedPass", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "touches", "value": "28"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "minsPlayed", "value": "89"}, {"type": "fouls", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "fwdPass", "value": "8"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "wonCorners", "value": "2"}, {"type": "totalClearance", "value": "4"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "b5lbifwyixas23f0orh10xvmy", "firstName": "Santiago Gabriel", "lastName": "<PERSON><PERSON>", "shortFirstName": "Santiago", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Wing Back", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "interceptionWon", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalPass", "value": "10"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "totalCornersIntobox", "value": "2"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "89"}, {"type": "touches", "value": "32"}, {"type": "aerialLost", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "wasFouled", "value": "2"}, {"type": "fwdPass", "value": "3"}, {"type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "totalTackle", "value": "4"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "totalCross", "value": "3"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "redCard", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "cornerTaken", "value": "3"}, {"type": "wonCorners", "value": "2"}, {"type": "passesLeft", "value": "4"}, {"type": "openPlayPass", "value": "8"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "rightsidePass", "value": "4"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "duelLost", "value": "6"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalThrows", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "4"}, {"type": "challengeLost", "value": "2"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "6"}, {"type": "possLostCtrl", "value": "11"}, {"type": "possLostAll", "value": "11"}, {"type": "penAreaEntries", "value": "2"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "accurateThrows", "value": "3"}, {"type": "backwardPass", "value": "2"}, {"type": "totalAttAssist", "value": "2"}, {"type": "accurateCross", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "attAssistSetplay", "value": "1"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "dispossessed", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "37bl9v0zpux1tjoutqz121u51", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Wing Back", "positionSide": "Right", "formationPlace": "2", "captain": "yes", "stat": [{"type": "accurateFwdZonePass", "value": "5"}, {"type": "wasFouled", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possWonDef3rd", "value": "6"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "cornerTaken", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalPass", "value": "12"}, {"type": "passesRight", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "89"}, {"type": "touches", "value": "20"}, {"type": "aerialLost", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "possLostAll", "value": "4"}, {"type": "penAreaEntries", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "dispossessed", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "openPlayPass", "value": "11"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "turnover", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "ballRecovery", "value": "7"}, {"type": "effectiveBlockedCross", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "duelLost", "value": "5"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "challengeLost", "value": "3"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "10"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "c43ver9u780ukdbs31y69rtux", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Midfielder", "positionSide": "Left", "formationPlace": "11", "stat": [{"type": "totalClearance", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "fwdPass", "value": "6"}, {"type": "attOpenplay", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "fouls", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "89"}, {"type": "touches", "value": "21"}, {"type": "attOboxMiss", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalChippedPass", "value": "3"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "attMissHigh", "value": "1"}, {"type": "totalPass", "value": "14"}, {"type": "passesRight", "value": "4"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "totalContest", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "totalFwdZonePass", "value": "10"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "possLostAll", "value": "6"}, {"type": "penAreaEntries", "value": "2"}, {"type": "backwardPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "10"}, {"type": "openPlayPass", "value": "14"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "rightsidePass", "value": "5"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "duelLost", "value": "4"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "19u762rcqpmnm2p40jtwdvje1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Franco", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "10", "stat": [{"type": "aerialLost", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "touches", "value": "54"}, {"type": "fouls", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "11"}, {"type": "totalCornersIntobox", "value": "2"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "totalChippedPass", "value": "5"}, {"type": "totalPass", "value": "41"}, {"type": "headPass", "value": "4"}, {"type": "interceptionWon", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "passesLeft", "value": "5"}, {"type": "wonCorners", "value": "1"}, {"type": "cornerTaken", "value": "2"}, {"type": "totalCross", "value": "4"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "totalTackle", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "6"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "accurateLaunches", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "fwdPass", "value": "12"}, {"type": "accurateFwdZonePass", "value": "19"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "interception", "value": "3"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "accuratePass", "value": "36"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalLongBalls", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "35"}, {"type": "rightsidePass", "value": "13"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "totalBackZonePass", "value": "19"}, {"type": "duelLost", "value": "2"}, {"type": "openPlayPass", "value": "40"}, {"type": "accurateLongBalls", "value": "5"}, {"type": "ballRecovery", "value": "3"}, {"type": "goalsConceded", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "freekick<PERSON><PERSON>", "value": "1"}, {"type": "attAssistSetplay", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "26"}, {"type": "backwardPass", "value": "5"}, {"type": "accurateCross", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "possLostAll", "value": "8"}, {"type": "penAreaEntries", "value": "5"}, {"type": "possLostCtrl", "value": "8"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "b8yk8yve4u39s9gjjvedzf6l0", "firstName": "<PERSON>", "lastName": "Redondo Solari", "shortFirstName": "<PERSON>", "shortLastName": "Redondo", "matchName": "<PERSON>. <PERSON>", "shirtNumber": 5, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "8", "stat": [{"type": "attemptsConcededIbox", "value": "6"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "20"}, {"type": "aerialWon", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "rightsidePass", "value": "6"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalBackZonePass", "value": "12"}, {"type": "duelLost", "value": "3"}, {"type": "openPlayPass", "value": "25"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "ballRecovery", "value": "7"}, {"type": "dispossessed", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "goalsConceded", "value": "2"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "14"}, {"type": "backwardPass", "value": "3"}, {"type": "possLostAll", "value": "8"}, {"type": "possLostCtrl", "value": "8"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "touches", "value": "40"}, {"type": "possWonAtt3rd", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "minsPlayed", "value": "90"}, {"type": "yellowCard", "value": "1"}, {"type": "accurateBackZonePass", "value": "10"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "totalContest", "value": "3"}, {"type": "totalChippedPass", "value": "4"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "totalPass", "value": "25"}, {"type": "headPass", "value": "1"}, {"type": "interceptionWon", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "totalCross", "value": "1"}, {"type": "duel<PERSON>on", "value": "7"}, {"type": "totalTackle", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "wasFouled", "value": "3"}, {"type": "fwdPass", "value": "6"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "interception", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "8y23wogr9k3syubwaumwelsdl", "firstName": "Francisco", "lastName": "<PERSON>", "shortFirstName": "Francisco", "shortLastName": "<PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Midfielder", "positionSide": "Right", "formationPlace": "7", "stat": [{"type": "interceptionWon", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalPass", "value": "16"}, {"type": "passesRight", "value": "7"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "totalContest", "value": "2"}, {"type": "attemptsObox", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "minsPlayed", "value": "84"}, {"type": "fouls", "value": "2"}, {"type": "touches", "value": "32"}, {"type": "totalFlickOn", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "fwdPass", "value": "4"}, {"type": "wasFouled", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "totalTackle", "value": "3"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "attSetpiece", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "openPlayPass", "value": "16"}, {"type": "turnover", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "handBall", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "rightsidePass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "duelLost", "value": "5"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalLongBalls", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "10"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "possLostCtrl", "value": "10"}, {"type": "penAreaEntries", "value": "1"}, {"type": "possLostAll", "value": "10"}, {"type": "backwardPass", "value": "4"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalFwdZonePass", "value": "12"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "5csw8o2v8b6vohlnpdje5e2qy", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "Gondou", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "rightsidePass", "value": "2"}, {"type": "attemptsIbox", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "duelLost", "value": "9"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "openPlayPass", "value": "8"}, {"type": "turnover", "value": "2"}, {"type": "totalSubOff", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "aerialWon", "value": "4"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "accuratePass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "backwardPass", "value": "2"}, {"type": "possLostCtrl", "value": "6"}, {"type": "possLostAll", "value": "6"}, {"type": "dispossessed", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "attMissRight", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "lostCorners", "value": "1"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "yellowCard", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalPass", "value": "9"}, {"type": "totalChippedPass", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "gameStarted", "value": "1"}, {"type": "totalFlickOn", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "attLfTotal", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "minsPlayed", "value": "56"}, {"type": "fouls", "value": "6"}, {"type": "attOboxMiss", "value": "1"}, {"type": "touches", "value": "20"}, {"type": "attHdTotal", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "duel<PERSON>on", "value": "7"}, {"type": "attemptedTackleFoul", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "fwdPass", "value": "2"}, {"type": "wasFouled", "value": "2"}, {"type": "attOpenplay", "value": "1"}, {"type": "attCmissRight", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "totalOffside", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "d31ev2jsivhgekixpi3d7qwkq", "firstName": "<PERSON>", "lastName": "Cabral", "shortFirstName": "<PERSON>", "shortLastName": "Cabral", "matchName": "<PERSON><PERSON>", "shirtNumber": 16, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "attemptsConcededIbox", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "minsPlayed", "value": "1"}, {"type": "touches", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "ekva81fqsa77x7dzu6ddrdz6y", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "Dom<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "passesRight", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "touches", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "minsPlayed", "value": "6"}, {"type": "fwdPass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "accuratePass", "value": "2"}, {"type": "challengeLost", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "btqmynmz9kep9327vp7bua8r9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "Leonel", "shortLastName": "<PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "dph8wxqda1ns6t931enjahjv9", "firstName": "<PERSON>", "lastName": "Heredia", "shortFirstName": "Leonardo", "shortLastName": "Heredia", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalFwdZonePass", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "minsPlayed", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "totalPass", "value": "2"}, {"type": "headPass", "value": "1"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "75gcy1dp3cxdopfhuttke2xw4", "firstName": "<PERSON>", "lastName": "Les<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "Les<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 41, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "2jvkdusyl6zxjo3y5elcmwmzu", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "T<PERSON>go", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "possLostCtrl", "value": "7"}, {"type": "possLostAll", "value": "7"}, {"type": "backwardPass", "value": "1"}, {"type": "goalsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "goalsConceded", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "turnover", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "duelLost", "value": "5"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "aerialWon", "value": "2"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "accuratePass", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "wasFouled", "value": "3"}, {"type": "fwdPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "totalCross", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "totalContest", "value": "3"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "minsPlayed", "value": "34"}, {"type": "touches", "value": "15"}, {"type": "aerialLost", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "244njroqb7rrmgygu713irp5g", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "Román", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "13w2dh2hl7u06jttfyjqysbth", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "2f846w2cjmm07ce9koce0e4gk", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Vega", "shortFirstName": "Román", "shortLastName": "Vega", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9qf3vz7n2kvtjmeh5c07808vd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 14, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8pc4sgpzp3i7k7u5e3s5vzn1m", "firstName": "Gastón Nicolás", "lastName": "Verón", "shortFirstName": "Gastón", "shortLastName": "Verón", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "possLostCtrl", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "openPlayPass", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "totalPass", "value": "2"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "aerialLost", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "63auw9130usejz6f2dvr8lln9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "5", "sh": "4", "type": "dispossessed", "value": "9"}, {"fh": "0", "sh": "2", "type": "goalsConceded", "value": "2"}, {"fh": "9", "sh": "9", "type": "effectiveClearance", "value": "18"}, {"fh": "2", "sh": "1", "type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "4", "sh": "10", "type": "fkFoulWon", "value": "14"}, {"fh": "2", "sh": "1", "type": "blockedScoringAtt", "value": "3"}, {"fh": "1", "sh": "0", "type": "attMissRight", "value": "1"}, {"fh": "1", "sh": "0", "type": "attBxCentre", "value": "1"}, {"fh": "1", "sh": "0", "type": "errorLeadToShot", "value": "1"}, {"fh": "6", "sh": "4", "type": "unsuccessfulTouch", "value": "10"}, {"fh": "1", "sh": "5", "type": "effectiveHeadClearance", "value": "6"}, {"fh": "63", "sh": "65", "type": "totalFwdZonePass", "value": "128"}, {"fh": "2", "sh": "0", "type": "attAssistSetplay", "value": "2"}, {"fh": "0", "sh": "2", "type": "goalsConcededIbox", "value": "2"}, {"fh": "0", "sh": "1", "type": "freekick<PERSON><PERSON>", "value": "1"}, {"fh": "3", "sh": "5", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"fh": "4", "sh": "1", "type": "accurateGoalKicks", "value": "5"}, {"fh": "0", "sh": "1", "type": "savedIbox", "value": "1"}, {"fh": "2", "sh": "1", "type": "totalAttAssist", "value": "3"}, {"fh": "2", "sh": "1", "type": "accurateCross", "value": "3"}, {"fh": "13", "sh": "19", "type": "backwardPass", "value": "32"}, {"fh": "3", "sh": "6", "type": "totalLaunches", "value": "9"}, {"fh": "46", "sh": "53", "type": "possLostCtrl", "value": "99"}, {"fh": "8", "sh": "9", "type": "penAreaEntries", "value": "17"}, {"fh": "46", "sh": "53", "type": "possLostAll", "value": "99"}, {"fh": "0", "sh": "1", "type": "attAssistOpenplay", "value": "1"}, {"fh": "7", "sh": "8", "type": "accurateThrows", "value": "15"}, {"fh": "2", "sh": "6", "type": "challengeLost", "value": "8"}, {"fh": "5", "sh": "6", "type": "aerialWon", "value": "11"}, {"fh": "2", "sh": "0", "type": "attemptsConcededObox", "value": "2"}, {"fh": "81", "sh": "98", "type": "accuratePass", "value": "179"}, {"fh": "1", "sh": "5", "type": "attemptsConcededIbox", "value": "6"}, {"fh": "13", "sh": "9", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "22"}, {"fh": "11", "sh": "9", "type": "totalThrows", "value": "20"}, {"fh": "18", "sh": "28", "type": "totalLongBalls", "value": "46"}, {"fh": "0", "sh": "1", "type": "totalLayoffs", "value": "1"}, {"fh": "2", "sh": "4", "type": "won<PERSON><PERSON><PERSON>", "value": "6"}, {"fh": "27.9", "sh": "39.1", "type": "possessionPercentage", "value": "33.2"}, {"fh": "3", "sh": "2", "type": "totalScoringAtt", "value": "5"}, {"fh": "32", "sh": "30", "type": "rightsidePass", "value": "62"}, {"fh": "1", "sh": "0", "type": "attemptsIbox", "value": "1"}, {"fh": "0", "sh": "1", "type": "divingSave", "value": "1"}, {"fh": "72", "sh": "86", "type": "successfulOpenPlayPass", "value": "158"}, {"fh": "0", "sh": "3", "type": "effectiveBlockedCross", "value": "3"}, {"fh": "0", "sh": "1", "type": "offtargetAttAssist", "value": "1"}, {"fh": "22", "sh": "27", "type": "duelLost", "value": "49"}, {"fh": "49", "sh": "73", "type": "totalBackZonePass", "value": "122"}, {"fh": "6", "sh": "9", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "15"}, {"fh": "17", "sh": "31", "type": "longPassOwnToOpp", "value": "48"}, {"fh": "25", "sh": "18", "type": "totalFinalThirdPasses", "value": "43"}, {"fh": "9", "sh": "16", "type": "accurateLongBalls", "value": "25"}, {"fh": "95", "sh": "117", "type": "openPlayPass", "value": "212"}, {"fh": "0", "sh": "1", "type": "punches", "value": "1"}, {"fh": "1", "sh": "0", "type": "handBall", "value": "1"}, {"fh": "16", "sh": "28", "type": "ballRecovery", "value": "44"}, {"fh": "2", "sh": "2", "type": "keeper<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "3", "sh": "5", "type": "blocked<PERSON><PERSON>", "value": "8"}, {"fh": "1", "sh": "1", "type": "fouledFinalThird", "value": "2"}, {"fh": "4", "sh": "5", "type": "totalCrossNocorner", "value": "9"}, {"fh": "2", "sh": "1", "type": "accurateCornersIntobox", "value": "3"}, {"fh": "9", "sh": "9", "type": "fkFoulLost", "value": "18"}, {"fh": "2", "sh": "1", "type": "crosses18yardplus", "value": "3"}, {"fh": "4", "sh": "3", "type": "wonCorners", "value": "7"}, {"fh": "4", "sh": "3", "type": "cornerTaken", "value": "7"}, {"fh": "10", "sh": "11", "type": "passesLeft", "value": "21"}, {"fh": "9", "sh": "9", "type": "totalClearance", "value": "18"}, {"fh": "2", "sh": "3", "type": "crosses18yard", "value": "5"}, {"fh": "6", "sh": "8", "type": "totalCross", "value": "14"}, {"fh": "8", "sh": "14", "type": "possWonDef3rd", "value": "22"}, {"fh": "0", "sh": "1", "type": "saves", "value": "1"}, {"fh": "1", "sh": "0", "type": "attSetpiece", "value": "1"}, {"fh": "6", "sh": "21", "type": "longPassOwnToOppSuccess", "value": "27"}, {"fh": "0", "sh": "2", "type": "attRfTotal", "value": "2"}, {"fh": "10", "sh": "5", "type": "totalTackle", "value": "15"}, {"fh": "22", "sh": "26", "type": "duel<PERSON>on", "value": "48"}, {"fh": "2", "sh": "0", "type": "ontargetAttAssist", "value": "2"}, {"fh": "1", "sh": "5", "type": "attemptedTackleFoul", "value": "6"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "sh": "5", "type": "headClea<PERSON>", "value": "6"}, {"fh": "0", "sh": "1", "type": "accurateLayoffs", "value": "1"}, {"fh": "3", "sh": "5", "type": "successfulPutThrough", "value": "8"}, {"fh": "40", "sh": "38", "type": "accurateFwdZonePass", "value": "78"}, {"fh": "32", "sh": "51", "type": "fwdPass", "value": "83"}, {"fh": "1", "sh": "0", "type": "totalKeeperSweeper", "value": "1"}, {"fh": "1", "sh": "3", "type": "accurateLaunches", "value": "4"}, {"fh": "4", "sh": "6", "type": "interception", "value": "10"}, {"fh": "1", "sh": "1", "type": "attOpenplay", "value": "2"}, {"fh": "0", "sh": "1", "type": "interceptionsInBox", "value": "1"}, {"fh": "1", "sh": "0", "type": "attCmissRight", "value": "1"}, {"fh": "0", "sh": "1", "type": "second<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "0", "sh": "1", "type": "totalOffside", "value": "1"}, {"fh": "18", "sh": "14", "type": "finalThirdEntries", "value": "32"}, {"fh": "2", "sh": "1", "type": "totalFlickOn", "value": "3"}, {"fh": "3", "sh": "10", "type": "possWonMid3rd", "value": "13"}, {"fh": "5", "sh": "4", "type": "aerialLost", "value": "9"}, {"fh": "4", "sh": "1", "type": "goalKicks", "value": "5"}, {"fh": "1", "sh": "0", "type": "attIboxBlocked", "value": "1"}, {"fh": "2", "sh": "0", "type": "attLfTotal", "value": "2"}, {"fh": "0", "sh": "1", "type": "totalRedCard", "value": "1"}, {"fh": "29", "sh": "30", "type": "leftside<PERSON><PERSON>", "value": "59"}, {"fh": "2", "sh": "2", "type": "possWonAtt3rd", "value": "4"}, {"fh": "1", "sh": "1", "type": "attOboxMiss", "value": "2"}, {"fh": "180", "sh": "215", "type": "touches", "value": "395"}, {"fh": "1", "sh": "0", "type": "attHdTotal", "value": "1"}, {"fh": "43", "sh": "61", "type": "accurateBackZonePass", "value": "104"}, {"fh": "1", "sh": "2", "type": "lostCorners", "value": "3"}, {"fh": "6", "sh": "5", "type": "touchesInOppBox", "value": "11"}, {"fh": "2", "sh": "3", "type": "totalCornersIntobox", "value": "5"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "5", "sh": "9", "type": "totalContest", "value": "14"}, {"fh": "1", "sh": "3", "type": "totalYellowCard", "value": "4"}, {"fh": "0", "sh": "5", "type": "subsMade", "value": "5"}, {"fh": "2", "sh": "2", "type": "attemptsObox", "value": "4"}, {"fh": "4", "sh": "6", "type": "interceptionWon", "value": "10"}, {"fh": "2", "sh": "2", "type": "attObxCentre", "value": "4"}, {"fh": "2", "sh": "2", "type": "accurateKeeperThrows", "value": "4"}, {"fh": "0", "sh": "1", "type": "attMissHigh", "value": "1"}, {"fh": "106", "sh": "130", "type": "totalPass", "value": "236"}, {"fh": "5", "sh": "12", "type": "accurateChippedPass", "value": "17"}, {"fh": "14", "sh": "25", "type": "totalChippedPass", "value": "39"}, {"fh": "0", "sh": "3", "type": "blocked<PERSON><PERSON>", "value": "3"}, {"fh": "18", "sh": "13", "type": "passesRight", "value": "31"}, {"fh": "1", "sh": "1", "type": "attOboxBlocked", "value": "2"}, {"type": "formationUsed", "value": "541"}], "kit": {"id": "11612", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "7npvq5px6m7m8edrmg177qlzp", "type": "Main", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>"}, {"id": "bdb5y7bhcp35de60mfi5ny7dh", "type": "Lineman 1", "firstName": "<PERSON><PERSON>", "lastName": "Torrealba", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Torrealba"}, {"id": "9m1j4r74jfm4fl75kifz90ixh", "type": "Lineman 2", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>"}, {"id": "7axof197c6t3b4unn7pzp20np", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "Arteaga Cabriales", "shortFirstName": "<PERSON>", "shortLastName": "Arteaga"}]}}}