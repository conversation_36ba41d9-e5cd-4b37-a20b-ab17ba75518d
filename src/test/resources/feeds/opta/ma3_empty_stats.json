{"matchInfo": {"id": "25uc7c4deb7do7uc2u26f88wk", "coverageLevel": "9", "date": "2025-05-18Z", "time": "16:00:00Z", "localDate": "2025-05-18", "localTime": "19:00:00", "week": "36", "numberOfPeriods": 2, "periodLength": 45, "var": "1", "lastUpdated": "2025-05-18T17:58:54Z", "description": "Samsunspor vs Sivasspor", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "482ofyysbdbeoxauk19yg7tdt", "name": "<PERSON><PERSON><PERSON>", "knownName": "Turkish Super Lig", "sponsorName": "trendyol SüperLig", "competitionCode": "SÜL", "competitionFormat": "Domestic league", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}, "tournamentCalendar": {"id": "1c0t9lv2rftjjx8dxn5s9csus", "startDate": "2024-08-09Z", "endDate": "2025-06-01Z", "name": "2024/2025"}, "stage": {"id": "1ckyv15iml4041do5vrva32ms", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2024-08-09Z", "endDate": "2025-06-01Z", "name": "Regular Season"}, "contestant": [{"id": "dpsnqu7pd2b0shfzjyn5j1znf", "name": "Samsunspor", "shortName": "Samsunspor", "officialName": "Samsunspor Kulübü", "code": "SAM", "position": "home", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}, {"id": "f432akygffyamal3h6poig65t", "name": "Sivasspor", "shortName": "Sivasspor", "officialName": "Sivasspor Kulübü", "code": "SİV", "position": "away", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}], "venue": {"id": "1ej47f9htsl0jn7jehm4pyn3e", "neutral": "no", "longName": "Samsun Yeni 19 Mayıs Stadyumu", "shortName": "Samsun Yeni 19 Mayıs Stadyumu"}}, "liveData": {"matchDetails": {"periodId": 14, "matchStatus": "Played", "winner": "home", "matchLengthMin": 94, "matchLengthSec": 11, "period": [{"id": 1, "start": "2025-05-18T16:04:09Z", "end": "2025-05-18T16:50:19Z", "lengthMin": 46, "lengthSec": 10, "announcedInjuryTime": 60}, {"id": 2, "start": "2025-05-18T17:10:44Z", "end": "2025-05-18T17:58:45Z", "lengthMin": 48, "lengthSec": 1}], "scores": {"ht": {"home": 1, "away": 0}, "ft": {"home": 1, "away": 0}, "total": {"home": 1, "away": 0}}}, "event": [{"id": 2816559887, "eventId": 1, "typeId": 34, "periodId": 16, "timeMin": 0, "timeSec": 0, "contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "outcome": 1, "x": 0.0, "y": 0.0, "timeStamp": "2025-05-18T15:03:50.518Z", "lastModified": "2025-05-18T16:02:08Z", "qualifier": [{"id": 5737389687, "qualifierId": 197, "value": "25217"}, {"id": 5737058263, "qualifierId": 130, "value": "8"}, {"id": 5737058269, "qualifierId": 227, "value": "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"}, {"id": 5737058267, "qualifierId": 194, "value": "7pxzxpxy8kzh7uevovjhveuhh"}, {"id": 5737058261, "qualifierId": 59, "value": "1, 18, 16, 10, 37, 4, 17, 6, 14, 21, 11, 45, 55, 2, 28, 96, 5, 13, 8, 7, 9"}, {"id": 5737058265, "qualifierId": 131, "value": "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"}, {"id": 5737058255, "qualifierId": 44, "value": "1, 2, 2, 3, 2, 2, 3, 3, 4, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5"}, {"id": 5737058251, "qualifierId": 30, "value": "ctgockgy49dl62spgtj6p89ud, 7pxzxpxy8kzh7ue<PERSON>v<PERSON><PERSON><PERSON><PERSON>h, dtkf4wnjv5rvjdmxh8acay4et, dzai0thce5hvwbvdrvmcnsyj9, 4drlqwqk6srxnncm79feyy7kl, ahhjd38eki0yws3iw1si0082x, 6dzx3oyd42ql69tqxqvg8mpxx, 3vvabqz2v7zgx4xikpm19clhx, 76n4eg1xu5qvf9bkwy4p8jsut, dlgueejo95xoqfugtpgc3i2nd, c6rzadc9bx74tw06zdubmtc5x, 7q2udflgi1xv9y2x04j38ralm, 1dzj2btdw0w4tm59kfbauqrys, 66jgw85uti6qsgxmlkzz2726t, 5o87rq6ux71867sagd5d2e39, 5l11c0cz9ej2a730469pz7zf8, 8ox8sr19qwfoxvk2hfhx0xmmt, crn7i9243dnczl74jti8i5pzp, 1qpdv43chhq4vcr1mc7qfpsr9, cl2mi40x3gjsan1iifpbwec4p, 8058605f3gip7nglewe7svqzu"}]}, {"id": 2816563885, "eventId": 1, "typeId": 34, "periodId": 16, "timeMin": 0, "timeSec": 0, "contestantId": "f432akygffyamal3h6poig65t", "outcome": 1, "x": 0.0, "y": 0.0, "timeStamp": "2025-05-18T15:05:54.520Z", "lastModified": "2025-05-18T16:02:35Z", "qualifier": [{"id": 5737075603, "qualifierId": 44, "value": "1, 2, 2, 3, 2, 2, 3, 3, 4, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5"}, {"id": 5737075601, "qualifierId": 30, "value": "2uot3bapay6um2zay1ff4ubbp, 3hx00k0xtmw5hgqfhheh5iad, cmzy3ey3786ipkvxbulctxxhx, dzv5l0y2ad05k7fiwzah07mcp, 1l6w8m9upv3l6svmdhw7lb651, 9dvbk42oqmy98gt1gae5lo15h, 1eut2mrcgcr2rji48n2yinrh1, cg1j982govx2perwr3rv33t1x, 6g44j80ofx3xij3ltql12bhsl, 5q231tqko859i311srdsm4zx1, angan7u6kc3t2ks368hvcsout, 4phywxtdq9cojmyhn1yipbfv8, az4vys76d0diwqjw8hy7908o9, 2frjnz2wa860fae3pl9rl0n4k, 21meptskq7d8atuzim2i2ixn9, e0ec2upmben78u7zmc7v57npx, 1l12v8lx25<PERSON>y5obp0wcfkr11, 35hkwhj0m65aqyalfrjweyml1, 7jpfgxy3b4ma6sp5fibpn7c9h, e36jmfhholbbk1tq2nff0bsh5, 2wb6xkgzfeoojp50dbv594esp"}, {"id": 5737075609, "qualifierId": 131, "value": "1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"}, {"id": 5737075607, "qualifierId": 130, "value": "7"}, {"id": 5737391043, "qualifierId": 197, "value": "29677"}, {"id": 5737075611, "qualifierId": 194, "value": "cmzy3ey3786ipkvxbulctxxhx"}, {"id": 5737075605, "qualifierId": 59, "value": "35, 7, 3, 80, 26, 14, 55, 12, 22, 8, 24, 74, 44, 53, 58, 23, 10, 11, 17, 90, 46"}, {"id": 5737075613, "qualifierId": 227, "value": "0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0"}]}, {"id": 2816624055, "eventId": 2, "typeId": 32, "periodId": 1, "timeMin": 0, "timeSec": 0, "contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "outcome": 1, "x": 0.0, "y": 0.0, "timeStamp": "2025-05-18T16:04:09.335Z", "lastModified": "2025-05-18T16:10:21Z", "qualifier": [{"id": 5737396747, "qualifierId": 127, "value": "Right to Left"}]}, {"id": 2816624047, "eventId": 2, "typeId": 32, "periodId": 1, "timeMin": 0, "timeSec": 0, "contestantId": "f432akygffyamal3h6poig65t", "outcome": 1, "x": 0.0, "y": 0.0, "timeStamp": "2025-05-18T16:04:09.335Z", "lastModified": "2025-05-18T16:10:33Z", "qualifier": [{"id": 5737396707, "qualifierId": 127, "value": "Left to Right"}]}, {"id": 2816624091, "eventId": 3, "typeId": 79, "periodId": 1, "timeMin": 0, "timeSec": 2, "contestantId": "f432akygffyamal3h6poig65t", "outcome": 0, "x": 0.0, "y": 0.0, "timeStamp": "2025-05-18T16:04:11.972Z", "lastModified": "2025-05-18T16:04:12Z", "qualifier": [{"id": 5737396901, "qualifierId": 344}]}, {"id": 2816632005, "eventId": 3, "typeId": 16, "periodId": 1, "timeMin": 8, "timeSec": 43, "contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "playerId": "76n4eg1xu5qvf9bkwy4p8jsut", "playerName": "<PERSON><PERSON>", "outcome": 1, "x": 88.5, "y": 50.1, "timeStamp": "2025-05-18T16:12:52.850Z", "lastModified": "2025-05-18T16:13:37Z", "qualifier": [{"id": 5737446569, "qualifierId": 20}, {"id": 5737442497, "qualifierId": 17}, {"id": 5737447187, "qualifierId": 102, "value": "50.1"}, {"id": 5737447181, "qualifierId": 78}, {"id": 5737447191, "qualifierId": 103, "value": "18.4"}, {"id": 5737447197, "qualifierId": 230, "value": "99.4"}, {"id": 5737446577, "qualifierId": 375, "value": "08:42.790"}, {"id": 5737446573, "qualifierId": 374, "value": "2025-05-18 17:12:52.125"}, {"id": 5737449749, "qualifierId": 189}, {"id": 5737447195, "qualifierId": 458}, {"id": 5737442499, "qualifierId": 56, "value": "Center"}, {"id": 5737442495, "qualifierId": 22}, {"id": 5737447199, "qualifierId": 231, "value": "50"}]}]}}