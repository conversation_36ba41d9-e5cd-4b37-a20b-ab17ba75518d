{"matchInfo": {"id": "bukekpxmpmbs5qmcsrmgx2b6c", "coverageLevel": "15", "date": "2021-08-14Z", "time": "19:00:00Z", "week": "2", "numberOfPeriods": 2, "periodLength": 45, "overtimeLength": 15, "lastUpdated": "2021-08-14T19:47:29Z", "description": "PSG vs Strasbourg", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "dm5ka0os1e3dxcp3vh05kmp33", "name": "Ligue 1", "competitionCode": "LI1", "competitionFormat": "Domestic league", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}, "tournamentCalendar": {"id": "buq1p8cf83xr4f0fygagjxn2s", "startDate": "2021-08-06Z", "endDate": "2022-05-21Z", "name": "2021/2022"}, "stage": {"id": "bux99e61orz4fnrkc6jzqr4lw", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2021-08-06Z", "endDate": "2022-05-21Z", "name": "Regular Season"}, "contestant": [{"id": "2b3mar72yy8d6uvat1ka6tn3r", "name": "PSG", "shortName": "PSG", "officialName": "Paris Saint-Germain FC", "code": "PSG", "position": "home", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}, {"id": "71ajatxnuhc5cnm3xvgdky49w", "name": "Strasbourg", "shortName": "Strasbourg", "officialName": "RC Strasbourg Alsace", "code": "STR", "position": "away", "country": {"id": "7gww28djs405rfga69smki84o", "name": "France"}}], "venue": {"id": "efcoydloex7y04q29pac9s67c", "neutral": "no", "longName": "Parc des Princes", "shortName": "Parc des Princes"}}, "liveData": {"matchDetails": {"periodId": 10, "matchStatus": "Playing", "period": [{"id": 1, "start": "2021-08-14T19:00:08Z", "end": "2021-08-14T19:48:07Z", "lengthMin": 47, "lengthSec": 59}], "scores": {"ht": {"home": 3, "away": 0}, "ft": {"home": 3, "away": 0}, "total": {"home": 3, "away": 0}}}, "goal": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 1, "timeMin": 3, "timeMinSec": "2:19", "lastUpdated": "2021-08-14T19:03:23Z", "timestamp": "2021-08-14T19:02:28Z", "type": "G", "scorerId": "drkx7msmzruq5myxk9ani6z11", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "dyigvzjpa74cw4urhh7fwzs2d", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318814599", "homeScore": 1, "awayScore": 0}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 1, "timeMin": 25, "timeMinSec": "24:49", "lastUpdated": "2021-08-14T19:26:06Z", "timestamp": "2021-08-14T19:24:57Z", "type": "G", "scorerId": "5e9ilgrz3tzg9kd1gk3yvrahh", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "36fznb9hzqvn6lnlla7f3ycwl", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2318835129", "homeScore": 2, "awayScore": 0}, {"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "periodId": 1, "timeMin": 27, "timeMinSec": "26:14", "lastUpdated": "2021-08-14T19:27:11Z", "timestamp": "2021-08-14T19:26:23Z", "type": "G", "scorerId": "36fznb9hzqvn6lnlla7f3ycwl", "scorerName": "<PERSON><PERSON>", "optaEventId": "2318836127", "homeScore": 3, "awayScore": 0}], "substitute": [{"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "periodId": 1, "timeMin": 42, "timeMinSec": "41:52", "lastUpdated": "2021-08-14T19:42:12Z", "timestamp": "2021-08-14T19:42:01Z", "playerOnId": "c8u45gxriyq1rup5fpr30xrdh", "playerOnName": "<PERSON><PERSON>", "playerOffId": "88nw24pkg2ikj9c91tccvqol", "playerOffName": "<PERSON><PERSON>", "subReason": "Injury"}], "lineUp": [{"contestantId": "2b3mar72yy8d6uvat1ka6tn3r", "formationUsed": "433", "player": [{"playerId": "9sme7jykdv8msgfu0nxx6h39x", "firstName": "<PERSON><PERSON>", "lastName": "Navas Gamboa", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "possLostAll", "value": "1"}, {"type": "openPlayPass", "value": "18"}, {"type": "fwdPass", "value": "10"}, {"type": "accurateGoalKicks", "value": "5"}, {"type": "possLostCtrl", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "accuratePass", "value": "22"}, {"type": "touches", "value": "26"}, {"type": "totalHighClaim", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "17"}, {"type": "gameStarted", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "goalKicks", "value": "5"}, {"type": "totalPass", "value": "23"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "5"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "rightsidePass", "value": "8"}, {"type": "totalBackZonePass", "value": "23"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateKeeperThrows", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "dyigvzjpa74cw4urhh7fwzs2d", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 22, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "fwdPass", "value": "14"}, {"type": "interception", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "12"}, {"type": "duelLost", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "openPlayPass", "value": "36"}, {"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "passesLeft", "value": "15"}, {"type": "possLostAll", "value": "4"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "goalAssist", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "totalFwdZonePass", "value": "29"}, {"type": "totalAttAssist", "value": "2"}, {"type": "backwardPass", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "possLostCtrl", "value": "4"}, {"type": "crosses18yardplus", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "touches", "value": "53"}, {"type": "totalContest", "value": "3"}, {"type": "headPass", "value": "1"}, {"type": "accuratePass", "value": "34"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "aerialWon", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "32"}, {"type": "totalPass", "value": "38"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "penAreaEntries", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "totalThrows", "value": "7"}, {"type": "rightsidePass", "value": "19"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "accurateCross", "value": "2"}, {"type": "totalBackZonePass", "value": "11"}, {"type": "totalCross", "value": "2"}, {"type": "goalAssistIntentional", "value": "1"}, {"type": "accurateThrows", "value": "7"}, {"type": "accurateFwdZonePass", "value": "25"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "10"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "1qywnx1icmb4x7dbhhsck0vrp", "firstName": "Presnel", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "captain": "yes", "stat": [{"type": "accurateLaunches", "value": "1"}, {"type": "totalBackZonePass", "value": "25"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "effectiveClearance", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "longPassOwnToOppSuccess", "value": "8"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "23"}, {"type": "accurateBackZonePass", "value": "24"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalPass", "value": "50"}, {"type": "totalLongBalls", "value": "3"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalClearance", "value": "3"}, {"type": "rightsidePass", "value": "12"}, {"type": "touches", "value": "55"}, {"type": "accuratePass", "value": "47"}, {"type": "headPass", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "44"}, {"type": "gameStarted", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "aerialWon", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "possLostAll", "value": "3"}, {"type": "passesLeft", "value": "14"}, {"type": "openPlayPass", "value": "47"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "offsideProvoked", "value": "1"}, {"type": "fwdPass", "value": "20"}, {"type": "possLostCtrl", "value": "3"}, {"type": "totalFwdZonePass", "value": "25"}, {"type": "backwardPass", "value": "4"}, {"type": "lostCorners", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "14"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "cm0gwoyh5rdb216fx685a7l5h", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "totalBackZonePass", "value": "37"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "5"}, {"type": "accurateFwdZonePass", "value": "13"}, {"type": "accurateBackZonePass", "value": "37"}, {"type": "totalPass", "value": "53"}, {"type": "dispossessed", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "rightsidePass", "value": "13"}, {"type": "touches", "value": "60"}, {"type": "accuratePass", "value": "50"}, {"type": "headPass", "value": "4"}, {"type": "interceptionWon", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "50"}, {"type": "gameStarted", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "aerialWon", "value": "4"}, {"type": "totalChippedPass", "value": "3"}, {"type": "duelLost", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "interception", "value": "2"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "possLostAll", "value": "4"}, {"type": "openPlayPass", "value": "53"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "fwdPass", "value": "17"}, {"type": "possLostCtrl", "value": "4"}, {"type": "passesRight", "value": "10"}, {"type": "totalFwdZonePass", "value": "16"}, {"type": "aerialLost", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "23"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "1vr896w03yky9kmu8x5tjkicp", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "turnover", "value": "1"}, {"type": "accuratePass", "value": "30"}, {"type": "totalOffside", "value": "1"}, {"type": "touches", "value": "48"}, {"type": "totalContest", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "crosses18yard", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "29"}, {"type": "fwdPass", "value": "7"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "openPlayPass", "value": "32"}, {"type": "possLostAll", "value": "7"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "14"}, {"type": "backwardPass", "value": "12"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFwdZonePass", "value": "17"}, {"type": "passesRight", "value": "12"}, {"type": "possLostCtrl", "value": "7"}, {"type": "attBxCentre", "value": "1"}, {"type": "totalBackZonePass", "value": "18"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalCross", "value": "2"}, {"type": "accurateThrows", "value": "7"}, {"type": "attRfTotal", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "attemptsIbox", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalPass", "value": "33"}, {"type": "accurateBackZonePass", "value": "16"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "penAreaEntries", "value": "2"}, {"type": "totalThrows", "value": "7"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attOpenplay", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "2lyib1m8bhrkcc3ukk12fbefp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "turnover", "value": "1"}, {"type": "accuratePass", "value": "16"}, {"type": "touches", "value": "23"}, {"type": "totalContest", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "14"}, {"type": "fwdPass", "value": "5"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "openPlayPass", "value": "16"}, {"type": "possLostAll", "value": "4"}, {"type": "passesLeft", "value": "9"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "backwardPass", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "passesRight", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "totalBackZonePass", "value": "5"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "accurateFwdZonePass", "value": "12"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "dispossessed", "value": "1"}, {"type": "totalPass", "value": "18"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "rightsidePass", "value": "6"}, {"type": "totalTackle", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "totalLongBalls", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "e40eqhxzzid2chfyxph37tuxh", "firstName": "Ander", "lastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "accurateFwdZonePass", "value": "25"}, {"type": "longPassOwnToOppSuccess", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "wasFouled", "value": "1"}, {"type": "totalBackZonePass", "value": "34"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "challengeLost", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "rightsidePass", "value": "21"}, {"type": "totalLongBalls", "value": "4"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalPass", "value": "62"}, {"type": "accurateBackZonePass", "value": "34"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalChippedPass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "58"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "touches", "value": "67"}, {"type": "accuratePass", "value": "59"}, {"type": "headPass", "value": "1"}, {"type": "totalFwdZonePass", "value": "28"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "backwardPass", "value": "15"}, {"type": "leftside<PERSON><PERSON>", "value": "15"}, {"type": "possLostCtrl", "value": "3"}, {"type": "passesRight", "value": "6"}, {"type": "longPassOwnToOpp", "value": "7"}, {"type": "wonCorners", "value": "1"}, {"type": "fwdPass", "value": "11"}, {"type": "totalFinalThirdPasses", "value": "8"}, {"type": "duelLost", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "passesLeft", "value": "6"}, {"type": "possLostAll", "value": "3"}, {"type": "openPlayPass", "value": "61"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "f5lfo1hz7u5bk494w53arcacq", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 36, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "turnover", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "accuratePass", "value": "51"}, {"type": "totalContest", "value": "2"}, {"type": "touches", "value": "63"}, {"type": "totalChippedPass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "50"}, {"type": "fwdPass", "value": "13"}, {"type": "wonCorners", "value": "1"}, {"type": "longPassOwnToOpp", "value": "9"}, {"type": "openPlayPass", "value": "55"}, {"type": "passesLeft", "value": "5"}, {"type": "possLostAll", "value": "7"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "finalThirdEntries", "value": "10"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "15"}, {"type": "duelLost", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "15"}, {"type": "backwardPass", "value": "14"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "totalFwdZonePass", "value": "31"}, {"type": "passesRight", "value": "13"}, {"type": "possLostCtrl", "value": "7"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "totalBackZonePass", "value": "25"}, {"type": "accurateFwdZonePass", "value": "26"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "10"}, {"type": "longPassOwnToOppSuccess", "value": "8"}, {"type": "dispossessed", "value": "1"}, {"type": "totalPass", "value": "56"}, {"type": "accurateBackZonePass", "value": "25"}, {"type": "rightsidePass", "value": "14"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "penAreaEntries", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalLongBalls", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "5e9ilgrz3tzg9kd1gk3yvrahh", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "totalScoringAtt", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalPass", "value": "19"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "attGoalLowLeft", "value": "1"}, {"type": "rightsidePass", "value": "6"}, {"type": "totalClearance", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "penAreaEntries", "value": "4"}, {"type": "accurateCross", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalCross", "value": "2"}, {"type": "attRfTotal", "value": "1"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "totalFlickOn", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "10"}, {"type": "wasFouled", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "fwdPass", "value": "4"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "passesLeft", "value": "8"}, {"type": "possLostAll", "value": "9"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "openPlayPass", "value": "18"}, {"type": "attBxLeft", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "14"}, {"type": "possWonAtt3rd", "value": "2"}, {"type": "duelLost", "value": "5"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "cornerTaken", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "totalFwdZonePass", "value": "19"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "aerialLost", "value": "2"}, {"type": "passesRight", "value": "2"}, {"type": "possLostCtrl", "value": "9"}, {"type": "interceptionWon", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "turnover", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "accuratePass", "value": "14"}, {"type": "touches", "value": "31"}, {"type": "totalContest", "value": "3"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "drkx7msmzruq5myxk9ani6z11", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "goals", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "aerialWon", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "attIboxGoal", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "touches", "value": "11"}, {"type": "accuratePass", "value": "3"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "possLostCtrl", "value": "3"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attHdTotal", "value": "2"}, {"type": "duelLost", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "possLostAll", "value": "3"}, {"type": "passesLeft", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "totalFlickOn", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "attHdGoal", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "attemptsIbox", "value": "2"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attBxCentre", "value": "2"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "attemptsObox", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "attCmissHigh", "value": "1"}, {"type": "totalLayoffs", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attMissHigh", "value": "1"}, {"type": "bigChanceScored", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "attHdMiss", "value": "1"}, {"type": "totalPass", "value": "5"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "attIboxMiss", "value": "1"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "36fznb9hzqvn6lnlla7f3ycwl", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "attRfTotal", "value": "1"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "totalBackZonePass", "value": "11"}, {"type": "totalCross", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "11"}, {"type": "accurateBackZonePass", "value": "11"}, {"type": "totalPass", "value": "23"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "dispossessed", "value": "2"}, {"type": "attOpenplay", "value": "1"}, {"type": "bigChanceScored", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "touches", "value": "30"}, {"type": "accuratePass", "value": "22"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "22"}, {"type": "gameStarted", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "goals", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "duelLost", "value": "2"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "openPlayPass", "value": "23"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "fwdPass", "value": "2"}, {"type": "possLostCtrl", "value": "5"}, {"type": "passesRight", "value": "7"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "goalAssist", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "backwardPass", "value": "11"}, {"type": "leftside<PERSON><PERSON>", "value": "7"}, {"type": "totalAttAssist", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "3775rpg889ojktofzx1y6jpat", "firstName": "<PERSON>", "lastName": "Alcântara do Nascimento", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "atlhc604ri3rr4w012otutyj8", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "77fldfqlxps1xbdg29anv4vai", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "34z1r2585ifo6zdcfuu6xumfo", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "G<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 35, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "5qg0coudjinnt978myh4n4n2t", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "dw9fzixumboknmucqgqffurmy", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Kalimuendo-Muinga", "matchName": "<PERSON><PERSON>", "shirtNumber": 29, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "eyzcfyn1crjcgmm98uk9u8oyy", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 38, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "ckjcm9q26zd6q8tkq0go6oxcl", "firstName": "Sergio", "lastName": "<PERSON>", "matchName": "Sergio Rico", "shirtNumber": 16, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4pyehox7p3z64c4l3k8yoyfv9", "firstName": "<PERSON>", "lastName": "Sarabia García", "matchName": "<PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "1yt2lhoij0ozjfg1othrlqsph", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "58", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "58"}, {"fh": "30", "type": "longPassOwnToOppSuccess", "value": "30"}, {"fh": "2", "type": "accurateLayoffs", "value": "2"}, {"fh": "6", "type": "effectiveClearance", "value": "6"}, {"fh": "5", "type": "attemptsIbox", "value": "5"}, {"fh": "2", "type": "keeper<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "2", "type": "fouledFinalThird", "value": "2"}, {"fh": "1", "type": "attHdGoal", "value": "1"}, {"fh": "2", "type": "totalFlickOn", "value": "2"}, {"fh": "165", "type": "accurateFwdZonePass", "value": "165"}, {"fh": "4", "type": "unsuccessfulTouch", "value": "4"}, {"fh": "2", "type": "accurateKeeperThrows", "value": "2"}, {"fh": "3", "type": "forwardGoals", "value": "3"}, {"fh": "14", "type": "accurateThrows", "value": "14"}, {"fh": "3", "type": "totalLayoffs", "value": "3"}, {"fh": "2", "type": "attGoalLowRight", "value": "2"}, {"fh": "4", "type": "attRfTotal", "value": "4"}, {"fh": "1", "type": "accurateLaunches", "value": "1"}, {"fh": "1", "type": "attCmissHigh", "value": "1"}, {"fh": "1", "type": "goalAssistIntentional", "value": "1"}, {"fh": "1", "type": "attemptsObox", "value": "1"}, {"fh": "2", "type": "challengeLost", "value": "2"}, {"fh": "3", "type": "attBxCentre", "value": "3"}, {"fh": "193", "type": "totalBackZonePass", "value": "193"}, {"fh": "15", "type": "accurateLongBalls", "value": "15"}, {"fh": "7", "type": "totalCross", "value": "7"}, {"fh": "1", "type": "attHdMiss", "value": "1"}, {"fh": "3", "type": "accurateCross", "value": "3"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "totalCornersIntobox", "value": "1"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "type": "attemptsConcededObox", "value": "3"}, {"fh": "1", "type": "attOboxBlocked", "value": "1"}, {"fh": "30", "type": "duel<PERSON>on", "value": "30"}, {"fh": "4", "type": "attOpenplay", "value": "4"}, {"fh": "1", "type": "attMissHigh", "value": "1"}, {"fh": "2", "type": "bigChanceScored", "value": "2"}, {"fh": "1", "type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "21", "type": "totalLongBalls", "value": "21"}, {"fh": "6", "type": "possWonMid3rd", "value": "6"}, {"fh": "102", "type": "rightsidePass", "value": "102"}, {"fh": "2", "type": "blockedScoringAtt", "value": "2"}, {"fh": "12", "type": "penAreaEntries", "value": "12"}, {"fh": "4", "type": "totalTackle", "value": "4"}, {"fh": "14", "type": "totalThrows", "value": "14"}, {"fh": "6", "type": "totalClearance", "value": "6"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "1", "type": "attGoalLowLeft", "value": "1"}, {"fh": "186", "type": "accurateBackZonePass", "value": "186"}, {"fh": "68", "type": "possessionPercentage", "value": "68"}, {"fh": "1", "type": "offtargetAttAssist", "value": "1"}, {"fh": "1", "type": "attIboxMiss", "value": "1"}, {"fh": "6", "type": "dispossessed", "value": "6"}, {"fh": "6", "type": "totalScoringAtt", "value": "6"}, {"fh": "5", "type": "goalKicks", "value": "5"}, {"fh": "380", "type": "totalPass", "value": "380"}, {"fh": "4", "type": "headClea<PERSON>", "value": "4"}, {"fh": "1", "type": "attObxCentre", "value": "1"}, {"fh": "14", "type": "ballRecovery", "value": "14"}, {"fh": "4", "type": "crosses18yard", "value": "4"}, {"fh": "1", "type": "successfulPutThrough", "value": "1"}, {"type": "firstHalfGoals", "value": "3"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "331", "type": "successfulOpenPlayPass", "value": "331"}, {"fh": "15", "type": "totalChippedPass", "value": "15"}, {"fh": "1", "type": "totalLaunches", "value": "1"}, {"fh": "10", "type": "aerialWon", "value": "10"}, {"fh": "6", "type": "totalCrossNocorner", "value": "6"}, {"fh": "3", "type": "goals", "value": "3"}, {"fh": "2", "type": "attemptsConcededIbox", "value": "2"}, {"fh": "348", "type": "accuratePass", "value": "348"}, {"fh": "1", "type": "totalOffside", "value": "1"}, {"fh": "467", "type": "touches", "value": "467"}, {"fh": "11", "type": "totalContest", "value": "11"}, {"fh": "2", "type": "fkFoulLost", "value": "2"}, {"fh": "2", "type": "crosses18yardplus", "value": "2"}, {"fh": "2", "type": "goalsOpenplay", "value": "2"}, {"fh": "6", "type": "interceptionWon", "value": "6"}, {"fh": "3", "type": "attIboxGoal", "value": "3"}, {"fh": "8", "type": "fkFoulWon", "value": "8"}, {"fh": "1", "type": "totalHighClaim", "value": "1"}, {"fh": "4", "type": "effectiveHeadClearance", "value": "4"}, {"fh": "51", "type": "passesRight", "value": "51"}, {"fh": "5", "type": "accurateGoalKicks", "value": "5"}, {"fh": "50", "type": "possLostCtrl", "value": "50"}, {"fh": "103", "type": "leftside<PERSON><PERSON>", "value": "103"}, {"fh": "2", "type": "cornerTaken", "value": "2"}, {"fh": "5", "type": "totalAttAssist", "value": "5"}, {"fh": "3", "type": "lostCorners", "value": "3"}, {"fh": "2", "type": "attRfGoal", "value": "2"}, {"fh": "70", "type": "backwardPass", "value": "70"}, {"fh": "5", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "8", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"fh": "2", "type": "attemptedTackleFoul", "value": "2"}, {"fh": "2", "type": "goalAssist", "value": "2"}, {"fh": "4", "type": "aerialLost", "value": "4"}, {"fh": "1", "type": "accurateCornersIntobox", "value": "1"}, {"fh": "2", "type": "goalAssistOpenplay", "value": "2"}, {"fh": "194", "type": "totalFwdZonePass", "value": "194"}, {"fh": "363", "type": "openPlayPass", "value": "363"}, {"fh": "1", "type": "attBxLeft", "value": "1"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"fh": "59", "type": "passesLeft", "value": "59"}, {"fh": "50", "type": "possLostAll", "value": "50"}, {"fh": "18", "type": "touchesInOppBox", "value": "18"}, {"fh": "33", "type": "finalThirdEntries", "value": "33"}, {"fh": "8", "type": "accurateChippedPass", "value": "8"}, {"fh": "6", "type": "interception", "value": "6"}, {"fh": "5", "type": "blocked<PERSON><PERSON>", "value": "5"}, {"fh": "2", "type": "attHdTotal", "value": "2"}, {"fh": "75", "type": "totalFinalThirdPasses", "value": "75"}, {"fh": "3", "type": "possWonAtt3rd", "value": "3"}, {"fh": "17", "type": "duelLost", "value": "17"}, {"fh": "4", "type": "ontargetAttAssist", "value": "4"}, {"fh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "105", "type": "fwdPass", "value": "105"}, {"fh": "2", "type": "wonCorners", "value": "2"}, {"fh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "3", "type": "ontargetScoringAtt", "value": "3"}, {"fh": "3", "type": "attAssistOpenplay", "value": "3"}, {"fh": "3", "type": "possWonDef3rd", "value": "3"}, {"fh": "38", "type": "longPassOwnToOpp", "value": "38"}, {"type": "formationUsed", "value": "433"}], "kit": {"id": "659", "colour1": "#000066", "colour2": "#FF0000", "type": "home"}}, {"contestantId": "71ajatxnuhc5cnm3xvgdky49w", "formationUsed": "532", "player": [{"playerId": "2vgr8c97x4b2e51tyvvixusid", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "accurateKeeperThrows", "value": "1"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "errorLeadToGoal", "value": "1"}, {"type": "rightsidePass", "value": "1"}, {"type": "totalLongBalls", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "goalKicks", "value": "2"}, {"type": "totalPass", "value": "8"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalLaunches", "value": "3"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "ballRecovery", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "accuratePass", "value": "3"}, {"type": "touches", "value": "9"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "accurateGoalKicks", "value": "2"}, {"type": "passesRight", "value": "1"}, {"type": "possLostCtrl", "value": "5"}, {"type": "fwdPass", "value": "4"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "possLostAll", "value": "5"}, {"type": "openPlayPass", "value": "5"}, {"type": "goalsConceded", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "d5at3cowukpxiptq5ozcbe6s5", "firstName": "<PERSON>", "lastName": "Djiku", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "accurateFwdZonePass", "value": "3"}, {"type": "effectiveClearance", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "totalTackle", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "rightsidePass", "value": "13"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalPass", "value": "18"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "totalChippedPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "ballRecovery", "value": "2"}, {"type": "interceptionWon", "value": "1"}, {"type": "touches", "value": "23"}, {"type": "headPass", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "passesRight", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "fwdPass", "value": "4"}, {"type": "goalsConceded", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "openPlayPass", "value": "18"}, {"type": "possLostAll", "value": "3"}, {"type": "passesLeft", "value": "1"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "cb6w7n7n1eh9huwzyzvgmdxih", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 27, "position": "Defender", "positionSide": "Centre", "formationPlace": "5", "stat": [{"type": "accurateBackZonePass", "value": "10"}, {"type": "totalPass", "value": "14"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "rightsidePass", "value": "7"}, {"type": "totalClearance", "value": "2"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalBackZonePass", "value": "11"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "openPlayPass", "value": "14"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "offsideProvoked", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "backwardPass", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accuratePass", "value": "13"}, {"type": "touches", "value": "17"}, {"type": "ballRecovery", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "totalChippedPass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "eo47h2ekymiisj3kry3og7tgp", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "6", "stat": [{"type": "fwdPass", "value": "7"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "possLostAll", "value": "3"}, {"type": "openPlayPass", "value": "17"}, {"type": "duelLost", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "interception", "value": "3"}, {"type": "backwardPass", "value": "2"}, {"type": "lostCorners", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "6"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "interceptionWon", "value": "3"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "accuratePass", "value": "15"}, {"type": "headPass", "value": "1"}, {"type": "touches", "value": "27"}, {"type": "aerialWon", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalLaunches", "value": "2"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "ballRecovery", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "14"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalPass", "value": "18"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "rightsidePass", "value": "3"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "challengeLost", "value": "1"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "wasFouled", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "9ysk6656hj0na1hrizgt83k5x", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Wing Back", "positionSide": "Left", "formationPlace": "3", "captain": "yes", "stat": [{"type": "totalLongBalls", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "totalCornersIntobox", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "penAreaEntries", "value": "4"}, {"type": "totalThrows", "value": "3"}, {"type": "rightsidePass", "value": "8"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "totalPass", "value": "15"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "accurateThrows", "value": "3"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "totalCross", "value": "4"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "challengeLost", "value": "2"}, {"type": "possLostCtrl", "value": "8"}, {"type": "passesRight", "value": "1"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "backwardPass", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "cornerTaken", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "goalsConceded", "value": "3"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "8"}, {"type": "passesLeft", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "openPlayPass", "value": "14"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "wonCorners", "value": "2"}, {"type": "fwdPass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "11"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "touches", "value": "26"}, {"type": "fouls", "value": "2"}, {"type": "accuratePass", "value": "12"}, {"type": "turnover", "value": "1"}, {"type": "crosses18yardplus", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "4ju2soyxqa6hu0oo007ej8wdl", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Wing Back", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "totalBackZonePass", "value": "12"}, {"type": "totalCross", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "challengeLost", "value": "1"}, {"type": "accurateThrows", "value": "1"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalPass", "value": "20"}, {"type": "dispossessed", "value": "1"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalTackle", "value": "3"}, {"type": "totalThrows", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "3"}, {"type": "turnover", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "touches", "value": "29"}, {"type": "accuratePass", "value": "19"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "19"}, {"type": "ballRecovery", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "duelLost", "value": "3"}, {"type": "openPlayPass", "value": "20"}, {"type": "possLostAll", "value": "4"}, {"type": "aerialLost", "value": "1"}, {"type": "totalFwdZonePass", "value": "9"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "backwardPass", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "passesRight", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "wg9qybhs3qeq9awhux331uqx", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "openPlayPass", "value": "20"}, {"type": "passesLeft", "value": "1"}, {"type": "possLostAll", "value": "6"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fwdPass", "value": "6"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "attOboxMiss", "value": "1"}, {"type": "passesRight", "value": "4"}, {"type": "possLostCtrl", "value": "6"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "cornerTaken", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFwdZonePass", "value": "12"}, {"type": "headPass", "value": "1"}, {"type": "accuratePass", "value": "16"}, {"type": "touches", "value": "24"}, {"type": "attObxCentre", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "16"}, {"type": "totalLaunches", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalPass", "value": "20"}, {"type": "totalCornersIntobox", "value": "2"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "attMissHigh", "value": "1"}, {"type": "totalLongBalls", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "rightsidePass", "value": "5"}, {"type": "penAreaEntries", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "attemptsObox", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalBackZonePass", "value": "10"}, {"type": "totalCross", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "42vsnv2j6emkbynforgon4n2t", "firstName": "Sanjin", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "8", "stat": [{"type": "accurateBackZonePass", "value": "13"}, {"type": "totalPass", "value": "29"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalLongBalls", "value": "7"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "rightsidePass", "value": "8"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalLayoffs", "value": "2"}, {"type": "accurateLongBalls", "value": "5"}, {"type": "totalBackZonePass", "value": "16"}, {"type": "attemptsObox", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "challengeLost", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "duelLost", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "accurateChippedPass", "value": "5"}, {"type": "goalsConceded", "value": "3"}, {"type": "passesLeft", "value": "3"}, {"type": "possLostAll", "value": "7"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "openPlayPass", "value": "29"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fwdPass", "value": "9"}, {"type": "possLostCtrl", "value": "7"}, {"type": "passesRight", "value": "5"}, {"type": "attOboxMiss", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "backwardPass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "totalAttAssist", "value": "1"}, {"type": "touches", "value": "33"}, {"type": "accuratePass", "value": "23"}, {"type": "headPass", "value": "1"}, {"type": "attMissRight", "value": "1"}, {"type": "interceptionWon", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "23"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "attLfTotal", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "totalChippedPass", "value": "6"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "88nw24pkg2ikj9c91tccvqol", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "fwdPass", "value": "1"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "possLostAll", "value": "5"}, {"type": "openPlayPass", "value": "7"}, {"type": "duelLost", "value": "4"}, {"type": "goalsConceded", "value": "3"}, {"type": "interception", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "possLostCtrl", "value": "5"}, {"type": "interceptionWon", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "accuratePass", "value": "6"}, {"type": "headPass", "value": "1"}, {"type": "handBall", "value": "1"}, {"type": "touches", "value": "14"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "ballRecovery", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "totalPass", "value": "7"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "challengeLost", "value": "2"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "overrun", "value": "1"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "950o8rjqvnl5bnidq2dc3fglx", "firstName": "<PERSON>", "lastName": "Gameiro", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "9", "stat": [{"type": "accuratePass", "value": "13"}, {"type": "headPass", "value": "1"}, {"type": "touches", "value": "17"}, {"type": "totalOffside", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "10"}, {"type": "gameStarted", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "attLfTotal", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "possLostAll", "value": "1"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "openPlayPass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "duelLost", "value": "3"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "possLostCtrl", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "aerialLost", "value": "2"}, {"type": "totalLayoffs", "value": "1"}, {"type": "attCmissHigh", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "attBxCentre", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "attemptsIbox", "value": "2"}, {"type": "accurateFwdZonePass", "value": "7"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "attIboxMiss", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "totalPass", "value": "13"}, {"type": "attOpenplay", "value": "2"}, {"type": "attMissHigh", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "am0pcyvtaws1i69jt09dgnsnp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Ajorque", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "attRfTotal", "value": "1"}, {"type": "goalsConcededIbox", "value": "3"}, {"type": "attemptsObox", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalFlickOn", "value": "1"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "totalPass", "value": "10"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLongBalls", "value": "1"}, {"type": "attemptsConcededObox", "value": "1"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "accuratePass", "value": "9"}, {"type": "headPass", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "touches", "value": "16"}, {"type": "ballRecovery", "value": "2"}, {"type": "attObxCentre", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "9"}, {"type": "gameStarted", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "attemptsConcededIbox", "value": "5"}, {"type": "passesLeft", "value": "2"}, {"type": "possLostAll", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "openPlayPass", "value": "10"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "duelLost", "value": "9"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "goalsConceded", "value": "3"}, {"type": "fwdPass", "value": "2"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "backwardPass", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "aerialLost", "value": "6"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "6u47fsriz3ejlf72hxtsew5bd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "d9y9v3rvj0dkiuaohfu46w7o5", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "bndkgzdolm2c3iteenkv8s3it", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "egtyl4noukbjkm1va1i11e87o", "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cgeh7qj32flqu9p3a4wnn3s9h", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 16, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "1ho9nox6j8s5cxd1kxjspsrca", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 32, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4gucmltvhuvakf8mrheveqdt6", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "c8u45gxriyq1rup5fpr30xrdh", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "duelLost", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "openPlayPass", "value": "2"}, {"type": "totalFwdZonePass", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "touches", "value": "3"}, {"type": "accuratePass", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "totalPass", "value": "2"}, {"type": "accurateBackZonePass", "value": "2"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "20n6djablebcoizw6b219rp3p", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "as310m2g7v386tnuzf1n0a19l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "5", "type": "totalScoringAtt", "value": "5"}, {"fh": "2", "type": "goalKicks", "value": "2"}, {"fh": "1", "type": "attIboxMiss", "value": "1"}, {"fh": "4", "type": "dispossessed", "value": "4"}, {"fh": "1", "type": "headClea<PERSON>", "value": "1"}, {"fh": "174", "type": "totalPass", "value": "174"}, {"fh": "93", "type": "accurateBackZonePass", "value": "93"}, {"fh": "32", "type": "possessionPercentage", "value": "32"}, {"fh": "1", "type": "offtargetAttAssist", "value": "1"}, {"fh": "9", "type": "possWonMid3rd", "value": "9"}, {"fh": "52", "type": "rightsidePass", "value": "52"}, {"fh": "2", "type": "blockedScoringAtt", "value": "2"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "6", "type": "totalClearance", "value": "6"}, {"fh": "10", "type": "penAreaEntries", "value": "10"}, {"fh": "9", "type": "totalTackle", "value": "9"}, {"fh": "4", "type": "totalThrows", "value": "4"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "3", "type": "totalCornersIntobox", "value": "3"}, {"fh": "3", "type": "attOpenplay", "value": "3"}, {"fh": "2", "type": "attMissHigh", "value": "2"}, {"fh": "7", "type": "won<PERSON><PERSON><PERSON>", "value": "7"}, {"fh": "23", "type": "totalLongBalls", "value": "23"}, {"fh": "1", "type": "attemptsConcededObox", "value": "1"}, {"fh": "1", "type": "attOboxBlocked", "value": "1"}, {"fh": "17", "type": "duel<PERSON>on", "value": "17"}, {"fh": "3", "type": "goalsConcededIbox", "value": "3"}, {"fh": "3", "type": "attemptsObox", "value": "3"}, {"fh": "8", "type": "challengeLost", "value": "8"}, {"fh": "110", "type": "totalBackZonePass", "value": "110"}, {"fh": "11", "type": "accurateLongBalls", "value": "11"}, {"fh": "8", "type": "totalCross", "value": "8"}, {"fh": "1", "type": "overrun", "value": "1"}, {"fh": "2", "type": "attBxCentre", "value": "2"}, {"fh": "4", "type": "totalLayoffs", "value": "4"}, {"fh": "4", "type": "accurateThrows", "value": "4"}, {"fh": "1", "type": "attCmissHigh", "value": "1"}, {"fh": "1", "type": "errorLeadToGoal", "value": "1"}, {"fh": "3", "type": "attRfTotal", "value": "3"}, {"fh": "53", "type": "accurateFwdZonePass", "value": "53"}, {"fh": "4", "type": "unsuccessfulTouch", "value": "4"}, {"fh": "1", "type": "accurateKeeperThrows", "value": "1"}, {"fh": "1", "type": "subsMade", "value": "1"}, {"fh": "1", "type": "totalFlickOn", "value": "1"}, {"fh": "20", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "20"}, {"fh": "10", "type": "longPassOwnToOppSuccess", "value": "10"}, {"fh": "1", "type": "keeper<PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "3", "type": "accurateLayoffs", "value": "3"}, {"fh": "6", "type": "effectiveClearance", "value": "6"}, {"fh": "2", "type": "attemptsIbox", "value": "2"}, {"fh": "3", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "42", "type": "fwdPass", "value": "42"}, {"fh": "3", "type": "attAssistOpenplay", "value": "3"}, {"fh": "11", "type": "possWonDef3rd", "value": "11"}, {"fh": "15", "type": "longPassOwnToOpp", "value": "15"}, {"fh": "1", "type": "attIboxBlocked", "value": "1"}, {"fh": "3", "type": "wonCorners", "value": "3"}, {"fh": "16", "type": "passesLeft", "value": "16"}, {"fh": "46", "type": "possLostAll", "value": "46"}, {"fh": "3", "type": "touchesInOppBox", "value": "3"}, {"fh": "12", "type": "finalThirdEntries", "value": "12"}, {"fh": "166", "type": "openPlayPass", "value": "166"}, {"fh": "26", "type": "totalFinalThirdPasses", "value": "26"}, {"fh": "30", "type": "duelLost", "value": "30"}, {"fh": "2", "type": "ontargetAttAssist", "value": "2"}, {"fh": "10", "type": "accurateChippedPass", "value": "10"}, {"fh": "6", "type": "interception", "value": "6"}, {"fh": "5", "type": "blocked<PERSON><PERSON>", "value": "5"}, {"fh": "3", "type": "goalsConceded", "value": "3"}, {"fh": "2", "type": "lostCorners", "value": "2"}, {"fh": "30", "type": "backwardPass", "value": "30"}, {"fh": "5", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "2", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "50", "type": "leftside<PERSON><PERSON>", "value": "50"}, {"fh": "3", "type": "cornerTaken", "value": "3"}, {"fh": "3", "type": "totalAttAssist", "value": "3"}, {"fh": "72", "type": "totalFwdZonePass", "value": "72"}, {"fh": "5", "type": "attemptedTackleFoul", "value": "5"}, {"fh": "10", "type": "aerialLost", "value": "10"}, {"fh": "2", "type": "accurateGoalKicks", "value": "2"}, {"fh": "2", "type": "attOboxMiss", "value": "2"}, {"fh": "18", "type": "passesRight", "value": "18"}, {"fh": "46", "type": "possLostCtrl", "value": "46"}, {"fh": "2", "type": "fkFoulWon", "value": "2"}, {"fh": "3", "type": "crosses18yardplus", "value": "3"}, {"fh": "6", "type": "interceptionWon", "value": "6"}, {"fh": "1", "type": "effectiveHeadClearance", "value": "1"}, {"fh": "1", "type": "attMissRight", "value": "1"}, {"fh": "146", "type": "accuratePass", "value": "146"}, {"fh": "9", "type": "fkFoulLost", "value": "9"}, {"fh": "1", "type": "handBall", "value": "1"}, {"fh": "1", "type": "totalOffside", "value": "1"}, {"fh": "238", "type": "touches", "value": "238"}, {"fh": "2", "type": "totalContest", "value": "2"}, {"fh": "4", "type": "aerialWon", "value": "4"}, {"fh": "15", "type": "totalChippedPass", "value": "15"}, {"fh": "7", "type": "totalLaunches", "value": "7"}, {"fh": "5", "type": "attemptsConcededIbox", "value": "5"}, {"fh": "2", "type": "attLfTotal", "value": "2"}, {"fh": "5", "type": "totalCrossNocorner", "value": "5"}, {"fh": "3", "type": "attObxCentre", "value": "3"}, {"fh": "21", "type": "ballRecovery", "value": "21"}, {"fh": "2", "type": "crosses18yard", "value": "2"}, {"fh": "139", "type": "successfulOpenPlayPass", "value": "139"}, {"fh": "4", "type": "successfulPutThrough", "value": "4"}, {"type": "formationUsed", "value": "532"}], "kit": {"id": "8124", "colour1": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "maohjb0q36etyc8e32pm1n11", "type": "Main", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "237wf33tlj0dp8abnkmk4lc5x", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "<PERSON>"}, {"id": "eky20xkl3yitd09g5lgeg7vth", "type": "Lineman 2", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>"}, {"id": "as0d8uqw2fqdu2elyjdqoop1x", "type": "Fourth official", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>"}]}}}