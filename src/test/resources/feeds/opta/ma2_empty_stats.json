{"matchInfo": {"id": "25uc7c4deb7do7uc2u26f88wk", "coverageLevel": "9", "date": "2025-05-18Z", "time": "16:00:00Z", "localDate": "2025-05-18", "localTime": "19:00:00", "week": "36", "postMatch": "1", "numberOfPeriods": 2, "periodLength": 45, "var": "1", "lastUpdated": "2025-05-18T18:01:54Z", "description": "Samsunspor vs Sivasspor", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "482ofyysbdbeoxauk19yg7tdt", "name": "<PERSON><PERSON><PERSON>", "knownName": "Turkish Super Lig", "sponsorName": "trendyol SüperLig", "competitionCode": "SÜL", "competitionFormat": "Domestic league", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}, "tournamentCalendar": {"id": "1c0t9lv2rftjjx8dxn5s9csus", "startDate": "2024-08-09Z", "endDate": "2025-06-01Z", "name": "2024/2025"}, "stage": {"id": "1ckyv15iml4041do5vrva32ms", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2024-08-09Z", "endDate": "2025-06-01Z", "name": "Regular Season"}, "contestant": [{"id": "dpsnqu7pd2b0shfzjyn5j1znf", "name": "Samsunspor", "shortName": "Samsunspor", "officialName": "Samsunspor Kulübü", "code": "SAM", "position": "home", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}, {"id": "f432akygffyamal3h6poig65t", "name": "Sivasspor", "shortName": "Sivasspor", "officialName": "Sivasspor Kulübü", "code": "SİV", "position": "away", "country": {"id": "6kd6webenogylfgwt2aa9l6vx", "name": "Türkiye"}}], "venue": {"id": "1ej47f9htsl0jn7jehm4pyn3e", "neutral": "no", "longName": "Samsun Yeni 19 Mayıs Stadyumu", "shortName": "Samsun Yeni 19 Mayıs Stadyumu"}}, "liveData": {"matchDetails": {"periodId": 14, "matchStatus": "Played", "winner": "home", "matchLengthMin": 94, "matchLengthSec": 11, "period": [{"id": 1, "start": "2025-05-18T16:04:09Z", "end": "2025-05-18T16:50:19Z", "lengthMin": 46, "lengthSec": 10, "announcedInjuryTime": 60}, {"id": 2, "start": "2025-05-18T17:10:44Z", "end": "2025-05-18T17:58:45Z", "lengthMin": 48, "lengthSec": 1}], "scores": {"ht": {"home": 1, "away": 0}, "ft": {"home": 1, "away": 0}, "total": {"home": 1, "away": 0}}}, "goal": [{"contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "periodId": 1, "timeMin": 9, "timeMinSec": "8:43", "lastUpdated": "2025-05-18T16:13:08Z", "timestamp": "2025-05-18T16:12:52Z", "type": "G", "scorerId": "76n4eg1xu5qvf9bkwy4p8jsut", "scorerName": "<PERSON><PERSON>", "optaEventId": "2816632005", "homeScore": 1, "awayScore": 0}], "card": [{"contestantId": "f432akygffyamal3h6poig65t", "periodId": 1, "timeMin": 40, "timeMinSec": "40:00", "lastUpdated": "2025-05-18T17:35:43Z", "timestamp": "2025-05-18T16:44:09Z", "type": "YC", "playerId": "9dvbk42oqmy98gt1gae5lo15h", "playerName": "S. <PERSON>", "optaEventId": "2816697245", "cardReason": "F<PERSON>l"}, {"contestantId": "f432akygffyamal3h6poig65t", "periodId": 1, "timeMin": 45, "timeMinSec": "44:03", "lastUpdated": "2025-05-18T17:36:02Z", "timestamp": "2025-05-18T16:48:13Z", "type": "YC", "playerId": "cg1j982govx2perwr3rv33t1x", "playerName": "<PERSON><PERSON>", "optaEventId": "2816697489", "cardReason": "F<PERSON>l"}, {"contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "periodId": 2, "timeMin": 86, "timeMinSec": "85:05", "lastUpdated": "2025-05-18T17:51:56Z", "timestamp": "2025-05-18T17:50:50Z", "type": "YC", "playerId": "8ox8sr19qwfoxvk2hfhx0xmmt", "playerName": "<PERSON><PERSON>", "optaEventId": "2816713359", "cardReason": "F<PERSON>l"}], "substitute": [{"contestantId": "f432akygffyamal3h6poig65t", "periodId": 2, "timeMin": 46, "timeMinSec": "45:00", "lastUpdated": "2025-05-18T17:36:44Z", "timestamp": "2025-05-18T17:10:44Z", "playerOnId": "2wb6xkgzfeoojp50dbv594esp", "playerOnName": "<PERSON><PERSON>", "playerOffId": "6g44j80ofx3xij3ltql12bhsl", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "periodId": 2, "timeMin": 73, "timeMinSec": "72:03", "lastUpdated": "2025-05-18T17:37:54Z", "timestamp": "2025-05-18T17:37:47Z", "playerOnId": "8058605f3gip7nglewe7svqzu", "playerOnName": "<PERSON><PERSON>", "playerOffId": "76n4eg1xu5qvf9bkwy4p8jsut", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "periodId": 2, "timeMin": 84, "timeMinSec": "83:05", "lastUpdated": "2025-05-18T17:48:56Z", "timestamp": "2025-05-18T17:48:49Z", "playerOnId": "8ox8sr19qwfoxvk2hfhx0xmmt", "playerOnName": "<PERSON><PERSON>", "playerOffId": "dlgueejo95xoqfugtpgc3i2nd", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "f432akygffyamal3h6poig65t", "periodId": 2, "timeMin": 84, "timeMinSec": "83:16", "lastUpdated": "2025-05-18T17:49:05Z", "timestamp": "2025-05-18T17:49:00Z", "playerOnId": "35hkwhj0m65aqyalfrjweyml1", "playerOnName": "<PERSON><PERSON>", "playerOffId": "dzv5l0y2ad05k7fiwzah07mcp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "dpsnqu7pd2b0shfzjyn5j1znf", "formationUsed": "4231", "player": [{"playerId": "ctgockgy49dl62spgtj6p89ud", "firstName": "<PERSON><PERSON>", "lastName": "Kocuk", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Kocuk", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "dtkf4wnjv5rvjdmxh8acay4et", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 16, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "ahhjd38eki0yws3iw1si0082x", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "4drlqwqk6srxnncm79feyy7kl", "firstName": "Ľubom<PERSON><PERSON>", "lastName": "Šatka", "shortFirstName": "Lubomír", "shortLastName": "Satka", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "7pxzxpxy8kzh7uevovjhveuhh", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "captain": "yes", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "dzai0thce5hvwbvdrvmcnsyj9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Defensive Midfielder", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "cleanSheet", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "3vvabqz2v7zgx4xikpm19clhx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Defensive Midfielder", "positionSide": "Centre/Right", "formationPlace": "8", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "c6rzadc9bx74tw06zdubmtc5x", "firstName": "<PERSON><PERSON>", "lastName": "Kılınç", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Attacking Midfielder", "positionSide": "Left", "formationPlace": "11", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "dlgueejo95xoqfugtpgc3i2nd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Carlo", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Attacking Midfielder", "positionSide": "Centre", "formationPlace": "10", "stat": [{"type": "minsPlayed", "value": "84"}, {"type": "totalSubOff", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "6dzx3oyd42ql69tqxqvg8mpxx", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Attacking Midfielder", "positionSide": "Right", "formationPlace": "7", "stat": [{"type": "cleanSheet", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "76n4eg1xu5qvf9bkwy4p8jsut", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 14, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "goals", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "minsPlayed", "value": "73"}, {"type": "attOpenplay", "value": "1"}, {"type": "attIboxGoal", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "attGoalLowCentre", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "touches", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "attemptsIbox", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "1qpdv43chhq4vcr1mc7qfpsr9", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "66jgw85uti6qsgxmlkzz2726t", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "5o87rq6ux71867sagd5d2e39", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "S. <PERSON>", "shirtNumber": 28, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8058605f3gip7nglewe7svqzu", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "minsPlayed", "value": "17"}, {"type": "totalSubOn", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "cl2mi40x3gjsan1iifpbwec4p", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "crn7i9243dnczl74jti8i5pzp", "firstName": "Flavien", "lastName": "Tai<PERSON>", "shortFirstName": "Flavien", "shortLastName": "Tai<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "7q2udflgi1xv9y2x04j38ralm", "firstName": "Halil", "lastName": "<PERSON><PERSON>", "shortFirstName": "Halil", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 45, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "8ox8sr19qwfoxvk2hfhx0xmmt", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "minsPlayed", "value": "6"}, {"type": "totalSubOn", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "5l11c0cz9ej2a730469pz7zf8", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Çetin", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Çetin", "matchName": "<PERSON><PERSON>", "shirtNumber": 96, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "1dzj2btdw0w4tm59kfbauqrys", "firstName": "<PERSON><PERSON>", "lastName": "Çift", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Çift", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": [{"id": "eykhtighw7mtblegzn9bretp1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "type": "manager"}], "stat": [{"type": "cleanSheet", "value": "1"}, {"fh": "1", "sh": "0", "type": "goalsOpenplay", "value": "1"}, {"fh": "1", "sh": "0", "type": "attBxCentre", "value": "1"}, {"fh": "1", "sh": "0", "type": "touchesInOppBox", "value": "1"}, {"fh": "1", "sh": "0", "type": "forwardGoals", "value": "1"}, {"fh": "1", "sh": "0", "type": "attemptsIbox", "value": "1"}, {"fh": "1", "sh": "0", "type": "attIboxGoal", "value": "1"}, {"fh": "1", "sh": "0", "type": "touches", "value": "1"}, {"fh": "1", "sh": "0", "type": "attRfGoal", "value": "1"}, {"fh": "1", "sh": "0", "type": "attGoalLowCentre", "value": "1"}, {"fh": "0", "sh": "1", "type": "totalYellowCard", "value": "1"}, {"fh": "1", "sh": "0", "type": "totalScoringAtt", "value": "1"}, {"type": "firstHalfGoals", "value": "1"}, {"fh": "1", "sh": "0", "type": "attRfTotal", "value": "1"}, {"fh": "1", "sh": "0", "type": "attOpenplay", "value": "1"}, {"fh": "1", "sh": "0", "type": "ontargetScoringAtt", "value": "1"}, {"fh": "1", "sh": "0", "type": "goals", "value": "1"}, {"fh": "0", "sh": "2", "type": "subsMade", "value": "2"}, {"type": "formationUsed", "value": "4231"}], "kit": {"id": "25217", "colour1": "#FFFFFF", "colour2": "#FF0000", "type": "away"}}, {"contestantId": "f432akygffyamal3h6poig65t", "formationUsed": "4141", "player": [{"playerId": "2uot3bapay6um2zay1ff4ubbp", "firstName": "<PERSON>", "lastName": "Vural", "shortFirstName": "<PERSON>", "shortLastName": "Vural", "matchName": "<PERSON><PERSON>", "shirtNumber": 35, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "goalsConceded", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "cmzy3ey3786ipkvxbulctxxhx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Çiftçi", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Çiftçi", "matchName": "<PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "captain": "yes", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "9dvbk42oqmy98gt1gae5lo15h", "firstName": "Samba", "lastName": "Camara", "shortFirstName": "Samba", "shortLastName": "Camara", "matchName": "S. <PERSON>", "shirtNumber": 14, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "1l6w8m9upv3l6svmdhw7lb651", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Uros", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "goalsConceded", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "3hx00k0xtmw5hgqfhheh5iad", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "goalsConceded", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "dzv5l0y2ad05k7fiwzah07mcp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Bekiroğlu", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Bekiroglu", "matchName": "<PERSON><PERSON>", "shirtNumber": 80, "position": "Defensive Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "goalsConceded", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "minsPlayed", "value": "84"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "angan7u6kc3t2ks368hvcsout", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 24, "position": "Midfielder", "positionSide": "Left", "formationPlace": "11", "stat": [{"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "goalsConceded", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "5q231tqko859i311srdsm4zx1", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "10", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "cg1j982govx2perwr3rv33t1x", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "8", "stat": [{"type": "minsPlayed", "value": "90"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "1eut2mrcgcr2rji48n2yinrh1", "firstName": "Bengali-<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 55, "position": "Midfielder", "positionSide": "Right", "formationPlace": "7", "stat": [{"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "goalsConceded", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "6g44j80ofx3xij3ltql12bhsl", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "Simic", "matchName": "<PERSON><PERSON>", "shirtNumber": 22, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "gameStarted", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "minsPlayed", "value": "45"}, {"type": "goalsConcededIbox", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "7jpfgxy3b4ma6sp5fibpn7c9h", "firstName": "Em<PERSON>", "lastName": "Başsan", "shortFirstName": "Em<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "2frjnz2wa860fae3pl9rl0n4k", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Başyiğit", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Basyigit", "matchName": "<PERSON><PERSON>", "shirtNumber": 53, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "2wb6xkgzfeoojp50dbv594esp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 46, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "totalSubOn", "value": "1"}, {"type": "minsPlayed", "value": "45"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "21meptskq7d8atuzim2i2ixn9", "firstName": "<PERSON><PERSON>", "lastName": "Erdal", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Erdal", "matchName": "<PERSON><PERSON>", "shirtNumber": 58, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4phywxtdq9cojmyhn1yipbfv8", "firstName": "<PERSON>rda", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>rda", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 74, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "35hkwhj0m65aqyalfrjweyml1", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "minsPlayed", "value": "6"}, {"type": "totalSubOn", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "e0ec2upmben78u7zmc7v57npx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>ş", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "az4vys76d0diwqjw8hy7908o9", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "Poungouras", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "Poungouras", "matchName": "<PERSON><PERSON>", "shirtNumber": 44, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "1l12v8lx25ipy5obp0wcfkr11", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 10, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "e36jmfhholbbk1tq2nff0bsh5", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 90, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}], "teamOfficial": [{"id": "c7hxj9vcr60lxskxh0pay96tx", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Çalımbay", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Çalimbay", "type": "manager"}], "stat": [{"fh": "2", "sh": "0", "type": "totalYellowCard", "value": "2"}, {"fh": "0", "sh": "2", "type": "subsMade", "value": "2"}, {"fh": "1", "sh": "0", "type": "goalsConceded", "value": "1"}, {"fh": "1", "sh": "0", "type": "attemptsConcededIbox", "value": "1"}, {"fh": "1", "sh": "0", "type": "goalsConcededIbox", "value": "1"}, {"type": "formationUsed", "value": "4141"}], "kit": {"id": "29677", "colour1": "#000000", "type": "third"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "7o7er9hig09et4x0g3brutwph", "type": "Main", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>"}, {"id": "50p0hxw7byqs577kfz4rtwiy1", "type": "Assistant referee 1", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Us<PERSON>", "shortFirstName": "Anil", "shortLastName": "Us<PERSON>"}, {"id": "3ztyapwjcxtt0hdzexw989m8l", "type": "Assistant referee 2", "firstName": "Bahtiyar", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "Bahtiyar", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "bvgnfskz7od1hzif6qq0l7cyy", "type": "Fourth official", "firstName": "Gürcan", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>"}, {"id": "2p5xmt8w8fwpnjffxh2tbbo5", "type": "Video Assistant Referee", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>"}, {"id": "es5cylrgf5nt1wro4g5qt9e51", "type": "Assistant VAR Official", "firstName": "<PERSON><PERSON>", "lastName": "Özütoprak", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Ozutoprak"}]}}}