{"matchInfo": {"id": "a5z57ypyrj5ikju38gntiivis", "coverageLevel": "15", "date": "2024-03-17Z", "time": "17:00:00Z", "localDate": "2024-03-17", "localTime": "18:00:00", "week": "29", "postMatch": "1", "numberOfPeriods": 2, "periodLength": 45, "lastUpdated": "2024-03-17T19:00:46Z", "description": "Roma vs Sassuolo", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "1r097lpxe0xn03ihb7wi98kao", "name": "Serie A", "knownName": "Italian Serie A", "sponsorName": "Serie A TIM", "competitionCode": "SEA", "competitionFormat": "Domestic league", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, "tournamentCalendar": {"id": "3r8v8kb4vebxrtcj5d7ofk1zo", "startDate": "2023-08-19Z", "endDate": "2024-05-26Z", "name": "2023/2024"}, "stage": {"id": "3s2gmzklgcca8q8oiygcsi87o", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2023-08-19Z", "endDate": "2024-05-26Z", "name": "Regular Season"}, "contestant": [{"id": "2tk2l9sgktwc9jhzqdd4mpdtb", "name": "Roma", "shortName": "Roma", "officialName": "AS Roma", "code": "ROM", "position": "home", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}, {"id": "6tuibxq39fdryu8ou06wcm0q3", "name": "Sassuolo", "shortName": "Sassuolo", "officialName": "US Sassuolo Calcio", "code": "SAS", "position": "away", "country": {"id": "25f2cmb2r8mk5rj92tzer6kvv", "name": "Italy"}}], "venue": {"id": "aenrk3ub34bddieyjn1calqu7", "neutral": "no", "longName": "Stadio Olimpico", "shortName": "Stadio Olimpico"}}, "liveData": {"matchDetails": {"periodId": 14, "matchStatus": "Played", "winner": "home", "matchLengthMin": 97, "matchLengthSec": 29, "period": [{"id": 1, "start": "2024-03-17T17:00:16Z", "end": "2024-03-17T17:48:52Z", "lengthMin": 48, "lengthSec": 36, "announcedInjuryTime": 120}, {"id": 2, "start": "2024-03-17T18:05:22Z", "end": "2024-03-17T18:54:15Z", "lengthMin": 48, "lengthSec": 53, "announcedInjuryTime": 240}], "scores": {"ht": {"home": 0, "away": 0}, "ft": {"home": 1, "away": 0}, "total": {"home": 1, "away": 0}}}, "goal": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 50, "timeMinSec": "49:53", "lastUpdated": "2024-03-17T18:11:23Z", "timestamp": "2024-03-17T18:10:16Z", "type": "G", "scorerId": "57bin6he6kbdo2bs4i78z2k0l", "scorerName": "<PERSON><PERSON>", "assistPlayerId": "apdtqouzo9b02ozdxk8mx4zbp", "assistPlayerName": "<PERSON><PERSON>", "optaEventId": "2661255153", "homeScore": 1, "awayScore": 0}], "card": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 59, "timeMinSec": "58:37", "lastUpdated": "2024-03-17T18:19:12Z", "timestamp": "2024-03-17T18:18:59Z", "type": "YC", "playerId": "57bin6he6kbdo2bs4i78z2k0l", "playerName": "<PERSON><PERSON>", "optaEventId": "2661263223", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 89, "timeMinSec": "88:23", "lastUpdated": "2024-03-17T18:49:28Z", "timestamp": "2024-03-17T18:48:46Z", "type": "YC", "playerId": "59pu8nnghyxvvt8r3zyndpfpx", "playerName": "<PERSON><PERSON>", "optaEventId": "2661283991", "cardReason": "F<PERSON>l"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 30, "timeMinSec": "30:46", "lastUpdated": "2024-03-17T18:50:27Z", "timestamp": "2024-03-17T18:50:09Z", "type": "YC", "playerId": "cfmha2icj5q0mp8ecajlbxb84", "playerName": "<PERSON>", "optaEventId": "2661284607", "cardReason": "F<PERSON>l"}], "substitute": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 1, "timeMin": 37, "timeMinSec": "36:24", "lastUpdated": "2024-03-17T17:36:57Z", "timestamp": "2024-03-17T17:36:41Z", "playerOnId": "9uthj64y9f77y63tq7did539x", "playerOnName": "<PERSON><PERSON><PERSON>", "playerOffId": "9ka7y81b5kst146mhq7mom5ed", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 70, "timeMinSec": "69:06", "lastUpdated": "2024-03-17T18:29:32Z", "timestamp": "2024-03-17T18:29:29Z", "playerOnId": "1pkymw7biivk760rxhubin1ed", "playerOnName": "<PERSON><PERSON>", "playerOffId": "8lgsqmwavspz4fv7t11rb6j9", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 70, "timeMinSec": "69:07", "lastUpdated": "2024-03-17T18:29:37Z", "timestamp": "2024-03-17T18:29:30Z", "playerOnId": "bk1gdyxtfp4drwb0yotwerbsa", "playerOnName": "<PERSON><PERSON>", "playerOffId": "412hlm2edd05jd1ad597brcvd", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "periodId": 2, "timeMin": 72, "timeMinSec": "71:51", "lastUpdated": "2024-03-17T18:32:24Z", "timestamp": "2024-03-17T18:32:14Z", "playerOnId": "bh9jia8qgkhpm5xn82bx0hhck", "playerOnName": "<PERSON><PERSON>", "playerOffId": "9k1ytj3z42ggp14vkn39hiymt", "playerOffName": "<PERSON>", "subReason": "Tactical"}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "periodId": 2, "timeMin": 73, "timeMinSec": "72:09", "lastUpdated": "2024-03-17T18:33:05Z", "timestamp": "2024-03-17T18:32:32Z", "playerOnId": "7qluty59wsg0ak1p5301o8pux", "playerOnName": "<PERSON><PERSON>", "playerOffId": "f2p5457ilcfrtzhe343whhkut", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "periodId": 2, "timeMin": 79, "timeMinSec": "78:39", "lastUpdated": "2024-03-17T18:39:18Z", "timestamp": "2024-03-17T18:39:01Z", "playerOnId": "6w3v6ck12wmaff8owswnhphm2", "playerOnName": "<PERSON><PERSON>", "playerOffId": "153ljvbov0n176vm9fdaizoix", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "periodId": 2, "timeMin": 79, "timeMinSec": "78:43", "lastUpdated": "2024-03-17T18:39:18Z", "timestamp": "2024-03-17T18:39:06Z", "playerOnId": "3sehm4zmy7141wm4ecfqqoxd5", "playerOnName": "<PERSON><PERSON>", "playerOffId": "5r7q0eg6pmoq0safxa9tnm5l", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 86, "timeMinSec": "85:09", "lastUpdated": "2024-03-17T18:45:53Z", "timestamp": "2024-03-17T18:45:31Z", "playerOnId": "59pu8nnghyxvvt8r3zyndpfpx", "playerOnName": "<PERSON><PERSON>", "playerOffId": "8qgbxff7xlg0zjtyees7ljljp", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "periodId": 2, "timeMin": 86, "timeMinSec": "85:20", "lastUpdated": "2024-03-17T18:45:53Z", "timestamp": "2024-03-17T18:45:43Z", "playerOnId": "cxxxfvmrslyjyi9wz9m8kwne2", "playerOnName": "<PERSON><PERSON>", "playerOffId": "2e0ipswbkv4vf7lav6ee1j3oq", "playerOffName": "<PERSON><PERSON>", "subReason": "Tactical"}, {"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "periodId": 2, "timeMin": 86, "timeMinSec": "85:23", "lastUpdated": "2024-03-17T18:45:43Z", "timestamp": "2024-03-17T18:45:46Z", "playerOnId": "d2hnxi1yi5rgqj1tvxh1813x0", "playerOnName": "<PERSON><PERSON>", "playerOffId": "a1iz91ed6hus61yhhk2r0vkd1", "playerOffName": "S. <PERSON>", "subReason": "Tactical"}], "lineUp": [{"contestantId": "2tk2l9sgktwc9jhzqdd4mpdtb", "formationUsed": "433", "player": [{"playerId": "el58uxf7oaqu95yx1vbgmr7it", "firstName": "Mile", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Mile", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 99, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "totalPass", "value": "39"}, {"type": "possLostAll", "value": "2"}, {"type": "accurateKeeperThrows", "value": "4"}, {"type": "openPlayPass", "value": "35"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "savedObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "ballRecovery", "value": "8"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "diveSave", "value": "1"}, {"type": "saves", "value": "3"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "totalLongBalls", "value": "6"}, {"type": "touches", "value": "47"}, {"type": "savedIbox", "value": "1"}, {"type": "totalBackZonePass", "value": "34"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "4"}, {"type": "accurateLaunches", "value": "3"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "accuratePass", "value": "37"}, {"type": "goalKicks", "value": "4"}, {"type": "divingSave", "value": "1"}, {"type": "totalLaunches", "value": "5"}, {"type": "successfulOpenPlayPass", "value": "33"}, {"type": "accurateBackZonePass", "value": "34"}, {"type": "possLostCtrl", "value": "2"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accurateGoalKicks", "value": "4"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "5"}, {"type": "rightsidePass", "value": "13"}, {"type": "fwdPass", "value": "18"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "9ka7y81b5kst146mhq7mom5ed", "firstName": "Leonardo", "lastName": "Spinazzola", "shortFirstName": "Leonardo", "shortLastName": "Spinazzola", "matchName": "<PERSON><PERSON>", "shirtNumber": 37, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "passesRight", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "minsPlayed", "value": "37"}, {"type": "openPlayPass", "value": "23"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "possLostAll", "value": "5"}, {"type": "totalPass", "value": "23"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalCross", "value": "4"}, {"type": "totalSubOff", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "backwardPass", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "accurateThrows", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalCrossNocorner", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "possLostCtrl", "value": "5"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "penAreaEntries", "value": "4"}, {"type": "accuratePass", "value": "20"}, {"type": "totalLongBalls", "value": "2"}, {"type": "touches", "value": "33"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "crosses18yard", "value": "3"}, {"type": "accurateCross", "value": "2"}, {"type": "totalThrows", "value": "2"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "fwdPass", "value": "6"}, {"type": "rightsidePass", "value": "12"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "9ao7l82r1xd2rjnvv5u6plcr9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>nte <PERSON>", "shortFirstName": "Diego", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON>", "shirtNumber": 14, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "stat": [{"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "attIboxTarget", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "aerialLost", "value": "3"}, {"type": "totalContest", "value": "1"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "7"}, {"type": "backwardPass", "value": "6"}, {"type": "ballRecovery", "value": "3"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "cleanSheet", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "headPass", "value": "3"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "effectiveClearance", "value": "5"}, {"type": "passesRight", "value": "4"}, {"type": "attHdTarget", "value": "1"}, {"type": "aerialWon", "value": "2"}, {"type": "passesLeft", "value": "8"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "21"}, {"type": "openPlayPass", "value": "66"}, {"type": "minsPlayed", "value": "90"}, {"type": "totalPass", "value": "71"}, {"type": "possLostAll", "value": "7"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "6"}, {"type": "touchesInOppBox", "value": "3"}, {"type": "fouls", "value": "3"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "attemptsIbox", "value": "2"}, {"type": "attSvHighCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "19"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "rightsidePass", "value": "28"}, {"type": "fwdPass", "value": "16"}, {"type": "interceptionWon", "value": "2"}, {"type": "accurateFwdZonePass", "value": "15"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "attIboxBlocked", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attBxLeft", "value": "1"}, {"type": "accurateGoalKicks", "value": "3"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "attHdTotal", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "60"}, {"type": "accurateBackZonePass", "value": "50"}, {"type": "possLostCtrl", "value": "7"}, {"type": "interception", "value": "2"}, {"type": "goalKicks", "value": "3"}, {"type": "totalLaunches", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalClearance", "value": "5"}, {"type": "accuratePass", "value": "65"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "totalBackZonePass", "value": "52"}, {"type": "totalLongBalls", "value": "3"}, {"type": "touches", "value": "87"}, {"type": "interceptionsInBox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "6fttn5mk6n1lstqt0dl5xzn4l", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "successfulOpenPlayPass", "value": "77"}, {"type": "accurateBackZonePass", "value": "52"}, {"type": "possLostCtrl", "value": "5"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "goalKicks", "value": "1"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "accurateChippedPass", "value": "4"}, {"type": "totalBackZonePass", "value": "53"}, {"type": "totalLongBalls", "value": "5"}, {"type": "touches", "value": "88"}, {"type": "accurateLongBalls", "value": "3"}, {"type": "totalClearance", "value": "2"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accuratePass", "value": "78"}, {"type": "lostCorners", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "fouls", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "crosses18yard", "value": "1"}, {"type": "totalChippedPass", "value": "6"}, {"type": "totalFinalThirdPasses", "value": "14"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "duelLost", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "finalThirdEntries", "value": "11"}, {"type": "fwdPass", "value": "19"}, {"type": "rightsidePass", "value": "14"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "30"}, {"type": "accurateFwdZonePass", "value": "26"}, {"type": "aerialWon", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "4"}, {"type": "passesRight", "value": "10"}, {"type": "effectiveClearance", "value": "2"}, {"type": "possLostAll", "value": "5"}, {"type": "totalPass", "value": "82"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "44"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "81"}, {"type": "aerialLost", "value": "2"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "longPassOwnToOppSuccess", "value": "10"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "12"}, {"type": "totalCross", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "3"}, {"type": "ballRecovery", "value": "5"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "8lgsqmwavspz4fv7t11rb6j9", "firstName": "<PERSON>", "lastName": "Karsdorp", "shortFirstName": "<PERSON>", "shortLastName": "Karsdorp", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "touches", "value": "45"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "accuratePass", "value": "26"}, {"type": "penAreaEntries", "value": "5"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "4"}, {"type": "possLostCtrl", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "26"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "accurateThrows", "value": "5"}, {"type": "totalTackle", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "totalFwdZonePass", "value": "20"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "fwdPass", "value": "4"}, {"type": "challengeLost", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "totalThrows", "value": "7"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "crosses18yard", "value": "4"}, {"type": "fouls", "value": "1"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "2"}, {"type": "totalPass", "value": "30"}, {"type": "possLostAll", "value": "12"}, {"type": "openPlayPass", "value": "30"}, {"type": "minsPlayed", "value": "70"}, {"type": "leftside<PERSON><PERSON>", "value": "17"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesRight", "value": "6"}, {"type": "gameStarted", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "backwardPass", "value": "9"}, {"type": "turnover", "value": "1"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "totalCross", "value": "4"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "57bin6he6kbdo2bs4i78z2k0l", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "captain": "yes", "stat": [{"type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "backwardPass", "value": "17"}, {"type": "totalContest", "value": "7"}, {"type": "turnover", "value": "2"}, {"type": "duel<PERSON>on", "value": "8"}, {"type": "totalCross", "value": "7"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "attOboxGoal", "value": "1"}, {"type": "attRfGoal", "value": "1"}, {"type": "fouledFinalThird", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "cleanSheet", "value": "1"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "3"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "ballRecovery", "value": "7"}, {"type": "goalsOpenplay", "value": "1"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "passesLeft", "value": "12"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "effectiveClearance", "value": "2"}, {"type": "passesRight", "value": "2"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "accurateFreekickCross", "value": "1"}, {"type": "totalPass", "value": "57"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "possLostAll", "value": "11"}, {"type": "attemptsObox", "value": "1"}, {"type": "accurateCornersIntobox", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "openPlayPass", "value": "55"}, {"type": "minsPlayed", "value": "90"}, {"type": "attOpenplay", "value": "1"}, {"type": "accurateCross", "value": "5"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "crosses18yard", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "accurateLayoffs", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "20"}, {"type": "totalChippedPass", "value": "5"}, {"type": "goals", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "5"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "attGoalLowRight", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "attObxCentre", "value": "1"}, {"type": "rightsidePass", "value": "20"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "42"}, {"type": "fwdPass", "value": "11"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "accurateFwdZonePass", "value": "37"}, {"type": "interceptionWon", "value": "1"}, {"type": "possLostCtrl", "value": "11"}, {"type": "successfulOpenPlayPass", "value": "51"}, {"type": "accurateBackZonePass", "value": "21"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "totalCrossNocorner", "value": "3"}, {"type": "totalAttAssist", "value": "3"}, {"type": "yellowCard", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "freekick<PERSON><PERSON>", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "attAssistSetplay", "value": "2"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalBackZonePass", "value": "22"}, {"type": "totalCornersIntobox", "value": "4"}, {"type": "cornerTaken", "value": "4"}, {"type": "touches", "value": "88"}, {"type": "totalLongBalls", "value": "2"}, {"type": "accuratePass", "value": "53"}, {"type": "penAreaEntries", "value": "14"}, {"type": "totalClearance", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"type": "winningGoal", "value": "1"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "apdtqouzo9b02ozdxk8mx4zbp", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 16, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "cleanSheet", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "backwardPass", "value": "9"}, {"type": "longPassOwnToOppSuccess", "value": "15"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "goalAssistOpenplay", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "19"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "possLostAll", "value": "8"}, {"type": "totalPass", "value": "105"}, {"type": "leftside<PERSON><PERSON>", "value": "21"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "102"}, {"type": "effectiveBlockedCross", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "passesLeft", "value": "13"}, {"type": "passesRight", "value": "11"}, {"type": "effectiveClearance", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "fwdPass", "value": "37"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "55"}, {"type": "rightsidePass", "value": "38"}, {"type": "finalThirdEntries", "value": "22"}, {"type": "accurateFwdZonePass", "value": "49"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "fouls", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "26"}, {"type": "challengeLost", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "totalChippedPass", "value": "10"}, {"type": "duelLost", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "totalBackZonePass", "value": "50"}, {"type": "goalAssist", "value": "1"}, {"type": "touches", "value": "110"}, {"type": "totalLongBalls", "value": "12"}, {"type": "accuratePass", "value": "97"}, {"type": "totalClearance", "value": "1"}, {"type": "accurateLongBalls", "value": "7"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "21"}, {"type": "possLostCtrl", "value": "8"}, {"type": "accurateBackZonePass", "value": "48"}, {"type": "successfulOpenPlayPass", "value": "95"}, {"type": "totalLaunches", "value": "2"}, {"type": "totalAttAssist", "value": "2"}, {"type": "accurateChippedPass", "value": "6"}, {"type": "totalTackle", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "1ftejgsl4hho40cceqne1hcyd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "totalFlickOn", "value": "1"}, {"type": "attObxCentre", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accurateFwdZonePass", "value": "30"}, {"type": "totalThroughBall", "value": "1"}, {"type": "fwdPass", "value": "20"}, {"type": "rightsidePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "40"}, {"type": "finalThirdEntries", "value": "14"}, {"type": "totalFinalThirdPasses", "value": "24"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "totalChippedPass", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "attOpenplay", "value": "2"}, {"type": "fouls", "value": "2"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "accurateFlickOn", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "touches", "value": "74"}, {"type": "totalLongBalls", "value": "2"}, {"type": "totalBackZonePass", "value": "25"}, {"type": "attOboxBlocked", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "16"}, {"type": "lostCorners", "value": "1"}, {"type": "blockedScoringAtt", "value": "2"}, {"type": "accuratePass", "value": "54"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possLostCtrl", "value": "12"}, {"type": "accurateBackZonePass", "value": "24"}, {"type": "successfulOpenPlayPass", "value": "53"}, {"type": "totalTackle", "value": "2"}, {"type": "totalAttAssist", "value": "1"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "headPass", "value": "4"}, {"type": "gameStarted", "value": "1"}, {"type": "cleanSheet", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "totalLayoffs", "value": "2"}, {"type": "backwardPass", "value": "9"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "totalContest", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "possLostAll", "value": "12"}, {"type": "totalPass", "value": "64"}, {"type": "attemptsObox", "value": "2"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "63"}, {"type": "leftside<PERSON><PERSON>", "value": "22"}, {"type": "attemptedTackleFoul", "value": "2"}, {"type": "attLfTotal", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "3"}, {"type": "aerialWon", "value": "2"}, {"type": "passesRight", "value": "18"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "accurateThroughBall", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "a1iz91ed6hus61yhhk2r0vkd1", "firstName": "<PERSON>", "lastName": "El <PERSON>wy", "shortFirstName": "<PERSON>", "shortLastName": "El <PERSON>wy", "matchName": "S. <PERSON>", "shirtNumber": 92, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "totalThrows", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "13"}, {"type": "totalChippedPass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "4"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "rightsidePass", "value": "13"}, {"type": "totalFwdZonePass", "value": "17"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "fwdPass", "value": "8"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "accurateFwdZonePass", "value": "13"}, {"type": "possLostCtrl", "value": "8"}, {"type": "successfulOpenPlayPass", "value": "25"}, {"type": "accurateBackZonePass", "value": "13"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "totalBackZonePass", "value": "13"}, {"type": "touches", "value": "40"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accuratePass", "value": "25"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "9"}, {"type": "aerialLost", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "totalContest", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalOffside", "value": "1"}, {"type": "wasFouled", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "ballRecovery", "value": "3"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "passesLeft", "value": "10"}, {"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "totalPass", "value": "29"}, {"type": "possLostAll", "value": "8"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "29"}, {"type": "minsPlayed", "value": "86"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "8qgbxff7xlg0zjtyees7ljljp", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 90, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "possLostAll", "value": "9"}, {"type": "totalPass", "value": "10"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "minsPlayed", "value": "86"}, {"type": "openPlayPass", "value": "9"}, {"type": "aerialWon", "value": "5"}, {"type": "attLfTotal", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "totalScoringAtt", "value": "3"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "wasFouled", "value": "2"}, {"type": "headPass", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "attMissLeft", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "duel<PERSON>on", "value": "7"}, {"type": "attMissRight", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "turnover", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attBxCentre", "value": "3"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "attIboxMiss", "value": "3"}, {"type": "touches", "value": "23"}, {"type": "accuratePass", "value": "7"}, {"type": "totalClearance", "value": "2"}, {"type": "lostCorners", "value": "1"}, {"type": "possLostCtrl", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "attCmissRight", "value": "1"}, {"type": "attHdTotal", "value": "2"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "rightsidePass", "value": "2"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "totalFwdZonePass", "value": "6"}, {"type": "attemptsIbox", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "attOpenplay", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "touchesInOppBox", "value": "7"}, {"type": "duelLost", "value": "3"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "attHdMiss", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "412hlm2edd05jd1ad597brcvd", "firstName": "Houssem-Eddine", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 22, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "duelLost", "value": "5"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "challengeLost", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "15"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "18"}, {"type": "rightsidePass", "value": "6"}, {"type": "fwdPass", "value": "2"}, {"type": "accurateFwdZonePass", "value": "15"}, {"type": "attemptsConcededObox", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalTackle", "value": "3"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "22"}, {"type": "possLostCtrl", "value": "8"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalClearance", "value": "1"}, {"type": "accuratePass", "value": "22"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "touches", "value": "34"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "totalOffside", "value": "2"}, {"type": "turnover", "value": "2"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "backwardPass", "value": "8"}, {"type": "totalLayoffs", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "dispossessed", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "8"}, {"type": "aerialWon", "value": "1"}, {"type": "passesLeft", "value": "2"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "openPlayPass", "value": "26"}, {"type": "minsPlayed", "value": "70"}, {"type": "totalPass", "value": "26"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "possLostAll", "value": "8"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "59pu8nnghyxvvt8r3zyndpfpx", "firstName": "Sarda<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "Sarda<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 17, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "passesRight", "value": "2"}, {"type": "totalPass", "value": "4"}, {"type": "possLostAll", "value": "1"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "openPlayPass", "value": "4"}, {"type": "minsPlayed", "value": "4"}, {"type": "backwardPass", "value": "2"}, {"type": "totalContest", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "wasFouled", "value": "2"}, {"type": "totalLayoffs", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "possLostCtrl", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "4"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "touches", "value": "8"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "fouls", "value": "1"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "rightsidePass", "value": "2"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "interceptionWon", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "363nsa82dww242zeo09wpcslx", "firstName": "<PERSON>", "lastName": "Bakumo<PERSON>Abraham", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "bk1gdyxtfp4drwb0yotwerbsa", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 35, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "totalClearance", "value": "1"}, {"type": "attFastbreak", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "touches", "value": "25"}, {"type": "ontargetScoringAtt", "value": "2"}, {"type": "totalTackle", "value": "2"}, {"type": "possLostCtrl", "value": "2"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "attLfTarget", "value": "1"}, {"type": "fwdPass", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "attemptsIbox", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "attBxRight", "value": "2"}, {"type": "shotFastbreak", "value": "1"}, {"type": "touchesInOppBox", "value": "4"}, {"type": "duelLost", "value": "3"}, {"type": "attRfTarget", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "attSvLowLeft", "value": "1"}, {"type": "minsPlayed", "value": "20"}, {"type": "openPlayPass", "value": "15"}, {"type": "attSvLowCentre", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "totalPass", "value": "15"}, {"type": "passesRight", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "attLfTotal", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "attIboxTarget", "value": "2"}, {"type": "aerialLost", "value": "1"}, {"type": "backwardPass", "value": "3"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "totalFastbreak", "value": "1"}, {"type": "totalContest", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "b0b0xalrak7k5pnfnum53umtw", "firstName": "<PERSON>", "lastName": "Boer", "shortFirstName": "<PERSON>", "shortLastName": "Boer", "matchName": "<PERSON><PERSON>", "shirtNumber": 63, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4cu8rbv55v6f6jiq50c4xatqs", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Bove", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Bove", "matchName": "<PERSON><PERSON>", "shirtNumber": 52, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9uthj64y9f77y63tq7did539x", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 69, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "fwdPass", "value": "15"}, {"type": "rightsidePass", "value": "7"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "accurateFwdZonePass", "value": "16"}, {"type": "attemptsConcededObox", "value": "2"}, {"type": "wonCorners", "value": "2"}, {"type": "duelLost", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "crosses18yard", "value": "2"}, {"type": "totalThrows", "value": "6"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "challengeLost", "value": "1"}, {"type": "totalClearance", "value": "2"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "28"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "lostCorners", "value": "2"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "totalLongBalls", "value": "2"}, {"type": "touches", "value": "49"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "accurateThrows", "value": "4"}, {"type": "accurateBackZonePass", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "27"}, {"type": "possLostCtrl", "value": "11"}, {"type": "totalLaunches", "value": "2"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "ballRecovery", "value": "4"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "headPass", "value": "4"}, {"type": "longPassOwnToOpp", "value": "5"}, {"type": "possWonDef3rd", "value": "4"}, {"type": "totalCross", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalContest", "value": "2"}, {"type": "backwardPass", "value": "8"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "3"}, {"type": "minsPlayed", "value": "53"}, {"type": "openPlayPass", "value": "32"}, {"type": "shieldBallOop", "value": "1"}, {"type": "possLostAll", "value": "11"}, {"type": "totalPass", "value": "33"}, {"type": "effectiveClearance", "value": "2"}, {"type": "effectiveBlockedCross", "value": "2"}, {"type": "passesLeft", "value": "15"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "d2hnxi1yi5rgqj1tvxh1813x0", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 3, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "totalPass", "value": "7"}, {"type": "possLostAll", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "openPlayPass", "value": "7"}, {"type": "minsPlayed", "value": "4"}, {"type": "backwardPass", "value": "2"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "accurateBackZonePass", "value": "4"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalLaunches", "value": "2"}, {"type": "totalBackZonePass", "value": "4"}, {"type": "touches", "value": "8"}, {"type": "totalLongBalls", "value": "2"}, {"type": "accuratePass", "value": "5"}, {"type": "totalSubOn", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "1"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "fwdPass", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "efzokrggje7s9wc6hg75yc6ax", "firstName": "Obite Evan", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "5kktohikuoy0a1oagcgps8mxg", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 61, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "du4ciqofpkn3nei5q3huwlnje", "firstName": "Nicola", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Nicola", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 59, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "7gu0t0g8nej405k918fwsf8id", "firstName": "<PERSON><PERSON>", "lastName": "dos Santos Patrício", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "dos Santos Patrício", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cfmha2icj5q0mp8ecajlbxb84", "firstName": "<PERSON>", "lastName": "e Costa <PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "e Costa <PERSON>", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 67, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "1pkymw7biivk760rxhubin1ed", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Çelik", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Çelik", "matchName": "<PERSON><PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "totalLongBalls", "value": "1"}, {"type": "touches", "value": "22"}, {"type": "totalBackZonePass", "value": "6"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "16"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "16"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "possLostCtrl", "value": "2"}, {"type": "totalTackle", "value": "2"}, {"type": "accurateChippedPass", "value": "3"}, {"type": "totalAttAssist", "value": "2"}, {"type": "accurateFwdZonePass", "value": "10"}, {"type": "fwdPass", "value": "6"}, {"type": "attemptsConcededIbox", "value": "2"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "totalChippedPass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "5"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonMid3rd", "value": "2"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "possLostAll", "value": "2"}, {"type": "totalPass", "value": "16"}, {"type": "minsPlayed", "value": "20"}, {"type": "openPlayPass", "value": "16"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "passesLeft", "value": "1"}, {"type": "passesRight", "value": "5"}, {"type": "effectiveClearance", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "turnover", "value": "1"}, {"type": "backwardPass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "ep6b55j8qlddyt4asgptcijth", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON>", "type": "manager"}, "stat": [{"fh": "1", "sh": "2", "type": "totalOffside", "value": "3"}, {"fh": "2", "sh": "2", "type": "attBxCentre", "value": "4"}, {"fh": "0", "sh": "1", "type": "attOboxGoal", "value": "1"}, {"fh": "0", "sh": "1", "type": "attRfGoal", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalAssistOpenplay", "value": "1"}, {"fh": "15", "sh": "6", "type": "totalCross", "value": "21"}, {"fh": "12", "sh": "12", "type": "possWonDef3rd", "value": "24"}, {"fh": "27", "sh": "32", "type": "longPassOwnToOpp", "value": "59"}, {"fh": "0", "sh": "3", "type": "attIboxTarget", "value": "3"}, {"fh": "0", "sh": "2", "type": "attRfTotal", "value": "2"}, {"fh": "2", "sh": "5", "type": "attAssistOpenplay", "value": "7"}, {"fh": "2", "sh": "2", "type": "<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "23", "sh": "20", "type": "longPassOwnToOppSuccess", "value": "43"}, {"fh": "45", "sh": "53", "type": "backwardPass", "value": "98"}, {"fh": "5", "sh": "15", "type": "totalContest", "value": "20"}, {"fh": "0", "sh": "1", "type": "totalFastbreak", "value": "1"}, {"fh": "21", "sh": "25", "type": "duel<PERSON>on", "value": "46"}, {"fh": "1", "sh": "0", "type": "attMissRight", "value": "1"}, {"fh": "68", "sh": "58.3", "type": "possessionPercentage", "value": "63.3"}, {"fh": "1", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON>Created", "value": "1"}, {"fh": "6", "sh": "3", "type": "aerialLost", "value": "9"}, {"fh": "0", "sh": "3", "type": "saves", "value": "3"}, {"fh": "2", "sh": "0", "type": "dispossessed", "value": "2"}, {"fh": "1", "sh": "6", "type": "ontargetAttAssist", "value": "7"}, {"fh": "1", "sh": "4", "type": "subsMade", "value": "5"}, {"fh": "24", "sh": "26", "type": "ballRecovery", "value": "50"}, {"fh": "1", "sh": "1", "type": "attMissLeft", "value": "2"}, {"fh": "6", "sh": "6", "type": "totalLayoffs", "value": "12"}, {"fh": "2", "sh": "1", "type": "offtargetAttAssist", "value": "3"}, {"fh": "2", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "1", "sh": "0", "type": "fouledFinalThird", "value": "1"}, {"fh": "3", "sh": "5", "type": "effectiveHeadClearance", "value": "8"}, {"type": "cleanSheet", "value": "1"}, {"fh": "0", "sh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "3", "sh": "16", "type": "effectiveClearance", "value": "19"}, {"fh": "43", "sh": "28", "type": "passesRight", "value": "71"}, {"fh": "0", "sh": "1", "type": "accurateThroughBall", "value": "1"}, {"fh": "3", "sh": "7", "type": "totalScoringAtt", "value": "10"}, {"fh": "2", "sh": "1", "type": "crosses18yardplus", "value": "3"}, {"fh": "32", "sh": "40", "type": "passesLeft", "value": "72"}, {"fh": "4", "sh": "5", "type": "blocked<PERSON><PERSON>", "value": "9"}, {"fh": "0", "sh": "4", "type": "attLfTotal", "value": "4"}, {"fh": "0", "sh": "1", "type": "goalsOpenplay", "value": "1"}, {"fh": "0", "sh": "3", "type": "effectiveBlockedCross", "value": "3"}, {"fh": "8", "sh": "4", "type": "aerialWon", "value": "12"}, {"fh": "0", "sh": "1", "type": "shieldBallOop", "value": "1"}, {"fh": "326", "sh": "267", "type": "openPlayPass", "value": "593"}, {"fh": "0", "sh": "1", "type": "attSvLowLeft", "value": "1"}, {"fh": "112", "sh": "64", "type": "leftside<PERSON><PERSON>", "value": "176"}, {"fh": "1", "sh": "2", "type": "accurateCornersIntobox", "value": "3"}, {"fh": "0", "sh": "1", "type": "savedObox", "value": "1"}, {"fh": "5", "sh": "4", "type": "attemptedTackleFoul", "value": "9"}, {"fh": "1", "sh": "0", "type": "accurateFreekickCross", "value": "1"}, {"fh": "334", "sh": "277", "type": "totalPass", "value": "611"}, {"fh": "51", "sh": "54", "type": "possLostAll", "value": "105"}, {"fh": "2", "sh": "0", "type": "possWonAtt3rd", "value": "2"}, {"fh": "2", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "0", "sh": "3", "type": "attemptsObox", "value": "3"}, {"fh": "0", "sh": "1", "type": "attSvLowCentre", "value": "1"}, {"fh": "2", "sh": "2", "type": "accurateKeeperThrows", "value": "4"}, {"fh": "3", "sh": "8", "type": "unsuccessfulTouch", "value": "11"}, {"fh": "2", "sh": "0", "type": "attHdMiss", "value": "2"}, {"fh": "0", "sh": "1", "type": "attGoalLowRight", "value": "1"}, {"fh": "0", "sh": "1", "type": "accurateFlickOn", "value": "1"}, {"fh": "10", "sh": "14", "type": "touchesInOppBox", "value": "24"}, {"fh": "8", "sh": "9", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "17"}, {"fh": "0", "sh": "1", "type": "attRfTarget", "value": "1"}, {"fh": "0", "sh": "1", "type": "goals", "value": "1"}, {"fh": "18", "sh": "23", "type": "duelLost", "value": "41"}, {"fh": "5", "sh": "5", "type": "accurateLayoffs", "value": "10"}, {"fh": "1", "sh": "6", "type": "challengeLost", "value": "7"}, {"fh": "4", "sh": "5", "type": "successfulPutThrough", "value": "9"}, {"fh": "84", "sh": "64", "type": "totalFinalThirdPasses", "value": "148"}, {"fh": "19", "sh": "15", "type": "totalChippedPass", "value": "34"}, {"fh": "1", "sh": "6", "type": "attOpenplay", "value": "7"}, {"fh": "7", "sh": "9", "type": "totalThrows", "value": "16"}, {"fh": "6", "sh": "2", "type": "accurateCross", "value": "8"}, {"fh": "10", "sh": "3", "type": "crosses18yard", "value": "13"}, {"fh": "8", "sh": "11", "type": "possWonMid3rd", "value": "19"}, {"fh": "134", "sh": "119", "type": "accurateFwdZonePass", "value": "253"}, {"fh": "0", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "0", "sh": "1", "type": "shotFastbreak", "value": "1"}, {"fh": "2", "sh": "2", "type": "interceptionWon", "value": "4"}, {"fh": "0", "sh": "2", "type": "attBxRight", "value": "2"}, {"fh": "0", "sh": "1", "type": "totalThroughBall", "value": "1"}, {"fh": "1", "sh": "3", "type": "attemptsConcededIbox", "value": "4"}, {"fh": "168", "sh": "143", "type": "totalFwdZonePass", "value": "311"}, {"fh": "102", "sh": "67", "type": "rightsidePass", "value": "169"}, {"fh": "7", "sh": "4", "type": "fkFoulLost", "value": "11"}, {"fh": "75", "sh": "93", "type": "fwdPass", "value": "168"}, {"fh": "0", "sh": "2", "type": "totalYellowCard", "value": "2"}, {"fh": "5", "sh": "0", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "39", "sh": "30", "type": "finalThirdEntries", "value": "69"}, {"fh": "2", "sh": "5", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"fh": "3", "sh": "4", "type": "attemptsIbox", "value": "7"}, {"fh": "0", "sh": "1", "type": "attSvHighCentre", "value": "1"}, {"fh": "0", "sh": "1", "type": "totalFlickOn", "value": "1"}, {"fh": "0", "sh": "3", "type": "attObxCentre", "value": "3"}, {"fh": "2", "sh": "2", "type": "wonCorners", "value": "4"}, {"fh": "1", "sh": "0", "type": "attBxLeft", "value": "1"}, {"fh": "4", "sh": "4", "type": "accurateGoalKicks", "value": "8"}, {"fh": "1", "sh": "0", "type": "bigChanceMissed", "value": "1"}, {"fh": "2", "sh": "2", "type": "attemptsConcededObox", "value": "4"}, {"fh": "1", "sh": "0", "type": "attIboxBlocked", "value": "1"}, {"fh": "7", "sh": "5", "type": "accurateThrows", "value": "12"}, {"fh": "4", "sh": "5", "type": "fkFoulWon", "value": "9"}, {"fh": "3", "sh": "1", "type": "attHdTotal", "value": "4"}, {"fh": "1", "sh": "0", "type": "freekick<PERSON><PERSON>", "value": "1"}, {"fh": "7", "sh": "11", "type": "totalTackle", "value": "18"}, {"fh": "0", "sh": "4", "type": "ontargetScoringAtt", "value": "4"}, {"fh": "1", "sh": "1", "type": "attAssistSetplay", "value": "2"}, {"fh": "3", "sh": "7", "type": "totalAttAssist", "value": "10"}, {"fh": "1", "sh": "0", "type": "attCmissRight", "value": "1"}, {"fh": "13", "sh": "10", "type": "accurateChippedPass", "value": "23"}, {"fh": "5", "sh": "5", "type": "won<PERSON><PERSON><PERSON>", "value": "10"}, {"fh": "3", "sh": "5", "type": "headClea<PERSON>", "value": "8"}, {"fh": "2", "sh": "2", "type": "interception", "value": "4"}, {"fh": "13", "sh": "4", "type": "totalCrossNocorner", "value": "17"}, {"fh": "0", "sh": "1", "type": "attLfTarget", "value": "1"}, {"fh": "4", "sh": "4", "type": "goalKicks", "value": "8"}, {"fh": "3", "sh": "11", "type": "totalLaunches", "value": "14"}, {"fh": "0", "sh": "1", "type": "divingSave", "value": "1"}, {"fh": "51", "sh": "54", "type": "possLostCtrl", "value": "105"}, {"fh": "294", "sh": "241", "type": "successfulOpenPlayPass", "value": "535"}, {"fh": "173", "sh": "134", "type": "accurateBackZonePass", "value": "307"}, {"fh": "3", "sh": "4", "type": "lostCorners", "value": "7"}, {"fh": "62", "sh": "52", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "114"}, {"fh": "1", "sh": "2", "type": "blockedScoringAtt", "value": "3"}, {"fh": "301", "sh": "251", "type": "accuratePass", "value": "552"}, {"fh": "21", "sh": "13", "type": "penAreaEntries", "value": "34"}, {"fh": "0", "sh": "3", "type": "accurateLaunches", "value": "3"}, {"fh": "2", "sh": "2", "type": "keeper<PERSON><PERSON><PERSON>", "value": "4"}, {"fh": "0", "sh": "1", "type": "attFastbreak", "value": "1"}, {"fh": "12", "sh": "6", "type": "accurateLongBalls", "value": "18"}, {"fh": "3", "sh": "16", "type": "totalClearance", "value": "19"}, {"fh": "400", "sh": "381", "type": "touches", "value": "781"}, {"fh": "0", "sh": "1", "type": "savedIbox", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalAssist", "value": "1"}, {"fh": "0", "sh": "1", "type": "interceptionsInBox", "value": "1"}, {"fh": "21", "sh": "17", "type": "totalLongBalls", "value": "38"}, {"fh": "181", "sh": "140", "type": "totalBackZonePass", "value": "321"}, {"fh": "2", "sh": "2", "type": "totalCornersIntobox", "value": "4"}, {"fh": "0", "sh": "3", "type": "blocked<PERSON><PERSON>", "value": "3"}, {"fh": "2", "sh": "2", "type": "cornerTaken", "value": "4"}, {"fh": "2", "sh": "1", "type": "attIboxMiss", "value": "3"}, {"fh": "0", "sh": "2", "type": "attOboxBlocked", "value": "2"}, {"type": "formationUsed", "value": "433"}], "kit": {"id": "8380", "colour1": "#990000", "colour2": "#F0BC42", "type": "home"}}, {"contestantId": "6tuibxq39fdryu8ou06wcm0q3", "formationUsed": "433", "player": [{"playerId": "1r5gn4osgwz2b2b4sefncbhed", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 47, "position": "Goalkeeper", "positionSide": "Centre", "formationPlace": "1", "stat": [{"type": "accurateKeeperThrows", "value": "5"}, {"type": "possLostAll", "value": "9"}, {"type": "totalPass", "value": "30"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "20"}, {"type": "aerialWon", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "passesRight", "value": "2"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "ballRecovery", "value": "10"}, {"type": "saves", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "11"}, {"type": "goalsConceded", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "totalBackZonePass", "value": "19"}, {"type": "touches", "value": "40"}, {"type": "savedIbox", "value": "3"}, {"type": "totalLongBalls", "value": "18"}, {"type": "accuratePass", "value": "21"}, {"type": "accurateLongBalls", "value": "9"}, {"type": "keeper<PERSON><PERSON><PERSON>", "value": "5"}, {"type": "accurateLaunches", "value": "4"}, {"type": "totalHighClaim", "value": "2"}, {"type": "possLostCtrl", "value": "9"}, {"type": "successfulOpenPlayPass", "value": "17"}, {"type": "accurateBackZonePass", "value": "18"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "divingSave", "value": "2"}, {"type": "totalLaunches", "value": "8"}, {"type": "goalKicks", "value": "4"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "accurateGoalKicks", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "fwdPass", "value": "18"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "rightsidePass", "value": "4"}, {"type": "finalThirdEntries", "value": "3"}, {"type": "accurateFwdZonePass", "value": "3"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "totalChippedPass", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "1"}]}, {"playerId": "2e0ipswbkv4vf7lav6ee1j3oq", "firstName": "Mattia", "lastName": "Viti", "shortFirstName": "Mattia", "shortLastName": "Viti", "matchName": "<PERSON><PERSON>", "shirtNumber": 21, "position": "Defender", "positionSide": "Left", "formationPlace": "3", "stat": [{"type": "totalFinalThirdPasses", "value": "5"}, {"type": "challengeLost", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalThrows", "value": "6"}, {"type": "attOpenplay", "value": "1"}, {"type": "attMissHigh", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "duelLost", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "bigChanceMissed", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "interceptionWon", "value": "1"}, {"type": "fwdPass", "value": "11"}, {"type": "rightsidePass", "value": "7"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "attemptsIbox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "headClea<PERSON>", "value": "3"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalLaunches", "value": "2"}, {"type": "interception", "value": "1"}, {"type": "possLostCtrl", "value": "12"}, {"type": "successfulOpenPlayPass", "value": "13"}, {"type": "accurateBackZonePass", "value": "5"}, {"type": "accurateThrows", "value": "5"}, {"type": "totalTackle", "value": "2"}, {"type": "attCmissHigh", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "touches", "value": "42"}, {"type": "offsideProvoked", "value": "1"}, {"type": "totalLongBalls", "value": "3"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "attIboxMiss", "value": "1"}, {"type": "lostCorners", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "accuratePass", "value": "14"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "totalClearance", "value": "3"}, {"type": "backwardPass", "value": "1"}, {"type": "duel<PERSON>on", "value": "5"}, {"type": "totalContest", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "possWonDef3rd", "value": "3"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "headPass", "value": "2"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "effectiveHeadClearance", "value": "3"}, {"type": "wasFouled", "value": "2"}, {"type": "dispossessed", "value": "5"}, {"type": "ballRecovery", "value": "3"}, {"type": "passesLeft", "value": "9"}, {"type": "effectiveClearance", "value": "3"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "possLostAll", "value": "12"}, {"type": "totalPass", "value": "20"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "minsPlayed", "value": "86"}, {"type": "openPlayPass", "value": "19"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "5"}, {"type": "formationPlace", "value": "3"}]}, {"playerId": "9rzl0j9m9i30a62o1yofzvaad", "firstName": "<PERSON><PERSON>", "lastName": "Ferrari", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "Ferrari", "matchName": "<PERSON><PERSON>", "shirtNumber": 13, "position": "Defender", "positionSide": "Left/Centre", "formationPlace": "6", "captain": "yes", "stat": [{"type": "ballRecovery", "value": "2"}, {"type": "effectiveHeadClearance", "value": "4"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "longPassOwnToOpp", "value": "11"}, {"type": "goalsConceded", "value": "1"}, {"type": "aerialLost", "value": "4"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "backwardPass", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "10"}, {"type": "leftside<PERSON><PERSON>", "value": "12"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "41"}, {"type": "possLostAll", "value": "2"}, {"type": "totalPass", "value": "47"}, {"type": "passesRight", "value": "1"}, {"type": "effectiveClearance", "value": "7"}, {"type": "aerialWon", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "10"}, {"type": "finalThirdEntries", "value": "9"}, {"type": "fwdPass", "value": "17"}, {"type": "rightsidePass", "value": "15"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "totalFwdZonePass", "value": "22"}, {"type": "accurateFwdZonePass", "value": "20"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateGoalKicks", "value": "3"}, {"type": "duelLost", "value": "4"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalChippedPass", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "11"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "accurateLongBalls", "value": "4"}, {"type": "totalClearance", "value": "7"}, {"type": "accurateLaunches", "value": "1"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accuratePass", "value": "45"}, {"type": "lostCorners", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "9"}, {"type": "totalBackZonePass", "value": "25"}, {"type": "totalLongBalls", "value": "5"}, {"type": "touches", "value": "58"}, {"type": "offsideProvoked", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalTackle", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "39"}, {"type": "accurateBackZonePass", "value": "25"}, {"type": "possLostCtrl", "value": "2"}, {"type": "totalLaunches", "value": "2"}, {"type": "goalKicks", "value": "3"}, {"type": "headClea<PERSON>", "value": "4"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "6"}]}, {"playerId": "e5fnoxb8f2hycjx4gnmce2wvd", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Defender", "positionSide": "Centre/Right", "formationPlace": "5", "stat": [{"type": "duel<PERSON>on", "value": "3"}, {"type": "backwardPass", "value": "3"}, {"type": "longPassOwnToOppSuccess", "value": "5"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "attBxCentre", "value": "1"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "attIboxTarget", "value": "1"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "headPass", "value": "2"}, {"type": "effectiveHeadClearance", "value": "2"}, {"type": "ballRecovery", "value": "5"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "attHdTarget", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "passesRight", "value": "8"}, {"type": "effectiveClearance", "value": "3"}, {"type": "possLostAll", "value": "6"}, {"type": "totalPass", "value": "37"}, {"type": "minsPlayed", "value": "90"}, {"type": "openPlayPass", "value": "35"}, {"type": "attemptedTackleFoul", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "14"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "fouls", "value": "4"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "attSvHighRight", "value": "1"}, {"type": "duelLost", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "totalFlickOn", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "attemptsIbox", "value": "1"}, {"type": "finalThirdEntries", "value": "4"}, {"type": "fwdPass", "value": "12"}, {"type": "rightsidePass", "value": "8"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "totalLaunches", "value": "1"}, {"type": "headClea<PERSON>", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "29"}, {"type": "accurateBackZonePass", "value": "22"}, {"type": "possLostCtrl", "value": "6"}, {"type": "totalTackle", "value": "2"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "attHdTotal", "value": "1"}, {"type": "yellowCard", "value": "1"}, {"type": "totalLongBalls", "value": "4"}, {"type": "touches", "value": "45"}, {"type": "totalBackZonePass", "value": "24"}, {"type": "lostCorners", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalClearance", "value": "3"}, {"type": "accuratePass", "value": "31"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "5"}]}, {"playerId": "e6xexn53yyoxmdt5naduyx8bu", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 3, "position": "Defender", "positionSide": "Right", "formationPlace": "2", "stat": [{"type": "touches", "value": "54"}, {"type": "offsideProvoked", "value": "1"}, {"type": "totalLongBalls", "value": "4"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "9"}, {"type": "accuratePass", "value": "24"}, {"type": "penAreaEntries", "value": "2"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "totalClearance", "value": "2"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "2"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "possLostCtrl", "value": "16"}, {"type": "successfulOpenPlayPass", "value": "24"}, {"type": "accurateBackZonePass", "value": "10"}, {"type": "accurateThrows", "value": "4"}, {"type": "totalTackle", "value": "2"}, {"type": "accurateChippedPass", "value": "2"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "totalFwdZonePass", "value": "17"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "rightsidePass", "value": "5"}, {"type": "fwdPass", "value": "12"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "challengeLost", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "10"}, {"type": "totalChippedPass", "value": "4"}, {"type": "totalThrows", "value": "6"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "fouls", "value": "2"}, {"type": "unsuccessfulTouch", "value": "2"}, {"type": "touchesInOppBox", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "8"}, {"type": "totalPass", "value": "31"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "possLostAll", "value": "16"}, {"type": "openPlayPass", "value": "31"}, {"type": "minsPlayed", "value": "90"}, {"type": "leftside<PERSON><PERSON>", "value": "9"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "effectiveClearance", "value": "2"}, {"type": "passesRight", "value": "9"}, {"type": "headPass", "value": "4"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "dispossessed", "value": "3"}, {"type": "ballRecovery", "value": "3"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "backwardPass", "value": "5"}, {"type": "totalContest", "value": "1"}, {"type": "turnover", "value": "2"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "aerialLost", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalCross", "value": "1"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "4"}, {"type": "formationPlace", "value": "2"}]}, {"playerId": "bqer8ajqm2vibthgumtuycbu1", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Midfielder", "positionSide": "Left/Centre", "formationPlace": "8", "stat": [{"type": "totalPass", "value": "24"}, {"type": "possLostAll", "value": "7"}, {"type": "leftside<PERSON><PERSON>", "value": "5"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "openPlayPass", "value": "21"}, {"type": "minsPlayed", "value": "90"}, {"type": "passesLeft", "value": "4"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "wasFouled", "value": "4"}, {"type": "headPass", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "2"}, {"type": "dispossessed", "value": "1"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "ballRecovery", "value": "4"}, {"type": "aerialLost", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "turnover", "value": "4"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "totalBackZonePass", "value": "9"}, {"type": "touches", "value": "39"}, {"type": "accuratePass", "value": "22"}, {"type": "penAreaEntries", "value": "1"}, {"type": "totalClearance", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "handBall", "value": "1"}, {"type": "possLostCtrl", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "19"}, {"type": "accurateBackZonePass", "value": "9"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "interception", "value": "1"}, {"type": "totalAttAssist", "value": "2"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalTackle", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "rightsidePass", "value": "4"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "totalFwdZonePass", "value": "15"}, {"type": "fwdPass", "value": "10"}, {"type": "finalThirdEntries", "value": "6"}, {"type": "accurateFwdZonePass", "value": "13"}, {"type": "interceptionWon", "value": "1"}, {"type": "totalThrows", "value": "1"}, {"type": "possWonMid3rd", "value": "3"}, {"type": "fouls", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "accurateLayoffs", "value": "2"}, {"type": "totalFinalThirdPasses", "value": "10"}, {"type": "totalChippedPass", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "unsuccessfulTouch", "value": "4"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "8"}]}, {"playerId": "9k1ytj3z42ggp14vkn39hiymt", "firstName": "<PERSON>", "lastName": "Mba Obiang Avomo", "shortFirstName": "<PERSON>", "shortLastName": "Mba Obiang Avomo", "knownName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 14, "position": "Midfielder", "positionSide": "Centre", "formationPlace": "4", "stat": [{"type": "finalThirdEntries", "value": "3"}, {"type": "fwdPass", "value": "11"}, {"type": "totalFwdZonePass", "value": "17"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "rightsidePass", "value": "11"}, {"type": "interceptionWon", "value": "2"}, {"type": "accurateFwdZonePass", "value": "13"}, {"type": "attOboxMiss", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attObxCentre", "value": "2"}, {"type": "duelLost", "value": "3"}, {"type": "attMissHigh", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "7"}, {"type": "totalClearance", "value": "2"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accuratePass", "value": "26"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "totalBackZonePass", "value": "14"}, {"type": "totalLongBalls", "value": "2"}, {"type": "touches", "value": "39"}, {"type": "attCmissHigh", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "accurateBackZonePass", "value": "13"}, {"type": "successfulOpenPlayPass", "value": "24"}, {"type": "possLostCtrl", "value": "5"}, {"type": "totalLaunches", "value": "1"}, {"type": "interception", "value": "2"}, {"type": "ballRecovery", "value": "2"}, {"type": "gameStarted", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "attRfTotal", "value": "2"}, {"type": "longPassOwnToOpp", "value": "3"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "aerialLost", "value": "3"}, {"type": "duel<PERSON>on", "value": "1"}, {"type": "backwardPass", "value": "5"}, {"type": "<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "4"}, {"type": "minsPlayed", "value": "72"}, {"type": "openPlayPass", "value": "29"}, {"type": "attemptsObox", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "totalPass", "value": "31"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "passesRight", "value": "6"}, {"type": "effectiveClearance", "value": "2"}, {"type": "passesLeft", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "4"}]}, {"playerId": "5r7q0eg6pmoq0safxa9tnm5l", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "Uros", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 6, "position": "Midfielder", "positionSide": "Centre/Right", "formationPlace": "7", "stat": [{"type": "possLostCtrl", "value": "11"}, {"type": "successfulOpenPlayPass", "value": "36"}, {"type": "accurateBackZonePass", "value": "15"}, {"type": "interception", "value": "1"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "ontargetScoringAtt", "value": "1"}, {"type": "totalBackZonePass", "value": "15"}, {"type": "touches", "value": "52"}, {"type": "totalLongBalls", "value": "7"}, {"type": "accuratePass", "value": "36"}, {"type": "penAreaEntries", "value": "1"}, {"type": "accurateLongBalls", "value": "2"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "attSvLowRight", "value": "1"}, {"type": "challengeLost", "value": "2"}, {"type": "accurateLayoffs", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "13"}, {"type": "totalChippedPass", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "attRfTarget", "value": "1"}, {"type": "duelLost", "value": "5"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "totalFlickOn", "value": "1"}, {"type": "attObxCentre", "value": "1"}, {"type": "totalFwdZonePass", "value": "29"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "rightsidePass", "value": "8"}, {"type": "fwdPass", "value": "20"}, {"type": "finalThirdEntries", "value": "7"}, {"type": "accurateFwdZonePass", "value": "21"}, {"type": "interceptionWon", "value": "1"}, {"type": "passesLeft", "value": "6"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "passesRight", "value": "7"}, {"type": "attOboxTarget", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalPass", "value": "44"}, {"type": "possLostAll", "value": "11"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "attemptsObox", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "10"}, {"type": "openPlayPass", "value": "44"}, {"type": "minsPlayed", "value": "79"}, {"type": "aerialLost", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "3"}, {"type": "backwardPass", "value": "6"}, {"type": "turnover", "value": "1"}, {"type": "totalSubOff", "value": "1"}, {"type": "possWonDef3rd", "value": "4"}, {"type": "longPassOwnToOpp", "value": "8"}, {"type": "attAssistOpenplay", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "offtargetAttAssist", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "totalLayoffs", "value": "1"}, {"type": "dispossessed", "value": "2"}, {"type": "ballRecovery", "value": "6"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "7"}]}, {"playerId": "27xj7me8icr5ptta7kwx0kfs9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 45, "position": "Striker", "positionSide": "Left/Centre", "formationPlace": "11", "stat": [{"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "totalScoringAtt", "value": "2"}, {"type": "passesLeft", "value": "3"}, {"type": "attLfTotal", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "accurateCornersIntobox", "value": "1"}, {"type": "openPlayPass", "value": "22"}, {"type": "minsPlayed", "value": "90"}, {"type": "totalPass", "value": "22"}, {"type": "possLostAll", "value": "12"}, {"type": "totalCross", "value": "6"}, {"type": "possWonDef3rd", "value": "1"}, {"type": "attRfTotal", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "backwardPass", "value": "8"}, {"type": "turnover", "value": "3"}, {"type": "totalFastbreak", "value": "1"}, {"type": "totalContest", "value": "4"}, {"type": "duel<PERSON>on", "value": "4"}, {"type": "dispossessed", "value": "1"}, {"type": "ontargetAttAssist", "value": "1"}, {"type": "ballRecovery", "value": "2"}, {"type": "wasFouled", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "totalAttAssist", "value": "1"}, {"type": "attAssistSetplay", "value": "1"}, {"type": "possLostCtrl", "value": "12"}, {"type": "accurateBackZonePass", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "20"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "accuratePass", "value": "20"}, {"type": "penAreaEntries", "value": "6"}, {"type": "totalClearance", "value": "1"}, {"type": "attFastbreak", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "12"}, {"type": "blockedScoringAtt", "value": "2"}, {"type": "totalCornersIntobox", "value": "5"}, {"type": "totalBackZonePass", "value": "7"}, {"type": "cornerTaken", "value": "5"}, {"type": "touches", "value": "42"}, {"type": "touchesInOppBox", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "duelLost", "value": "3"}, {"type": "unsuccessfulTouch", "value": "3"}, {"type": "totalPullBack", "value": "1"}, {"type": "accurateCross", "value": "1"}, {"type": "attOpenplay", "value": "2"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "challengeLost", "value": "1"}, {"type": "successfulPutThrough", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "14"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFwdZonePass", "value": "21"}, {"type": "attemptsConcededIbox", "value": "7"}, {"type": "rightsidePass", "value": "11"}, {"type": "fwdPass", "value": "2"}, {"type": "finalThirdEntries", "value": "5"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "3"}, {"type": "attemptsIbox", "value": "2"}, {"type": "accurateFwdZonePass", "value": "14"}, {"type": "shotFastbreak", "value": "1"}, {"type": "wonCorners", "value": "2"}, {"type": "attIboxBlocked", "value": "2"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "2"}, {"type": "formationPlace", "value": "11"}]}, {"playerId": "153ljvbov0n176vm9fdaizoix", "firstName": "<PERSON>", "lastName": "Pi<PERSON>ont<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "Pi<PERSON>ont<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 9, "position": "Striker", "positionSide": "Centre", "formationPlace": "9", "stat": [{"type": "totalAttAssist", "value": "2"}, {"type": "totalTackle", "value": "1"}, {"type": "possLostCtrl", "value": "6"}, {"type": "accurateBackZonePass", "value": "3"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "accuratePass", "value": "9"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "totalBackZonePass", "value": "3"}, {"type": "touches", "value": "20"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "fouls", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "11"}, {"type": "fwdPass", "value": "2"}, {"type": "rightsidePass", "value": "3"}, {"type": "totalFwdZonePass", "value": "12"}, {"type": "attemptsConcededIbox", "value": "6"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "accurateFwdZonePass", "value": "6"}, {"type": "wonCorners", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "totalFlickOn", "value": "4"}, {"type": "passesRight", "value": "2"}, {"type": "aerialWon", "value": "4"}, {"type": "passesLeft", "value": "2"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "minsPlayed", "value": "79"}, {"type": "openPlayPass", "value": "14"}, {"type": "possLostAll", "value": "6"}, {"type": "totalPass", "value": "15"}, {"type": "totalSubOff", "value": "1"}, {"type": "attAssistOpenplay", "value": "2"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "goalsConceded", "value": "1"}, {"type": "aerialLost", "value": "1"}, {"type": "backwardPass", "value": "2"}, {"type": "duel<PERSON>on", "value": "6"}, {"type": "ontargetAttAssist", "value": "2"}, {"type": "ballRecovery", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "headPass", "value": "4"}, {"type": "gameStarted", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "9"}]}, {"playerId": "f2p5457ilcfrtzhe343whhkut", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Grégoire", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 92, "position": "Striker", "positionSide": "Centre/Right", "formationPlace": "10", "stat": [{"type": "passesLeft", "value": "1"}, {"type": "blocked<PERSON><PERSON>", "value": "2"}, {"type": "attLfTotal", "value": "1"}, {"type": "effectiveClearance", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "totalScoringAtt", "value": "1"}, {"type": "totalPass", "value": "19"}, {"type": "possLostAll", "value": "8"}, {"type": "attemptsObox", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "8"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "openPlayPass", "value": "19"}, {"type": "minsPlayed", "value": "73"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "backwardPass", "value": "5"}, {"type": "turnover", "value": "1"}, {"type": "totalContest", "value": "2"}, {"type": "duel<PERSON>on", "value": "3"}, {"type": "totalSubOff", "value": "1"}, {"type": "longPassOwnToOpp", "value": "2"}, {"type": "possWonDef3rd", "value": "2"}, {"type": "goalsConceded", "value": "1"}, {"type": "wasFouled", "value": "2"}, {"type": "effectiveHeadClearance", "value": "1"}, {"type": "gameStarted", "value": "1"}, {"type": "goalsConcededObox", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "ballRecovery", "value": "6"}, {"type": "possLostCtrl", "value": "8"}, {"type": "accurateBackZonePass", "value": "6"}, {"type": "successfulOpenPlayPass", "value": "15"}, {"type": "headClea<PERSON>", "value": "1"}, {"type": "totalTackle", "value": "1"}, {"type": "totalBackZonePass", "value": "8"}, {"type": "attOboxBlocked", "value": "1"}, {"type": "touches", "value": "31"}, {"type": "totalLongBalls", "value": "1"}, {"type": "accuratePass", "value": "15"}, {"type": "totalClearance", "value": "1"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "blockedScoringAtt", "value": "1"}, {"type": "attOpenplay", "value": "1"}, {"type": "possWonMid3rd", "value": "4"}, {"type": "fouls", "value": "1"}, {"type": "successfulPutThrough", "value": "2"}, {"type": "challengeLost", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "6"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "6"}, {"type": "duelLost", "value": "5"}, {"type": "unsuccessfulTouch", "value": "1"}, {"type": "attemptsConcededObox", "value": "3"}, {"type": "attObxCentre", "value": "1"}, {"type": "rightsidePass", "value": "3"}, {"type": "totalFwdZonePass", "value": "11"}, {"type": "attemptsConcededIbox", "value": "4"}, {"type": "fwdPass", "value": "3"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "accurateFwdZonePass", "value": "9"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "3"}, {"type": "formationPlace", "value": "10"}]}, {"playerId": "3sehm4zmy7141wm4ecfqqoxd5", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "4"}, {"type": "crosses18yard", "value": "2"}, {"type": "accurateFwdZonePass", "value": "4"}, {"type": "finalThirdEntries", "value": "1"}, {"type": "totalFwdZonePass", "value": "8"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "fwdPass", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "5"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "possLostCtrl", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "4"}, {"type": "penAreaEntries", "value": "3"}, {"type": "accuratePass", "value": "5"}, {"type": "totalSubOn", "value": "1"}, {"type": "touches", "value": "9"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "totalCornersIntobox", "value": "2"}, {"type": "cornerTaken", "value": "2"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "totalCross", "value": "4"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "backwardPass", "value": "1"}, {"type": "ballRecovery", "value": "1"}, {"type": "passesRight", "value": "3"}, {"type": "passesLeft", "value": "1"}, {"type": "openPlayPass", "value": "5"}, {"type": "minsPlayed", "value": "11"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "totalPass", "value": "5"}, {"type": "possLostAll", "value": "4"}, {"type": "possWonAtt3rd", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "7qluty59wsg0ak1p5301o8pux", "firstName": "<PERSON>", "lastName": "Boloca", "shortFirstName": "<PERSON>", "shortLastName": "Boloca", "matchName": "<PERSON><PERSON>", "shirtNumber": 24, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalFinalThirdPasses", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "duelLost", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateFwdZonePass", "value": "5"}, {"type": "totalFwdZonePass", "value": "7"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "rightsidePass", "value": "3"}, {"type": "fwdPass", "value": "3"}, {"type": "won<PERSON><PERSON><PERSON>", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "6"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "possLostCtrl", "value": "3"}, {"type": "totalTackle", "value": "1"}, {"type": "touches", "value": "10"}, {"type": "totalBackZonePass", "value": "1"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "6"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "longPassOwnToOppSuccess", "value": "1"}, {"type": "longPassOwnToOpp", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "dispossessed", "value": "1"}, {"type": "passesLeft", "value": "1"}, {"type": "aerialWon", "value": "1"}, {"type": "passesRight", "value": "4"}, {"type": "totalPass", "value": "8"}, {"type": "possLostAll", "value": "3"}, {"type": "openPlayPass", "value": "8"}, {"type": "minsPlayed", "value": "17"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "1"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "6jwy293owzzhldv4i9wxntjv9", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Cragno", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Cragno", "matchName": "<PERSON><PERSON>", "shirtNumber": 28, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "4xnll2f9tb69wrezm04ayg76y", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cddggachzf198p1yhq9owtofd", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 19, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "9cv979t3dmbwoy586m01xdvkk", "firstName": "Luca", "lastName": "Lipani", "shortFirstName": "Luca", "shortLastName": "Lipani", "matchName": "<PERSON><PERSON>", "shirtNumber": 35, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "ataq92vho6o4euh045am8cbo4", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 2, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "6w3v6ck12wmaff8owswnhphm2", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 8, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "duel<PERSON>on", "value": "1"}, {"type": "totalContest", "value": "1"}, {"type": "passesRight", "value": "1"}, {"type": "possLostAll", "value": "1"}, {"type": "totalPass", "value": "3"}, {"type": "minsPlayed", "value": "11"}, {"type": "openPlayPass", "value": "3"}, {"type": "leftside<PERSON><PERSON>", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "3"}, {"type": "totalPullBack", "value": "1"}, {"type": "touchesInOppBox", "value": "1"}, {"type": "accurateFwdZonePass", "value": "2"}, {"type": "totalFwdZonePass", "value": "3"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "rightsidePass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "possLostCtrl", "value": "1"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "touches", "value": "4"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "6k790p49tjwkhlwf9afll9jo5", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 25, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "0"}]}, {"playerId": "cxxxfvmrslyjyi9wz9m8kwne2", "firstName": "<PERSON>uan", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>uan", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "knownName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 44, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "passesRight", "value": "1"}, {"type": "openPlayPass", "value": "3"}, {"type": "minsPlayed", "value": "4"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "totalPass", "value": "3"}, {"type": "possLostAll", "value": "2"}, {"type": "totalCross", "value": "1"}, {"type": "headPass", "value": "1"}, {"type": "accurateThrows", "value": "1"}, {"type": "totalCrossNocorner", "value": "1"}, {"type": "possLostCtrl", "value": "2"}, {"type": "successfulOpenPlayPass", "value": "2"}, {"type": "accurateBackZonePass", "value": "1"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "2"}, {"type": "touches", "value": "5"}, {"type": "totalBackZonePass", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"type": "totalThrows", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "accurateFwdZonePass", "value": "1"}, {"type": "attemptsConcededIbox", "value": "1"}, {"type": "totalFwdZonePass", "value": "2"}, {"type": "fwdPass", "value": "1"}, {"type": "wonCorners", "value": "1"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}, {"playerId": "bh9jia8qgkhpm5xn82bx0hhck", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Volpato", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Volpato", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "totalLongBalls", "value": "2"}, {"type": "touches", "value": "15"}, {"type": "accurateLongBalls", "value": "1"}, {"type": "penAreaEntries", "value": "3"}, {"type": "totalSubOn", "value": "1"}, {"type": "accuratePass", "value": "8"}, {"type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"type": "successfulOpenPlayPass", "value": "8"}, {"type": "possLostCtrl", "value": "5"}, {"type": "totalLaunches", "value": "1"}, {"type": "totalCrossNocorner", "value": "2"}, {"type": "accurateChippedPass", "value": "1"}, {"type": "totalFlickOn", "value": "1"}, {"type": "finalThirdEntries", "value": "2"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"type": "fwdPass", "value": "7"}, {"type": "totalFwdZonePass", "value": "13"}, {"type": "attemptsConcededIbox", "value": "3"}, {"type": "rightsidePass", "value": "1"}, {"type": "accurateFwdZonePass", "value": "8"}, {"type": "fouls", "value": "1"}, {"type": "crosses18yard", "value": "1"}, {"type": "possWonMid3rd", "value": "1"}, {"type": "totalChippedPass", "value": "1"}, {"type": "totalFinalThirdPasses", "value": "9"}, {"type": "challengeLost", "value": "1"}, {"type": "duelLost", "value": "2"}, {"type": "touchesInOppBox", "value": "5"}, {"type": "accurateFlickOn", "value": "1"}, {"type": "possLostAll", "value": "5"}, {"type": "totalPass", "value": "11"}, {"type": "attemptedTackleFoul", "value": "1"}, {"type": "leftside<PERSON><PERSON>", "value": "2"}, {"type": "minsPlayed", "value": "18"}, {"type": "openPlayPass", "value": "11"}, {"type": "crosses18yardplus", "value": "1"}, {"type": "passesRight", "value": "4"}, {"type": "ballRecovery", "value": "1"}, {"type": "duel<PERSON>on", "value": "2"}, {"type": "totalContest", "value": "2"}, {"type": "backwardPass", "value": "1"}, {"type": "longPassOwnToOppSuccess", "value": "2"}, {"type": "longPassOwnToOpp", "value": "4"}, {"type": "totalCross", "value": "2"}, {"type": "winningGoal", "value": "0"}, {"type": "timesTackled", "value": "0"}, {"type": "formationPlace", "value": "0"}]}], "teamOfficial": {"id": "eaoqc7m6a2ww9dq1x0hxn5xp1", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON>", "type": "manager"}, "stat": [{"fh": "21", "sh": "25", "type": "duelLost", "value": "46"}, {"fh": "0", "sh": "1", "type": "attRfTarget", "value": "1"}, {"fh": "8", "sh": "12", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "20"}, {"fh": "3", "sh": "16", "type": "touchesInOppBox", "value": "19"}, {"fh": "0", "sh": "1", "type": "accurateFlickOn", "value": "1"}, {"fh": "0", "sh": "1", "type": "attSvHighRight", "value": "1"}, {"fh": "0", "sh": "2", "type": "totalPullBack", "value": "2"}, {"fh": "5", "sh": "6", "type": "unsuccessfulTouch", "value": "11"}, {"fh": "1", "sh": "1", "type": "attMissHigh", "value": "2"}, {"fh": "0", "sh": "1", "type": "attSvLowRight", "value": "1"}, {"fh": "0", "sh": "6", "type": "crosses18yard", "value": "6"}, {"fh": "9", "sh": "7", "type": "possWonMid3rd", "value": "16"}, {"fh": "0", "sh": "1", "type": "accurateCross", "value": "1"}, {"fh": "4", "sh": "10", "type": "totalThrows", "value": "14"}, {"fh": "3", "sh": "4", "type": "attOpenplay", "value": "7"}, {"fh": "12", "sh": "7", "type": "totalChippedPass", "value": "19"}, {"fh": "48", "sh": "64", "type": "totalFinalThirdPasses", "value": "112"}, {"fh": "4", "sh": "4", "type": "successfulPutThrough", "value": "8"}, {"fh": "2", "sh": "5", "type": "challengeLost", "value": "7"}, {"fh": "1", "sh": "2", "type": "accurateLayoffs", "value": "3"}, {"fh": "1", "sh": "3", "type": "attemptsIbox", "value": "4"}, {"fh": "1", "sh": "6", "type": "<PERSON><PERSON><PERSON><PERSON>", "value": "7"}, {"fh": "28", "sh": "23", "type": "finalThirdEntries", "value": "51"}, {"fh": "0", "sh": "1", "type": "totalYellowCard", "value": "1"}, {"fh": "58", "sh": "72", "type": "fwdPass", "value": "130"}, {"fh": "4", "sh": "6", "type": "fkFoulLost", "value": "10"}, {"fh": "44", "sh": "43", "type": "rightsidePass", "value": "87"}, {"fh": "3", "sh": "4", "type": "attemptsConcededIbox", "value": "7"}, {"fh": "93", "sh": "121", "type": "totalFwdZonePass", "value": "214"}, {"fh": "3", "sh": "2", "type": "interceptionWon", "value": "5"}, {"fh": "1", "sh": "0", "type": "shotFastbreak", "value": "1"}, {"fh": "66", "sh": "85", "type": "accurateFwdZonePass", "value": "151"}, {"fh": "1", "sh": "0", "type": "attOboxMiss", "value": "1"}, {"fh": "1", "sh": "1", "type": "attIboxBlocked", "value": "2"}, {"fh": "0", "sh": "3", "type": "attemptsConcededObox", "value": "3"}, {"fh": "0", "sh": "1", "type": "bigChanceMissed", "value": "1"}, {"fh": "2", "sh": "2", "type": "accurateGoalKicks", "value": "4"}, {"fh": "3", "sh": "4", "type": "wonCorners", "value": "7"}, {"fh": "2", "sh": "2", "type": "attObxCentre", "value": "4"}, {"fh": "3", "sh": "4", "type": "totalFlickOn", "value": "7"}, {"fh": "4", "sh": "3", "type": "accurateChippedPass", "value": "7"}, {"fh": "3", "sh": "3", "type": "totalAttAssist", "value": "6"}, {"fh": "1", "sh": "1", "type": "attCmissHigh", "value": "2"}, {"fh": "0", "sh": "1", "type": "attAssistSetplay", "value": "1"}, {"fh": "0", "sh": "2", "type": "ontargetScoringAtt", "value": "2"}, {"fh": "5", "sh": "10", "type": "totalTackle", "value": "15"}, {"fh": "0", "sh": "1", "type": "attHdTotal", "value": "1"}, {"fh": "7", "sh": "4", "type": "fkFoulWon", "value": "11"}, {"fh": "2", "sh": "9", "type": "accurateThrows", "value": "11"}, {"fh": "64", "sh": "72", "type": "accurateBackZonePass", "value": "136"}, {"fh": "121", "sh": "146", "type": "successfulOpenPlayPass", "value": "267"}, {"fh": "47", "sh": "62", "type": "possLostCtrl", "value": "109"}, {"fh": "0", "sh": "2", "type": "divingSave", "value": "2"}, {"fh": "9", "sh": "8", "type": "totalLaunches", "value": "17"}, {"fh": "4", "sh": "3", "type": "goalKicks", "value": "7"}, {"fh": "3", "sh": "2", "type": "interception", "value": "5"}, {"fh": "0", "sh": "7", "type": "totalCrossNocorner", "value": "7"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "8", "sh": "3", "type": "headClea<PERSON>", "value": "11"}, {"fh": "2", "sh": "5", "type": "won<PERSON><PERSON><PERSON>", "value": "7"}, {"fh": "13", "sh": "7", "type": "totalClearance", "value": "20"}, {"fh": "8", "sh": "14", "type": "accurateLongBalls", "value": "22"}, {"fh": "1", "sh": "0", "type": "attFastbreak", "value": "1"}, {"fh": "2", "sh": "3", "type": "keeper<PERSON><PERSON><PERSON>", "value": "5"}, {"fh": "3", "sh": "2", "type": "accurateLaunches", "value": "5"}, {"fh": "6", "sh": "12", "type": "penAreaEntries", "value": "18"}, {"fh": "130", "sh": "156", "type": "accuratePass", "value": "286"}, {"fh": "0", "sh": "1", "type": "handBall", "value": "1"}, {"fh": "2", "sh": "2", "type": "blockedScoringAtt", "value": "4"}, {"fh": "31", "sh": "49", "type": "successful<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "80"}, {"fh": "2", "sh": "2", "type": "lostCorners", "value": "4"}, {"fh": "1", "sh": "1", "type": "totalHighClaim", "value": "2"}, {"fh": "1", "sh": "1", "type": "attOboxBlocked", "value": "2"}, {"fh": "0", "sh": "1", "type": "attIboxMiss", "value": "1"}, {"fh": "3", "sh": "4", "type": "cornerTaken", "value": "7"}, {"fh": "3", "sh": "4", "type": "totalCornersIntobox", "value": "7"}, {"fh": "70", "sh": "80", "type": "totalBackZonePass", "value": "150"}, {"fh": "22", "sh": "24", "type": "totalLongBalls", "value": "46"}, {"fh": "0", "sh": "3", "type": "savedIbox", "value": "3"}, {"fh": "226", "sh": "279", "type": "touches", "value": "505"}, {"fh": "3", "sh": "2", "type": "attAssistOpenplay", "value": "5"}, {"fh": "1", "sh": "4", "type": "attRfTotal", "value": "5"}, {"fh": "0", "sh": "1", "type": "attIboxTarget", "value": "1"}, {"fh": "28", "sh": "28", "type": "longPassOwnToOpp", "value": "56"}, {"fh": "11", "sh": "8", "type": "possWonDef3rd", "value": "19"}, {"fh": "3", "sh": "11", "type": "totalCross", "value": "14"}, {"fh": "0", "sh": "1", "type": "goalsConceded", "value": "1"}, {"fh": "0", "sh": "2", "type": "attBxCentre", "value": "2"}, {"fh": "0", "sh": "3", "type": "saves", "value": "3"}, {"fh": "8", "sh": "4", "type": "aerialLost", "value": "12"}, {"fh": "32", "sh": "41.7", "type": "possessionPercentage", "value": "36.7"}, {"fh": "18", "sh": "23", "type": "duel<PERSON>on", "value": "41"}, {"fh": "1", "sh": "0", "type": "totalFastbreak", "value": "1"}, {"fh": "3", "sh": "8", "type": "totalContest", "value": "11"}, {"fh": "20", "sh": "25", "type": "backwardPass", "value": "45"}, {"fh": "15", "sh": "16", "type": "longPassOwnToOppSuccess", "value": "31"}, {"fh": "1", "sh": "2", "type": "<PERSON><PERSON><PERSON>", "value": "3"}, {"fh": "1", "sh": "2", "type": "totalLayoffs", "value": "3"}, {"fh": "24", "sh": "22", "type": "ballRecovery", "value": "46"}, {"fh": "0", "sh": "5", "type": "subsMade", "value": "5"}, {"fh": "2", "sh": "3", "type": "ontargetAttAssist", "value": "5"}, {"fh": "5", "sh": "9", "type": "dispossessed", "value": "14"}, {"fh": "8", "sh": "3", "type": "effectiveHeadClearance", "value": "11"}, {"fh": "0", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "1"}, {"fh": "0", "sh": "1", "type": "goalsConcededObox", "value": "1"}, {"fh": "1", "sh": "0", "type": "offtargetAttAssist", "value": "1"}, {"fh": "3", "sh": "5", "type": "totalScoringAtt", "value": "8"}, {"fh": "0", "sh": "1", "type": "attOboxTarget", "value": "1"}, {"fh": "23", "sh": "30", "type": "passesRight", "value": "53"}, {"fh": "13", "sh": "7", "type": "effectiveClearance", "value": "20"}, {"fh": "0", "sh": "1", "type": "attHdTarget", "value": "1"}, {"fh": "6", "sh": "3", "type": "aerialWon", "value": "9"}, {"fh": "2", "sh": "0", "type": "attLfTotal", "value": "2"}, {"fh": "4", "sh": "4", "type": "blocked<PERSON><PERSON>", "value": "8"}, {"fh": "14", "sh": "27", "type": "passesLeft", "value": "41"}, {"fh": "0", "sh": "1", "type": "crosses18yardplus", "value": "1"}, {"fh": "3", "sh": "6", "type": "attemptedTackleFoul", "value": "9"}, {"fh": "0", "sh": "1", "type": "accurateCornersIntobox", "value": "1"}, {"fh": "38", "sh": "50", "type": "leftside<PERSON><PERSON>", "value": "88"}, {"fh": "147", "sh": "178", "type": "openPlayPass", "value": "325"}, {"fh": "2", "sh": "3", "type": "accurateKeeperThrows", "value": "5"}, {"fh": "2", "sh": "2", "type": "attemptsObox", "value": "4"}, {"fh": "1", "sh": "1", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "2"}, {"fh": "0", "sh": "3", "type": "possWonAtt3rd", "value": "3"}, {"fh": "47", "sh": "62", "type": "possLostAll", "value": "109"}, {"fh": "160", "sh": "190", "type": "totalPass", "value": "350"}, {"type": "formationUsed", "value": "433"}], "kit": {"id": "11941", "colour1": "#FFFFFF", "colour2": "#FFFFFF", "type": "away"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "dgn40a8zunxggvm9hrfrgta6t", "type": "Main", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "8a18vovob6w7942x63pn8w1qt", "type": "Assistant referee 1", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>"}, {"id": "9l62onlv1dpd5pkh5h51ks91x", "type": "Assistant referee 2", "firstName": "<PERSON>", "lastName": "Tegoni", "shortFirstName": "<PERSON>", "shortLastName": "Tegoni"}, {"id": "4vn9fawqk7c4d2y73hpobyj8p", "type": "Fourth official", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>"}, {"id": "ep5ncxce5bide1sea2uxhnfth", "type": "Video Assistant Referee", "firstName": "<PERSON>", "lastName": "Sozza", "shortFirstName": "<PERSON>", "shortLastName": "Sozza"}, {"id": "4wvxc13d1hcimm76bg8q9d5h", "type": "Assistant VAR Official", "firstName": "Rosario", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Rosario", "shortLastName": "<PERSON><PERSON><PERSON>"}]}}}