{"matchInfo": {"id": "arjpqu92xd4tcn13abppo7ims", "coverageLevel": "15", "date": "2023-10-05Z", "time": "00:30:00Z", "localDate": "2023-10-04", "localTime": "19:30:00", "numberOfPeriods": 2, "periodLength": 45, "lastUpdated": "2023-10-05T04:01:04Z", "description": "Dallas vs Colorado Rapids", "sport": {"id": "289u5typ3vp4ifwh5thalohmq", "name": "Soccer"}, "ruleset": {"id": "79plas4983031idr6vw83nuel", "name": "Men"}, "competition": {"id": "287tckirbfj9nb8ar2k9r60vn", "name": "MLS", "knownName": "US Major League Soccer", "competitionCode": "MLS", "competitionFormat": "Domestic league", "country": {"id": "7hr2f89v44y65dyu9k92vprwn", "name": "USA"}}, "tournamentCalendar": {"id": "7a5umgwn0v0mbrtadxnnp9b84", "startDate": "2023-02-25Z", "endDate": "2023-12-10Z", "name": "2023"}, "stage": {"id": "7aka6xzf7gbvufhwu5e75l3is", "formatId": "e2q01r9o9jwiq1fls93d1sslx", "startDate": "2023-02-25Z", "endDate": "2023-10-22Z", "name": "Regular Season"}, "contestant": [{"id": "4gi4qw4gt6nfq0c6r8y574qgp", "name": "Dallas", "shortName": "Dallas", "officialName": "FC Dallas", "code": "DAL", "position": "home", "country": {"id": "7hr2f89v44y65dyu9k92vprwn", "name": "USA"}}, {"id": "cvrl5bces32x6fup1ks1hvets", "name": "Colorado Rapids", "shortName": "Colorado", "officialName": "Colorado Rapids", "code": "COL", "position": "away", "country": {"id": "7hr2f89v44y65dyu9k92vprwn", "name": "USA"}}], "venue": {"id": "86i7ztgeobez2lcwxl282817y", "neutral": "no", "longName": "Toyota Stadium", "shortName": "Toyota Stadium"}}, "liveData": {"matchDetails": {"periodId": 16, "matchStatus": "Postponed"}, "lineUp": [{"contestantId": "4gi4qw4gt6nfq0c6r8y574qgp", "player": [{"playerId": "lvbnk881mde943xx8nrr1nmd", "firstName": "Eugene", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "Eugene", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 31, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "5y8srw7hc5nlu0efcq710if3d", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 18, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "93o28ydbvwtnx57fivsejpi0a", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON><PERSON>", "shirtNumber": 9, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "4m1a1e1aqovig7w39zrfy8smh", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "Sam", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 29, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "4rbdimd60mev06u2km69yyvo", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 77, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "76pewf0m3ujozkvcsrtkybff9", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON>", "shirtNumber": 3, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "9aozq6tkkb4ufscam4o1q0kb9", "firstName": "<PERSON>", "lastName": "Maurer", "shortFirstName": "<PERSON>", "shortLastName": "Maurer", "matchName": "<PERSON><PERSON>", "shirtNumber": 1, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "40r697pq53er9cir2o46yk91x", "firstName": "Facundo <PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "Facundo", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 5, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "8tukt8jsmzkblxxs5w85x4dii", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 11, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}], "teamOfficial": {"id": "4z8eter0zc0ucxz9kcbynuuhh", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "Nico", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "manager"}, "kit": {"id": "18438", "colour1": "#FFFFFF", "colour2": "#FFFFFF", "type": "away"}}, {"contestantId": "cvrl5bces32x6fup1ks1hvets", "formationUsed": "4231", "player": [{"playerId": "6pgcyaivg3sjcm6jju4nn3omi", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 23, "position": "Defensive Midfielder", "positionSide": "Left/Centre", "formationPlace": "4", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "ehv4rise2fx4u1wepwtwllb6x", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 20, "position": "Defensive Midfielder", "positionSide": "Centre/Right", "formationPlace": "8", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "ah2s6yhizmnyhjdo9yk56h39x", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "Sam", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 28, "position": "Attacking Midfielder", "positionSide": "Right", "formationPlace": "7", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "b6u6116764v2j7apsi25vx3v9", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 33, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "oyfrc7by0bs2xynwaof0xtrd", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Cabral", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Cabral", "knownName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 91, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "efr6uboj041gcgi2qxm0pa5be", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 12, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "6trmqwons1idaeoem1g7u467e", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Galván", "shortFirstName": "<PERSON><PERSON><PERSON>", "shortLastName": "Galván", "matchName": "B. Galván", "shirtNumber": 29, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "1s1fyh1gza8ll4aeenib2vmcp", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 7, "position": "Substitute", "subPosition": "Attacker", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "errrv56acc41z74f8kumo7qlm", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 15, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "34wufspo6zerdhewo82hbszmy", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Priso-Mbongue", "shortFirstName": "<PERSON>", "shortLastName": "P<PERSON><PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 97, "position": "Substitute", "subPosition": "Midfielder", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "2m7xhno7xnktvmzwa6j7wlgfe", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 26, "position": "Substitute", "subPosition": "Goalkeeper", "stat": [{"type": "formationPlace", "value": "Unknown"}]}, {"playerId": "bocl3lbg3kiz5un46fftpqk0l", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "matchName": "<PERSON><PERSON>", "shirtNumber": 4, "position": "Substitute", "subPosition": "Defender", "stat": [{"type": "formationPlace", "value": "Unknown"}]}], "teamOfficial": {"id": "1ksqc3eaem2sso45h1wc3dox6", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>", "type": "manager"}, "kit": {"id": "19331", "colour1": "#990033", "colour2": "#00CCFF", "type": "home"}}], "matchDetailsExtra": {"matchOfficial": [{"id": "8e1yoibd4p85izrcc4ww4rx79", "type": "Main", "firstName": "<PERSON>", "lastName": "<PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON>"}, {"id": "2sa2puirlgg0rs8pabhwc49g5", "type": "Lineman 1", "firstName": "<PERSON>", "lastName": "Uranga", "shortFirstName": "<PERSON>", "shortLastName": "Uranga"}, {"id": "2wudb1fsg3ca1zfqmpuq7iz1l", "type": "Lineman 2", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>"}, {"id": "cv632y9125jzypnp1a079o89h", "type": "Fourth official", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "shortFirstName": "<PERSON><PERSON>", "shortLastName": "<PERSON><PERSON><PERSON>"}]}}}