<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.wsf.dataingestor" level="${app.log.level:-INFO}"/>

    <root level="${root.log.level:-ERROR}">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
