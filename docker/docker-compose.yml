version: '3'
services:

  ingestor:
    image: 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/ingestor
    ports:
      - '8080:8080'
      - '5005:5005'

  mongo-replica:
    image: flqw/docker-mongo-local-replicaset
    ports:
      - '27001:27001'
      - '27002:27002'
      - '27003:27003'
    restart: always
    environment:
      REPLICA_SET_NAME: mongo-rs
    volumes:
      - ./data/mongodb:/data

  zookeeper:
    image: wurstmeister/zookeeper
    ports:
    - "2181:2181"

  kafka:
    image: wurstmeister/kafka
    ports:
    - '9092:9092'
    environment:
      KAFKA_CREATE_TOPICS: 'player_ratings:1:1,contracts:1:1,transactions:1:1'
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
