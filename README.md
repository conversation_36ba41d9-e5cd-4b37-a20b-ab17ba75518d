[![CircleCI](https://circleci.com/bb/wsf-dublin/data-ingestor.svg?style=svg)](https://circleci.com/bb/wsf-dublin/data-ingestor)

# WSF Data-Ingestor
  Wall Street Football data ingestor. It is responsible for retrieving players rankings and stats from third party services (eg. Opta)
  and save them to DB.

## Getting started

### Requirements
> Mongodb running

### Running the project
```bash
./gradlew bootRun
```
> Or just run/debug `com.wsf.dataingestor.DataIngestorApplication.class` in your IDE

### Versioning

We use [Semantic Versions](http://semver.org/) for versioning. For the versions available, see the tags on this repository.


### Integration Tests

The automated integration tests can be found in the `src/integration-test/` folder.
Every test suite spawns an embedded version of MongoDB which the tests are run against.

There is also a manual way to test the system in an integration test fashion.
The idea is to compare the database export of the `live_ratings` collection of the master branch at the end of PSG-Strasbourg 
 simulation, to the export of the feature branch.

The master branch export can be found at `src/integration-test/resources/db_exports/ligue1.live_ratings-master.json`.
The export is done with the following aggregation on the `live_ratings` collection of the `ligue1` db:

```json lines
[
  {
    $group:
      {
        _id: "$playerId",
        ratings: {
          $push: "$$ROOT",
        },
      },
  },
  {
    $sort:
      {
        "ratings._id": 1,
      }
  },
  {
    $sort:
      {
        _id: 1,
      },
  },
  {
    $project:
      {
        "ratings._id": 0,
      },
  },
]
```

In order to evaluate how a new development affects the evolution of live ratings (hence the events sent to kafka), the
recommended process is to:
- apply the same aggregation to the `live_ratings` collection after running the match simulator.
- export the result into a Json "relaxed" format (you can find it in the "Advanced JSON Format" tab of the export window in Mongo Compass)
- compare the branch export to the master export with a diff tool (eg. Meld)

It might be useful to also remove the "feedId" field from the export as it contains a timestamp that will always show 
different results depending on when the simulator is run.
The field is currently present in the master export as it might be useful to debug issues.


### Docker local

Build local docker image
```bash
# login on normal docker hub to be able to download the images
docker login --username ${DOCKER_USER} --password ${DOCKER_PASSWORD}

# Build the local image
./gradlew jib

# you can see the images 
docker images

# and run (add one more -p 5005:5005 to expose the remote debug port to use on the ide if needed) 
docker run -p 8080:8080 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/ingestor 
## or
docker run -p 8080:8080 -p 5005:5005 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/ingestor:${SPECIFIC_VERSION}

# to see the process running:
diego@ data-ingestor > docker ps
CONTAINER ID        IMAGE               COMMAND                  CREATED             STATUS              PORTS                              NAMES
d3cc934d2ffa        ingestor            "java -cp /app/resou…"   15 seconds ago      Up 13 seconds       5005/tcp, 0.0.0.0:8080->8080/tcp   vigilant_feynman
59df7def01c2        mongo               "docker-entrypoint.s…"   2 minutes ago       Up 2 minutes        0.0.0.0:27017->27017/tcp           docker_mongo_1 

```

If you want to run the stack locally (ingestor + mongo) just run the following command from the `/docker` folder:
```
docker-compose up
```

For debugging you use either docker or running locally the application. 
For local debug don't forget to replace the ENV_LOCAL_HOST with localhost(dont commit)
For docker use remote debug on port 5005(Still needs docker compose with mongo/kafka... running)
Production will expose the remote debug ports till we decide we don't need anymore. This will make easier to debug if there's a problem there.

### Actuator endpoints
http://{$HOST}/prometheus
http://{$HOST}/health
http://{$HOST}/info


### Publishing to Amazon ECR(Elastic container registry)
You need to have the aws cli configured
```bash
#Get git login for the private hub 
$(aws ecr get-login --no-include-email --region eu-west-1)

#Publish the image to aws ecr (check the images on aws ECR) 
mvn compile jib:build

#pull the image(the version will be printed out on the command jib:build)
docker pull 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/ingestor:${IMAGE_VERSION}

# run the image(same as locally)
docker run -p 8080:8080 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/ingestor
```

## Installing Mongo as Replica Set

A mongo as replica set is needed for the change-streams feature we're using to stream db changes to clients (live ratings, 
contracts).

You can run one on your local machine using Docker and the docker-compose.yml file in the `/docker` folder.

```bash
cd docker
docker-compose up mongo-replica
```

## Live Ratings Simulator

A Live Ratings Simulator script `simulate_live_ratings.py` is available under `src/main/scripts/index/simulators/ratings`.
It can be used to simulate live ratings being retrieved by the ingestor and stored in the db. 
Once the script is launched, it'll run a infinite loop that sends a new live rating to the ingestor every 10sec.

```bash
python simulate_live_ratings.py {ingestor_url} {folder}
```

where:
- `ingestor_url` is the url of the ingestor you want to send the ratings to (eg. `http://localhost:8080`).
- `folder` is the path of the folder where the ma2 ratings are stored. (default to `seriea/20-21/live/`).

This can be particularly useful when testing the live ratings stream API.

### Publishing Live Ratings Simulator to ECR

```bash
IMAGE_VERSION="1.$(date '+%Y%m%d%H%M').$(git rev-parse --short HEAD)"

cd src/main/scripts/simulators/ratings
docker build -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-ratings-simulator:$IMAGE_VERSION -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-ratings-simulator:latest . -f docker/Dockerfile
docker push 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-ratings-simulator:$IMAGE_VERSION
```

## Live Contracts Simulator

A Live Contracts Simulator script `simulate_live_contracts.py` is available under `src/main/scripts/index/simulators/contracts`.
It can be used to simulate live contracts being stored in the db.
Once the script is launched, it'll run a infinite loop that stores a new live contract into the db every 10sec.

```bash
python simulate_live_contracts.py {db_url} {db_name}
```

where:
- `db_url` is the url of the db you want to store the contracts into. (eg. `mongodb://localhost:27001,localhost:27002/?replicaSet=mongo-rs`).
- `db_name` is the name of the db. (eg. `test_customer`).

This can be particularly useful when testing the live contracts stream API.

### Publishing Live Contracts Simulator to ECR

```bash
IMAGE_VERSION="1.$(date '+%Y%m%d%H%M').$(git rev-parse --short HEAD)"

cd src/main/scripts/simulators/contracts
docker build -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-contracts-simulator:$IMAGE_VERSION -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-contracts-simulator:latest . -f docker/Dockerfile
docker push 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-contracts-simulator:$IMAGE_VERSION
```


## Live Match Simulator

A Live Match Simulator script `simulate_live_match.py` is available under `src/main/scripts/index/simulators/match`.
It can be used to simulate live match sending them to the ingestor.
Once the script is launched, it'll run a infinite loop that sends a match or more match in the following order:
1. Send last feed of the previous match
2. Send lineups of the match
3. Send all the live feeds of the match
4. Send the final feed of the match

### Folder Structure
All the full matches must be in the folder `matches/{tournament}/`.

Each match folder should include 3 folders:
- `/prev` this folder must include the last 2 feeds of previous matches (one for each team).
- `/lineups` this folder must include the feed at `matchTime=0`.
- `/live` this folder must include all the live feeds in alphabetical order .
- `/final` this folder must include all the final feeds in alphabetical order .


### Export Env Variables
Export the following variables:

**Required variables**
- `ENV_LIVE_MATCH_INGESTOR_URL` is the url of the ingestor you want to send the ratings to (eg. `http://localhost:8080`).
- `ENV_LIVE_MATCH_DB_NAME` is the name of the db where ratings and props are stored. (eg.`wsf`).
- `ENV_LIVE_MATCH_DB_URL` is the url of the db where ratings and props are stored. (eg.`mongodb://...`).
- `ENV_LIVE_MATCH_DB_COLLECTION` name of the tournament db (e.g. `seriea`)
- `ENV_LIVE_MATCH_TOURNAMENT_FOLDER` name of the tournament folder with feeds (e.g. `seriea`)
- `ENV_LIVE_MATCH_COMPETITION_ID` competition id (e.g `5d3a33fe999fc4072ab589af`)
- `ENV_LIVE_MATCH_TOURNAMENT_ID` id of the tournament (e.g. `5d3a33fe999fc4072ab589af`)

**Optional variables**
- `ENV_LIVE_MATCH_SEND_PREV_MATCH` (optional) True to send previous matches, false otherwise (default: `true`)
- `ENV_LIVE_MATCH_TEST_MODE` (optional) True to start in test mode (only few live feeds sent per match), false otherwise (default: `false`) 

### Start simulator
```bash
python simulate_live_match.py
```

### Publishing Live Match Simulator to ECR

```bash
APP_VERSION="1.$(date '+%Y%m%d%H%M').$(git rev-parse --short HEAD)"

cd src/main/scripts/simulators/match
docker build -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-match-simulator:$APP_VERSION -t 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-match-simulator:latest . -f docker/Dockerfile
docker push 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/live-match-simulator:$APP_VERSION
```

## Releasing
See [Deployment](https://github.com/wallstreetfootball/k8s-cicd/blob/master/docs/service-deployment.md)


### Release specific tournament
Create helm package:
```
helm package .helm/ --app-version $APP_VERSION
```
Upgrade live match simulator helm release with tournament values:
```
helm upgrade --install live-match-simulator live-match-simulator-0.1.0.tgz -f .helm/values_seriea.yaml
```

## Dev Team
- ___Amod Pandey___
- ___Diego Furtado___
- ___Marco Campanelli___

### Built With

- [Spring boot](https://docs.spring.io/spring-boot/docs/1.5.9.BUILD-SNAPSHOT/reference/htmlsingle) - Main Framework
- [Maven](https://maven.apache.org/) - Dependency Management
- [Spring data mongodb](https://docs.spring.io/spring-data/mongodb/docs/current/reference/html/) - Spring Data repositories for mongodb
- [Dropwizard Metrics](http://metrics.dropwizard.io/3.2.3/getting-started.html) - Metrics Framework
