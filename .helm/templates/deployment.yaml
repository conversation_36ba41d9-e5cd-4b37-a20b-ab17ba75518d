apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
  {{- include "common.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.deployment.replicaCount }}
  selector:
    matchLabels:
  {{- include "common.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.deployment.podAnnotations }}
      annotations:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
    {{- include "common.labels" . | nindent 8 }}
    spec:
      {{- if .Values.deployment.serviceAccount }}
      serviceAccountName: {{ .Values.deployment.serviceAccount.name }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.deployment.image.repository }}:{{ .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.deployment.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{.Values.service.port}}
              protocol: TCP
          startupProbe:
            httpGet:
              path: {{ .Values.deployment.startup.path }}
              port: http
            initialDelaySeconds: {{ .Values.deployment.startup.initialDelaySeconds }}
            periodSeconds: {{ .Values.deployment.startup.periodSeconds }}
            failureThreshold: {{ .Values.deployment.startup.failureThreshold }}
          livenessProbe:
            httpGet:
              path: {{ .Values.deployment.liveness.path }}
              port: http
            initialDelaySeconds: {{ .Values.deployment.liveness.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.deployment.liveness.timeoutSeconds }}
            periodSeconds: {{ .Values.deployment.liveness.periodSeconds }}
            failureThreshold: {{ .Values.deployment.liveness.failureThreshold }}
          readinessProbe:
            httpGet:
              path: {{ .Values.deployment.readness.path }}
              port: http
            initialDelaySeconds: {{ .Values.deployment.readness.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.deployment.readness.timeoutSeconds }}
            periodSeconds: {{ .Values.deployment.readness.periodSeconds }}
            failureThreshold: {{ .Values.deployment.readness.failureThreshold }}
          env:
            - name: spring.profiles.active
              value: {{ .Values.service.env }}
            - name: ENV_OPTA_OUTLET_KEY
              valueFrom:
                secretKeyRef:
                  name: ingestor-{{ .Values.service.env }}
                  key: opta-outlet-key
            - name: ENV_SPORTMONKS_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: ingestor-{{ .Values.service.env }}
                  key: sportmonks-api-token
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: ingestor-{{ .Values.service.env }}
                  key: db-url
            - name: ENV_EMAIL_APP_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ingestor-{{ .Values.service.env }}
                  key: techtools-email-app-password
          resources:
      {{- toYaml .Values.deployment.resources | nindent 12 }}
      {{- with .Values.deployment.nodeSelector }}
      nodeSelector:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.deployment.affinity }}
      affinity:
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.deployment.tolerations }}
      tolerations:
  {{- toYaml . | nindent 8 }}
  {{- end }}
