{{- define "common.labels" -}}
{{ include "common.selectorLabels" . }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
roles: {{ include "common.utils.join" .Values.service.roles}}
{{ include "common.selectorLabels" . }}
{{- end }}

{{- define "common.selectorLabels" -}}
environment: {{.Values.service.env}}
service: {{ .Chart.Name }}
app.kubernetes.io/name: {{ .Release.Name }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}


{{- define "common.utils.join" -}}
{{- $local := dict "first" true -}}
{{- range $k, $v := . -}}{{- if not $local.first -}}-{{- end -}}{{- $v -}}{{- $_ := set $local "first" false -}}{{- end -}}
{{- end -}}