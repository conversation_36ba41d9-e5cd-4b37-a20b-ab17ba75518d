service:
  env: sandbox
  type: NodePort # change to ClusterIP
  port: 8080
  roles:
    - api
    - public

deployment:
  port: 80
  replicaCount: 1
  startup:
    path: /health
    initialDelaySeconds: 25
    periodSeconds: 10
    failureThreshold: 20
  liveness:
    path: /health
    initialDelaySeconds: 5
    timeoutSeconds: 15
    periodSeconds: 10
    failureThreshold: 10
  readness:
    path: /health
    initialDelaySeconds: 5
    timeoutSeconds: 15
    periodSeconds: 10
    failureThreshold: 10
  image:
    repository: 268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release/data-ingestor
    pullPolicy: IfNotPresent
  resources:
    limits:
      cpu: 2000m
      memory: 800Mi
    requests:
      cpu: 100m
      memory: 300Mi
  podAnnotations: {
    prometheus.io/scrape: 'true',
    prometheus.io/path: '/prometheus'
  }
  imagePullSecrets: [ ]
  nodeSelector:
    role: general
  tolerations:
    - key: "role"
      operator: "Equal"
      value: "general"
      effect: "NoSchedule"
  affinity: {}
