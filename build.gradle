plugins {
  id 'java'
  id 'org.springframework.boot' version '3.1.6'
  id 'io.spring.dependency-management' version '1.1.0'
  id 'com.google.cloud.tools.jib' version '3.4.0'
}

version = '2'
group = 'com.wsf.dataingestor'

project.sourceCompatibility = JavaVersion.VERSION_21
project.targetCompatibility = JavaVersion.VERSION_21

project.ext {
  privateDockerHub = '268764888866.dkr.ecr.eu-west-1.amazonaws.com/services-release'
}

repositories {
  mavenLocal()
  maven {
    url "s3://wsf-repository/releases"
    credentials(AwsCredentials) {
      accessKey = System.getenv("AWS_S3_REPO_ACCESS_KEY") ?: findProperty('aws_s3_repo_access_key')
      secretKey = System.getenv("AWS_S3_REPO_SECRET_KEY") ?: findProperty('aws_s3_repo_secret_key')
    }
  }
  mavenCentral()
}

dependencies {

  implementation 'com.wsf:db-infra:8.10.9'
  implementation 'com.wsf.kafka:producers:3.6.0'
  implementation 'com.wsf.kafka:interface:6.2.1'
  implementation 'com.wsf:rserve-client:1.23.1'

  implementation 'org.springframework.boot:spring-boot-starter'
  implementation 'org.springframework.boot:spring-boot-starter-aop'
  implementation 'org.springframework.boot:spring-boot-starter-cache'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  implementation 'org.springframework.boot:spring-boot-starter-logging'
  implementation 'org.springframework.boot:spring-boot-actuator-autoconfigure'
  implementation 'org.springframework.boot:spring-boot-starter-data-redis'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation "org.springframework.boot:spring-boot-starter-mail"
  implementation 'org.springframework:spring-aspects'
  implementation 'org.springframework:spring-context-support:5.2.10.RELEASE'
  implementation "org.springframework.kafka:spring-kafka"
  implementation "org.springframework.retry:spring-retry"

  implementation 'javax.mail:mail:1.4.7'
  implementation 'com.github.ben-manes.caffeine:caffeine'
  implementation 'org.apache.httpcomponents.client5:httpclient5'
  implementation 'com.jano7:executor:2.0.2'
  implementation 'io.micrometer:micrometer-registry-prometheus'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.3'
  implementation 'com.fasterxml.jackson.datatype:jackson-datatype-guava:2.11.3'
  implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.9.8'
  implementation 'org.quartz-scheduler:quartz:2.3.2'
  implementation 'io.fluidsonic.mirror:quartz-mongodb:2.2.0-rc2'
  implementation 'com.neovisionaries:nv-websocket-client:2.14'
  implementation 'org.json:json:20231013'
  implementation 'org.rosuda.REngine:Rserve:1.8.1'
  implementation 'org.apache.commons:commons-pool2:2.11.1'
  implementation 'net.jodah:failsafe:2.4.0'
  implementation 'com.google.guava:guava:32.0.0-jre'
  implementation 'org.apache.commons:commons-lang3:3.11'
  implementation 'org.apache.commons:commons-collections4:4.4'
  implementation 'org.apache.commons:commons-text:1.10.0'
  implementation 'commons-io:commons-io:2.16.1'
  implementation 'org.apache.commons:commons-csv:1.7'
  implementation 'commons-fileupload:commons-fileupload:1.5'
  implementation 'com.amazonaws:aws-java-sdk-s3:1.12.268'
  implementation 'jakarta.validation:jakarta.validation-api:3.0.2'
  implementation 'org.hibernate.validator:hibernate-validator:8.0.1.Final'
  implementation 'org.openjdk.jmh:jmh-core:1.19'
  implementation 'org.openjdk.jmh:jmh-generator-annprocess:1.19'
  implementation 'software.amazon.msk:aws-msk-iam-auth:1.1.9'
  annotationProcessor 'org.projectlombok:lombok'
  compileOnly 'org.projectlombok:lombok'

  // Test
  testImplementation 'junit:junit'
  testImplementation 'pl.pragmatists:JUnitParams:1.1.1'

  testImplementation 'org.springframework.boot:spring-boot-starter-test:2.3.5.RELEASE'
  testImplementation 'org.projectlombok:lombok'
  testAnnotationProcessor 'org.projectlombok:lombok'
  testImplementation "de.flapdoodle.embed:de.flapdoodle.embed.mongo:3.4.8"
  testImplementation 'org.awaitility:awaitility:3.0.0'
}

jib {
  from {
    image = 'amazoncorretto:21-alpine3.19-jdk'
    platforms {
      platform {
        architecture = 'amd64'
        os = 'linux'
      }
      platform {
        architecture = 'arm64'
        os = 'linux'
      }
    }
  }
  to {
    image = "$privateDockerHub/${project.name}"
    tags = ['latest', "${getVersionTag()}"]
    auth {
      username = 'AWS'
      password = "aws ecr get-login-password --region eu-west-1".execute().text.trim()
    }
  }
  container {
    mainClass = 'com.wsf.dataingestor.DataIngestorApplication'
    ports = ['8082']
    labels = [service: 'data-ingestor']
    jvmFlags = [
      '-Xmx800m', // Heap size
    ]
  }
}

def getVersionTag() {
  def date = new Date().format('yyyyMMdd.HHmmss')
  def gitCommitHash = 'git rev-parse --verify --short HEAD'.execute().text.trim()
  return "${project.version}.${date}-${gitCommitHash}"
}

task unitTest(type: Test) {
  useJUnitPlatform()
  include 'com/wsf/dataingestor/unit/**'
}

task integrationTest(type: Test) {
  useJUnitPlatform()
  include 'com/wsf/dataingestor/integration/**'
}

task componentTest(type: Test) {
  useJUnitPlatform()
  include 'com/wsf/dataingestor/component/**'
}
