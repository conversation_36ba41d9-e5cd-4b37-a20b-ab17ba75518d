defaults: &defaults
  docker:
    - image: cimg/openjdk:21.0.2
  working_directory: ~/data-ingestor

version: 2.1

orbs:
  aws-cli: circleci/aws-cli@1.3.0

jobs:

  build-integration:
    <<: *defaults
    steps:
      - checkout
      - restore_cache:
          keys:
            - gradle-{{ checksum "build.gradle" }}
      - aws-cli/install
      - run:
          name: build project
          command: |
            ./gradlew assemble
      - run:
          name: install openssl
          command: |
            wget http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
            sudo dpkg -i libssl1.1_1.1.1f-1ubuntu2_amd64.deb
      - run:
          name: unit tests
          command: ./gradlew unitTest
      - store_test_results:
          path: ./build/test-results/unitTest
      - run:
          name: integration tests
          command: ./gradlew integrationTest
      - store_test_results:
          path: ./build/test-results/integrationTest
      - run:
          name: component tests
          command: ./gradlew componentTest
      - store_test_results:
          path: ./build/test-results/componentTest
      - save_cache:
          key: gradle-{{ checksum "build.gradle" }}
          paths:
            - ~/.gradle
      - persist_to_workspace:
          root: .
          paths:
            - '*'

  publish:
    <<: *defaults
    executor: aws-cli/default
    steps:
      - attach_workspace:
          at: ~/data-ingestor
      - restore_cache:
          keys:
            - gradle-{{ checksum "build.gradle" }}
      - setup_remote_docker:
          docker_layer_caching: false
      - aws-cli/install
      - aws-cli/setup:
          aws-access-key-id: AWS_EKS_ACCESS_KEY
          aws-secret-access-key: AWS_EKS_SECRET_KEY
          aws-region: AWS_REGION
      - run:
          name: aws ecr login
          command: aws ecr get-login-password | docker login --username AWS --password-stdin 268764888866.dkr.ecr.eu-west-1.amazonaws.com
      - run:
          name: docker publish
          command: |
            ./gradlew jib
      - save_cache:
          key: gradle-{{ checksum "build.gradle" }}
          paths:
            - ~/.gradle

workflows:
  workflow:
    jobs:
      - build-integration:
          context: global
      - publish:
          context: global
          requires:
            - build-integration
          filters:
            branches:
              only:
                - master
